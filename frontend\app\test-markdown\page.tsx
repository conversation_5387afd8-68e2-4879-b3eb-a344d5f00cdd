"use client"

import { FormattedMessage } from "@/components/formatted-message"

const testMarkdown = "您好，系统管理员！👋\n\n" +
  "我是 **CellForge AI** 智能顾问，专注于单细胞测序技术咨询。\n\n" +
  "🔬 **我的专长**：\n" +
  "• 单细胞RNA测序 (scRNA-seq)\n" +
  "• 单细胞ATAC测序 (scATAC-seq)\n" +
  "• 多组学测序 (Multiome)\n" +
  "• 空间转录组学\n" +
  "• 实验设计与数据分析\n\n" +
  "💡 **打招呼式**：\n" +
  "您可以直接提问，我会智能收集您的需求信息，或者使用右侧的需求收集助手来填写项目信息。\n\n" +
  "请告诉我您的研究目标，我来为您制定最适合的技术方案！\n\n" +
  "💎 置信度: 100%\n\n" +
  "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n" +
  "📚 **相关文献支持**\n\n" +
  "**[1] Comprehensive Integration of Single-Cell Data**\n" +
  "*<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, P.等*\n" +
  "📖 Nature Biotechnology (2018)\n" +
  "🏆 影响因子: 36.558\n" +
  "💡 **相关性**: 高影响因子期刊(Nature Biotechnology, IF: 36.558); 高引用文献(8500次引用); 与单细胞测序高度相关\n" +
  "🔑 **支持要点**: 关键发现: Seurat v3 provides comprehensive tools for single-cell data integration and analysis\n" +
  "🔗 DOI: 10.1038/nbt.4096\n\n" +
  "**[2] 单细胞数据分析专家（开源流程）**\n" +
  "**联系人**：\n" +
  "Prof. Chen Xia（清华大学生物信息中心）\n" +
  "**专长**：\n" +
  "• 单细胞数据分析算法开发\n" +
  "• 开源工具链构建和优化\n" +
  "• 大规模数据处理和可视化"

export default function TestMarkdownPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Markdown Rendering Test</h1>

        <div className="bg-white border border-slate-200 rounded-2xl p-6 shadow-sm">
          <h2 className="text-lg font-semibold mb-4">Original Text:</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm mb-6 whitespace-pre-wrap">
            {testMarkdown}
          </pre>

          <h2 className="text-lg font-semibold mb-4">Formatted Result:</h2>
          <div className="border border-gray-200 p-4 rounded">
            <FormattedMessage content={testMarkdown} />
          </div>
        </div>
      </div>
    </div>
  )
}
