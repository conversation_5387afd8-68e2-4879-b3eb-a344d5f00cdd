"""
文献服务统一接口
定义所有文献相关服务的标准接口和契约
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, AsyncIterator
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ServiceHealth(Enum):
    """服务健康状态"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNAVAILABLE = "unavailable"


class SearchStrategy(Enum):
    """搜索策略类型"""
    COMPREHENSIVE = "comprehensive"  # 全面搜索
    TARGETED = "targeted"           # 精准搜索
    FALLBACK = "fallback"          # 降级搜索


@dataclass
class ServiceMetrics:
    """服务性能指标"""
    response_time_ms: float
    success_rate: float
    cache_hit_rate: float
    error_count: int
    last_error: Optional[str] = None


@dataclass
class SearchRequest:
    """标准化搜索请求"""
    query: str
    max_results: int = 10
    strategy: SearchStrategy = SearchStrategy.COMPREHENSIVE
    user_requirements: Optional[Dict[str, Any]] = None
    intent_analysis: Optional[Dict[str, Any]] = None
    timeout_seconds: int = 30
    enable_cache: bool = True
    fallback_enabled: bool = True


@dataclass
class SearchResponse:
    """标准化搜索响应"""
    papers: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    source_info: Dict[str, List[str]]  # 数据源信息
    cache_used: bool
    response_time_ms: float
    strategy_used: SearchStrategy
    success: bool
    error_message: Optional[str] = None


@dataclass
class RecommendationRequest:
    """推荐请求"""
    requirements: Dict[str, Any]
    user_message: str = ""
    recommendation_type: str = "comprehensive"
    enable_ai_analysis: bool = True
    timeout_seconds: int = 60


@dataclass
class RecommendationResponse:
    """推荐响应"""
    hot_papers: List[Dict[str, Any]]
    expanded_keywords: Dict[str, List[str]]
    search_links: List[Dict[str, Any]]
    literature_results: Dict[str, Any]
    analysis_summary: str
    confidence_score: float
    generation_metadata: Dict[str, Any]
    success: bool
    error_message: Optional[str] = None


class LiteratureServiceInterface(ABC):
    """文献服务统一接口"""
    
    @abstractmethod
    async def initialize(self) -> bool:
        """
        初始化服务
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> ServiceHealth:
        """
        服务健康检查
        
        Returns:
            ServiceHealth: 服务健康状态
        """
        pass
    
    @abstractmethod
    async def get_metrics(self) -> ServiceMetrics:
        """
        获取服务性能指标
        
        Returns:
            ServiceMetrics: 服务指标
        """
        pass
    
    @abstractmethod
    async def search_literature(self, request: SearchRequest) -> SearchResponse:
        """
        搜索文献
        
        Args:
            request: 搜索请求
            
        Returns:
            SearchResponse: 搜索结果
        """
        pass
    
    @abstractmethod
    async def batch_search(self, requests: List[SearchRequest]) -> List[SearchResponse]:
        """
        批量搜索文献
        
        Args:
            requests: 搜索请求列表
            
        Returns:
            List[SearchResponse]: 搜索结果列表
        """
        pass
    
    @abstractmethod
    async def stream_search(self, request: SearchRequest) -> AsyncIterator[Dict[str, Any]]:
        """
        流式搜索文献（逐步返回结果）
        
        Args:
            request: 搜索请求
            
        Yields:
            Dict[str, Any]: 增量搜索结果
        """
        pass
    
    @abstractmethod
    async def cleanup(self):
        """清理资源"""
        pass


class RecommendationServiceInterface(ABC):
    """推荐服务统一接口"""
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化推荐服务"""
        pass
    
    @abstractmethod
    async def health_check(self) -> ServiceHealth:
        """服务健康检查"""
        pass
    
    @abstractmethod
    async def generate_recommendations(self, request: RecommendationRequest) -> RecommendationResponse:
        """
        生成推荐
        
        Args:
            request: 推荐请求
            
        Returns:
            RecommendationResponse: 推荐结果
        """
        pass
    
    @abstractmethod
    async def get_hot_papers(self, requirements: Dict[str, Any], max_results: int = 6) -> List[Dict[str, Any]]:
        """获取热点文献"""
        pass
    
    @abstractmethod
    async def generate_keywords(self, requirements: Dict[str, Any]) -> Dict[str, List[str]]:
        """生成扩展关键词"""
        pass
    
    @abstractmethod
    async def create_search_links(self, requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建搜索链接"""
        pass
    
    @abstractmethod
    async def cleanup(self):
        """清理资源"""
        pass


class ServiceRegistryInterface(ABC):
    """服务注册中心接口"""
    
    @abstractmethod
    async def register_service(self, service_name: str, service_instance: Any) -> bool:
        """注册服务"""
        pass
    
    @abstractmethod
    async def unregister_service(self, service_name: str) -> bool:
        """注销服务"""
        pass
    
    @abstractmethod
    async def get_service(self, service_name: str) -> Optional[Any]:
        """获取服务实例"""
        pass
    
    @abstractmethod
    async def get_healthy_service(self, service_name: str) -> Optional[Any]:
        """获取健康的服务实例"""
        pass
    
    @abstractmethod
    async def list_services(self) -> Dict[str, ServiceHealth]:
        """列出所有服务及其健康状态"""
        pass


# 服务名称常量
class ServiceNames:
    """标准化服务名称"""
    UNIFIED_LITERATURE = "unified_literature"
    EXTERNAL_LITERATURE = "external_literature"
    RECOMMENDATION_HUB = "recommendation_hub"
    AI_DRIVEN_LITERATURE = "ai_driven_literature"
    INTELLIGENT_RECOMMENDATION = "intelligent_recommendation"
    COMPREHENSIVE_SOLUTION = "comprehensive_solution"


# 错误类型
class ServiceError(Exception):
    """服务基础异常"""
    def __init__(self, message: str, service_name: str = "", error_code: str = ""):
        super().__init__(message)
        self.service_name = service_name
        self.error_code = error_code


class ServiceUnavailableError(ServiceError):
    """服务不可用异常"""
    pass


class ServiceTimeoutError(ServiceError):
    """服务超时异常"""
    pass


class ServiceDegradedError(ServiceError):
    """服务降级异常"""
    pass


class ConfigurationError(ServiceError):
    """配置错误异常"""
    pass