# CellForge AI智能对话页面完整开发提示词

## 🎯 项目概述

你是一个全栈开发专家，需要开发一个专业的单细胞测序方案生成系统的智能对话界面 - **CellForge AI**。

### 产品定位
- **目标用户**：生物技术研究人员、实验室管理员、生物技术公司
- **核心功能**：通过AI对话收集需求，智能生成个性化的单细胞测序实验方案
- **技术领域**：单细胞RNA测序(scRNA-seq)、单细胞ATAC测序(scATAC-seq)、多组学测序(Multiome)

## 🏗️ 技术架构要求

### 前端技术栈
- **React 18** + **TypeScript** + **Tailwind CSS**
- **Framer Motion** (动画效果)
- **React Query** (数据管理)
- **Zustand** (状态管理)
- **React Hook Form** (表单处理)
- **React Markdown** (消息渲染)

### 设计系统
- **现代生物科技风格**：深蓝色主题 + 科技感渐变
- **响应式设计**：支持桌面端和移动端
- **无障碍友好**：支持键盘导航和屏幕阅读器

## 🎨 界面设计规范

### 配色方案
```css
:root {
  /* 主色调 - 深蓝科技风 */
  --primary-blue: #1e40af;
  --primary-blue-light: #3b82f6;
  --primary-blue-dark: #1e3a8a;
  
  /* 辅助色 */
  --accent-cyan: #06b6d4;
  --accent-purple: #8b5cf6;
  --accent-green: #10b981;
  
  /* 中性色 */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* 功能色 */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
}
```

### 布局结构
```
┌─────────────────────────────────────────────────────────┐
│ Header: CellForge AI Logo + Navigation                 │
├─────────────┬─────────────────────────┬─────────────────┤
│             │                         │                 │
│ Left        │    Main Chat Area       │ Right Sidebar   │
│ Sidebar     │                         │ (需求收集助手)   │
│             │                         │                 │
│ • 智能对话   │  ┌─ AI消息 ─┐            │ ┌─ 进度显示 ─┐   │
│ • 客户画像   │  │Hello! 👋│            │ │ 完成度: 0%  │   │
│ • 方案展示   │  └─────────┘            │ └───────────┘   │
│ • 知识库     │                         │                 │
│ • 数据分析   │  ┌─ 用户消息 ─┐          │ ┌─ 当前阶段 ─┐   │
│             │  │我想做...  │          │ │ 基础信息   │   │
├─────────────┤  └─────────┘            │ └───────────┘   │
│ 用户信息     │                         │                 │
│ 系统: 管理员 │  [输入框 + 发送按钮]     │ [智能建议卡片]   │
└─────────────┴─────────────────────────┴─────────────────┘
```

## 💻 核心组件开发

### 1. 主应用组件 (App.tsx)
```typescript
// 需要实现的功能：
interface AppProps {
  // 全局状态管理
  // 路由配置
  // 主题提供者
  // 错误边界
}

// 主要特性：
- 响应式布局切换（桌面/移动端）
- 全局错误处理和loading状态
- 主题切换支持（明/暗主题）
- 国际化支持（中/英文）
```

### 2. 聊天界面组件 (ChatInterface.tsx)
```typescript
interface ChatInterfaceProps {
  // 消息历史管理
  messages: Message[];
  // 实时对话状态
  isTyping: boolean;
  // AI响应处理
  onSendMessage: (message: string) => void;
}

// 核心功能要求：
1. 消息展示
   - 用户消息：右侧对齐，蓝色气泡
   - AI消息：左侧对齐，白色气泡，带头像
   - 系统消息：居中显示，灰色文本
   - 支持Markdown渲染（代码块、表格、列表）

2. 实时打字效果
   - AI回复时显示"CellForge AI正在思考..."
   - 逐字显示效果（typewriter animation）
   - 消息状态指示（发送中/已发送/已读）

3. 交互功能
   - 消息复制功能
   - 重新生成回答
   - 消息点赞/点踩反馈
   - 快速回复建议按钮

4. 输入增强
   - 自动扩展的文本框
   - 支持换行和格式化
   - 快捷键支持（Ctrl+Enter发送）
   - 文件上传（拖拽或点击）
   - 语音输入（可选）
```

### 3. 智能建议组件 (SmartSuggestions.tsx)
```typescript
interface SmartSuggestionsProps {
  // 当前对话上下文
  context: ConversationContext;
  // 建议点击处理
  onSuggestionClick: (suggestion: string) => void;
}

// 设计要求：
1. 建议卡片样式
   - 圆角卡片，浅蓝色背景
   - hover效果和点击动画
   - 图标 + 文字的组合
   - 建议类型分类（问题/操作/信息）

2. 智能建议内容
   - 基于当前阶段显示相关建议
   - 例如："我需要进行单细胞RNA测序"
   - "请推荐适合心脏组织的方案"
   - "我的预算有限，有什么经济方案？"
   - "解释不同测序平台的区别"

3. 动态更新
   - 根据对话进展更新建议
   - 避免重复建议
   - 优先显示高相关性建议
```

### 4. 需求收集助手 (RequirementCollector.tsx)
```typescript
interface RequirementCollectorProps {
  // 收集进度
  progress: number;
  // 当前需求
  requirements: UserRequirement;
  // 更新处理
  onRequirementUpdate: (req: Partial<UserRequirement>) => void;
}

// 界面设计：
1. 进度显示
   - 环形进度条，显示完成百分比
   - 阶段列表：基础信息→项目规划→技术细节→高级选项
   - 已完成项目标记为绿色✓

2. 表单组件
   - 研究目标：多选框 + 自定义输入
   - 样本类型：下拉选择 + 智能搜索
   - 预期细胞数：滑块选择器
   - 预算范围：范围滑块
   - 时间要求：时间选择器

3. 实时验证
   - 字段级验证和错误提示
   - 智能默认值建议
   - 不兼容选择的警告提示
   - 保存草稿功能
```

### 5. 方案预览组件 (ProtocolPreview.tsx)
```typescript
interface ProtocolPreviewProps {
  // 当前生成的方案
  protocol: Protocol | null;
  // 预估信息
  estimates: CostTimeEstimate;
}

// 展示内容：
1. 方案卡片
   - 方案名称和描述
   - 技术路线图标
   - 关键指标（成本/时间/细胞数）
   - 置信度评分

2. 成本预览
   - 总成本显示
   - 成本构成饼图
   - 与预算的对比
   - 优化建议

3. 时间规划
   - 总时长显示
   - 关键阶段时间线
   - 里程碑节点

4. 交互功能
   - 查看详细方案
   - 修改配置
   - 保存方案
   - 分享链接
```

## 🔧 状态管理 (Zustand Store)

### 应用状态结构
```typescript
interface AppState {
  // UI状态
  ui: {
    sidebarCollapsed: boolean;
    theme: 'light' | 'dark';
    currentView: 'chat' | 'requirements' | 'protocols';
    isLoading: boolean;
    errors: Record<string, string>;
  };

  // 用户状态
  user: {
    id: string;
    name: string;
    role: 'admin' | 'researcher' | 'student';
    preferences: UserPreferences;
  };

  // 对话状态
  chat: {
    currentSessionId: string;
    messages: Message[];
    isTyping: boolean;
    typingMessage: string;
    suggestions: Suggestion[];
  };

  // 需求收集状态
  requirements: {
    data: UserRequirement;
    progress: number;
    currentStep: number;
    validationErrors: Record<string, string>;
    isComplete: boolean;
  };

  // 方案状态
  protocols: {
    current: Protocol | null;
    alternatives: Protocol[];
    estimates: CostTimeEstimate;
    comparison: ProtocolComparison | null;
  };

  // 操作方法
  actions: {
    // UI操作
    toggleSidebar: () => void;
    setTheme: (theme: 'light' | 'dark') => void;
    setCurrentView: (view: string) => void;
    
    // 聊天操作
    sendMessage: (content: string) => Promise<void>;
    addMessage: (message: Message) => void;
    setTyping: (isTyping: boolean) => void;
    
    // 需求操作
    updateRequirement: (field: string, value: any) => void;
    validateRequirements: () => boolean;
    
    // 方案操作
    generateProtocol: () => Promise<void>;
    updateProtocol: (updates: Partial<Protocol>) => void;
  };
}
```

## 🎬 动画和交互效果

### 1. 页面过渡动画
```typescript
// 使用Framer Motion实现
const pageTransition = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
  transition: { duration: 0.3, ease: "easeInOut" }
};

// 消息动画
const messageAnimation = {
  initial: { opacity: 0, scale: 0.8 },
  animate: { opacity: 1, scale: 1 },
  transition: { duration: 0.2, ease: "easeOut" }
};
```

### 2. 微交互效果
```css
/* 按钮hover效果 */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 transform hover:scale-105 
         transition-all duration-200 ease-in-out;
}

/* 卡片悬停效果 */
.card-hover {
  @apply hover:shadow-lg hover:-translate-y-1 
         transition-all duration-300 ease-out;
}

/* 输入框聚焦效果 */
.input-focus {
  @apply focus:ring-2 focus:ring-blue-500 focus:border-blue-500
         transition-all duration-200;
}
```

## 📱 响应式设计

### 断点设计
```css
/* Tailwind CSS响应式断点 */
/* sm: 640px */
/* md: 768px */  
/* lg: 1024px */
/* xl: 1280px */
/* 2xl: 1536px */

/* 移动端布局 */
@media (max-width: 768px) {
  .main-layout {
    /* 隐藏侧边栏，改为抽屉式 */
    grid-template-columns: 1fr;
  }
  
  .sidebar {
    /* 转换为底部抽屉 */
    position: fixed;
    bottom: 0;
    transform: translateY(100%);
  }
}
```

### 移动端优化
- 触摸友好的按钮尺寸（最小44px）
- 滑动手势支持
- 虚拟键盘适配
- 横屏模式支持

## 🚀 性能优化要求

### 1. 代码分割
```typescript
// 路由级别的懒加载
const ChatInterface = lazy(() => import('./components/ChatInterface'));
const ProtocolViewer = lazy(() => import('./components/ProtocolViewer'));

// 组件级别的懒加载
const HeavyChart = lazy(() => import('./components/HeavyChart'));
```

### 2. 数据缓存
```typescript
// React Query配置
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
    },
  },
});
```

### 3. 虚拟化滚动
```typescript
// 长消息列表使用虚拟滚动
import { FixedSizeList as List } from 'react-window';

const MessageList = ({ messages }) => (
  <List
    height={600}
    itemCount={messages.length}
    itemSize={80}
  >
    {MessageItem}
  </List>
);
```

## 🧪 测试要求

### 1. 单元测试
```typescript
// 使用Jest + Testing Library
describe('ChatInterface', () => {
  test('should send message when enter is pressed', () => {
    render(<ChatInterface />);
    const input = screen.getByPlaceholderText('输入您的问题...');
    
    fireEvent.change(input, { target: { value: '测试消息' } });
    fireEvent.keyPress(input, { key: 'Enter', code: 'Enter' });
    
    expect(screen.getByText('测试消息')).toBeInTheDocument();
  });
});
```

### 2. E2E测试
```typescript
// 使用Cypress
describe('完整对话流程', () => {
  it('应该能够完成需求收集和方案生成', () => {
    cy.visit('/');
    cy.get('[data-testid="message-input"]').type('我需要做心脏组织的单细胞测序');
    cy.get('[data-testid="send-button"]').click();
    cy.get('[data-testid="ai-response"]').should('be.visible');
  });
});
```

## 🔌 API集成

### 1. HTTP客户端配置
```typescript
// Axios配置
const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

### 2. WebSocket连接
```typescript
// 实时消息处理
const useWebSocket = () => {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  
  useEffect(() => {
    const ws = new WebSocket(process.env.REACT_APP_WS_URL);
    
    ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      // 处理实时消息
    };
    
    setSocket(ws);
    return () => ws.close();
  }, []);
  
  return socket;
};
```

## 📋 开发清单

### Phase 1: 基础框架 (Week 1)
- [ ] 项目初始化和依赖安装
- [ ] 基础布局和路由配置
- [ ] 设计系统和主题配置
- [ ] 基础组件库开发

### Phase 2: 核心功能 (Week 2-3)
- [ ] 聊天界面开发
- [ ] 消息组件和动画效果
- [ ] 状态管理实现
- [ ] API集成和数据流

### Phase 3: 高级功能 (Week 4)
- [ ] 需求收集助手
- [ ] 方案预览和展示
- [ ] 智能建议系统
- [ ] 文件上传和处理

### Phase 4: 优化和测试 (Week 5)
- [ ] 性能优化
- [ ] 单元测试和E2E测试
- [ ] 响应式设计完善
- [ ] 无障碍功能实现

### Phase 5: 部署和监控 (Week 6)
- [ ] 生产环境配置
- [ ] CI/CD流水线
- [ ] 错误监控和日志
- [ ] 用户反馈收集

## 🎯 交付标准

### 1. 代码质量
- TypeScript严格模式，无any类型
- ESLint + Prettier代码规范
- 组件复用率 > 80%
- 测试覆盖率 > 85%

### 2. 性能指标
- 首次内容渲染 < 2秒
- 最大内容渲染 < 3秒
- 累积布局偏移 < 0.1
- 首次输入延迟 < 100ms

### 3. 用户体验
- 页面加载动画流畅
- 所有交互响应 < 200ms
- 支持键盘导航
- 移动端体验良好

### 4. 浏览器兼容
- Chrome 90+ ✅
- Firefox 88+ ✅  
- Safari 14+ ✅
- Edge 90+ ✅

---

## 💡 开发提示

1. **先搭建骨架**：从基础布局开始，逐步添加功能
2. **组件化思维**：每个功能都要做成可复用的组件
3. **类型安全**：充分利用TypeScript的类型检查
4. **用户体验优先**：每个交互都要有反馈和过渡效果
5. **性能意识**：避免不必要的重渲染和计算

请按照以上详细要求开发CellForge AI智能对话系统，确保代码质量、用户体验和性能都达到生产级标准！