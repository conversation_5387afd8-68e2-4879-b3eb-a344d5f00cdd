{"prompt_performance_config": {"version": "1.0", "description": "提示词性能配置文件", "last_updated": "2024-01-20T00:00:00Z", "performance_thresholds": {"excellent": {"quality_score": 0.9, "response_time": 2.0, "success_rate": 0.98, "user_satisfaction": 0.9}, "good": {"quality_score": 0.8, "response_time": 5.0, "success_rate": 0.95, "user_satisfaction": 0.8}, "acceptable": {"quality_score": 0.7, "response_time": 10.0, "success_rate": 0.9, "user_satisfaction": 0.7}, "poor": {"quality_score": 0.6, "response_time": 15.0, "success_rate": 0.8, "user_satisfaction": 0.6}}, "optimization_triggers": {"quality_degradation": {"threshold": 0.75, "consecutive_failures": 5, "time_window_hours": 24}, "response_time_increase": {"threshold": 8.0, "percentage_increase": 0.5, "time_window_hours": 6}, "success_rate_drop": {"threshold": 0.85, "time_window_hours": 12}, "user_feedback_decline": {"threshold": 0.65, "sample_size": 20}}, "ab_testing_config": {"statistical_significance": {"confidence_level": 0.95, "min_sample_size": 100, "max_test_duration_days": 14, "min_improvement_threshold": 0.05}, "traffic_allocation": {"control_group_min": 0.3, "control_group_max": 0.7, "max_concurrent_tests": 5}, "early_stopping": {"enabled": true, "check_interval_hours": 24, "min_runtime_hours": 48, "significance_threshold": 0.01}}, "quality_metrics": {"content_quality": {"weights": {"relevance": 0.3, "accuracy": 0.25, "completeness": 0.2, "clarity": 0.15, "structure": 0.1}, "scoring_rules": {"professional_terms_bonus": 0.1, "structure_bonus": 0.1, "length_penalty_threshold": 3000, "length_bonus_range": [100, 2000]}}, "user_experience": {"response_time_weight": 0.4, "content_quality_weight": 0.4, "user_satisfaction_weight": 0.2}, "business_metrics": {"conversion_rate": 0.3, "user_retention": 0.3, "task_completion": 0.4}}, "performance_analysis": {"time_windows": {"real_time": "5m", "short_term": "1h", "medium_term": "24h", "long_term": "7d"}, "aggregation_methods": {"response_time": "percentile_95", "quality_score": "mean", "success_rate": "ratio", "user_satisfaction": "weighted_mean"}, "comparison_baselines": {"previous_period": true, "control_group": true, "best_performer": true, "industry_benchmark": false}}, "optimization_strategies": {"content_optimization": {"techniques": ["prompt_engineering", "example_enhancement", "instruction_clarification", "context_enrichment"], "parameters": {"max_prompt_length": 4000, "example_count_range": [2, 5], "instruction_clarity_score": 0.8}}, "performance_optimization": {"techniques": ["caching", "preprocessing", "parallel_processing", "model_selection"], "parameters": {"cache_hit_target": 0.7, "response_time_target": 3.0, "throughput_target": 100}}}, "monitoring_dashboards": {"real_time_metrics": ["current_response_time", "active_requests", "error_rate", "quality_score_trend"], "performance_trends": ["response_time_24h", "quality_score_7d", "success_rate_trend", "user_satisfaction_trend"], "optimization_insights": ["improvement_opportunities", "performance_degradation_alerts", "ab_test_results", "benchmark_comparisons"]}, "alert_configuration": {"performance_alerts": [{"name": "quality_score_drop", "metric": "quality_score", "condition": "below", "threshold": 0.75, "duration_minutes": 30, "severity": "high"}, {"name": "response_time_spike", "metric": "response_time_p95", "condition": "above", "threshold": 10.0, "duration_minutes": 15, "severity": "medium"}, {"name": "success_rate_decline", "metric": "success_rate", "condition": "below", "threshold": 0.9, "duration_minutes": 60, "severity": "high"}], "optimization_alerts": [{"name": "optimization_opportunity", "trigger": "quality_degradation", "threshold": 0.8, "action": "suggest_ab_test"}, {"name": "ab_test_completion", "trigger": "test_duration_reached", "action": "analyze_results"}]}, "reporting": {"frequency": {"real_time": "continuous", "hourly": "0 * * * *", "daily": "0 0 * * *", "weekly": "0 0 * * 0"}, "report_types": {"performance_summary": {"metrics": ["avg_quality_score", "avg_response_time", "success_rate", "user_satisfaction"], "time_periods": ["1h", "24h", "7d"]}, "optimization_report": {"content": ["improvement_opportunities", "ab_test_results", "performance_trends", "recommendations"]}, "quality_analysis": {"dimensions": ["template_performance", "user_segment_analysis", "content_type_breakdown", "error_pattern_analysis"]}}}, "data_retention": {"raw_metrics": "30d", "aggregated_hourly": "90d", "aggregated_daily": "1y", "test_results": "2y", "performance_baselines": "permanent"}}}