"""
性能分析器
分析系统性能指标，识别性能瓶颈和异常
"""

import time
import json
import sqlite3
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class SeverityLevel(Enum):
    """严重程度级别"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"


@dataclass
class PerformanceIssue:
    """性能问题"""
    issue_type: str
    severity: SeverityLevel
    description: str
    metric_name: str
    current_value: float
    threshold_value: float
    timestamp: float
    recommendations: List[str]
    affected_endpoints: List[str] = None


@dataclass
class PerformanceReport:
    """性能报告"""
    timestamp: float
    overall_score: float
    issues: List[PerformanceIssue]
    summary: Dict[str, Any]
    recommendations: List[str]


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self, config_path: str = None):
        self.config = self._load_config(config_path)
        self.thresholds = self._load_performance_thresholds()
        self.historical_data = {}
        self.analysis_cache = {}
        self.last_analysis_time = 0
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置"""
        if not config_path:
            config_path = "/mnt/c/Users/<USER>/Desktop/Dev/CellForge AI/backend/app/monitoring/monitoring_config.json"
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return {}
    
    def _load_performance_thresholds(self) -> Dict:
        """加载性能阈值"""
        return {
            # API性能阈值
            'api_response_time': {
                'warning': 2000,  # 2秒
                'critical': 5000  # 5秒
            },
            'api_error_rate': {
                'warning': 0.05,  # 5%
                'critical': 0.10  # 10%
            },
            'api_throughput': {
                'warning': 100,   # 每分钟请求数
                'critical': 50
            },
            
            # 系统资源阈值
            'cpu_usage': {
                'warning': 70,    # 70%
                'critical': 90    # 90%
            },
            'memory_usage': {
                'warning': 80,    # 80%
                'critical': 95    # 95%
            },
            'disk_usage': {
                'warning': 85,    # 85%
                'critical': 95    # 95%
            },
            
            # 业务指标阈值
            'literature_success_rate': {
                'warning': 0.8,   # 80%
                'critical': 0.6   # 60%
            },
            'ai_service_quality': {
                'warning': 0.7,   # 70%
                'critical': 0.5   # 50%
            },
            'cache_hit_rate': {
                'warning': 0.6,   # 60%
                'critical': 0.4   # 40%
            }
        }
    
    def analyze_performance(self, time_window: int = 3600) -> PerformanceReport:
        """分析性能"""
        current_time = time.time()
        
        # 检查缓存
        if current_time - self.last_analysis_time < 300:  # 5分钟缓存
            if 'last_report' in self.analysis_cache:
                return self.analysis_cache['last_report']
        
        try:
            # 收集分析数据
            analysis_data = self._collect_analysis_data(time_window)
            
            # 执行各项分析
            issues = []
            issues.extend(self._analyze_api_performance(analysis_data))
            issues.extend(self._analyze_system_performance(analysis_data))
            issues.extend(self._analyze_business_metrics(analysis_data))
            issues.extend(self._analyze_trends(analysis_data))
            
            # 计算整体性能得分
            overall_score = self._calculate_performance_score(issues, analysis_data)
            
            # 生成摘要和建议
            summary = self._generate_summary(analysis_data, issues)
            recommendations = self._generate_recommendations(issues)
            
            # 创建性能报告
            report = PerformanceReport(
                timestamp=current_time,
                overall_score=overall_score,
                issues=issues,
                summary=summary,
                recommendations=recommendations
            )
            
            # 缓存结果
            self.analysis_cache['last_report'] = report
            self.last_analysis_time = current_time
            
            return report
            
        except Exception as e:
            logger.error(f"性能分析失败: {e}")
            return self._create_empty_report()
    
    def _collect_analysis_data(self, time_window: int) -> Dict:
        """收集分析数据"""
        end_time = time.time()
        start_time = end_time - time_window
        
        data = {
            'api_metrics': self._get_api_metrics(start_time, end_time),
            'system_metrics': self._get_system_metrics(start_time, end_time),
            'business_metrics': self._get_business_metrics(start_time, end_time),
            'time_window': time_window,
            'start_time': start_time,
            'end_time': end_time
        }
        
        return data
    
    def _get_api_metrics(self, start_time: float, end_time: float) -> Dict:
        """获取API指标数据"""
        try:
            conn = sqlite3.connect('/mnt/c/Users/<USER>/Desktop/Dev/CellForge AI/backend/cellforge.db')
            cursor = conn.cursor()
            
            # 获取API调用数据
            cursor.execute('''
                SELECT endpoint, method, response_time, status_code
                FROM metrics_api 
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY timestamp DESC
            ''', (start_time, end_time))
            
            api_calls = cursor.fetchall()
            
            # 聚合分析
            endpoint_stats = {}
            total_requests = 0
            total_errors = 0
            all_response_times = []
            
            for endpoint, method, response_time, status_code in api_calls:
                key = f"{method}:{endpoint}"
                
                if key not in endpoint_stats:
                    endpoint_stats[key] = {
                        'request_count': 0,
                        'error_count': 0,
                        'response_times': [],
                        'endpoint': endpoint,
                        'method': method
                    }
                
                endpoint_stats[key]['request_count'] += 1
                endpoint_stats[key]['response_times'].append(response_time)
                all_response_times.append(response_time)
                total_requests += 1
                
                if status_code >= 400:
                    endpoint_stats[key]['error_count'] += 1
                    total_errors += 1
            
            # 计算统计信息
            for key, stats in endpoint_stats.items():
                response_times = stats['response_times']
                stats['avg_response_time'] = statistics.mean(response_times)
                stats['p95_response_time'] = statistics.quantiles(response_times, n=20)[18] if len(response_times) > 1 else response_times[0]
                stats['error_rate'] = stats['error_count'] / stats['request_count']
            
            conn.close()
            
            return {
                'endpoint_stats': endpoint_stats,
                'total_requests': total_requests,
                'total_errors': total_errors,
                'overall_error_rate': total_errors / max(total_requests, 1),
                'overall_avg_response_time': statistics.mean(all_response_times) if all_response_times else 0,
                'overall_p95_response_time': statistics.quantiles(all_response_times, n=20)[18] if len(all_response_times) > 1 else 0
            }
            
        except Exception as e:
            logger.error(f"获取API指标失败: {e}")
            return {}
    
    def _get_system_metrics(self, start_time: float, end_time: float) -> Dict:
        """获取系统指标数据"""
        try:
            conn = sqlite3.connect('/mnt/c/Users/<USER>/Desktop/Dev/CellForge AI/backend/cellforge.db')
            cursor = conn.cursor()
            
            system_metrics = {}
            
            # 获取各类系统指标
            metric_names = ['cpu_usage_percent', 'memory_usage_percent', 'disk_usage_percent']
            
            for metric_name in metric_names:
                cursor.execute('''
                    SELECT value, timestamp
                    FROM metrics_system 
                    WHERE metric_name = ? AND timestamp BETWEEN ? AND ?
                    ORDER BY timestamp DESC
                ''', (metric_name, start_time, end_time))
                
                values = cursor.fetchall()
                
                if values:
                    metric_values = [v[0] for v in values]
                    system_metrics[metric_name] = {
                        'current': metric_values[0],
                        'average': statistics.mean(metric_values),
                        'max': max(metric_values),
                        'min': min(metric_values),
                        'trend': self._calculate_trend([v[0] for v in values[-10:]])  # 最近10个数据点
                    }
            
            conn.close()
            return system_metrics
            
        except Exception as e:
            logger.error(f"获取系统指标失败: {e}")
            return {}
    
    def _get_business_metrics(self, start_time: float, end_time: float) -> Dict:
        """获取业务指标数据"""
        try:
            conn = sqlite3.connect('/mnt/c/Users/<USER>/Desktop/Dev/CellForge AI/backend/cellforge.db')
            cursor = conn.cursor()
            
            business_metrics = {}
            
            cursor.execute('''
                SELECT metric_type, value, timestamp
                FROM metrics_business 
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY timestamp DESC
            ''', (start_time, end_time))
            
            metrics_data = cursor.fetchall()
            
            # 按指标类型聚合
            metric_groups = {}
            for metric_type, value, timestamp in metrics_data:
                if metric_type not in metric_groups:
                    metric_groups[metric_type] = []
                metric_groups[metric_type].append((value, timestamp))
            
            # 计算每个指标的统计信息
            for metric_type, values in metric_groups.items():
                metric_values = [v[0] for v in values]
                business_metrics[metric_type] = {
                    'current': metric_values[0] if metric_values else 0,
                    'average': statistics.mean(metric_values) if metric_values else 0,
                    'max': max(metric_values) if metric_values else 0,
                    'min': min(metric_values) if metric_values else 0,
                    'sample_count': len(metric_values)
                }
            
            conn.close()
            return business_metrics
            
        except Exception as e:
            logger.error(f"获取业务指标失败: {e}")
            return {}
    
    def _analyze_api_performance(self, data: Dict) -> List[PerformanceIssue]:
        """分析API性能"""
        issues = []
        api_data = data.get('api_metrics', {})
        
        # 分析整体API性能
        overall_response_time = api_data.get('overall_avg_response_time', 0)
        overall_error_rate = api_data.get('overall_error_rate', 0)
        
        # 响应时间分析
        if overall_response_time > self.thresholds['api_response_time']['critical']:
            issues.append(PerformanceIssue(
                issue_type="high_response_time",
                severity=SeverityLevel.CRITICAL,
                description=f"API平均响应时间过高 ({overall_response_time:.2f}ms)",
                metric_name="api_response_time",
                current_value=overall_response_time,
                threshold_value=self.thresholds['api_response_time']['critical'],
                timestamp=time.time(),
                recommendations=[
                    "优化数据库查询",
                    "增加缓存层",
                    "检查外部API调用",
                    "考虑异步处理长时间任务"
                ]
            ))
        elif overall_response_time > self.thresholds['api_response_time']['warning']:
            issues.append(PerformanceIssue(
                issue_type="elevated_response_time",
                severity=SeverityLevel.WARNING,
                description=f"API响应时间偏高 ({overall_response_time:.2f}ms)",
                metric_name="api_response_time",
                current_value=overall_response_time,
                threshold_value=self.thresholds['api_response_time']['warning'],
                timestamp=time.time(),
                recommendations=[
                    "监控慢查询",
                    "优化算法复杂度",
                    "考虑添加缓存"
                ]
            ))
        
        # 错误率分析
        if overall_error_rate > self.thresholds['api_error_rate']['critical']:
            issues.append(PerformanceIssue(
                issue_type="high_error_rate",
                severity=SeverityLevel.CRITICAL,
                description=f"API错误率过高 ({overall_error_rate:.2%})",
                metric_name="api_error_rate",
                current_value=overall_error_rate,
                threshold_value=self.thresholds['api_error_rate']['critical'],
                timestamp=time.time(),
                recommendations=[
                    "检查应用程序日志",
                    "验证外部依赖服务",
                    "增强错误处理机制",
                    "实施熔断器模式"
                ]
            ))
        elif overall_error_rate > self.thresholds['api_error_rate']['warning']:
            issues.append(PerformanceIssue(
                issue_type="elevated_error_rate",
                severity=SeverityLevel.WARNING,
                description=f"API错误率偏高 ({overall_error_rate:.2%})",
                metric_name="api_error_rate",
                current_value=overall_error_rate,
                threshold_value=self.thresholds['api_error_rate']['warning'],
                timestamp=time.time(),
                recommendations=[
                    "检查错误日志模式",
                    "改进输入验证",
                    "监控外部服务状态"
                ]
            ))
        
        # 分析单个端点性能
        endpoint_stats = api_data.get('endpoint_stats', {})
        slow_endpoints = []
        
        for endpoint_key, stats in endpoint_stats.items():
            if stats['avg_response_time'] > self.thresholds['api_response_time']['warning']:
                slow_endpoints.append(f"{endpoint_key} ({stats['avg_response_time']:.2f}ms)")
        
        if slow_endpoints:
            issues.append(PerformanceIssue(
                issue_type="slow_endpoints",
                severity=SeverityLevel.WARNING,
                description=f"发现 {len(slow_endpoints)} 个慢端点",
                metric_name="endpoint_response_time",
                current_value=len(slow_endpoints),
                threshold_value=0,
                timestamp=time.time(),
                recommendations=[
                    "优化慢端点的业务逻辑",
                    "检查数据库查询性能",
                    "考虑端点级别的缓存"
                ],
                affected_endpoints=slow_endpoints
            ))
        
        return issues
    
    def _analyze_system_performance(self, data: Dict) -> List[PerformanceIssue]:
        """分析系统性能"""
        issues = []
        system_data = data.get('system_metrics', {})
        
        # 分析CPU使用率
        cpu_data = system_data.get('cpu_usage_percent', {})
        if cpu_data:
            current_cpu = cpu_data.get('current', 0)
            avg_cpu = cpu_data.get('average', 0)
            
            if current_cpu > self.thresholds['cpu_usage']['critical']:
                issues.append(PerformanceIssue(
                    issue_type="high_cpu_usage",
                    severity=SeverityLevel.CRITICAL,
                    description=f"CPU使用率过高 ({current_cpu:.1f}%)",
                    metric_name="cpu_usage_percent",
                    current_value=current_cpu,
                    threshold_value=self.thresholds['cpu_usage']['critical'],
                    timestamp=time.time(),
                    recommendations=[
                        "检查高CPU消耗的进程",
                        "优化算法复杂度",
                        "考虑水平扩展",
                        "实施负载均衡"
                    ]
                ))
            elif avg_cpu > self.thresholds['cpu_usage']['warning']:
                issues.append(PerformanceIssue(
                    issue_type="elevated_cpu_usage",
                    severity=SeverityLevel.WARNING,
                    description=f"CPU使用率偏高 (平均 {avg_cpu:.1f}%)",
                    metric_name="cpu_usage_percent",
                    current_value=avg_cpu,
                    threshold_value=self.thresholds['cpu_usage']['warning'],
                    timestamp=time.time(),
                    recommendations=[
                        "监控CPU使用趋势",
                        "优化热点代码路径",
                        "考虑异步处理"
                    ]
                ))
        
        # 分析内存使用率
        memory_data = system_data.get('memory_usage_percent', {})
        if memory_data:
            current_memory = memory_data.get('current', 0)
            
            if current_memory > self.thresholds['memory_usage']['critical']:
                issues.append(PerformanceIssue(
                    issue_type="high_memory_usage",
                    severity=SeverityLevel.CRITICAL,
                    description=f"内存使用率过高 ({current_memory:.1f}%)",
                    metric_name="memory_usage_percent",
                    current_value=current_memory,
                    threshold_value=self.thresholds['memory_usage']['critical'],
                    timestamp=time.time(),
                    recommendations=[
                        "检查内存泄漏",
                        "优化数据结构",
                        "实施对象池",
                        "增加系统内存"
                    ]
                ))
            elif current_memory > self.thresholds['memory_usage']['warning']:
                issues.append(PerformanceIssue(
                    issue_type="elevated_memory_usage",
                    severity=SeverityLevel.WARNING,
                    description=f"内存使用率偏高 ({current_memory:.1f}%)",
                    metric_name="memory_usage_percent",
                    current_value=current_memory,
                    threshold_value=self.thresholds['memory_usage']['warning'],
                    timestamp=time.time(),
                    recommendations=[
                        "监控内存使用模式",
                        "优化缓存策略",
                        "检查大对象分配"
                    ]
                ))
        
        # 分析磁盘使用率
        disk_data = system_data.get('disk_usage_percent', {})
        if disk_data:
            current_disk = disk_data.get('current', 0)
            
            if current_disk > self.thresholds['disk_usage']['critical']:
                issues.append(PerformanceIssue(
                    issue_type="high_disk_usage",
                    severity=SeverityLevel.CRITICAL,
                    description=f"磁盘使用率过高 ({current_disk:.1f}%)",
                    metric_name="disk_usage_percent",
                    current_value=current_disk,
                    threshold_value=self.thresholds['disk_usage']['critical'],
                    timestamp=time.time(),
                    recommendations=[
                        "清理临时文件",
                        "归档旧日志",
                        "扩展存储空间",
                        "实施日志轮转"
                    ]
                ))
            elif current_disk > self.thresholds['disk_usage']['warning']:
                issues.append(PerformanceIssue(
                    issue_type="elevated_disk_usage",
                    severity=SeverityLevel.WARNING,
                    description=f"磁盘使用率偏高 ({current_disk:.1f}%)",
                    metric_name="disk_usage_percent",
                    current_value=current_disk,
                    threshold_value=self.thresholds['disk_usage']['warning'],
                    timestamp=time.time(),
                    recommendations=[
                        "监控磁盘使用增长",
                        "清理不必要的文件",
                        "优化数据存储"
                    ]
                ))
        
        return issues
    
    def _analyze_business_metrics(self, data: Dict) -> List[PerformanceIssue]:
        """分析业务指标"""
        issues = []
        business_data = data.get('business_metrics', {})
        
        # 分析文献搜索成功率
        literature_data = business_data.get('literature_search_success_rate', {})
        if literature_data:
            current_rate = literature_data.get('current', 0)
            
            if current_rate < self.thresholds['literature_success_rate']['critical']:
                issues.append(PerformanceIssue(
                    issue_type="low_literature_success_rate",
                    severity=SeverityLevel.CRITICAL,
                    description=f"文献搜索成功率过低 ({current_rate:.2%})",
                    metric_name="literature_success_rate",
                    current_value=current_rate,
                    threshold_value=self.thresholds['literature_success_rate']['critical'],
                    timestamp=time.time(),
                    recommendations=[
                        "检查外部API连接",
                        "优化搜索算法",
                        "验证搜索关键词质量",
                        "增加备用数据源"
                    ]
                ))
            elif current_rate < self.thresholds['literature_success_rate']['warning']:
                issues.append(PerformanceIssue(
                    issue_type="declining_literature_success_rate",
                    severity=SeverityLevel.WARNING,
                    description=f"文献搜索成功率偏低 ({current_rate:.2%})",
                    metric_name="literature_success_rate",
                    current_value=current_rate,
                    threshold_value=self.thresholds['literature_success_rate']['warning'],
                    timestamp=time.time(),
                    recommendations=[
                        "监控搜索质量趋势",
                        "改进关键词生成",
                        "优化搜索策略"
                    ]
                ))
        
        # 分析AI服务质量
        ai_quality_data = business_data.get('ai_service_quality_score', {})
        if ai_quality_data:
            current_score = ai_quality_data.get('current', 0)
            
            if current_score < self.thresholds['ai_service_quality']['critical']:
                issues.append(PerformanceIssue(
                    issue_type="low_ai_service_quality",
                    severity=SeverityLevel.CRITICAL,
                    description=f"AI服务质量评分过低 ({current_score:.2f})",
                    metric_name="ai_service_quality",
                    current_value=current_score,
                    threshold_value=self.thresholds['ai_service_quality']['critical'],
                    timestamp=time.time(),
                    recommendations=[
                        "检查AI模型性能",
                        "优化提示词模板",
                        "验证训练数据质量",
                        "考虑模型微调"
                    ]
                ))
            elif current_score < self.thresholds['ai_service_quality']['warning']:
                issues.append(PerformanceIssue(
                    issue_type="declining_ai_service_quality",
                    severity=SeverityLevel.WARNING,
                    description=f"AI服务质量评分偏低 ({current_score:.2f})",
                    metric_name="ai_service_quality",
                    current_value=current_score,
                    threshold_value=self.thresholds['ai_service_quality']['warning'],
                    timestamp=time.time(),
                    recommendations=[
                        "监控AI服务质量趋势",
                        "改进提示词设计",
                        "增强上下文理解"
                    ]
                ))
        
        # 分析缓存命中率
        cache_data = business_data.get('cache_hit_rate', {})
        if cache_data:
            current_rate = cache_data.get('current', 0)
            
            if current_rate < self.thresholds['cache_hit_rate']['critical']:
                issues.append(PerformanceIssue(
                    issue_type="low_cache_hit_rate",
                    severity=SeverityLevel.WARNING,  # 缓存问题通常不是关键问题
                    description=f"缓存命中率过低 ({current_rate:.2%})",
                    metric_name="cache_hit_rate",
                    current_value=current_rate,
                    threshold_value=self.thresholds['cache_hit_rate']['critical'],
                    timestamp=time.time(),
                    recommendations=[
                        "分析缓存失效模式",
                        "优化缓存策略",
                        "增加缓存容量",
                        "改进缓存键设计"
                    ]
                ))
        
        return issues
    
    def _analyze_trends(self, data: Dict) -> List[PerformanceIssue]:
        """分析性能趋势"""
        issues = []
        
        # 分析系统资源使用趋势
        system_data = data.get('system_metrics', {})
        
        for metric_name, metric_data in system_data.items():
            trend = metric_data.get('trend', 'stable')
            current_value = metric_data.get('current', 0)
            
            if trend == 'increasing':
                if 'cpu' in metric_name and current_value > 60:
                    issues.append(PerformanceIssue(
                        issue_type="increasing_resource_usage",
                        severity=SeverityLevel.WARNING,
                        description=f"{metric_name.replace('_', ' ').title()} 呈上升趋势",
                        metric_name=metric_name,
                        current_value=current_value,
                        threshold_value=0,
                        timestamp=time.time(),
                        recommendations=[
                            "监控资源使用趋势",
                            "分析增长原因",
                            "制定扩展计划"
                        ]
                    ))
                elif 'memory' in metric_name and current_value > 70:
                    issues.append(PerformanceIssue(
                        issue_type="increasing_resource_usage",
                        severity=SeverityLevel.WARNING,
                        description=f"{metric_name.replace('_', ' ').title()} 呈上升趋势",
                        metric_name=metric_name,
                        current_value=current_value,
                        threshold_value=0,
                        timestamp=time.time(),
                        recommendations=[
                            "检查内存泄漏",
                            "优化内存使用",
                            "监控内存增长模式"
                        ]
                    ))
        
        return issues
    
    def _calculate_performance_score(self, issues: List[PerformanceIssue], data: Dict) -> float:
        """计算整体性能得分 (0-100)"""
        base_score = 100.0
        
        # 根据问题严重程度扣分
        for issue in issues:
            if issue.severity == SeverityLevel.CRITICAL:
                base_score -= 20
            elif issue.severity == SeverityLevel.WARNING:
                base_score -= 10
            else:  # INFO
                base_score -= 5
        
        # 确保分数在合理范围内
        score = max(0, min(100, base_score))
        
        return round(score, 1)
    
    def _generate_summary(self, data: Dict, issues: List[PerformanceIssue]) -> Dict[str, Any]:
        """生成性能摘要"""
        api_data = data.get('api_metrics', {})
        system_data = data.get('system_metrics', {})
        business_data = data.get('business_metrics', {})
        
        return {
            'total_issues': len(issues),
            'critical_issues': len([i for i in issues if i.severity == SeverityLevel.CRITICAL]),
            'warning_issues': len([i for i in issues if i.severity == SeverityLevel.WARNING]),
            'api_summary': {
                'total_requests': api_data.get('total_requests', 0),
                'error_rate': api_data.get('overall_error_rate', 0),
                'avg_response_time': api_data.get('overall_avg_response_time', 0)
            },
            'system_summary': {
                'cpu_usage': system_data.get('cpu_usage_percent', {}).get('current', 0),
                'memory_usage': system_data.get('memory_usage_percent', {}).get('current', 0),
                'disk_usage': system_data.get('disk_usage_percent', {}).get('current', 0)
            },
            'business_summary': {
                'metrics_count': len(business_data),
                'active_services': len([k for k, v in business_data.items() if v.get('current', 0) > 0])
            }
        }
    
    def _generate_recommendations(self, issues: List[PerformanceIssue]) -> List[str]:
        """生成整体建议"""
        recommendations = []
        
        # 根据问题类型生成建议
        issue_types = [issue.issue_type for issue in issues]
        
        if any('response_time' in it for it in issue_types):
            recommendations.append("优化API响应时间是当前优先级")
        
        if any('error_rate' in it for it in issue_types):
            recommendations.append("需要紧急处理API错误率问题")
        
        if any('cpu' in it for it in issue_types):
            recommendations.append("考虑CPU资源扩展或优化")
        
        if any('memory' in it for it in issue_types):
            recommendations.append("检查内存使用模式和潜在泄漏")
        
        if any('literature' in it for it in issue_types):
            recommendations.append("改进文献搜索服务质量")
        
        if any('ai_service' in it for it in issue_types):
            recommendations.append("优化AI服务配置和模型性能")
        
        # 如果没有严重问题，给出一般性建议
        if not recommendations:
            recommendations.append("系统运行正常，建议继续监控性能指标")
        
        return recommendations
    
    def _calculate_trend(self, values: List[float]) -> str:
        """计算趋势"""
        if len(values) < 3:
            return "stable"
        
        # 使用简单的线性回归判断趋势
        recent_avg = statistics.mean(values[-3:])
        earlier_avg = statistics.mean(values[:-3])
        
        if recent_avg > earlier_avg * 1.1:
            return "increasing"
        elif recent_avg < earlier_avg * 0.9:
            return "decreasing"
        else:
            return "stable"
    
    def _create_empty_report(self) -> PerformanceReport:
        """创建空的性能报告"""
        return PerformanceReport(
            timestamp=time.time(),
            overall_score=0.0,
            issues=[],
            summary={},
            recommendations=["无法生成性能报告，请检查系统状态"]
        )
    
    def get_performance_history(self, hours: int = 24) -> Dict:
        """获取性能历史"""
        end_time = time.time()
        start_time = end_time - (hours * 3600)
        
        try:
            conn = sqlite3.connect('/mnt/c/Users/<USER>/Desktop/Dev/CellForge AI/backend/cellforge.db')
            cursor = conn.cursor()
            
            # 获取API性能历史
            cursor.execute('''
                SELECT 
                    strftime('%Y-%m-%d %H:00:00', datetime(timestamp, 'unixepoch')) as hour,
                    AVG(response_time) as avg_response_time,
                    COUNT(*) as request_count,
                    SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END) as error_count
                FROM metrics_api 
                WHERE timestamp BETWEEN ? AND ?
                GROUP BY hour
                ORDER BY hour
            ''', (start_time, end_time))
            
            api_history = []
            for row in cursor.fetchall():
                hour, avg_time, req_count, err_count = row
                api_history.append({
                    'timestamp': hour,
                    'avg_response_time': avg_time,
                    'request_count': req_count,
                    'error_rate': err_count / max(req_count, 1)
                })
            
            # 获取系统资源历史
            cursor.execute('''
                SELECT 
                    strftime('%Y-%m-%d %H:00:00', datetime(timestamp, 'unixepoch')) as hour,
                    metric_name,
                    AVG(value) as avg_value
                FROM metrics_system 
                WHERE timestamp BETWEEN ? AND ? 
                AND metric_name IN ('cpu_usage_percent', 'memory_usage_percent', 'disk_usage_percent')
                GROUP BY hour, metric_name
                ORDER BY hour, metric_name
            ''', (start_time, end_time))
            
            system_history = {}
            for row in cursor.fetchall():
                hour, metric_name, avg_value = row
                if hour not in system_history:
                    system_history[hour] = {}
                system_history[hour][metric_name] = avg_value
            
            conn.close()
            
            return {
                'api_history': api_history,
                'system_history': system_history,
                'time_range': {
                    'start': datetime.fromtimestamp(start_time).isoformat(),
                    'end': datetime.fromtimestamp(end_time).isoformat(),
                    'hours': hours
                }
            }
            
        except Exception as e:
            logger.error(f"获取性能历史失败: {e}")
            return {}


# 全局性能分析器实例
performance_analyzer = PerformanceAnalyzer()