"""
文献资源服务 - 集成外部API和本地文献库
"""
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func

from app.models.literature import Literature, LiteratureRecommendation, LiteratureCollection
from app.core.database import get_db
from app.services.external_literature_service import get_external_literature_service
from app.core.external_apis import get_api_manager

logger = logging.getLogger(__name__)


class LiteratureService:
    """文献资源服务类 - 集成外部API和本地文献库"""
    
    def __init__(self):
        self.api_manager = get_api_manager()
        # 保留少量核心文献作为降级备选
        self.fallback_literature = [
            {
                "title": "Comprehensive Integration of Single-Cell Data",
                "authors": ["Butler, A.", "Hoffman, P.", "<PERSON><PERSON><PERSON>, P.", "Papalexi, E.", "Satija, R."],
                "journal": "Nature Biotechnology",
                "publication_year": 2018,
                "doi": "10.1038/nbt.4096",
                "pubmed_id": "29608179",
                "abstract": "Single-cell RNA sequencing (scRNA-seq) has enabled the molecular profiling of individual cells...",
                "category": "methodology",
                "technology_tags": ["scRNA-seq", "data_integration", "seurat"],
                "application_tags": ["computational_biology", "bioinformatics"],
                "impact_factor": 36.558,
                "citation_count": 8500,
                "relevance_score": 0.95,
                "key_findings": "Seurat v3 provides comprehensive tools for single-cell data integration and analysis",
                "methodology_summary": "Introduces anchor-based integration methods for combining diverse single-cell datasets",
                "business_value": "Essential tool for single-cell data analysis workflows, widely adopted in research and industry",
                "source": "Fallback"
            },
            {
                "title": "The Human Cell Atlas: from vision to reality",
                "authors": ["Regev, A.", "Teichmann, S.A.", "Lander, E.S.", "Amit, I."],
                "journal": "Nature",
                "publication_year": 2017,
                "doi": "10.1038/nature24041",
                "pubmed_id": "29072724",
                "abstract": "The recent advent of methods for high-throughput single-cell molecular profiling has catalyzed a growing sense in the scientific community...",
                "category": "review",
                "technology_tags": ["scRNA-seq", "cell_atlas", "multi-omics"],
                "application_tags": ["cell_atlas", "reference", "standards", "cell_typing"],
                "impact_factor": 49.962,
                "citation_count": 2800,
                "relevance_score": 0.94,
                "key_findings": "Comprehensive framework for standardized cell type identification and annotation",
                "methodology_summary": "Establishes best practices for cell atlas construction and reference-based annotation",
                "business_value": "Provides industry standards for cell type identification workflows",
                "source": "Fallback"
            }
        ]
    
    async def search_literature(
        self, 
        query: str, 
        category: Optional[str] = None,
        technology_tags: Optional[List[str]] = None,
        top_k: int = 10
    ) -> List[Dict[str, Any]]:
        """搜索文献 - 优先使用外部API，降级到本地库"""
        try:
            results = []
            
            # 首先尝试使用外部文献搜索服务
            if self.api_manager.is_literature_search_enabled():
                try:
                    external_service = await get_external_literature_service()
                    external_results = await external_service.comprehensive_search(query, max_results_per_source=5)
                    
                    if external_results and external_results.get("papers"):
                        logger.info(f"外部API搜索成功，找到 {len(external_results['papers'])} 篇文献")
                        
                        # 转换外部API结果格式
                        for paper in external_results["papers"]:
                            converted_paper = self._convert_external_paper_format(paper, query)
                            results.append(converted_paper)
                        
                        # 按相关性排序并返回
                        results.sort(key=lambda x: x.get("combined_score", 0), reverse=True)
                        return results[:top_k]
                        
                except Exception as e:
                    logger.warning(f"外部文献搜索失败，降级到本地库: {e}")
            
            # 如果外部API不可用或失败，使用本地降级文献库
            logger.info("使用本地降级文献库进行搜索")
            results = await self._search_fallback_literature(query, category, technology_tags, top_k)
            
            return results
            
        except Exception as e:
            logger.error(f"文献搜索失败: {e}")
            return []
    
    def _convert_external_paper_format(self, paper: Dict[str, Any], query: str) -> Dict[str, Any]:
        """转换外部API论文格式为统一格式"""
        try:
            # 基础信息
            result = {
                "title": paper.get("title", ""),
                "authors": paper.get("authors", []),
                "journal": paper.get("journal", ""),
                "publication_year": paper.get("publication_year", 2024),
                "doi": paper.get("doi", ""),
                "abstract": paper.get("abstract", ""),
                "source": paper.get("source", "External"),
                "relevance_score": paper.get("relevance_score", 0.5),
                "citation_count": paper.get("citation_count", 0),
                "key_findings": paper.get("key_findings", ""),
                "methodology_summary": paper.get("methodology_summary", ""),
                "open_access": paper.get("open_access", False)
            }
            
            # 计算搜索评分
            query_lower = query.lower()
            score = 0
            
            if query_lower in result["title"].lower():
                score += 5
            if query_lower in result["abstract"].lower():
                score += 3
            if query_lower in result["key_findings"].lower():
                score += 4
            
            # 根据来源调整分数
            if result["source"] == "PubMed":
                score += 2  # PubMed权威性加分
            elif result["source"] == "OpenAlex":
                score += 1  # OpenAlex覆盖面广加分
            
            result["search_score"] = score
            result["combined_score"] = score * result["relevance_score"]
            
            # 推断分类和标签
            result["category"] = self._infer_category(result)
            result["technology_tags"] = self._infer_technology_tags(result)
            result["application_tags"] = self._infer_application_tags(result)
            result["business_value"] = self._generate_business_value(result)
            
            return result
            
        except Exception as e:
            logger.error(f"转换外部论文格式失败: {e}")
            return {}
    
    def _infer_category(self, paper: Dict[str, Any]) -> str:
        """推断论文分类"""
        title_abstract = f"{paper.get('title', '')} {paper.get('abstract', '')}".lower()
        
        if any(keyword in title_abstract for keyword in ["review", "perspective", "atlas", "framework"]):
            return "review"
        elif any(keyword in title_abstract for keyword in ["method", "algorithm", "tool", "platform", "protocol"]):
            return "methodology"
        elif any(keyword in title_abstract for keyword in ["application", "analysis", "profiling", "identification"]):
            return "application"
        else:
            return "research"
    
    def _infer_technology_tags(self, paper: Dict[str, Any]) -> List[str]:
        """推断技术标签"""
        title_abstract = f"{paper.get('title', '')} {paper.get('abstract', '')}".lower()
        tags = []
        
        if "single-cell" in title_abstract or "scrna-seq" in title_abstract:
            tags.append("scRNA-seq")
        if "10x" in title_abstract or "chromium" in title_abstract:
            tags.append("10x_genomics")
        if "smart-seq" in title_abstract:
            tags.append("Smart-seq2")
        if "pbmc" in title_abstract:
            tags.append("PBMC")
        if "seurat" in title_abstract:
            tags.append("seurat")
        
        return tags
    
    def _infer_application_tags(self, paper: Dict[str, Any]) -> List[str]:
        """推断应用标签"""
        title_abstract = f"{paper.get('title', '')} {paper.get('abstract', '')}".lower()
        tags = []
        
        if "cell type" in title_abstract or "cell typing" in title_abstract:
            tags.append("cell_typing")
        if "immunology" in title_abstract or "immune" in title_abstract:
            tags.append("immunology")
        if "reference" in title_abstract:
            tags.append("reference")
        if "atlas" in title_abstract:
            tags.append("cell_atlas")
        if "bioinformatics" in title_abstract:
            tags.append("bioinformatics")
        
        return tags
    
    def _generate_business_value(self, paper: Dict[str, Any]) -> str:
        """生成商业价值描述"""
        if paper.get("citation_count", 0) > 1000:
            return "高影响力研究，为该领域提供重要参考标准"
        elif paper.get("category") == "methodology":
            return "提供实用的方法学工具，可直接应用于研究工作流程"
        elif paper.get("category") == "application":
            return "展示实际应用案例，为相关研究提供实施参考"
        else:
            return "为单细胞分析领域提供有价值的研究见解"
    
    async def _search_fallback_literature(
        self, 
        query: str, 
        category: Optional[str] = None,
        technology_tags: Optional[List[str]] = None,
        top_k: int = 10
    ) -> List[Dict[str, Any]]:
        """搜索降级文献库"""
        results = []
        query_lower = query.lower()
        
        for lit in self.fallback_literature:
            score = 0
            
            # 标题匹配
            if query_lower in lit["title"].lower():
                score += 5
            
            # 摘要匹配
            if query_lower in lit["abstract"].lower():
                score += 3
            
            # 关键发现匹配
            if query_lower in lit["key_findings"].lower():
                score += 4
            
            # 技术标签匹配
            for tag in lit["technology_tags"]:
                if query_lower in tag.lower() or tag.lower() in query_lower:
                    score += 3
            
            # 应用标签匹配
            for tag in lit["application_tags"]:
                if query_lower in tag.lower() or tag.lower() in query_lower:
                    score += 2
            
            # 分类过滤
            if category and lit["category"] != category:
                score *= 0.5
            
            # 技术标签过滤
            if technology_tags:
                tag_match = any(tag in lit["technology_tags"] for tag in technology_tags)
                if not tag_match:
                    score *= 0.3
            
            if score > 0:
                result = lit.copy()
                result["search_score"] = score
                result["combined_score"] = score * lit["relevance_score"]
                results.append(result)
        
        # 按综合评分排序
        results.sort(key=lambda x: x["combined_score"], reverse=True)
        
        return results[:top_k]
    
    async def get_literature_recommendations(
        self,
        context: Dict[str, Any],
        recommendation_type: str = "mixed",
        top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """基于上下文获取文献推荐"""
        try:
            # 提取上下文中的关键信息
            user_query = context.get("user_message", "")
            requirements = context.get("requirements", {})
            user_profile = context.get("user_profile", {})
            
            # 构建搜索查询
            search_terms = []
            
            # 从用户消息中提取关键词
            if user_query:
                search_terms.append(user_query)
            
            # 从需求中提取技术相关词汇
            if requirements:
                if requirements.get("sequencing_type"):
                    search_terms.append(requirements["sequencing_type"])
                if requirements.get("sample_type"):
                    search_terms.append(requirements["sample_type"])
                if requirements.get("research_goal"):
                    search_terms.append(requirements["research_goal"])
            
            # 合并搜索词
            combined_query = " ".join(search_terms)
            
            # 搜索相关文献
            literature_results = await self.search_literature(combined_query, top_k=top_k * 2)
            
            # 根据推荐类型筛选和排序
            recommendations = []
            
            for lit in literature_results:
                recommendation = {
                    "literature": lit,
                    "recommendation_type": self._determine_recommendation_type(lit, context),
                    "relevance_explanation": self._generate_relevance_explanation(lit, context),
                    "business_value": lit.get("business_value", ""),
                    "key_support_points": self._extract_support_points(lit, context)
                }
                recommendations.append(recommendation)
            
            # 按推荐类型和相关性排序
            recommendations = self._rank_recommendations(recommendations, recommendation_type)
            
            return recommendations[:top_k]
            
        except Exception as e:
            logger.error(f"获取文献推荐失败: {e}")
            return []
    
    def _determine_recommendation_type(self, literature: Dict, context: Dict) -> str:
        """确定推荐类型"""
        if literature["impact_factor"] > 30:
            return "authority"
        elif literature["category"] == "methodology":
            return "methodology"
        elif literature["category"] == "application":
            return "application"
        else:
            return "reference"
    
    def _generate_relevance_explanation(self, literature: Dict, context: Dict) -> str:
        """生成相关性解释"""
        explanations = []
        
        if literature["impact_factor"] > 20:
            explanations.append(f"高影响因子期刊({literature['journal']}, IF: {literature['impact_factor']})")
        
        if literature["citation_count"] > 1000:
            explanations.append(f"高引用文献({literature['citation_count']}次引用)")
        
        if literature["relevance_score"] > 0.9:
            explanations.append("与单细胞测序高度相关")
        
        return "; ".join(explanations)
    
    def _extract_support_points(self, literature: Dict, context: Dict) -> List[str]:
        """提取支持要点"""
        points = []
        
        if literature["key_findings"]:
            points.append(f"关键发现: {literature['key_findings']}")
        
        if literature["methodology_summary"]:
            points.append(f"方法学价值: {literature['methodology_summary']}")
        
        if literature["business_value"]:
            points.append(f"商业价值: {literature['business_value']}")
        
        return points
    
    def _rank_recommendations(self, recommendations: List[Dict], preference_type: str) -> List[Dict]:
        """对推荐结果进行排序"""
        if preference_type == "authority":
            return sorted(recommendations, 
                         key=lambda x: x["literature"]["impact_factor"], 
                         reverse=True)
        elif preference_type == "methodology":
            return sorted(recommendations,
                         key=lambda x: (x["literature"]["category"] == "methodology", 
                                      x["literature"]["combined_score"]),
                         reverse=True)
        else:
            return sorted(recommendations,
                         key=lambda x: x["literature"]["combined_score"],
                         reverse=True)


# 创建全局服务实例
literature_service = LiteratureService()
