"""
Comprehensive test suite for AI Keyword Generator
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

from app.services.ai_keyword_generator import (
    AIKeywordGenerator,
    KeywordGeneratorFactory,
    KeywordGeneratorDiagnostics,
    generate_keywords_with_timeout,
    optimize_terminology_mapping
)
from app.exceptions.keyword_exceptions import (
    InvalidInputError,
    KeywordGenerationError,
    PerformanceError,
    RateLimitError
)
from tests.conftest import (
    assert_valid_keyword_result,
    assert_performance_within_limits,
    create_test_terminology_mapping,
    create_test_domain_keywords
)


class TestAIKeywordGenerator:
    """Test cases for AI Keyword Generator"""
    
    @pytest.fixture
    async def keyword_generator(
        self, 
        test_keyword_config,
        test_terminology_config,
        mock_validator,
        mock_cache_manager,
        mock_metrics_collector
    ):
        """Create test keyword generator instance"""
        generator = AIKeywordGenerator(
            config=test_keyword_config,
            terminology_config=test_terminology_config,
            validator=mock_validator,
            cache_manager=mock_cache_manager
        )
        generator.metrics_collector = mock_metrics_collector
        generator._terminology_mapping = create_test_terminology_mapping()
        generator._domain_keywords = create_test_domain_keywords()
        await generator.initialize()
        return generator
    
    async def test_initialization(self, keyword_generator):
        """Test proper initialization of keyword generator"""
        assert keyword_generator._initialized
        assert keyword_generator.validator is not None
        assert keyword_generator.cache_manager is not None
        assert keyword_generator.metrics_collector is not None
    
    async def test_basic_keyword_generation(self, keyword_generator, sample_query_data):
        """Test basic keyword generation functionality"""
        query = sample_query_data["simple_chinese"]
        
        result = await keyword_generator.generate_keywords_from_query(query, max_keywords=10)
        
        assert_valid_keyword_result(result)
        assert result["user_query"] == query
        assert len(result["extracted_keywords"]["chinese"]) > 0
        assert len(result["extracted_keywords"]["english"]) > 0
    
    async def test_complex_query_processing(self, keyword_generator, sample_query_data):
        """Test processing of complex queries"""
        query = sample_query_data["complex_chinese"]
        
        result = await keyword_generator.generate_keywords_from_query(query, max_keywords=15)
        
        assert_valid_keyword_result(result)
        analysis = result["keyword_analysis"]
        
        # Should extract multiple keywords from complex query
        assert analysis["total_keywords"] >= 3
        assert analysis["confidence_score"] > 0.0
        
        # Should classify into multiple domains
        assert len(result["domain_classification"]) >= 1
    
    async def test_english_query_processing(self, keyword_generator, sample_query_data):
        """Test processing of English queries"""
        query = sample_query_data["english"]
        
        result = await keyword_generator.generate_keywords_from_query(query, max_keywords=10)
        
        assert_valid_keyword_result(result)
        # English queries should still produce valid results
        assert len(result["extracted_keywords"]["english"]) > 0
    
    async def test_mixed_language_query(self, keyword_generator, sample_query_data):
        """Test processing of mixed language queries"""
        query = sample_query_data["mixed"]
        
        result = await keyword_generator.generate_keywords_from_query(query, max_keywords=10)
        
        assert_valid_keyword_result(result)
        # Should handle both Chinese and English terms
        keywords = result["extracted_keywords"]["english"]
        assert any("10x" in kw.lower() or "genomics" in kw.lower() for kw in keywords)
    
    async def test_input_validation_errors(self, keyword_generator, error_test_cases):
        """Test input validation error handling"""
        for test_case in error_test_cases:
            with pytest.raises(Exception) as exc_info:
                await keyword_generator.generate_keywords_from_query(
                    test_case["query"],
                    context=test_case.get("context"),
                    max_keywords=test_case.get("max_keywords", 10)
                )
            
            # Verify correct exception type is raised
            assert test_case["expected_error"] in str(type(exc_info.value))
    
    async def test_cache_functionality(self, keyword_generator, cache_test_scenarios):
        """Test caching functionality"""
        # Mock cache manager to return cached result
        cache_scenario = cache_test_scenarios[0]  # cache_hit scenario
        keyword_generator.cache_manager.is_available.return_value = True
        keyword_generator.cache_manager.get.return_value = cache_scenario["cached_result"]
        
        result = await keyword_generator.generate_keywords_from_query(
            cache_scenario["query"], 
            max_keywords=10
        )
        
        # Should return cached result
        assert result == cache_scenario["cached_result"]
        
        # Verify cache hit was recorded
        keyword_generator.metrics_collector.record_cache_hit.assert_called_once()
    
    async def test_cache_miss_scenario(self, keyword_generator, cache_test_scenarios):
        """Test cache miss scenario"""
        cache_scenario = cache_test_scenarios[1]  # cache_miss scenario
        keyword_generator.cache_manager.is_available.return_value = True
        keyword_generator.cache_manager.get.return_value = None
        
        result = await keyword_generator.generate_keywords_from_query(
            cache_scenario["query"],
            max_keywords=10
        )
        
        assert_valid_keyword_result(result)
        
        # Verify cache miss was recorded
        keyword_generator.metrics_collector.record_cache_miss.assert_called_once()
        
        # Verify result was cached
        keyword_generator.cache_manager.set.assert_called_once()
    
    async def test_fuzzy_matching(self, keyword_generator):
        """Test fuzzy matching functionality"""
        # Test with slight variations of known terms
        fuzzy_query = "单细跑测序"  # Slight typo in "单细胞"
        
        result = await keyword_generator.generate_keywords_from_query(fuzzy_query, max_keywords=10)
        
        assert_valid_keyword_result(result)
        # Should still match despite typo if fuzzy matching is enabled
        if keyword_generator.config.ENABLE_FUZZY_MATCHING:
            assert len(result["extracted_keywords"]["english"]) > 0
    
    async def test_domain_classification(self, keyword_generator):
        """Test domain classification functionality"""
        query = "T细胞免疫反应和肿瘤细胞分析"
        
        result = await keyword_generator.generate_keywords_from_query(query, max_keywords=15)
        
        assert_valid_keyword_result(result)
        
        domains = result["domain_classification"]
        # Should classify into immunology and oncology domains
        expected_domains = ["immunology", "oncology"]
        found_domains = [domain for domain in expected_domains if domain in domains]
        assert len(found_domains) >= 1
    
    async def test_search_strategy_generation(self, keyword_generator):
        """Test search strategy generation"""
        query = "单细胞RNA测序数据分析"
        
        result = await keyword_generator.generate_keywords_from_query(query, max_keywords=10)
        
        strategies = result["search_strategies"]
        
        # Should generate various types of search strategies
        assert "primary_search_terms" in strategies
        assert "secondary_search_terms" in strategies
        assert "combined_queries" in strategies
        assert "boolean_queries" in strategies
        assert "field_specific_queries" in strategies
        
        # Primary search terms should not be empty
        assert len(strategies["primary_search_terms"]) > 0
    
    async def test_confidence_scoring(self, keyword_generator):
        """Test confidence score calculation"""
        # Test with query containing many known terms
        high_confidence_query = "单细胞RNA测序T细胞B细胞分析"
        result1 = await keyword_generator.generate_keywords_from_query(
            high_confidence_query, max_keywords=10
        )
        
        # Test with query containing few known terms
        low_confidence_query = "未知领域研究方法"
        result2 = await keyword_generator.generate_keywords_from_query(
            low_confidence_query, max_keywords=10
        )
        
        confidence1 = result1["keyword_analysis"]["confidence_score"]
        confidence2 = result2["keyword_analysis"]["confidence_score"]
        
        # High confidence query should have higher score
        assert confidence1 >= confidence2
        assert 0.0 <= confidence1 <= 1.0
        assert 0.0 <= confidence2 <= 1.0
    
    async def test_rate_limiting(self, keyword_generator, mock_rate_limiter):
        """Test rate limiting functionality"""
        keyword_generator.rate_limiter = mock_rate_limiter
        
        # Test normal request
        result = await keyword_generator.generate_keywords_from_query(
            "test query", user_id="test_user", max_keywords=5
        )
        
        assert_valid_keyword_result(result)
        mock_rate_limiter.check_rate_limit.assert_called_once_with("test_user")
    
    async def test_rate_limit_exceeded(self, keyword_generator):
        """Test rate limit exceeded scenario"""
        # Mock rate limiter to raise rate limit error
        mock_rate_limiter = AsyncMock()
        mock_rate_limiter.check_rate_limit.side_effect = RateLimitError(
            "Rate limit exceeded",
            service="test",
            limit=100,
            window=3600,
            retry_after=300
        )
        keyword_generator.rate_limiter = mock_rate_limiter
        
        with pytest.raises(RateLimitError):
            await keyword_generator.generate_keywords_from_query(
                "test query", user_id="test_user", max_keywords=5
            )
    
    async def test_metrics_recording(self, keyword_generator):
        """Test metrics collection"""
        query = "单细胞测序分析"
        
        result = await keyword_generator.generate_keywords_from_query(query, max_keywords=10)
        
        assert_valid_keyword_result(result)
        
        # Verify metrics were recorded
        metrics = keyword_generator.metrics_collector
        metrics.record_quality.assert_called_once()
        
        # Check that quality metrics have expected values
        call_args = metrics.record_quality.call_args[1]
        assert call_args["query"] == query
        assert call_args["keywords_count"] >= 0
        assert 0.0 <= call_args["confidence_score"] <= 1.0
        assert call_args["processing_time"] >= 0.0
    
    async def test_error_fallback(self, keyword_generator):
        """Test error fallback functionality"""
        # Mock an internal method to raise an exception
        with patch.object(keyword_generator, '_extract_rule_based_keywords') as mock_method:
            mock_method.side_effect = Exception("Internal error")
            
            result = await keyword_generator.generate_keywords_from_query(
                "test query", max_keywords=5
            )
            
            # Should return fallback result
            assert "error" in result
            assert result["keyword_analysis"]["total_keywords"] == 0
            assert result["keyword_analysis"]["confidence_score"] == 0.0


class TestKeywordGeneratorFactory:
    """Test cases for Keyword Generator Factory"""
    
    async def test_singleton_instance(self):
        """Test singleton pattern"""
        factory = KeywordGeneratorFactory()
        
        instance1 = await factory.get_instance()
        instance2 = await factory.get_instance()
        
        # Should return the same instance
        assert instance1 is instance2
    
    async def test_create_new_instance(self, test_keyword_config):
        """Test creating new instances"""
        factory = KeywordGeneratorFactory()
        
        instance1 = await factory.create_new_instance(config=test_keyword_config)
        instance2 = await factory.create_new_instance(config=test_keyword_config)
        
        # Should create different instances
        assert instance1 is not instance2
        assert instance1.config is test_keyword_config
        assert instance2.config is test_keyword_config
    
    async def test_reset_instance(self):
        """Test resetting singleton instance"""
        factory = KeywordGeneratorFactory()
        
        instance1 = await factory.get_instance()
        await factory.reset_instance()
        instance2 = await factory.get_instance()
        
        # Should create new instance after reset
        assert instance1 is not instance2


class TestKeywordGeneratorDiagnostics:
    """Test cases for diagnostics utilities"""
    
    async def test_health_check(self, keyword_generator):
        """Test health check functionality"""
        diagnostics = KeywordGeneratorDiagnostics()
        
        health_status = await diagnostics.health_check(keyword_generator)
        
        assert "status" in health_status
        assert "checks" in health_status
        assert "timestamp" in health_status
        
        checks = health_status["checks"]
        assert "initialized" in checks
        assert "validator" in checks
        assert "cache" in checks
        assert "basic_functionality" in checks
    
    async def test_performance_benchmark(self, keyword_generator, performance_test_data):
        """Test performance benchmarking"""
        diagnostics = KeywordGeneratorDiagnostics()
        
        benchmark_results = await diagnostics.performance_benchmark(
            keyword_generator,
            test_queries=performance_test_data["test_queries"][:2]  # Limit for faster tests
        )
        
        assert "test_queries_count" in benchmark_results
        assert "results" in benchmark_results
        assert "summary" in benchmark_results
        
        # Check individual results
        for result in benchmark_results["results"]:
            assert "query" in result
            assert "duration" in result
            assert "success" in result
            
            if result["success"]:
                assert "keywords_count" in result
                assert "confidence" in result
                assert_performance_within_limits(result["duration"])
        
        # Check summary statistics
        if benchmark_results["summary"]:
            summary = benchmark_results["summary"]
            assert "avg_duration" in summary
            assert "success_rate" in summary
            assert 0.0 <= summary["success_rate"] <= 1.0


class TestPerformanceOptimizations:
    """Test cases for performance optimizations"""
    
    async def test_timeout_wrapper(self, keyword_generator):
        """Test timeout wrapper functionality"""
        # Test normal operation within timeout
        result = await generate_keywords_with_timeout(
            keyword_generator,
            "单细胞测序",
            timeout=5.0,
            max_keywords=5
        )
        
        assert_valid_keyword_result(result)
    
    async def test_timeout_exceeded(self, keyword_generator):
        """Test timeout exceeded scenario"""
        # Mock slow operation
        with patch.object(keyword_generator, 'generate_keywords_from_query') as mock_method:
            mock_method.side_effect = asyncio.sleep(10)  # Simulate slow operation
            
            with pytest.raises(PerformanceError):
                await generate_keywords_with_timeout(
                    keyword_generator,
                    "test query",
                    timeout=0.1,  # Very short timeout
                    max_keywords=5
                )
    
    async def test_terminology_optimization(self, keyword_generator):
        """Test terminology mapping optimization"""
        optimization_results = await optimize_terminology_mapping(keyword_generator)
        
        assert "original_size" in optimization_results
        assert "optimizations_applied" in optimization_results
        assert "final_size" in optimization_results
        
        assert optimization_results["original_size"] > 0
        assert isinstance(optimization_results["optimizations_applied"], list)


class TestIntegrationScenarios:
    """Integration test scenarios"""
    
    async def test_end_to_end_workflow(self, keyword_generator):
        """Test complete end-to-end workflow"""
        # Test queries in different languages and complexities
        test_queries = [
            "单细胞RNA测序分析",
            "T cell immune response study",
            "10x Genomics PBMC单细胞转录组",
            "tumor microenvironment analysis"
        ]
        
        for query in test_queries:
            result = await keyword_generator.generate_keywords_from_query(
                query, max_keywords=15
            )
            
            assert_valid_keyword_result(result)
            assert result["user_query"] == query
            
            # Should have reasonable performance
            assert result["keyword_analysis"]["total_keywords"] >= 0
            assert 0.0 <= result["keyword_analysis"]["confidence_score"] <= 1.0
    
    async def test_concurrent_requests(self, keyword_generator):
        """Test handling of concurrent requests"""
        queries = [
            "单细胞测序",
            "免疫细胞分析", 
            "基因表达研究",
            "细胞分化过程"
        ]
        
        # Execute concurrent requests
        tasks = [
            keyword_generator.generate_keywords_from_query(query, max_keywords=10)
            for query in queries
        ]
        
        results = await asyncio.gather(*tasks)
        
        # All requests should succeed
        assert len(results) == len(queries)
        for result in results:
            assert_valid_keyword_result(result)
    
    async def test_stress_testing(self, keyword_generator):
        """Basic stress testing"""
        # Test with many small requests
        tasks = []
        for i in range(10):  # Reduced for CI
            task = keyword_generator.generate_keywords_from_query(
                f"测试查询{i}", max_keywords=5
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Most requests should succeed
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) >= len(tasks) * 0.8  # At least 80% success rate
    
    async def test_configuration_variations(self):
        """Test with different configuration variations"""
        from app.config.keyword_config import KeywordConfig, TerminologyConfig
        
        # Test with minimal configuration
        minimal_config = KeywordConfig(
            MAX_KEYWORDS_PER_QUERY=5,
            ENABLE_FUZZY_MATCHING=False,
            ENABLE_CACHING=False
        )
        
        generator = AIKeywordGenerator(config=minimal_config)
        generator._terminology_mapping = create_test_terminology_mapping()
        generator._domain_keywords = create_test_domain_keywords()
        await generator.initialize()
        
        result = await generator.generate_keywords_from_query(
            "单细胞测序", max_keywords=5
        )
        
        assert_valid_keyword_result(result)
        assert result["keyword_analysis"]["total_keywords"] <= 5