"""
客户画像API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any

from app.core.database import get_db
from app.core.auth import get_current_active_user
from app.models.user import User
from app.services.customer_profile_service import CustomerProfileService
from app.schemas.customer_profile import (
    CustomerProfileResponse,
    CustomerProfileWithDetails,
    CustomerProfileUpdate,
    ProfileAnalysisRequest,
    ProfileAnalysisResponse,
    ProfileInsight,
    CustomerSegmentResponse,
    BehaviorEventCreate,
    RequirementHistoryCreate
)

router = APIRouter()
profile_service = CustomerProfileService()


@router.get("/profile/{user_id}", response_model=CustomerProfileResponse)
async def get_customer_profile(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取客户画像
    """
    try:
        # 权限检查：只能查看自己的画像或管理员可以查看所有
        if current_user.id != user_id and current_user.role not in ["super_admin", "sales"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限查看该用户画像"
            )
        
        profile = await profile_service.get_or_create_profile(db, user_id)
        return profile
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取客户画像失败: {str(e)}"
        )


@router.get("/profile/{user_id}/details", response_model=CustomerProfileWithDetails)
async def get_customer_profile_details(
    user_id: int,
    include_history: bool = Query(True, description="是否包含历史记录"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取客户画像详细信息
    """
    try:
        # 权限检查
        if current_user.id != user_id and current_user.role not in ["super_admin", "sales"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限查看该用户画像详情"
            )
        
        profile = await profile_service.get_or_create_profile(db, user_id)
        
        # 构建详细响应
        profile_details = CustomerProfileWithDetails(
            **profile.__dict__,
            dimensions=[],  # 暂时简化，实际应该查询相关维度
            recent_behaviors=[],  # 暂时简化，实际应该查询最近行为
            requirement_history=[]  # 暂时简化，实际应该查询需求历史
        )
        
        return profile_details
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取客户画像详情失败: {str(e)}"
        )


@router.put("/profile/{user_id}", response_model=CustomerProfileResponse)
async def update_customer_profile(
    user_id: int,
    profile_update: CustomerProfileUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新客户画像
    """
    try:
        # 权限检查：只能更新自己的画像或管理员可以更新所有
        if current_user.id != user_id and current_user.role not in ["super_admin", "sales"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限更新该用户画像"
            )
        
        profile = await profile_service.get_or_create_profile(db, user_id)
        
        # 更新画像字段
        update_data = profile_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(profile, field, value)
        
        db.commit()
        db.refresh(profile)
        
        return profile
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新客户画像失败: {str(e)}"
        )


@router.post("/analyze")
async def analyze_customer_profile(
    request: ProfileAnalysisRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    分析客户画像
    """
    try:
        # 权限检查
        if current_user.id != request.user_id and current_user.role not in ["super_admin", "sales"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限分析该用户画像"
            )
        
        # 执行画像分析
        conversation_analysis = await profile_service.analyze_conversation_behavior(db, request.user_id)
        requirement_analysis = await profile_service.analyze_requirement_patterns(db, request.user_id)
        insights = await profile_service.generate_profile_insights(db, request.user_id)
        recommendations = await profile_service.get_personalized_recommendations(db, request.user_id)
        
        # 构建分析响应
        analysis_response = {
            "user_id": request.user_id,
            "analysis_timestamp": "2024-01-01T00:00:00Z",  # 实际应该使用当前时间
            "profile_summary": {
                "conversation_behavior": conversation_analysis,
                "requirement_patterns": requirement_analysis
            },
            "key_insights": [insight.title for insight in insights],
            "recommendations": [
                f"内容推荐: {', '.join(recommendations.get('content_recommendations', []))}",
                f"服务推荐: {', '.join(recommendations.get('service_recommendations', []))}",
                f"学习路径: {' -> '.join(recommendations.get('learning_path', []))}"
            ],
            "confidence_scores": {
                "overall": 0.75,
                "conversation_analysis": 0.8,
                "requirement_analysis": 0.7
            },
            "next_actions": recommendations.get("next_best_actions", [])
        }
        
        return analysis_response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析客户画像失败: {str(e)}"
        )


@router.get("/insights/{user_id}", response_model=List[ProfileInsight])
async def get_customer_insights(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取客户洞察
    """
    try:
        # 权限检查
        if current_user.id != user_id and current_user.role not in ["super_admin", "sales"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限查看该用户洞察"
            )
        
        insights = await profile_service.generate_profile_insights(db, user_id)
        return insights
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取客户洞察失败: {str(e)}"
        )


@router.get("/recommendations/{user_id}")
async def get_personalized_recommendations(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取个性化推荐
    """
    try:
        # 权限检查
        if current_user.id != user_id and current_user.role not in ["super_admin", "sales"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限查看该用户推荐"
            )
        
        recommendations = await profile_service.get_personalized_recommendations(db, user_id)
        return recommendations
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取个性化推荐失败: {str(e)}"
        )


@router.post("/behavior/{user_id}")
async def record_behavior_event(
    user_id: int,
    behavior_event: BehaviorEventCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    记录用户行为事件
    """
    try:
        # 权限检查：只能记录自己的行为或系统记录
        if current_user.id != user_id and current_user.role not in ["super_admin", "system"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限记录该用户行为"
            )
        
        # 记录行为事件并更新画像
        behavior_data = behavior_event.dict()
        await profile_service.update_profile_from_behavior(db, user_id, behavior_data)
        
        return {"message": "行为事件记录成功"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"记录行为事件失败: {str(e)}"
        )


@router.post("/requirements/{user_id}")
async def update_profile_from_requirements(
    user_id: int,
    requirement_data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    基于需求数据更新画像
    """
    try:
        # 权限检查
        if current_user.id != user_id and current_user.role not in ["super_admin", "system"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限更新该用户画像"
            )
        
        # 基于需求更新画像
        await profile_service.update_profile_from_requirements(db, user_id, requirement_data)
        
        return {"message": "基于需求更新画像成功"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"基于需求更新画像失败: {str(e)}"
        )


@router.get("/segments", response_model=List[CustomerSegmentResponse])
async def get_customer_segments(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取客户分群信息
    """
    try:
        # 权限检查：只有管理员和销售可以查看分群
        if current_user.role not in ["super_admin", "sales"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限查看客户分群"
            )
        
        # 简化的分群数据
        segments = [
            CustomerSegmentResponse(
                segment_id="high_value",
                segment_name="高价值客户",
                description="预算充足、需求复杂、参与度高的客户",
                user_count=25,
                key_characteristics=["预算>50万", "技术需求复杂", "高参与度"],
                recommended_strategies=["专属服务", "定制方案", "优先支持"]
            ),
            CustomerSegmentResponse(
                segment_id="growing_users",
                segment_name="成长型用户",
                description="技术需求逐步提升、学习意愿强的客户",
                user_count=45,
                key_characteristics=["需求复杂度递增", "学习积极", "预算适中"],
                recommended_strategies=["教育内容", "进阶服务", "社区建设"]
            ),
            CustomerSegmentResponse(
                segment_id="new_users",
                segment_name="新手用户",
                description="刚接触单细胞技术、需要基础指导的客户",
                user_count=80,
                key_characteristics=["技术基础薄弱", "需要指导", "预算有限"],
                recommended_strategies=["基础教程", "入门服务", "简化流程"]
            )
        ]
        
        return segments
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取客户分群失败: {str(e)}"
        )
