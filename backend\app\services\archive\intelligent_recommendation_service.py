"""
智能推荐服务 - 兼容性接口
将智能推荐功能重定向到现有的增强解决方案生成器
"""
from typing import Dict, List, Any, Optional
import logging

from app.services.enhanced_solution_generator import EnhancedSolutionGenerator, get_enhanced_solution_generator
from app.services.unified_literature_api_service import get_unified_literature_api_service

logger = logging.getLogger(__name__)


class IntelligentRecommendationService:
    """智能推荐服务 - 基于增强解决方案生成器的兼容性实现"""
    
    def __init__(self):
        self.solution_generator = get_enhanced_solution_generator()
        self.literature_service = get_unified_literature_api_service()
    
    async def generate_intelligent_recommendations(
        self,
        requirements: Dict[str, Any],
        user_message: str = ""
    ) -> Dict[str, Any]:
        """
        生成智能推荐
        使用增强解决方案生成器的功能
        """
        try:
            # 使用增强解决方案生成器生成推荐
            solution = await self.solution_generator.generate_comprehensive_solution(
                requirements=requirements,
                user_message=user_message
            )
            
            # 转换为推荐格式
            recommendations = {
                "solution_recommendations": solution.get("recommended_solution", {}),
                "literature_recommendations": solution.get("literature_analysis", {}),
                "technical_recommendations": solution.get("technical_analysis", {}),
                "cost_recommendations": solution.get("cost_analysis", {}),
                "timeline_recommendations": solution.get("timeline", {}),
                "risk_assessment": solution.get("risk_assessment", {}),
                "next_steps": solution.get("next_steps", []),
                "generated_at": solution.get("generated_at", ""),
                "recommendation_id": solution.get("solution_id", "")
            }
            
            return recommendations
            
        except Exception as e:
            logger.error(f"智能推荐生成失败: {str(e)}")
            return {
                "error": f"推荐生成失败: {str(e)}",
                "fallback_recommendations": {
                    "message": "请联系专家获取个性化推荐",
                    "contact_info": "<EMAIL>"
                }
            }
    
    async def get_literature_recommendations(
        self,
        research_keywords: List[str],
        research_context: str = ""
    ) -> Dict[str, Any]:
        """
        获取文献推荐
        """
        try:
            # 使用统一文献API服务
            literature_results = await self.literature_service.search_literature(
                query=" ".join(research_keywords),
                max_results=10
            )
            
            return {
                "literature_recommendations": literature_results.get("papers", []),
                "search_keywords": research_keywords,
                "total_found": len(literature_results.get("papers", [])),
                "recommendation_context": research_context
            }
            
        except Exception as e:
            logger.error(f"文献推荐失败: {str(e)}")
            return {
                "error": f"文献推荐失败: {str(e)}",
                "fallback_message": "请手动搜索相关文献"
            }
    
    async def get_technical_recommendations(
        self,
        experiment_type: str,
        sample_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        获取技术推荐
        """
        try:
            # 基于实验类型和样本信息生成技术推荐
            requirements = {
                "experimentType": experiment_type,
                "sampleInfo": sample_info
            }
            
            solution = await self.solution_generator.generate_comprehensive_solution(
                requirements=requirements
            )
            
            return {
                "technical_recommendations": solution.get("technical_analysis", {}),
                "platform_recommendations": solution.get("recommended_solution", {}).get("platform", ""),
                "workflow_recommendations": solution.get("recommended_solution", {}).get("workflow", []),
                "quality_control_recommendations": solution.get("recommended_solution", {}).get("quality_control", [])
            }
            
        except Exception as e:
            logger.error(f"技术推荐失败: {str(e)}")
            return {
                "error": f"技术推荐失败: {str(e)}",
                "fallback_recommendations": ["请咨询技术专家", "参考标准流程"]
            }


# 全局服务实例
intelligent_recommendation_service = IntelligentRecommendationService()


def get_intelligent_recommendation_service() -> IntelligentRecommendationService:
    """获取智能推荐服务实例"""
    return intelligent_recommendation_service
