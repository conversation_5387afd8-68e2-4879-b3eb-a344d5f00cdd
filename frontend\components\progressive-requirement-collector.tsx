"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Circle, Zap, AlertTriangle } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "sonner"
import { CircularProgress, CircularProgressMini } from "@/components/ui/circular-progress"
import { CellCountSlider, BudgetRangeSlider } from "@/components/ui/range-slider"
import { FieldValidator, ValidationRules, SmartSuggestion } from "@/components/ui/field-validator"

interface RequirementData {
  // 第1步：基础分类信息
  speciesType: string          // 物种类型 - 最重要的基础信息
  experimentType: string       // 实验类型 - 决定技术路线
  researchGoal: string        // 研究目标

  // 第2步：样本详细信息
  sampleType: string          // 样本类型（根据物种动态调整）
  sampleCount: string         // 样本数目
  sampleCondition: string     // 样本状态（新鲜/冷冻/固定等）
  sampleProcessing: string    // 样本处理方式
  cellCount: string           // 预期细胞数量
  cellViability: string       // 预期细胞活力

  // 第3步：项目规划
  budget: string              // 预算范围
  timeline: string            // 项目周期
  urgencyLevel: string        // 紧急程度

  // 第4步：技术细节和高级选项
  sequencingDepth: string     // 测序深度
  analysisType: string        // 分析类型
  dataAnalysisNeeds: string   // 数据分析需求
  specialRequirements: string // 特殊要求
  needsCellSorting: string    // 是否需要细胞分选

  // 自动推断信息
  recommendedPlatform: string
  estimatedCost: string
  riskFactors: string[]

  // 完成度
  completeness: number
  collectedFields: string[]
}

export function ProgressiveRequirementCollector({
  onRequirementsChange,
  onSubmitRequirements,
  isCompact = false,
  resetTrigger
}: {
  onRequirementsChange: (requirements: RequirementData) => void
  onSubmitRequirements?: (requirements: RequirementData) => void
  isCompact?: boolean
  resetTrigger?: number // 用于触发重置的计数器
}) {
  const { user } = useAuth()
  const [requirements, setRequirements] = useState<RequirementData>({
    // 第1步：基础分类信息
    speciesType: "",
    experimentType: "",
    researchGoal: "",

    // 第2步：样本详细信息
    sampleType: "",
    sampleCount: "",
    sampleCondition: "",
    sampleProcessing: "",
    cellCount: "",
    cellViability: "",

    // 第3步：项目规划
    budget: "",
    timeline: "",
    urgencyLevel: "",

    // 第4步：技术细节和高级选项
    sequencingDepth: "",
    analysisType: "",
    dataAnalysisNeeds: "",
    specialRequirements: "",
    needsCellSorting: "",

    // 自动推断信息
    recommendedPlatform: "",
    estimatedCost: "",
    riskFactors: [],

    // 完成度
    completeness: 0,
    collectedFields: []
  })

  // 分步骤状态管理
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [expandedSteps, setExpandedSteps] = useState<number[]>([1]) // 控制哪些步骤展开
  const [isDraftSaved, setIsDraftSaved] = useState(false)
  const [lastSaveTime, setLastSaveTime] = useState<Date | null>(null)

  // 重新设计的步骤定义
  const steps = [
    {
      id: 1,
      title: "基础分类",
      icon: "🧬",
      description: "物种类型、实验类型和研究目标",
      fields: ["speciesType", "experimentType", "researchGoal"]
    },
    {
      id: 2,
      title: "样本信息",
      icon: "🔬",
      description: "样本类型、数目、状态和处理方式",
      fields: ["sampleType", "sampleCount", "sampleCondition", "sampleProcessing", "cellCount", "cellViability"]
    },
    {
      id: 3,
      title: "项目规划",
      icon: "📅",
      description: "预算、时间和紧急程度",
      fields: ["budget", "timeline", "urgencyLevel"]
    },
    {
      id: 4,
      title: "技术细节",
      icon: "⚙️",
      description: "测序深度、分析需求和特殊要求",
      fields: ["sequencingDepth", "analysisType", "dataAnalysisNeeds", "specialRequirements", "needsCellSorting"]
    }
  ]

  // 预定义选项 - 重新组织和扩展

  // 物种类型选项
  const speciesTypes = [
    "人类 (Homo sapiens)",
    "小鼠 (Mus musculus)",
    "大鼠 (Rattus norvegicus)",
    "斑马鱼 (Danio rerio)",
    "果蝇 (Drosophila melanogaster)",
    "线虫 (C. elegans)",
    "拟南芥 (Arabidopsis thaliana)",
    "其他模式生物",
    "非模式生物"
  ]

  // 实验类型选项
  const experimentTypes = [
    "单细胞RNA测序 (scRNA-seq)",
    "单细胞ATAC测序 (scATAC-seq)",
    "单细胞多组学 (Multiome)",
    "空间转录组学 (Spatial)",
    "单细胞蛋白质组学 (CITE-seq)",
    "单细胞表观遗传学",
    "其他单细胞技术"
  ]

  // 研究目标选项
  const researchGoals = [
    "细胞类型鉴定与分类",
    "发育轨迹分析",
    "疾病机制研究",
    "药物筛选与评估",
    "免疫细胞功能分析",
    "肿瘤异质性研究",
    "神经发育与功能",
    "干细胞分化研究",
    "其他研究目标"
  ]

  // 动态样本类型选项（根据物种类型调整）
  const getSampleTypesBySpecies = (speciesType: string) => {
    const commonTypes = ["细胞系", "原代细胞", "其他"]

    if (speciesType.includes("人类")) {
      return [
        "PBMC (外周血单核细胞)",
        "肿瘤组织",
        "正常组织",
        "血液样本",
        "骨髓样本",
        "脑组织",
        "肝脏组织",
        "肺组织",
        "肾脏组织",
        "心脏组织",
        ...commonTypes
      ]
    } else if (speciesType.includes("小鼠") || speciesType.includes("大鼠")) {
      return [
        "脑组织",
        "肝脏组织",
        "肺组织",
        "肾脏组织",
        "心脏组织",
        "脾脏组织",
        "骨髓",
        "胚胎组织",
        "肿瘤组织",
        ...commonTypes
      ]
    } else if (speciesType.includes("斑马鱼")) {
      return [
        "胚胎",
        "幼鱼",
        "成鱼组织",
        "脑组织",
        "心脏组织",
        ...commonTypes
      ]
    } else if (speciesType.includes("拟南芥")) {
      return [
        "叶片",
        "根部",
        "茎部",
        "花朵",
        "种子",
        "幼苗",
        ...commonTypes
      ]
    } else {
      return [
        "组织样本",
        "器官样本",
        "全胚",
        ...commonTypes
      ]
    }
  }

  // 样本数目选项
  const sampleCountOptions = [
    "1个样本",
    "2-3个样本",
    "4-5个样本",
    "6-10个样本",
    "10个以上样本"
  ]

  // 样本状态选项
  const sampleConditions = [
    "新鲜样本",
    "冷冻样本 (-80°C)",
    "液氮冷冻",
    "甲醛固定石蜡包埋 (FFPE)",
    "冷冻切片",
    "培养细胞",
    "其他"
  ]

  // 样本处理方式选项
  const sampleProcessingOptions = [
    "机械解离",
    "酶解离",
    "机械+酶解离",
    "流式细胞分选",
    "磁珠分选",
    "激光显微切割",
    "无需特殊处理",
    "其他方式"
  ]

  // 细胞活力选项
  const cellViabilityOptions = [
    "> 90%",
    "80-90%",
    "70-80%",
    "60-70%",
    "< 60%",
    "不确定"
  ]

  // 预算范围选项
  const budgetRanges = [
    "5万以下",
    "5-10万",
    "10-20万",
    "20-50万",
    "50万以上"
  ]

  // 时间周期选项
  const timelineOptions = [
    "1个月内",
    "2-3个月",
    "3-6个月",
    "6个月以上"
  ]

  // 紧急程度选项
  const urgencyLevels = [
    "非常紧急（加急处理）",
    "较为紧急",
    "正常进度",
    "不急（可排队）"
  ]

  // 分析类型选项
  const analysisTypes = [
    "细胞类型注释",
    "差异表达分析",
    "发育轨迹分析",
    "细胞通讯分析",
    "功能富集分析",
    "拷贝数变异分析",
    "其他分析"
  ]

  // 数据分析需求选项
  const dataAnalysisOptions = [
    "基础分析报告",
    "高级生物信息学分析",
    "个性化分析方案",
    "仅提供原始数据",
    "需要后续技术支持"
  ]

  // 细胞分选需求选项
  const cellSortingOptions = [
    "需要细胞分选",
    "不需要细胞分选",
    "不确定"
  ]

  // 检查步骤是否完成
  const isStepCompleted = (stepId: number) => {
    const step = steps.find(s => s.id === stepId)
    if (!step) return false

    return step.fields.every(field => requirements[field as keyof RequirementData])
  }

  // 检查步骤是否可以访问
  const canAccessStep = (stepId: number) => {
    if (stepId === 1) return true

    // 前一个步骤必须完成才能访问下一个步骤
    for (let i = 1; i < stepId; i++) {
      if (!isStepCompleted(i)) return false
    }
    return true
  }

  // 自动推进到下一步
  const checkAndAdvanceStep = () => {
    if (isStepCompleted(currentStep) && !completedSteps.includes(currentStep)) {
      setCompletedSteps(prev => [...prev, currentStep])

      // 如果不是最后一步，自动展开下一步
      if (currentStep < steps.length) {
        setTimeout(() => {
          setCurrentStep(currentStep + 1)
          // 收起当前步骤，展开下一步
          setExpandedSteps([currentStep + 1])
          toast.success(`${steps[currentStep - 1].title}已完成，请继续填写${steps[currentStep].title}`)
        }, 500)
      } else {
        // 最后一步完成，收起所有步骤
        setExpandedSteps([])
        toast.success("所有信息已完成，可以提交需求了！")
      }
    }
  }

  // 计算完成度 - 更新必需字段
  const calculateCompleteness = (reqs: RequirementData) => {
    // 核心必需字段（每个20分）
    const coreRequiredFields = ["speciesType", "experimentType", "researchGoal", "sampleType", "budget"]

    // 重要字段（每个10分）
    const importantFields = ["sampleCondition", "cellCount", "timeline"]

    // 可选字段（每个2分）
    const optionalFields = [
      "sampleProcessing", "cellViability", "urgencyLevel",
      "sequencingDepth", "analysisType", "dataAnalysisNeeds",
      "specialRequirements", "needsCellSorting"
    ]

    const completedCore = coreRequiredFields.filter(field => reqs[field as keyof RequirementData]).length
    const completedImportant = importantFields.filter(field => reqs[field as keyof RequirementData]).length
    const completedOptional = optionalFields.filter(field => reqs[field as keyof RequirementData]).length

    const completeness = (completedCore * 20) + (completedImportant * 10) + (completedOptional * 2)

    return Math.min(completeness, 100)
  }

  // 检查是否可以提交 - 更新必需字段
  const canSubmit = () => {
    const requiredFields = ["speciesType", "experimentType", "researchGoal", "sampleType", "budget"]
    return requiredFields.every(field => requirements[field as keyof RequirementData])
  }

  // 保存草稿到本地存储
  const saveDraft = (reqs: RequirementData) => {
    try {
      const draftKey = `cellforge_requirements_draft_${user?.id || 'anonymous'}`
      localStorage.setItem(draftKey, JSON.stringify({
        ...reqs,
        savedAt: new Date().toISOString()
      }))
      setIsDraftSaved(true)
      setLastSaveTime(new Date())

      // 3秒后隐藏保存提示
      setTimeout(() => setIsDraftSaved(false), 3000)
    } catch (error) {
      console.error('保存草稿失败:', error)
    }
  }

  // 加载草稿
  const loadDraft = () => {
    try {
      const draftKey = `cellforge_requirements_draft_${user?.id || 'anonymous'}`
      const savedDraft = localStorage.getItem(draftKey)
      if (savedDraft) {
        const draft = JSON.parse(savedDraft)
        delete draft.savedAt // 移除保存时间字段
        setRequirements(draft)
        onRequirementsChange(draft)
        toast.success("已加载保存的草稿")
      }
    } catch (error) {
      console.error('加载草稿失败:', error)
    }
  }

  // 清除草稿
  const clearDraft = () => {
    try {
      const draftKey = `cellforge_requirements_draft_${user?.id || 'anonymous'}`
      localStorage.removeItem(draftKey)
      setLastSaveTime(null)
      toast.success("草稿已清除")
    } catch (error) {
      console.error('清除草稿失败:', error)
    }
  }

  // 重置所有状态到初始值
  const resetAllStates = () => {
    const initialRequirements = {
      // 第1步：基础分类信息
      speciesType: "",
      experimentType: "",
      researchGoal: "",

      // 第2步：样本详细信息
      sampleType: "",
      sampleCount: "",
      sampleCondition: "",
      sampleProcessing: "",
      cellCount: "",
      cellViability: "",

      // 第3步：项目规划
      budget: "",
      timeline: "",
      urgencyLevel: "",

      // 第4步：技术细节和高级选项
      sequencingDepth: "",
      analysisType: "",
      dataAnalysisNeeds: "",
      specialRequirements: "",
      needsCellSorting: "",

      // 自动推断信息
      recommendedPlatform: "",
      estimatedCost: "",
      riskFactors: [],

      // 完成度
      completeness: 0,
      collectedFields: []
    }

    setRequirements(initialRequirements)
    setCurrentStep(1)
    setCompletedSteps([])
    setExpandedSteps([1])
    setIsDraftSaved(false)
    setLastSaveTime(null)

    // 清除草稿
    try {
      const draftKey = `cellforge_requirements_draft_${user?.id || 'anonymous'}`
      localStorage.removeItem(draftKey)
    } catch (error) {
      console.error('清除草稿失败:', error)
    }

    // 通知父组件
    onRequirementsChange(initialRequirements)
  }

  // 监听重置触发器
  useEffect(() => {
    if (resetTrigger && resetTrigger > 0) {
      resetAllStates()
    }
  }, [resetTrigger])

  // 组件加载时尝试加载草稿
  useEffect(() => {
    if (!resetTrigger || resetTrigger === 0) {
      loadDraft()
    }
  }, [])

  // 更新需求数据
  const updateRequirement = (field: string, value: string) => {
    const newReqs = { ...requirements, [field]: value }
    const collectedFields = [...new Set([...newReqs.collectedFields, field])]
    newReqs.collectedFields = collectedFields
    newReqs.completeness = calculateCompleteness(newReqs)

    setRequirements(newReqs)
    onRequirementsChange(newReqs)

    // 自动保存草稿（防抖）
    setTimeout(() => saveDraft(newReqs), 1000)

    // 检查是否可以推进到下一步
    setTimeout(checkAndAdvanceStep, 100)
  }

  // 处理提交需求
  const handleSubmitRequirements = () => {
    if (!canSubmit()) {
      toast.error("请先填写所有必需的信息")
      return
    }

    const finalRequirements = {
      ...requirements,
      completeness: 100
    }

    // 更新本地状态
    onRequirementsChange(finalRequirements)

    // 调用提交回调，触发AI建议生成
    if (onSubmitRequirements) {
      onSubmitRequirements(finalRequirements)
    }

    toast.success("需求信息已提交，正在生成专业建议...")
  }

  return (
    <div className={`space-y-4 ${isCompact ? 'text-sm' : ''}`}>
      {/* 进度指示器 - 使用环形进度条 */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader className={isCompact ? "p-3" : "p-4"}>
          <CardTitle className={`flex items-center gap-2 ${isCompact ? 'text-sm' : 'text-base'}`}>
            <Zap className={`${isCompact ? 'h-4 w-4' : 'h-5 w-5'} text-blue-600`} />
            需求收集助手
          </CardTitle>

          <div className="flex items-center gap-4">
            {/* 环形进度条 */}
            <div className="flex-shrink-0">
              {isCompact ? (
                <CircularProgressMini
                  value={requirements.completeness}
                  size={50}
                  strokeWidth={3}
                />
              ) : (
                <CircularProgress
                  value={requirements.completeness}
                  size={80}
                  strokeWidth={6}
                />
              )}
            </div>

            {/* 步骤信息 */}
            <div className="flex-1 space-y-2">
              <div className="flex items-center justify-between">
                <span className={`${isCompact ? 'text-xs' : 'text-sm'} text-slate-600`}>
                  当前步骤
                </span>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  {currentStep}/{steps.length}
                </Badge>
              </div>

              <div className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-800`}>
                {steps[currentStep - 1]?.icon} {steps[currentStep - 1]?.title}
              </div>

              <div className={`${isCompact ? 'text-xs' : 'text-sm'} text-slate-500`}>
                {steps[currentStep - 1]?.description}
              </div>

              {/* 线性进度条作为补充 */}
              <Progress value={requirements.completeness} className="h-1.5 bg-blue-100" />

              {/* 草稿保存状态 */}
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center gap-1">
                  {isDraftSaved ? (
                    <>
                      <CheckCircle className="h-3 w-3 text-green-600" />
                      <span className="text-green-600">草稿已保存</span>
                    </>
                  ) : lastSaveTime ? (
                    <>
                      <Circle className="h-3 w-3 text-slate-400" />
                      <span className="text-slate-500">
                        上次保存: {lastSaveTime.toLocaleTimeString()}
                      </span>
                    </>
                  ) : null}
                </div>

                {lastSaveTime && (
                  <button
                    onClick={clearDraft}
                    className="text-slate-400 hover:text-slate-600 transition-colors"
                  >
                    清除草稿
                  </button>
                )}
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 分步表单 */}
      <div className="space-y-3">
        {steps.map((step) => {
          const isCompleted = isStepCompleted(step.id)
          const isActive = currentStep === step.id
          const canAccess = canAccessStep(step.id)

          return (
            <Card
              key={step.id}
              className={`border transition-all duration-300 ${
                isCompleted
                  ? 'border-green-200 bg-green-50'
                  : isActive
                  ? 'border-blue-200 bg-blue-50'
                  : canAccess
                  ? 'border-slate-200 bg-white hover:border-slate-300'
                  : 'border-slate-100 bg-slate-50'
              }`}
            >
              <CardHeader
                className={`${isCompact ? 'p-3' : 'p-4'} cursor-pointer`}
                onClick={() => {
                  if (canAccess) {
                    setCurrentStep(step.id)
                    // 切换展开状态
                    if (expandedSteps.includes(step.id)) {
                      setExpandedSteps(expandedSteps.filter(id => id !== step.id))
                    } else {
                      setExpandedSteps([step.id])
                    }
                  }
                }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      isCompleted
                        ? 'bg-green-500 text-white'
                        : isActive
                        ? 'bg-blue-500 text-white'
                        : canAccess
                        ? 'bg-slate-200 text-slate-600'
                        : 'bg-slate-100 text-slate-400'
                    }`}>
                      {isCompleted ? '✓' : step.id}
                    </div>
                    <div>
                      <h3 className={`${isCompact ? 'text-sm' : 'text-base'} font-medium ${
                        canAccess ? 'text-slate-900' : 'text-slate-400'
                      }`}>
                        {step.icon} {step.title}
                      </h3>
                      <p className={`${isCompact ? 'text-xs' : 'text-sm'} ${
                        canAccess ? 'text-slate-600' : 'text-slate-400'
                      }`}>
                        {step.description}
                      </p>
                    </div>
                  </div>
                  {canAccess && (
                    <div className={`text-xs px-2 py-1 rounded ${
                      isCompleted
                        ? 'bg-green-100 text-green-700'
                        : isActive
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-slate-100 text-slate-600'
                    }`}>
                      {isCompleted ? '已完成' : isActive ? '进行中' : '待填写'}
                    </div>
                  )}
                </div>
              </CardHeader>

              {/* 根据展开状态显示内容 */}
              {expandedSteps.includes(step.id) && canAccess && (
                <CardContent className={`${isCompact ? 'p-3 pt-0' : 'p-4 pt-0'} space-y-3`}>
                  {renderStepContent(step.id)}
                </CardContent>
              )}
            </Card>
          )
        })}
      </div>

      {/* 提交按钮 */}
      {canSubmit() && (
        <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <CardContent className={`${isCompact ? 'p-3' : 'p-4'}`}>
            <Button
              onClick={handleSubmitRequirements}
              className={`w-full ${isCompact ? 'h-8 text-xs' : 'h-10'} bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700`}
            >
              🚀 提交需求，生成方案
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )

  // 渲染步骤内容 - 重新设计
  function renderStepContent(stepId: number) {
    switch (stepId) {
      case 1: // 基础分类信息
        return (
          <>
            {/* 物种类型 */}
            <FieldValidator
              value={requirements.speciesType}
              rules={[ValidationRules.required("请选择物种类型")]}
            >
              <div>
                <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                  物种类型 <span className="text-red-500">*</span>
                </Label>
                <Select value={requirements.speciesType} onValueChange={(value) => updateRequirement("speciesType", value)}>
                  <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                    <SelectValue placeholder="选择研究的物种类型" />
                  </SelectTrigger>
                  <SelectContent>
                    {speciesTypes.map(species => (
                      <SelectItem key={species} value={species}>{species}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </FieldValidator>

            {/* 实验类型 */}
            <FieldValidator
              value={requirements.experimentType}
              rules={[
                ValidationRules.required("请选择实验类型"),
                ValidationRules.speciesCompatibility(requirements.speciesType)
              ]}
            >
              <div>
                <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                  实验类型 <span className="text-red-500">*</span>
                </Label>
                <Select
                  value={requirements.experimentType}
                  onValueChange={(value) => updateRequirement("experimentType", value)}
                  disabled={!requirements.speciesType}
                >
                  <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                    <SelectValue placeholder={requirements.speciesType ? "选择单细胞实验类型" : "请先选择物种类型"} />
                  </SelectTrigger>
                  <SelectContent>
                    {experimentTypes.map(type => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* 兼容性警告 */}
                {requirements.speciesType?.includes("拟南芥") && requirements.experimentType?.includes("ATAC") && (
                  <div className="mt-1 flex items-center gap-1 text-xs text-amber-600">
                    <AlertTriangle className="h-3 w-3" />
                    <span>植物样本的ATAC测序技术要求较高，建议咨询专家</span>
                  </div>
                )}
              </div>
            </FieldValidator>

            {/* 研究目标 */}
            <FieldValidator
              value={requirements.researchGoal}
              rules={[ValidationRules.required("请选择研究目标")]}
            >
              <div>
                <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                  研究目标 <span className="text-red-500">*</span>
                </Label>
                <Select value={requirements.researchGoal} onValueChange={(value) => updateRequirement("researchGoal", value)}>
                  <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                    <SelectValue placeholder="选择您的研究目标" />
                  </SelectTrigger>
                  <SelectContent>
                    {researchGoals.map(goal => (
                      <SelectItem key={goal} value={goal}>{goal}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </FieldValidator>
          </>
        )

      case 2: // 样本信息
        return (
          <>
            <div>
              <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                样本类型 <span className="text-red-500">*</span>
              </Label>
              <Select
                value={requirements.sampleType}
                onValueChange={(value) => updateRequirement("sampleType", value)}
                disabled={!requirements.speciesType}
              >
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder={requirements.speciesType ? "选择样本类型" : "请先选择物种类型"} />
                </SelectTrigger>
                <SelectContent>
                  {getSampleTypesBySpecies(requirements.speciesType).map(type => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 样本数目 */}
            <div>
              <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                样本数目
              </Label>
              <Select value={requirements.sampleCount} onValueChange={(value) => updateRequirement("sampleCount", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择样本数目" />
                </SelectTrigger>
                <SelectContent>
                  {sampleCountOptions.map(count => (
                    <SelectItem key={count} value={count}>{count}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                样本状态
              </Label>
              <Select value={requirements.sampleCondition} onValueChange={(value) => updateRequirement("sampleCondition", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择样本保存状态" />
                </SelectTrigger>
                <SelectContent>
                  {sampleConditions.map(condition => (
                    <SelectItem key={condition} value={condition}>{condition}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                样本处理方式
              </Label>
              <Select value={requirements.sampleProcessing} onValueChange={(value) => updateRequirement("sampleProcessing", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择样本处理方式" />
                </SelectTrigger>
                <SelectContent>
                  {sampleProcessingOptions.map(option => (
                    <SelectItem key={option} value={option}>{option}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 预期细胞数量 - 使用滑块选择器 */}
            <FieldValidator
              value={requirements.cellCount}
              rules={[ValidationRules.cellCount()]}
            >
              <div>
                <CellCountSlider
                  value={requirements.cellCount}
                  onValueChange={(value) => updateRequirement("cellCount", value)}
                  className="mt-1"
                />

                {/* 智能建议 */}
                <SmartSuggestion
                  field="cellCount"
                  value={requirements.cellCount}
                  onSuggestionApply={(suggestion) => updateRequirement("cellCount", suggestion)}
                />
              </div>
            </FieldValidator>

            <div>
              <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                预期细胞活力
              </Label>
              <Select value={requirements.cellViability} onValueChange={(value) => updateRequirement("cellViability", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择预期细胞活力" />
                </SelectTrigger>
                <SelectContent>
                  {cellViabilityOptions.map(option => (
                    <SelectItem key={option} value={option}>{option}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </>
        )

      case 3: // 项目规划
        return (
          <>
            {/* 预算范围 - 使用滑块选择器 */}
            <FieldValidator
              value={requirements.budget}
              rules={[ValidationRules.required("请选择预算范围")]}
            >
              <div>
                <BudgetRangeSlider
                  value={requirements.budget}
                  onValueChange={(value) => updateRequirement("budget", value)}
                  className="mt-1"
                />
              </div>
            </FieldValidator>

            <div>
              <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                项目周期
              </Label>
              <Select value={requirements.timeline} onValueChange={(value) => updateRequirement("timeline", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择项目周期" />
                </SelectTrigger>
                <SelectContent>
                  {timelineOptions.map(option => (
                    <SelectItem key={option} value={option}>{option}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                紧急程度
              </Label>
              <Select value={requirements.urgencyLevel} onValueChange={(value) => updateRequirement("urgencyLevel", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择项目紧急程度" />
                </SelectTrigger>
                <SelectContent>
                  {urgencyLevels.map(level => (
                    <SelectItem key={level} value={level}>{level}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </>
        )

      case 4: // 技术细节
        return (
          <>
            {/* 测序深度 - 添加智能建议 */}
            <div>
              <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                测序深度
              </Label>
              <Input
                value={requirements.sequencingDepth}
                onChange={(e) => updateRequirement("sequencingDepth", e.target.value)}
                placeholder="例如: 50,000 reads/cell"
                className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}
              />

              {/* 智能建议 */}
              <SmartSuggestion
                field="sequencingDepth"
                value={requirements.sequencingDepth}
                onSuggestionApply={(suggestion) => updateRequirement("sequencingDepth", suggestion)}
              />
            </div>

            <div>
              <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                分析类型
              </Label>
              <Select value={requirements.analysisType} onValueChange={(value) => updateRequirement("analysisType", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择分析类型" />
                </SelectTrigger>
                <SelectContent>
                  {analysisTypes.map(type => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                数据分析需求
              </Label>
              <Select value={requirements.dataAnalysisNeeds} onValueChange={(value) => updateRequirement("dataAnalysisNeeds", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择数据分析需求" />
                </SelectTrigger>
                <SelectContent>
                  {dataAnalysisOptions.map(option => (
                    <SelectItem key={option} value={option}>{option}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                是否需要细胞分选
              </Label>
              <Select value={requirements.needsCellSorting} onValueChange={(value) => updateRequirement("needsCellSorting", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择是否需要细胞分选" />
                </SelectTrigger>
                <SelectContent>
                  {cellSortingOptions.map(option => (
                    <SelectItem key={option} value={option}>{option}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                特殊要求
              </Label>
              <Textarea
                value={requirements.specialRequirements}
                onChange={(e) => updateRequirement("specialRequirements", e.target.value)}
                placeholder="任何特殊的技术要求或限制..."
                rows={isCompact ? 2 : 3}
                className={`mt-1 ${isCompact ? 'text-xs' : ''}`}
              />
            </div>
          </>
        )

      default:
        return null
    }
  }
}
