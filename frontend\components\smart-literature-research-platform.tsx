"use client"

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { 
  Sparkles, 
  FileText, 
  Search, 
  Zap, 
  BookOpen, 
  ArrowRight,
  CheckCircle,
  Clock,
  Users
} from 'lucide-react'
import { ProgressiveRequirementCollector } from './progressive-requirement-collector'
import { PerplexityLiteratureSearch } from './perplexity-literature-search'
import { LiteratureSearchToggle } from './literature-search-toggle'
import { toast } from 'sonner'

interface RequirementData {
  speciesType: string
  experimentType: string
  researchGoal: string
  sampleType: string
  budget: string
  timeline: string
  analysisType: string
  completeness: number
  // ... 其他字段
}

interface SearchResult {
  query: string
  sources: string[]
  papers: any[]
  summary: string
  relatedTopics: string[]
  searchTime: number
}

export function SmartLiteratureResearchPlatform() {
  const [requirements, setRequirements] = useState<RequirementData | null>(null)
  const [searchResults, setSearchResults] = useState<SearchResult | null>(null)
  const [enableLiteratureSearch, setEnableLiteratureSearch] = useState(true)
  const [activeTab, setActiveTab] = useState("requirements")
  const [isSubmitted, setIsSubmitted] = useState(false)

  // 处理需求变更
  const handleRequirementsChange = (newRequirements: RequirementData) => {
    setRequirements(newRequirements)
  }

  // 处理需求提交
  const handleRequirementsSubmit = (finalRequirements: RequirementData) => {
    setRequirements(finalRequirements)
    setIsSubmitted(true)
    setActiveTab("search")
    toast.success("需求已收集完成，现在可以开始文献搜索")
  }

  // 处理搜索完成
  const handleSearchComplete = (results: SearchResult) => {
    setSearchResults(results)
    toast.success(`搜索完成！找到 ${results.papers.length} 篇相关文献`)
  }

  // 获取完成状态
  const getCompletionStatus = () => {
    const hasRequirements = requirements && requirements.completeness >= 80
    const hasSearch = searchResults !== null
    
    return {
      requirements: hasRequirements,
      search: hasSearch,
      overall: hasRequirements && hasSearch
    }
  }

  const completionStatus = getCompletionStatus()

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* 头部介绍 */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl text-blue-900">
            <Sparkles className="h-6 w-6" />
            智能文献研究平台
          </CardTitle>
          <p className="text-blue-700">
            基于您的研究需求，AI智能生成搜索策略，提供类似Perplexity的文献搜索体验
          </p>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-blue-200">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                completionStatus.requirements ? 'bg-green-500 text-white' : 'bg-blue-500 text-white'
              }`}>
                {completionStatus.requirements ? '✓' : '1'}
              </div>
              <div>
                <div className="font-medium text-blue-900">需求收集</div>
                <div className="text-xs text-blue-600">填写研究背景信息</div>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-blue-200">
              <div className={`w-8 h-8 rounded-full flex items-center justify-between ${
                completionStatus.search ? 'bg-green-500 text-white' : 
                completionStatus.requirements ? 'bg-blue-500 text-white' : 'bg-slate-300 text-slate-600'
              }`}>
                {completionStatus.search ? '✓' : '2'}
              </div>
              <div>
                <div className="font-medium text-blue-900">智能搜索</div>
                <div className="text-xs text-blue-600">AI驱动的文献发现</div>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-blue-200">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                completionStatus.overall ? 'bg-green-500 text-white' : 'bg-slate-300 text-slate-600'
              }`}>
                {completionStatus.overall ? '✓' : '3'}
              </div>
              <div>
                <div className="font-medium text-blue-900">研究洞察</div>
                <div className="text-xs text-blue-600">获得AI分析结果</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 主要内容区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="requirements" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            需求收集
            {completionStatus.requirements && (
              <CheckCircle className="h-4 w-4 text-green-600" />
            )}
          </TabsTrigger>
          <TabsTrigger 
            value="search" 
            disabled={!completionStatus.requirements}
            className="flex items-center gap-2"
          >
            <Search className="h-4 w-4" />
            文献搜索
            {completionStatus.search && (
              <CheckCircle className="h-4 w-4 text-green-600" />
            )}
          </TabsTrigger>
        </TabsList>

        {/* 需求收集标签页 */}
        <TabsContent value="requirements" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* 主要需求收集区域 */}
            <div className="lg:col-span-3">
              <ProgressiveRequirementCollector
                onRequirementsChange={handleRequirementsChange}
                onSubmitRequirements={handleRequirementsSubmit}
                resetTrigger={0}
              />
            </div>
            
            {/* 侧边栏 - 设置和状态 */}
            <div className="space-y-4">
              {/* 文献搜索设置 */}
              <LiteratureSearchToggle
                enabled={enableLiteratureSearch}
                onToggle={setEnableLiteratureSearch}
              />
              
              {/* 需求状态卡片 */}
              {requirements && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Zap className="h-4 w-4" />
                      需求状态
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between text-xs">
                      <span>完成度</span>
                      <Badge variant="secondary">
                        {requirements.completeness}%
                      </Badge>
                    </div>
                    
                    <div className="space-y-2 text-xs">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-3 w-3 text-green-600" />
                        <span>研究目标已确定</span>
                      </div>
                      {requirements.experimentType && (
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-3 w-3 text-green-600" />
                          <span>实验技术已选择</span>
                        </div>
                      )}
                      {requirements.budget && (
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-3 w-3 text-green-600" />
                          <span>预算范围已设定</span>
                        </div>
                      )}
                    </div>
                    
                    {requirements.completeness >= 80 && !isSubmitted && (
                      <Button 
                        size="sm" 
                        className="w-full"
                        onClick={() => handleRequirementsSubmit(requirements)}
                      >
                        开始文献搜索
                        <ArrowRight className="h-3 w-3 ml-1" />
                      </Button>
                    )}
                  </CardContent>
                </Card>
              )}
              
              {/* 提示卡片 */}
              <Card className="border-amber-200 bg-amber-50">
                <CardContent className="p-4">
                  <div className="flex items-start gap-2">
                    <BookOpen className="h-4 w-4 text-amber-600 mt-0.5" />
                    <div className="text-xs text-amber-800">
                      <div className="font-medium mb-1">💡 提示</div>
                      <div>
                        详细填写需求信息可以获得更精准的文献推荐。
                        AI会根据您的背景自动优化搜索策略。
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* 文献搜索标签页 */}
        <TabsContent value="search" className="space-y-6">
          {requirements ? (
            <div className="space-y-6">
              {/* 需求摘要 */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    基于您的需求信息
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                    <div>
                      <div className="text-slate-600">研究目标</div>
                      <div className="font-medium">{requirements.researchGoal || '未指定'}</div>
                    </div>
                    <div>
                      <div className="text-slate-600">实验类型</div>
                      <div className="font-medium">{requirements.experimentType || '未指定'}</div>
                    </div>
                    <div>
                      <div className="text-slate-600">样本类型</div>
                      <div className="font-medium">{requirements.sampleType || '未指定'}</div>
                    </div>
                    <div>
                      <div className="text-slate-600">预算范围</div>
                      <div className="font-medium">{requirements.budget || '未指定'}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Perplexity风格搜索界面 */}
              <PerplexityLiteratureSearch
                requirements={requirements}
                onSearchComplete={handleSearchComplete}
              />
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="space-y-4">
                  <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto">
                    <FileText className="h-8 w-8 text-slate-400" />
                  </div>
                  <div>
                    <h3 className="font-medium text-slate-900">需要先完成需求收集</h3>
                    <p className="text-sm text-slate-600 mt-1">
                      请先在"需求收集"标签页中填写您的研究需求信息
                    </p>
                  </div>
                  <Button onClick={() => setActiveTab("requirements")}>
                    <ArrowRight className="h-4 w-4 mr-2" />
                    前往需求收集
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* 底部状态栏 */}
      {(requirements || searchResults) && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-2 text-green-800">
                  <Clock className="h-4 w-4" />
                  <span>系统状态：运行正常</span>
                </div>
                {searchResults && (
                  <div className="flex items-center gap-2 text-green-700">
                    <BookOpen className="h-4 w-4" />
                    <span>已找到 {searchResults.papers.length} 篇文献</span>
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                {completionStatus.requirements && (
                  <Badge variant="outline" className="border-green-300 text-green-700">
                    需求已收集
                  </Badge>
                )}
                {completionStatus.search && (
                  <Badge variant="outline" className="border-green-300 text-green-700">
                    搜索已完成
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default SmartLiteratureResearchPlatform