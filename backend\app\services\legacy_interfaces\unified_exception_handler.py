"""
统一异常处理机制
提供服务降级策略、重试机制和熔断保护
"""
import asyncio
import logging
import time
import traceback
from typing import Dict, List, Any, Optional, Callable, Type, Union
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
from functools import wraps

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"           # 可忽略的错误
    MEDIUM = "medium"     # 需要关注的错误
    HIGH = "high"         # 严重错误
    CRITICAL = "critical" # 致命错误


class RetryStrategy(Enum):
    """重试策略"""
    NONE = "none"                    # 不重试
    FIXED_DELAY = "fixed_delay"      # 固定延迟
    EXPONENTIAL_BACKOFF = "exponential_backoff"  # 指数退避
    LINEAR_BACKOFF = "linear_backoff"            # 线性退避


class FallbackStrategy(Enum):
    """降级策略"""
    NONE = "none"           # 不降级
    CACHE = "cache"         # 使用缓存
    DEFAULT = "default"     # 返回默认值
    SERVICE = "service"     # 使用备用服务
    SIMPLIFIED = "simplified"  # 简化响应


@dataclass
class ErrorContext:
    """错误上下文"""
    service_name: str
    method_name: str
    error_type: str
    error_message: str
    severity: ErrorSeverity
    timestamp: datetime
    stack_trace: Optional[str] = None
    user_context: Optional[Dict[str, Any]] = None
    retry_count: int = 0
    max_retries: int = 0


@dataclass 
class RetryConfig:
    """重试配置"""
    max_attempts: int = 3
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_multiplier: float = 2.0
    jitter: bool = True
    retry_on: List[Type[Exception]] = field(default_factory=list)
    stop_on: List[Type[Exception]] = field(default_factory=list)


@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: int = 5
    recovery_timeout: int = 60
    success_threshold: int = 3  # 半开状态需要的连续成功次数
    timeout: float = 30.0


@dataclass
class FallbackConfig:
    """降级配置"""
    strategy: FallbackStrategy = FallbackStrategy.DEFAULT
    cache_ttl: int = 300  # 缓存有效期（秒）
    default_response: Optional[Any] = None
    fallback_service: Optional[str] = None
    simplified_fields: List[str] = field(default_factory=list)


class CircuitBreakerState(Enum):
    """熔断器状态"""
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 熔断状态
    HALF_OPEN = "half_open"  # 半开状态


class CircuitBreaker:
    """熔断器实现"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.last_success_time = None
        
    def can_execute(self) -> bool:
        """判断是否可以执行请求"""
        current_time = time.time()
        
        if self.state == CircuitBreakerState.CLOSED:
            return True
        elif self.state == CircuitBreakerState.OPEN:
            if (self.last_failure_time and 
                current_time - self.last_failure_time >= self.config.recovery_timeout):
                self.state = CircuitBreakerState.HALF_OPEN
                self.success_count = 0
                logger.info(f"熔断器进入半开状态")
                return True
            return False
        else:  # HALF_OPEN
            return True
    
    def record_success(self):
        """记录成功"""
        self.last_success_time = time.time()
        
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self.state = CircuitBreakerState.CLOSED
                self.failure_count = 0
                logger.info(f"熔断器恢复到关闭状态")
        elif self.state == CircuitBreakerState.CLOSED:
            self.failure_count = 0
    
    def record_failure(self):
        """记录失败"""
        self.last_failure_time = time.time()
        self.failure_count += 1
        
        if self.state == CircuitBreakerState.CLOSED:
            if self.failure_count >= self.config.failure_threshold:
                self.state = CircuitBreakerState.OPEN
                logger.warning(f"熔断器开启，失败次数: {self.failure_count}")
        elif self.state == CircuitBreakerState.HALF_OPEN:
            self.state = CircuitBreakerState.OPEN
            logger.warning(f"熔断器从半开状态回到开启状态")
    
    def get_state_info(self) -> Dict[str, Any]:
        """获取熔断器状态信息"""
        return {
            "state": self.state.value,
            "failure_count": self.failure_count,
            "success_count": self.success_count,
            "last_failure_time": self.last_failure_time,
            "last_success_time": self.last_success_time,
            "failure_threshold": self.config.failure_threshold,
            "recovery_timeout": self.config.recovery_timeout
        }


class RetryHandler:
    """重试处理器"""
    
    def __init__(self, config: RetryConfig):
        self.config = config
    
    async def execute_with_retry(
        self,
        func: Callable,
        *args,
        error_context: Optional[ErrorContext] = None,
        **kwargs
    ) -> Any:
        """执行函数并重试"""
        last_exception = None
        
        for attempt in range(self.config.max_attempts):
            try:
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                return result
                
            except Exception as e:
                last_exception = e
                
                # 检查是否应该停止重试
                if self._should_stop_retry(e):
                    logger.info(f"遇到停止重试的异常: {type(e).__name__}")
                    break
                
                # 检查是否应该重试
                if not self._should_retry(e):
                    logger.info(f"遇到不重试的异常: {type(e).__name__}")
                    break
                
                # 更新错误上下文
                if error_context:
                    error_context.retry_count = attempt + 1
                    error_context.max_retries = self.config.max_attempts
                
                if attempt < self.config.max_attempts - 1:
                    delay = self._calculate_delay(attempt)
                    logger.warning(
                        f"重试 {attempt + 1}/{self.config.max_attempts}，"
                        f"延迟 {delay:.2f}s: {e}"
                    )
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"重试失败，已达到最大次数 {self.config.max_attempts}: {e}")
        
        raise last_exception
    
    def _should_retry(self, exception: Exception) -> bool:
        """判断是否应该重试"""
        if not self.config.retry_on:
            return True
        
        return any(isinstance(exception, exc_type) for exc_type in self.config.retry_on)
    
    def _should_stop_retry(self, exception: Exception) -> bool:
        """判断是否应该停止重试"""
        if not self.config.stop_on:
            return False
        
        return any(isinstance(exception, exc_type) for exc_type in self.config.stop_on)
    
    def _calculate_delay(self, attempt: int) -> float:
        """计算重试延迟"""
        if self.config.strategy == RetryStrategy.FIXED_DELAY:
            delay = self.config.base_delay
        elif self.config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = self.config.base_delay * (self.config.backoff_multiplier ** attempt)
        elif self.config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = self.config.base_delay * (attempt + 1)
        else:
            delay = self.config.base_delay
        
        # 限制最大延迟
        delay = min(delay, self.config.max_delay)
        
        # 添加抖动
        if self.config.jitter:
            import random
            jitter = random.uniform(0.1, 0.3) * delay
            delay += jitter
        
        return delay


class FallbackHandler:
    """降级处理器"""
    
    def __init__(self, config: FallbackConfig):
        self.config = config
        self.cache = {}
        self.cache_timestamps = {}
    
    async def handle_fallback(
        self,
        error_context: ErrorContext,
        original_args: tuple = None,
        original_kwargs: dict = None
    ) -> Any:
        """处理降级"""
        try:
            if self.config.strategy == FallbackStrategy.CACHE:
                return await self._handle_cache_fallback(error_context, original_args, original_kwargs)
            elif self.config.strategy == FallbackStrategy.DEFAULT:
                return await self._handle_default_fallback(error_context)
            elif self.config.strategy == FallbackStrategy.SERVICE:
                return await self._handle_service_fallback(error_context, original_args, original_kwargs)
            elif self.config.strategy == FallbackStrategy.SIMPLIFIED:
                return await self._handle_simplified_fallback(error_context)
            else:
                raise Exception("无法进行服务降级")
                
        except Exception as e:
            logger.error(f"降级处理失败: {e}")
            return self.config.default_response
    
    async def _handle_cache_fallback(self, error_context: ErrorContext, args: tuple, kwargs: dict) -> Any:
        """缓存降级"""
        cache_key = self._generate_cache_key(error_context.service_name, error_context.method_name, args, kwargs)
        
        if cache_key in self.cache:
            cache_time = self.cache_timestamps.get(cache_key, 0)
            if time.time() - cache_time < self.config.cache_ttl:
                logger.info(f"使用缓存降级: {cache_key}")
                return self.cache[cache_key]
        
        return self.config.default_response
    
    async def _handle_default_fallback(self, error_context: ErrorContext) -> Any:
        """默认值降级"""
        logger.info(f"使用默认值降级: {error_context.service_name}.{error_context.method_name}")
        return self.config.default_response
    
    async def _handle_service_fallback(self, error_context: ErrorContext, args: tuple, kwargs: dict) -> Any:
        """备用服务降级"""
        if not self.config.fallback_service:
            return self.config.default_response
        
        logger.info(f"使用备用服务降级: {self.config.fallback_service}")
        # 这里需要具体的服务注册中心来获取备用服务
        # 暂时返回默认响应
        return self.config.default_response
    
    async def _handle_simplified_fallback(self, error_context: ErrorContext) -> Any:
        """简化响应降级"""
        logger.info(f"使用简化响应降级")
        
        if isinstance(self.config.default_response, dict):
            simplified = {}
            for field in self.config.simplified_fields:
                if field in self.config.default_response:
                    simplified[field] = self.config.default_response[field]
            return simplified
        
        return self.config.default_response
    
    def cache_result(self, service_name: str, method_name: str, args: tuple, kwargs: dict, result: Any):
        """缓存结果"""
        cache_key = self._generate_cache_key(service_name, method_name, args, kwargs)
        self.cache[cache_key] = result
        self.cache_timestamps[cache_key] = time.time()
        
        # 清理过期缓存
        self._cleanup_expired_cache()
    
    def _generate_cache_key(self, service_name: str, method_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        import hashlib
        key_data = f"{service_name}_{method_name}_{str(args)}_{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _cleanup_expired_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self.cache_timestamps.items()
            if current_time - timestamp > self.config.cache_ttl
        ]
        
        for key in expired_keys:
            self.cache.pop(key, None)
            self.cache_timestamps.pop(key, None)


class UnifiedExceptionHandler:
    """统一异常处理器"""
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.retry_handlers: Dict[str, RetryHandler] = {}
        self.fallback_handlers: Dict[str, FallbackHandler] = {}
        self.error_stats: Dict[str, Dict[str, int]] = {}
        self.error_history: List[ErrorContext] = []
        self.max_history_size = 1000
    
    def register_service(
        self,
        service_name: str,
        circuit_breaker_config: Optional[CircuitBreakerConfig] = None,
        retry_config: Optional[RetryConfig] = None,
        fallback_config: Optional[FallbackConfig] = None
    ):
        """注册服务的异常处理配置"""
        if circuit_breaker_config:
            self.circuit_breakers[service_name] = CircuitBreaker(circuit_breaker_config)
        
        if retry_config:
            self.retry_handlers[service_name] = RetryHandler(retry_config)
        
        if fallback_config:
            self.fallback_handlers[service_name] = FallbackHandler(fallback_config)
        
        self.error_stats[service_name] = {
            "total_errors": 0,
            "critical_errors": 0,
            "high_errors": 0,
            "medium_errors": 0,
            "low_errors": 0,
            "circuit_breaker_trips": 0,
            "successful_retries": 0,
            "failed_retries": 0,
            "fallback_used": 0
        }
        
        logger.info(f"注册服务异常处理配置: {service_name}")
    
    async def execute_with_protection(
        self,
        service_name: str,
        method_name: str,
        func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """使用保护机制执行函数"""
        error_context = ErrorContext(
            service_name=service_name,
            method_name=method_name,
            error_type="",
            error_message="",
            severity=ErrorSeverity.LOW,
            timestamp=datetime.utcnow()
        )
        
        # 检查熔断器
        circuit_breaker = self.circuit_breakers.get(service_name)
        if circuit_breaker and not circuit_breaker.can_execute():
            self.error_stats[service_name]["circuit_breaker_trips"] += 1
            error_context.error_type = "CircuitBreakerOpen"
            error_context.error_message = "服务熔断器开启"
            error_context.severity = ErrorSeverity.HIGH
            
            await self._handle_error(error_context, args, kwargs)
            return await self._execute_fallback(error_context, args, kwargs)
        
        # 执行函数（带重试）
        try:
            retry_handler = self.retry_handlers.get(service_name)
            if retry_handler:
                result = await retry_handler.execute_with_retry(
                    func, *args, error_context=error_context, **kwargs
                )
                self.error_stats[service_name]["successful_retries"] += 1
            else:
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
            
            # 记录成功
            if circuit_breaker:
                circuit_breaker.record_success()
            
            # 缓存成功结果
            fallback_handler = self.fallback_handlers.get(service_name)
            if fallback_handler:
                fallback_handler.cache_result(service_name, method_name, args, kwargs, result)
            
            return result
            
        except Exception as e:
            # 记录失败
            if circuit_breaker:
                circuit_breaker.record_failure()
            
            self.error_stats[service_name]["failed_retries"] += 1
            
            # 更新错误上下文
            error_context.error_type = type(e).__name__
            error_context.error_message = str(e)
            error_context.severity = self._classify_error_severity(e)
            error_context.stack_trace = traceback.format_exc()
            
            await self._handle_error(error_context, args, kwargs)
            
            # 尝试降级
            return await self._execute_fallback(error_context, args, kwargs)
    
    async def _execute_fallback(self, error_context: ErrorContext, args: tuple, kwargs: dict) -> Any:
        """执行降级策略"""
        fallback_handler = self.fallback_handlers.get(error_context.service_name)
        if fallback_handler:
            self.error_stats[error_context.service_name]["fallback_used"] += 1
            try:
                return await fallback_handler.handle_fallback(error_context, args, kwargs)
            except Exception as e:
                logger.error(f"降级策略执行失败: {e}")
        
        # 最终降级：抛出原始异常
        raise Exception(f"服务 {error_context.service_name}.{error_context.method_name} 不可用: {error_context.error_message}")
    
    async def _handle_error(self, error_context: ErrorContext, args: tuple, kwargs: dict):
        """处理错误"""
        # 更新统计
        stats = self.error_stats.get(error_context.service_name, {})
        stats["total_errors"] = stats.get("total_errors", 0) + 1
        
        severity_key = f"{error_context.severity.value}_errors"
        stats[severity_key] = stats.get(severity_key, 0) + 1
        
        # 记录错误到历史
        self.error_history.append(error_context)
        if len(self.error_history) > self.max_history_size:
            self.error_history.pop(0)
        
        # 根据严重程度决定日志级别
        if error_context.severity == ErrorSeverity.CRITICAL:
            logger.critical(f"致命错误 - {error_context.service_name}.{error_context.method_name}: {error_context.error_message}")
        elif error_context.severity == ErrorSeverity.HIGH:
            logger.error(f"严重错误 - {error_context.service_name}.{error_context.method_name}: {error_context.error_message}")
        elif error_context.severity == ErrorSeverity.MEDIUM:
            logger.warning(f"警告错误 - {error_context.service_name}.{error_context.method_name}: {error_context.error_message}")
        else:
            logger.info(f"轻微错误 - {error_context.service_name}.{error_context.method_name}: {error_context.error_message}")
    
    def _classify_error_severity(self, exception: Exception) -> ErrorSeverity:
        """分类错误严重程度"""
        # 根据异常类型分类严重程度
        critical_errors = [
            "DatabaseError",
            "OutOfMemoryError", 
            "SystemExit",
            "KeyboardInterrupt"
        ]
        
        high_errors = [
            "ConnectionError",
            "TimeoutError", 
            "AuthenticationError",
            "PermissionError"
        ]
        
        medium_errors = [
            "ValidationError",
            "ConfigurationError",
            "FileNotFoundError",
            "ValueError"
        ]
        
        error_name = type(exception).__name__
        
        if error_name in critical_errors:
            return ErrorSeverity.CRITICAL
        elif error_name in high_errors:
            return ErrorSeverity.HIGH
        elif error_name in medium_errors:
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    def get_service_health_report(self, service_name: str) -> Dict[str, Any]:
        """获取服务健康报告"""
        stats = self.error_stats.get(service_name, {})
        circuit_breaker = self.circuit_breakers.get(service_name)
        
        report = {
            "service_name": service_name,
            "error_statistics": stats,
            "circuit_breaker_info": circuit_breaker.get_state_info() if circuit_breaker else None,
            "recent_errors": [
                {
                    "timestamp": ctx.timestamp.isoformat(),
                    "method": ctx.method_name,
                    "error_type": ctx.error_type,
                    "severity": ctx.severity.value,
                    "message": ctx.error_message
                }
                for ctx in self.error_history[-10:]
                if ctx.service_name == service_name
            ]
        }
        
        return report
    
    def get_global_health_report(self) -> Dict[str, Any]:
        """获取全局健康报告"""
        total_errors = sum(stats.get("total_errors", 0) for stats in self.error_stats.values())
        total_critical = sum(stats.get("critical_errors", 0) for stats in self.error_stats.values())
        total_high = sum(stats.get("high_errors", 0) for stats in self.error_stats.values())
        
        # 计算服务健康度
        service_health = {}
        for service_name, stats in self.error_stats.items():
            total_service_errors = stats.get("total_errors", 0)
            if total_service_errors == 0:
                health_score = 1.0
            else:
                critical_weight = stats.get("critical_errors", 0) * 1.0
                high_weight = stats.get("high_errors", 0) * 0.7
                medium_weight = stats.get("medium_errors", 0) * 0.4
                low_weight = stats.get("low_errors", 0) * 0.1
                
                weighted_errors = critical_weight + high_weight + medium_weight + low_weight
                health_score = max(0.0, 1.0 - (weighted_errors / max(100, total_service_errors)))
            
            service_health[service_name] = {
                "health_score": health_score,
                "total_errors": total_service_errors,
                "circuit_breaker_state": self.circuit_breakers.get(service_name).state.value if self.circuit_breakers.get(service_name) else "none"
            }
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "global_statistics": {
                "total_errors": total_errors,
                "critical_errors": total_critical,
                "high_errors": total_high,
                "services_monitored": len(self.error_stats)
            },
            "service_health": service_health,
            "active_circuit_breakers": [
                service_name for service_name, cb in self.circuit_breakers.items()
                if cb.state != CircuitBreakerState.CLOSED
            ]
        }


# 全局异常处理器实例
global_exception_handler = UnifiedExceptionHandler()


def resilient_service(
    service_name: str,
    circuit_breaker_config: Optional[CircuitBreakerConfig] = None,
    retry_config: Optional[RetryConfig] = None,
    fallback_config: Optional[FallbackConfig] = None
):
    """服务弹性装饰器"""
    def decorator(cls_or_func):
        # 注册服务配置
        global_exception_handler.register_service(
            service_name,
            circuit_breaker_config,
            retry_config,
            fallback_config
        )
        
        if isinstance(cls_or_func, type):
            # 装饰类
            original_init = cls_or_func.__init__
            
            def wrapped_init(self, *args, **kwargs):
                original_init(self, *args, **kwargs)
                self._service_name = service_name
                self._exception_handler = global_exception_handler
            
            cls_or_func.__init__ = wrapped_init
            return cls_or_func
        else:
            # 装饰函数
            @wraps(cls_or_func)
            async def wrapper(*args, **kwargs):
                return await global_exception_handler.execute_with_protection(
                    service_name,
                    cls_or_func.__name__,
                    cls_or_func,
                    *args,
                    **kwargs
                )
            return wrapper
    
    return decorator


async def get_global_exception_handler() -> UnifiedExceptionHandler:
    """获取全局异常处理器实例"""
    return global_exception_handler