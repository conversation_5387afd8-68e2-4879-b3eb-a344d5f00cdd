"""
Configuration management for keyword generation service
"""
from typing import Dict, List, Optional, Union
from pydantic import BaseModel, Field, validator
from app.core.config import settings


class KeywordConfig(BaseModel):
    """Configuration for keyword generation service"""
    
    # Performance thresholds
    MAX_QUERY_LENGTH: int = Field(default=1000, description="Maximum query length in characters")
    MAX_KEYWORDS_PER_QUERY: int = Field(default=50, description="Maximum keywords to generate per query")
    MAX_PROCESSING_TIME: float = Field(default=30.0, description="Maximum processing time in seconds")
    
    # Cache configuration
    ENABLE_CACHING: bool = Field(default=True, description="Enable Redis caching")
    CACHE_TTL_TERMINOLOGY: int = Field(default=86400, description="Terminology mapping cache TTL (seconds)")
    CACHE_TTL_KEYWORDS: int = Field(default=3600, description="Keyword results cache TTL (seconds)")
    CACHE_TTL_DOMAIN_CLASSIFICATION: int = Field(default=7200, description="Domain classification cache TTL (seconds)")
    CACHE_PREFIX: str = Field(default="keyword_gen:", description="Cache key prefix")
    
    # Rate limiting
    ENABLE_RATE_LIMITING: bool = Field(default=True, description="Enable rate limiting")
    RATE_LIMIT_REQUESTS: int = Field(default=100, description="Max requests per window")
    RATE_LIMIT_WINDOW: int = Field(default=3600, description="Rate limit window in seconds")
    
    # AI service configuration
    AI_TIMEOUT: float = Field(default=15.0, description="AI service timeout in seconds")
    AI_MAX_RETRIES: int = Field(default=3, description="Maximum AI service retry attempts")
    AI_RETRY_DELAY: float = Field(default=1.0, description="Delay between retries in seconds")
    ENABLE_AI_FALLBACK: bool = Field(default=True, description="Enable fallback when AI service fails")
    
    # Quality thresholds
    MIN_CONFIDENCE_SCORE: float = Field(default=0.3, description="Minimum confidence score for results")
    MIN_KEYWORDS_COUNT: int = Field(default=1, description="Minimum number of keywords to return")
    MAX_DOMAIN_COVERAGE: int = Field(default=6, description="Maximum number of domains to classify")
    
    # Input validation
    ALLOWED_LANGUAGES: List[str] = Field(default=["zh", "en"], description="Allowed input languages")
    MIN_QUERY_LENGTH: int = Field(default=2, description="Minimum query length in characters")
    FORBIDDEN_PATTERNS: List[str] = Field(
        default=["<script", "javascript:", "data:", "vbscript:"],
        description="Forbidden patterns in input"
    )
    
    # Terminology mapping
    ENABLE_FUZZY_MATCHING: bool = Field(default=True, description="Enable fuzzy matching for terms")
    FUZZY_MATCH_THRESHOLD: float = Field(default=0.8, description="Fuzzy matching threshold")
    ENABLE_PARTIAL_MATCHING: bool = Field(default=True, description="Enable partial term matching")
    
    # Search strategy optimization
    PRIMARY_SEARCH_TERMS_COUNT: int = Field(default=5, description="Number of primary search terms")
    SECONDARY_SEARCH_TERMS_COUNT: int = Field(default=5, description="Number of secondary search terms")
    MAX_COMBINED_QUERIES: int = Field(default=3, description="Maximum combined queries to generate")
    MAX_BOOLEAN_QUERIES: int = Field(default=3, description="Maximum boolean queries to generate")
    
    # Monitoring and metrics
    ENABLE_METRICS: bool = Field(default=True, description="Enable performance metrics collection")
    METRICS_RETENTION_DAYS: int = Field(default=30, description="Metrics retention period in days")
    LOG_PERFORMANCE_THRESHOLD: float = Field(default=5.0, description="Log operations slower than this threshold")
    
    @validator('MAX_QUERY_LENGTH')
    def validate_max_query_length(cls, v):
        if v <= 0 or v > 10000:
            raise ValueError("MAX_QUERY_LENGTH must be between 1 and 10000")
        return v
    
    @validator('MIN_CONFIDENCE_SCORE')
    def validate_confidence_score(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("MIN_CONFIDENCE_SCORE must be between 0.0 and 1.0")
        return v
    
    @validator('FUZZY_MATCH_THRESHOLD')
    def validate_fuzzy_threshold(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("FUZZY_MATCH_THRESHOLD must be between 0.0 and 1.0")
        return v
    
    class Config:
        env_prefix = "KEYWORD_"
        case_sensitive = True


class TerminologyConfig(BaseModel):
    """Configuration for terminology mappings"""
    
    # Technical weight multipliers for scoring
    TECH_PLATFORM_WEIGHT: float = Field(default=2.0, description="Weight for technical platform terms")
    CELL_TYPE_WEIGHT: float = Field(default=1.5, description="Weight for cell type terms")
    BIOLOGICAL_PROCESS_WEIGHT: float = Field(default=1.0, description="Weight for biological process terms")
    DISEASE_WEIGHT: float = Field(default=1.2, description="Weight for disease-related terms")
    
    # Domain importance scores
    DOMAIN_WEIGHTS: Dict[str, float] = Field(
        default={
            "immunology": 1.0,
            "oncology": 1.0,
            "neuroscience": 1.0,
            "developmental_biology": 0.9,
            "metabolism": 0.9,
            "stem_cell": 0.8
        },
        description="Weight multipliers for different domains"
    )
    
    # Technology platform priorities
    PLATFORM_PRIORITIES: Dict[str, int] = Field(
        default={
            "10x Genomics": 1,
            "Smart-seq": 2,
            "Drop-seq": 3,
            "MARS-seq": 4,
            "CEL-seq": 5,
            "Fluidigm": 6
        },
        description="Priority ranking for technology platforms"
    )


class CacheConfig(BaseModel):
    """Cache-specific configuration"""
    
    REDIS_HOST: str = Field(default="localhost", description="Redis host")
    REDIS_PORT: int = Field(default=6379, description="Redis port")
    REDIS_DB: int = Field(default=1, description="Redis database number for keyword service")
    REDIS_PASSWORD: Optional[str] = Field(default=None, description="Redis password")
    
    CONNECTION_POOL_SIZE: int = Field(default=10, description="Redis connection pool size")
    CONNECTION_TIMEOUT: float = Field(default=5.0, description="Redis connection timeout")
    SOCKET_TIMEOUT: float = Field(default=5.0, description="Redis socket timeout")
    
    ENABLE_COMPRESSION: bool = Field(default=True, description="Enable cache data compression")
    COMPRESSION_LEVEL: int = Field(default=6, description="Compression level (1-9)")
    
    # Cache key patterns
    KEY_PATTERNS: Dict[str, str] = Field(
        default={
            "terminology": "keyword_gen:terminology:{hash}",
            "query_result": "keyword_gen:query:{query_hash}",
            "domain_classification": "keyword_gen:domain:{keywords_hash}",
            "search_strategies": "keyword_gen:strategies:{keywords_hash}",
            "rate_limit": "keyword_gen:ratelimit:{user_id}:{window}"
        },
        description="Cache key patterns"
    )


class MetricsConfig(BaseModel):
    """Metrics and monitoring configuration"""
    
    # Performance metrics
    TRACK_RESPONSE_TIMES: bool = Field(default=True, description="Track API response times")
    TRACK_CACHE_HIT_RATES: bool = Field(default=True, description="Track cache hit rates")
    TRACK_ERROR_RATES: bool = Field(default=True, description="Track error rates")
    TRACK_KEYWORD_QUALITY: bool = Field(default=True, description="Track keyword generation quality")
    
    # Alerting thresholds
    ERROR_RATE_THRESHOLD: float = Field(default=0.05, description="Error rate alert threshold")
    RESPONSE_TIME_THRESHOLD: float = Field(default=10.0, description="Response time alert threshold (seconds)")
    CACHE_HIT_RATE_THRESHOLD: float = Field(default=0.7, description="Minimum cache hit rate threshold")
    
    # Metrics aggregation
    METRICS_BATCH_SIZE: int = Field(default=100, description="Metrics batch size")
    METRICS_FLUSH_INTERVAL: int = Field(default=60, description="Metrics flush interval (seconds)")


# Global configuration instances
keyword_config = KeywordConfig()
terminology_config = TerminologyConfig()
cache_config = CacheConfig()
metrics_config = MetricsConfig()


def get_keyword_config() -> KeywordConfig:
    """Get keyword service configuration"""
    return keyword_config


def get_terminology_config() -> TerminologyConfig:
    """Get terminology configuration"""
    return terminology_config


def get_cache_config() -> CacheConfig:
    """Get cache configuration"""
    return cache_config


def get_metrics_config() -> MetricsConfig:
    """Get metrics configuration"""
    return metrics_config


def validate_configuration() -> Dict[str, bool]:
    """Validate all configurations and return status"""
    validation_results = {}
    
    try:
        # Validate keyword config
        KeywordConfig()
        validation_results["keyword_config"] = True
    except Exception as e:
        validation_results["keyword_config"] = False
        validation_results["keyword_config_error"] = str(e)
    
    try:
        # Validate terminology config
        TerminologyConfig()
        validation_results["terminology_config"] = True
    except Exception as e:
        validation_results["terminology_config"] = False
        validation_results["terminology_config_error"] = str(e)
    
    try:
        # Validate cache config
        CacheConfig()
        validation_results["cache_config"] = True
    except Exception as e:
        validation_results["cache_config"] = False
        validation_results["cache_config_error"] = str(e)
    
    try:
        # Validate metrics config
        MetricsConfig()
        validation_results["metrics_config"] = True
    except Exception as e:
        validation_results["metrics_config"] = False
        validation_results["metrics_config_error"] = str(e)
    
    return validation_results