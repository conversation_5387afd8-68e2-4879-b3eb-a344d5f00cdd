"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Circle, Lightbulb, Zap } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "sonner"

interface RequirementData {
  // 基础信息
  researchGoal: string
  sampleType: string
  cellCount: string
  budget: string
  timeline: string

  // 技术细节
  sequencingDepth: string
  analysisType: string
  experimentType: string
  dataAnalysisNeeds: string
  specialRequirements: string

  // 自动推断信息
  recommendedPlatform: string
  estimatedCost: string
  riskFactors: string[]

  // 完成度
  completeness: number
  collectedFields: string[]
}

interface SmartSuggestion {
  field: string
  value: string
  reason: string
  confidence: number
}

export function SmartRequirementCollector({
  onRequirementsChange,
  isCompact = false
}: {
  onRequirementsChange: (requirements: RequirementData) => void
  isCompact?: boolean
}) {
  const { user } = useAuth()
  const [requirements, setRequirements] = useState<RequirementData>({
    researchGoal: "",
    sampleType: "",
    cellCount: "",
    budget: "",
    timeline: "",
    sequencingDepth: "",
    analysisType: "",
    experimentType: "",
    dataAnalysisNeeds: "",
    specialRequirements: "",
    recommendedPlatform: "",
    estimatedCost: "",
    riskFactors: [],
    completeness: 0,
    collectedFields: []
  })

  // 分步骤状态管理
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])

  // 定义步骤
  const steps = [
    {
      id: 1,
      title: "基础信息",
      icon: "📋",
      description: "研究目标和基本需求",
      fields: ["researchGoal", "sampleType", "cellCount"]
    },
    {
      id: 2,
      title: "项目规划",
      icon: "📅",
      description: "预算和时间安排",
      fields: ["budget", "timeline"]
    },
    {
      id: 3,
      title: "技术细节",
      icon: "🔬",
      description: "实验和分析要求",
      fields: ["experimentType", "analysisType", "dataAnalysisNeeds"]
    },
    {
      id: 4,
      title: "高级选项",
      icon: "⚙️",
      description: "测序深度和特殊要求",
      fields: ["sequencingDepth", "specialRequirements"]
    }
  ]

  const [smartSuggestions, setSmartSuggestions] = useState<SmartSuggestion[]>([])

  // 检查步骤是否完成
  const isStepCompleted = (stepId: number) => {
    const step = steps.find(s => s.id === stepId)
    if (!step) return false

    return step.fields.every(field => requirements[field as keyof RequirementData])
  }

  // 检查步骤是否可以访问
  const canAccessStep = (stepId: number) => {
    if (stepId === 1) return true

    // 前一个步骤必须完成才能访问下一个步骤
    for (let i = 1; i < stepId; i++) {
      if (!isStepCompleted(i)) return false
    }
    return true
  }

  // 自动推进到下一步
  const checkAndAdvanceStep = () => {
    if (isStepCompleted(currentStep) && !completedSteps.includes(currentStep)) {
      setCompletedSteps(prev => [...prev, currentStep])

      // 如果不是最后一步，自动展开下一步
      if (currentStep < steps.length) {
        setTimeout(() => {
          setCurrentStep(currentStep + 1)
        }, 500) // 延迟展开，给用户反馈时间
      }
    }
  }

  // 预定义选项
  const researchGoals = [
    "细胞类型鉴定",
    "发育轨迹分析",
    "疾病机制研究",
    "药物筛选",
    "肿瘤微环境分析",
    "免疫细胞分析",
    "神经发育研究",
    "其他"
  ]

  const sampleTypes = [
    "PBMC (外周血单核细胞)",
    "肿瘤组织",
    "脑组织",
    "肝脏组织",
    "肺组织",
    "肾脏组织",
    "培养细胞",
    "其他组织"
  ]

  const budgetRanges = [
    "< 5万元",
    "5-10万元",
    "10-20万元",
    "20-50万元",
    "> 50万元"
  ]

  const timelineOptions = [
    "1个月内",
    "2-3个月",
    "3-6个月",
    "6个月以上"
  ]

  const experimentTypes = [
    "单细胞RNA测序 (scRNA-seq)",
    "单细胞ATAC测序 (scATAC-seq)",
    "单细胞多组学 (Multiome)",
    "空间转录组学",
    "单细胞蛋白质组学",
    "单细胞表观遗传学",
    "其他"
  ]

  const analysisTypes = [
    "细胞类型注释",
    "差异表达分析",
    "发育轨迹分析",
    "细胞通讯分析",
    "功能富集分析",
    "拷贝数变异分析",
    "空间分析",
    "其他"
  ]

  const dataAnalysisOptions = [
    "基础分析报告",
    "高级生物信息学分析",
    "个性化分析方案",
    "仅提供原始数据",
    "需要后续技术支持"
  ]

  // 智能推理函数
  const generateSmartSuggestions = (currentReqs: RequirementData) => {
    const suggestions: SmartSuggestion[] = []

    // 基于研究目标推荐平台
    if (currentReqs.researchGoal && !currentReqs.recommendedPlatform) {
      if (currentReqs.researchGoal.includes("发育轨迹") || currentReqs.researchGoal.includes("时序")) {
        suggestions.push({
          field: "recommendedPlatform",
          value: "10x Genomics (高时间分辨率)",
          reason: "发育轨迹分析需要高质量的时序数据",
          confidence: 0.9
        })
      } else if (currentReqs.researchGoal.includes("肿瘤")) {
        suggestions.push({
          field: "recommendedPlatform",
          value: "10x Genomics + Spatial Transcriptomics",
          reason: "肿瘤研究需要空间信息和细胞异质性分析",
          confidence: 0.85
        })
      }
    }

    // 基于样本类型推荐细胞数量
    if (currentReqs.sampleType && !currentReqs.cellCount) {
      if (currentReqs.sampleType.includes("PBMC")) {
        suggestions.push({
          field: "cellCount",
          value: "5,000-10,000",
          reason: "PBMC样本通常需要中等通量以捕获免疫细胞多样性",
          confidence: 0.8
        })
      } else if (currentReqs.sampleType.includes("肿瘤")) {
        suggestions.push({
          field: "cellCount",
          value: "10,000-20,000",
          reason: "肿瘤组织异质性高，需要更多细胞数量",
          confidence: 0.85
        })
      }
    }

    // 基于预算推荐方案
    if (currentReqs.budget && !currentReqs.estimatedCost) {
      const budgetNum = currentReqs.budget.includes("5万") ? 5 :
                       currentReqs.budget.includes("10万") ? 10 :
                       currentReqs.budget.includes("20万") ? 20 : 50

      if (budgetNum <= 10) {
        suggestions.push({
          field: "recommendedPlatform",
          value: "Smart-seq3 (成本优化)",
          reason: "预算有限，推荐性价比较高的方案",
          confidence: 0.75
        })
      }
    }

    setSmartSuggestions(suggestions)
  }

  // 计算完成度
  const calculateCompleteness = (reqs: RequirementData) => {
    const requiredFields = ["researchGoal", "sampleType", "cellCount", "budget", "timeline"]
    const optionalFields = ["sequencingDepth", "analysisType", "experimentType", "dataAnalysisNeeds", "specialRequirements"]

    const completedRequired = requiredFields.filter(field => reqs[field as keyof RequirementData]).length
    const completedOptional = optionalFields.filter(field => reqs[field as keyof RequirementData]).length

    const completeness = (completedRequired * 16 + completedOptional * 4) // 必需字段16分，可选4分

    return Math.min(completeness, 100)
  }

  // 检查是否可以提交
  const canSubmit = () => {
    const requiredFields = ["researchGoal", "sampleType", "cellCount", "budget", "timeline"]
    return requiredFields.every(field => requirements[field as keyof RequirementData])
  }

  // 处理提交需求
  const handleSubmitRequirements = () => {
    if (!canSubmit()) {
      toast.error("请先填写所有必需的信息")
      return
    }

    // 触发需求提交处理
    onRequirementsChange({
      ...requirements,
      completeness: 100 // 提交时设为100%
    })

    toast.success("需求信息已提交，正在生成专业建议...")
  }

  // 更新需求数据
  const updateRequirement = (field: string, value: string) => {
    const newReqs = { ...requirements, [field]: value }
    const collectedFields = [...new Set([...newReqs.collectedFields, field])]
    newReqs.collectedFields = collectedFields
    newReqs.completeness = calculateCompleteness(newReqs)

    setRequirements(newReqs)
    onRequirementsChange(newReqs)

    // 生成智能建议
    generateSmartSuggestions(newReqs)

    // 检查是否可以推进到下一步
    setTimeout(checkAndAdvanceStep, 100)
  }

  // 应用智能建议
  const applySuggestion = (suggestion: SmartSuggestion) => {
    updateRequirement(suggestion.field, suggestion.value)
    setSmartSuggestions(prev => prev.filter(s => s.field !== suggestion.field))
  }

  // 步骤配置
  const steps = [
    {
      title: "研究目标",
      description: "告诉我们您的研究目标",
      fields: ["researchGoal"]
    },
    {
      title: "样本信息",
      description: "样本类型和数量",
      fields: ["sampleType", "cellCount"]
    },
    {
      title: "预算时间",
      description: "预算范围和时间要求",
      fields: ["budget", "timeline"]
    },
    {
      title: "技术细节",
      description: "可选的技术要求",
      fields: ["sequencingDepth", "analysisType", "specialRequirements"]
    }
  ]

  return (
    <div className={`space-y-4 ${isCompact ? 'text-sm' : ''}`}>
      {/* 进度指示器 */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader className={isCompact ? "p-4" : "p-6"}>
          <CardTitle className={`flex items-center gap-2 ${isCompact ? 'text-base' : 'text-lg'}`}>
            <Zap className={`${isCompact ? 'h-4 w-4' : 'h-5 w-5'} text-blue-600`} />
            需求收集助手
          </CardTitle>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs text-slate-600">
              <span>完成度</span>
              <span className="font-medium text-blue-700">{requirements.completeness}%</span>
            </div>
            <Progress value={requirements.completeness} className="h-2 bg-blue-100" />
            <div className="text-xs text-slate-500">
              {requirements.collectedFields.length}/10 项信息已收集
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 智能建议 */}
      {smartSuggestions.length > 0 && (
        <Card className="border-amber-200 bg-gradient-to-r from-amber-50 to-yellow-50">
          <CardHeader className={isCompact ? "p-3" : "p-4"}>
            <CardTitle className={`flex items-center gap-2 text-amber-800 ${isCompact ? 'text-sm' : 'text-base'}`}>
              <Lightbulb className={`${isCompact ? 'h-4 w-4' : 'h-5 w-5'}`} />
              💡 AI智能建议
            </CardTitle>
          </CardHeader>
          <CardContent className={`space-y-2 ${isCompact ? 'p-3 pt-0' : 'pt-0'}`}>
            {smartSuggestions.slice(0, isCompact ? 2 : 3).map((suggestion, index) => (
              <div key={index} className="p-3 bg-white rounded-lg border border-amber-100 shadow-sm">
                <div className="space-y-2">
                  <div className={`font-medium ${isCompact ? 'text-xs' : 'text-sm'} text-slate-800`}>
                    {suggestion.value}
                  </div>
                  <div className={`${isCompact ? 'text-xs' : 'text-xs'} text-slate-600 leading-relaxed`}>
                    {suggestion.reason}
                  </div>
                  <div className="flex items-center justify-between">
                    <Badge variant="secondary" className={`${isCompact ? 'text-xs px-2 py-0.5' : ''}`}>
                      置信度 {Math.round(suggestion.confidence * 100)}%
                    </Badge>
                    <Button
                      size={isCompact ? "sm" : "sm"}
                      onClick={() => applySuggestion(suggestion)}
                      className="bg-amber-600 hover:bg-amber-700 text-white"
                    >
                      采用
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* 分步表单 */}
      <div className="space-y-3">
        {steps.map((step, index) => {
          const isCompleted = isStepCompleted(step.id)
          const isActive = currentStep === step.id
          const canAccess = canAccessStep(step.id)

          return (
            <Card
              key={step.id}
              className={`border transition-all duration-300 ${
                isCompleted
                  ? 'border-green-200 bg-green-50'
                  : isActive
                  ? 'border-blue-200 bg-blue-50'
                  : canAccess
                  ? 'border-slate-200 bg-white hover:border-slate-300'
                  : 'border-slate-100 bg-slate-50'
              }`}
            >
              <CardHeader
                className={`${isCompact ? 'p-3' : 'p-4'} cursor-pointer`}
                onClick={() => canAccess && setCurrentStep(step.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      isCompleted
                        ? 'bg-green-500 text-white'
                        : isActive
                        ? 'bg-blue-500 text-white'
                        : canAccess
                        ? 'bg-slate-200 text-slate-600'
                        : 'bg-slate-100 text-slate-400'
                    }`}>
                      {isCompleted ? '✓' : step.id}
                    </div>
                    <div>
                      <h3 className={`${isCompact ? 'text-sm' : 'text-base'} font-medium ${
                        canAccess ? 'text-slate-900' : 'text-slate-400'
                      }`}>
                        {step.icon} {step.title}
                      </h3>
                      <p className={`${isCompact ? 'text-xs' : 'text-sm'} ${
                        canAccess ? 'text-slate-600' : 'text-slate-400'
                      }`}>
                        {step.description}
                      </p>
                    </div>
                  </div>
                  {canAccess && (
                    <div className={`text-xs px-2 py-1 rounded ${
                      isCompleted
                        ? 'bg-green-100 text-green-700'
                        : isActive
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-slate-100 text-slate-600'
                    }`}>
                      {isCompleted ? '已完成' : isActive ? '进行中' : '待填写'}
                    </div>
                  )}
                </div>
              </CardHeader>

              {/* 只有当前步骤或已完成步骤才展开内容 */}
              {(isActive || isCompleted) && canAccess && (
                <CardContent className={`${isCompact ? 'p-3 pt-0' : 'p-4 pt-0'} space-y-3`}>
                  {/* 步骤1: 基础信息 */}
                  {step.id === 1 && (
                    <>
                      <div>
                        <Label htmlFor="researchGoal" className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                          研究目标 <span className="text-red-500">*</span>
                        </Label>
              <Select value={requirements.researchGoal} onValueChange={(value) => updateRequirement("researchGoal", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择您的研究目标" />
                </SelectTrigger>
                <SelectContent>
                  {researchGoals.map(goal => (
                    <SelectItem key={goal} value={goal} className={isCompact ? 'text-xs' : ''}>{goal}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="sampleType" className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                样本类型 <span className="text-red-500">*</span>
              </Label>
              <Select value={requirements.sampleType} onValueChange={(value) => updateRequirement("sampleType", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择样本类型" />
                </SelectTrigger>
                <SelectContent>
                  {sampleTypes.map(type => (
                    <SelectItem key={type} value={type} className={isCompact ? 'text-xs' : ''}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="cellCount" className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                预期细胞数量 <span className="text-red-500">*</span>
              </Label>
              <Input
                id="cellCount"
                value={requirements.cellCount}
                onChange={(e) => updateRequirement("cellCount", e.target.value)}
                placeholder="例如: 5,000-10,000"
                className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}
              />
            </div>
          </CardContent>
        </Card>

        {/* 预算时间 */}
        <Card className="border-slate-200">
          <CardHeader className={isCompact ? "p-4" : "p-6"}>
            <CardTitle className={`${isCompact ? 'text-sm' : 'text-base'} text-slate-800`}>
              💰 预算与时间
            </CardTitle>
          </CardHeader>
          <CardContent className={`space-y-3 ${isCompact ? 'p-4 pt-0' : 'pt-0'}`}>
            <div>
              <Label htmlFor="budget" className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                预算范围 <span className="text-red-500">*</span>
              </Label>
              <Select value={requirements.budget} onValueChange={(value) => updateRequirement("budget", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择预算范围" />
                </SelectTrigger>
                <SelectContent>
                  {budgetRanges.map(range => (
                    <SelectItem key={range} value={range} className={isCompact ? 'text-xs' : ''}>{range}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="timeline" className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                项目周期 <span className="text-red-500">*</span>
              </Label>
              <Select value={requirements.timeline} onValueChange={(value) => updateRequirement("timeline", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择项目周期" />
                </SelectTrigger>
                <SelectContent>
                  {timelineOptions.map(option => (
                    <SelectItem key={option} value={option} className={isCompact ? 'text-xs' : ''}>{option}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="specialRequirements" className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                特殊要求
              </Label>
              <Textarea
                id="specialRequirements"
                value={requirements.specialRequirements}
                onChange={(e) => updateRequirement("specialRequirements", e.target.value)}
                placeholder="任何特殊的技术要求或限制..."
                rows={isCompact ? 2 : 3}
                className={`mt-1 ${isCompact ? 'text-xs' : ''}`}
              />
            </div>
          </CardContent>
        </Card>

        {/* 技术细节 */}
        <Card className="border-slate-200">
          <CardHeader className={isCompact ? "p-4" : "p-6"}>
            <CardTitle className={`${isCompact ? 'text-sm' : 'text-base'} text-slate-800`}>
              🔬 技术细节
            </CardTitle>
          </CardHeader>
          <CardContent className={`space-y-3 ${isCompact ? 'p-4 pt-0' : 'pt-0'}`}>
            <div>
              <Label htmlFor="experimentType" className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                实验类型
              </Label>
              <Select value={requirements.experimentType} onValueChange={(value) => updateRequirement("experimentType", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择实验类型" />
                </SelectTrigger>
                <SelectContent>
                  {experimentTypes.map(type => (
                    <SelectItem key={type} value={type} className={isCompact ? 'text-xs' : ''}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="analysisType" className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                分析类型
              </Label>
              <Select value={requirements.analysisType} onValueChange={(value) => updateRequirement("analysisType", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择分析类型" />
                </SelectTrigger>
                <SelectContent>
                  {analysisTypes.map(type => (
                    <SelectItem key={type} value={type} className={isCompact ? 'text-xs' : ''}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="dataAnalysisNeeds" className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-slate-700`}>
                数据分析需求
              </Label>
              <Select value={requirements.dataAnalysisNeeds} onValueChange={(value) => updateRequirement("dataAnalysisNeeds", value)}>
                <SelectTrigger className={`mt-1 ${isCompact ? 'h-8 text-xs' : 'h-10'}`}>
                  <SelectValue placeholder="选择数据分析需求" />
                </SelectTrigger>
                <SelectContent>
                  {dataAnalysisOptions.map(option => (
                    <SelectItem key={option} value={option} className={isCompact ? 'text-xs' : ''}>{option}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 收集状态总览 */}
      {!isCompact && (
        <Card className="border-green-200 bg-gradient-to-r from-green-50 to-emerald-50">
          <CardHeader className="p-4">
            <CardTitle className="text-sm text-green-800">
              ✅ 信息收集状态
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="grid grid-cols-2 gap-3">
              {[
                { field: "researchGoal", label: "研究目标", required: true },
                { field: "sampleType", label: "样本类型", required: true },
                { field: "cellCount", label: "细胞数量", required: true },
                { field: "budget", label: "预算范围", required: true },
                { field: "timeline", label: "项目周期", required: true },
                { field: "experimentType", label: "实验类型", required: false },
                { field: "analysisType", label: "分析类型", required: false },
                { field: "dataAnalysisNeeds", label: "数据分析需求", required: false },
                { field: "sequencingDepth", label: "测序深度", required: false },
                { field: "specialRequirements", label: "特殊要求", required: false }
              ].map(item => (
                <div key={item.field} className="flex items-center gap-2">
                  {requirements[item.field as keyof RequirementData] ? (
                    <CheckCircle className="h-3 w-3 text-green-600 flex-shrink-0" />
                  ) : (
                    <Circle className="h-3 w-3 text-slate-400 flex-shrink-0" />
                  )}
                  <span className={`text-xs ${item.required ? 'font-medium text-slate-800' : 'text-slate-600'}`}>
                    {item.label}
                    {item.required && <span className="text-red-500 ml-1">*</span>}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 紧凑模式的状态指示器 */}
      {isCompact && requirements.completeness >= 80 && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-3">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle className="h-4 w-4" />
              <span className="text-xs font-medium">需求信息收集完成，可生成方案</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 提交需求按钮 */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardContent className={`${isCompact ? 'p-3' : 'p-4'}`}>
          <div className="space-y-3">
            <div className="text-center">
              <div className={`${isCompact ? 'text-xs' : 'text-sm'} text-slate-600 mb-2`}>
                {canSubmit()
                  ? "✅ 必需信息已完整，可以提交需求"
                  : "⚠️ 请先填写所有必需信息（标有 * 的字段）"
                }
              </div>
              <Button
                onClick={handleSubmitRequirements}
                disabled={!canSubmit()}
                className={`w-full ${isCompact ? 'h-8 text-xs' : 'h-10'} bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-slate-400 disabled:to-slate-500`}
              >
                {canSubmit() ? "🚀 提交需求，生成方案" : "📝 完善信息后提交"}
              </Button>
            </div>

            {!canSubmit() && (
              <div className={`${isCompact ? 'text-xs' : 'text-sm'} text-amber-700 bg-amber-100 rounded-lg p-2 border border-amber-200`}>
                <div className="font-medium mb-1">还需要填写：</div>
                <div className="space-y-1">
                  {["researchGoal", "sampleType", "cellCount", "budget", "timeline"].map(field => {
                    const labels = {
                      researchGoal: "研究目标",
                      sampleType: "样本类型",
                      cellCount: "细胞数量",
                      budget: "预算范围",
                      timeline: "项目周期"
                    }
                    return !requirements[field as keyof RequirementData] ? (
                      <div key={field} className="flex items-center gap-1">
                        <Circle className="h-3 w-3" />
                        <span>{labels[field as keyof typeof labels]}</span>
                      </div>
                    ) : null
                  })}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
