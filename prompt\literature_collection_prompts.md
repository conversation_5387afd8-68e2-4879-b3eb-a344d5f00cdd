CellForge 智能文献搜集筛选系统
系统目标
基于客户已填写的研究需求信息，通过多源API搜集文献，使用AI智能判断文献与需求的匹配度，自动筛选出支撑文献。
核心流程
客户需求信息 → 生成搜索策略 → 多源API搜索 → AI相关性判断 → 筛选支撑文献 → 返回结果
技术架构
1. 需求分析模块

解析客户需求信息
生成多维度搜索关键词
确定文献筛选标准

2. 多源搜索模块

PubMed API搜索
Semantic Scholar API搜索
bioRxiv API搜索
搜索结果聚合去重

3. AI筛选模块

基于摘要的相关性判断
文献质量评估
匹配度评分
自动分类标记

4. 结果管理模块

支撑文献库管理
搜索历史记录
文献标注和备注

核心API设计
主要端点
POST /api/v1/literature/collect-for-requirements  # 基于需求搜集文献
GET  /api/v1/literature/supporting-papers         # 获取支撑文献
POST /api/v1/literature/evaluate-relevance        # 评估文献相关性
PUT  /api/v1/literature/update-support-status     # 更新支撑状态
数据模型
客户需求信息
json{
  "research_question": "如何识别心肌细胞亚型的基因表达差异",
  "tissue_type": "cardiac",
  "research_goal": "cell_type_identification", 
  "sample_info": {
    "sample_size": 8,
    "sample_source": "human_heart_tissue"
  },
  "technical_requirements": {
    "sequencing_depth": "standard",
    "analysis_focus": ["differential_expression", "cell_clustering"]
  },
  "constraints": {
    "budget_range": "medium",
    "timeline": "3_months"
  }
}
文献筛选结果
json{
  "literature_id": "pmid_12345678",
  "title": "Single-cell RNA sequencing reveals...",
  "abstract": "文献摘要内容",
  "relevance_analysis": {
    "overall_score": 0.85,
    "match_criteria": {
      "tissue_match": 0.9,
      "method_match": 0.8,
      "goal_match": 0.85
    },
    "supporting_evidence": [
      "使用了相同的组织类型",
      "采用了类似的分析方法",
      "研究目标高度相关"
    ]
  },
  "support_status": "confirmed", // confirmed, potential, rejected
  "auto_selected": true
}
详细实现方案
第一步：需求解析和搜索策略生成
pythonclass RequirementAnalyzer:
    def analyze_customer_requirements(self, requirements):
        """分析客户需求，生成搜索策略"""
        return {
            "primary_keywords": [...],
            "secondary_keywords": [...], 
            "must_include_terms": [...],
            "exclude_terms": [...],
            "filters": {...}
        }
    
    def generate_search_queries(self, strategy):
        """基于策略生成多个搜索查询"""
        return [
            "single cell RNA sequencing cardiac tissue",
            "cardiomyocyte transcriptome profiling", 
            "heart cell type identification scRNA-seq"
        ]
第二步：多源文献搜索
pythonclass LiteratureCollector:
    async def collect_literature(self, requirements):
        """基于需求搜集文献"""
        # 1. 分析需求生成搜索策略
        strategy = self.analyzer.analyze_customer_requirements(requirements)
        queries = self.analyzer.generate_search_queries(strategy)
        
        # 2. 并行搜索多个数据源
        all_papers = []
        for query in queries:
            papers = await self.search_multiple_sources(query, strategy.filters)
            all_papers.extend(papers)
        
        # 3. 去重和初步筛选
        deduplicated = self.deduplicate_papers(all_papers)
        
        # 4. AI相关性评估
        evaluated_papers = await self.evaluate_relevance_batch(
            deduplicated, requirements
        )
        
        # 5. 自动筛选支撑文献
        supporting_papers = self.auto_select_supporting_papers(evaluated_papers)
        
        return {
            "total_found": len(deduplicated),
            "evaluated_papers": evaluated_papers,
            "supporting_papers": supporting_papers,
            "search_summary": self.generate_search_summary(strategy)
        }
第三步：AI相关性判断
pythonclass RelevanceEvaluator:
    async def evaluate_relevance(self, paper, requirements):
        """评估单篇文献的相关性"""
        
        evaluation_prompt = f"""
        作为单细胞测序领域专家，请评估以下文献与客户需求的匹配度：

        客户需求：
        - 研究问题: {requirements.research_question}
        - 组织类型: {requirements.tissue_type}
        - 研究目标: {requirements.research_goal}
        - 技术要求: {requirements.technical_requirements}

        待评估文献：
        - 标题: {paper.title}
        - 摘要: {paper.abstract}
        - 期刊: {paper.journal}
        - 发表年份: {paper.year}

        请从以下维度评估匹配度(0-1分)：
        1. 组织类型匹配度
        2. 研究方法匹配度  
        3. 研究目标匹配度
        4. 技术路线匹配度
        5. 结果应用性

        判断该文献是否应该作为支撑文献：
        - confirmed: 高度相关，强烈推荐
        - potential: 部分相关，可以考虑
        - rejected: 相关性较低，不推荐

        返回JSON格式：
        {{
            "overall_score": 0.85,
            "dimension_scores": {{
                "tissue_match": 0.9,
                "method_match": 0.8,
                "goal_match": 0.85,
                "technical_match": 0.8,
                "applicability": 0.75
            }},
            "support_decision": "confirmed",
            "reasoning": "详细的判断理由",
            "key_supporting_points": ["支撑点1", "支撑点2"],
            "potential_limitations": ["局限性1", "局限性2"],
            "relevance_quotes": ["相关的摘要片段1", "相关的摘要片段2"]
        }}
        """
        
        return await self.ai_service.evaluate_literature(evaluation_prompt)
第四步：智能筛选和分类
pythonclass SupportingLiteratureManager:
    def auto_select_supporting_papers(self, evaluated_papers):
        """自动选择支撑文献"""
        supporting_papers = []
        potential_papers = []
        
        for paper in evaluated_papers:
            if paper.relevance_analysis.overall_score >= 0.8:
                paper.support_status = "confirmed"
                supporting_papers.append(paper)
            elif paper.relevance_analysis.overall_score >= 0.6:
                paper.support_status = "potential"  
                potential_papers.append(paper)
            else:
                paper.support_status = "rejected"
        
        return {
            "confirmed_supporting": supporting_papers,
            "potential_supporting": potential_papers,
            "selection_summary": self.generate_selection_summary(evaluated_papers)
        }
    
    def categorize_supporting_papers(self, papers):
        """对支撑文献进行分类"""
        categories = {
            "methodology_papers": [],      # 方法学文献
            "case_study_papers": [],      # 案例研究文献  
            "review_papers": [],          # 综述文献
            "protocol_papers": [],        # 实验方案文献
            "comparative_papers": []      # 对比分析文献
        }
        
        for paper in papers:
            category = self.classify_paper_type(paper)
            categories[category].append(paper)
        
        return categories
API实现
主要API端点实现
python# app/api/endpoints/literature_collection.py

@router.post("/collect-for-requirements", response_model=LiteratureCollectionResponse)
async def collect_literature_for_requirements(
    request: LiteratureCollectionRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """基于客户需求搜集和筛选文献"""
    
    collector = LiteratureCollector()
    
    # 执行文献搜集和筛选
    collection_result = await collector.collect_literature(request.requirements)
    
    # 保存搜集结果到数据库
    collection_record = await save_literature_collection(
        user_id=current_user.id,
        requirements=request.requirements,
        results=collection_result,
        db=db
    )
    
    return LiteratureCollectionResponse(
        collection_id=collection_record.id,
        total_papers_found=collection_result["total_found"],
        supporting_papers=collection_result["supporting_papers"]["confirmed_supporting"],
        potential_papers=collection_result["supporting_papers"]["potential_supporting"],
        search_summary=collection_result["search_summary"],
        collection_timestamp=datetime.utcnow()
    )

@router.get("/supporting-papers/{collection_id}", response_model=SupportingPapersResponse)
async def get_supporting_papers(
    collection_id: int,
    category: Optional[str] = None,
    min_score: Optional[float] = 0.6,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取支撑文献列表"""
    
    collection = await get_literature_collection(collection_id, db)
    
    if not collection or collection.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="文献搜集记录未找到")
    
    # 过滤和分类支撑文献
    filtered_papers = filter_supporting_papers(
        collection.supporting_papers,
        category=category,
        min_score=min_score
    )
    
    categorized_papers = categorize_supporting_papers(filtered_papers)
    
    return SupportingPapersResponse(
        collection_id=collection_id,
        categorized_papers=categorized_papers,
        total_supporting=len(filtered_papers),
        filter_criteria={"category": category, "min_score": min_score}
    )

@router.post("/evaluate-relevance", response_model=RelevanceEvaluationResponse)
async def evaluate_literature_relevance(
    request: RelevanceEvaluationRequest,
    current_user: User = Depends(get_current_user)
):
    """评估特定文献的相关性"""
    
    evaluator = RelevanceEvaluator()
    
    evaluation_result = await evaluator.evaluate_relevance(
        paper=request.paper_info,
        requirements=request.requirements
    )
    
    return RelevanceEvaluationResponse(
        paper_id=request.paper_info.id,
        relevance_analysis=evaluation_result,
        recommendation=evaluation_result["support_decision"],
        evaluation_timestamp=datetime.utcnow()
    )

@router.put("/update-support-status", response_model=StatusUpdateResponse)
async def update_literature_support_status(
    request: SupportStatusUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """手动更新文献支撑状态"""
    
    updated_record = await update_paper_support_status(
        collection_id=request.collection_id,
        paper_id=request.paper_id,
        new_status=request.new_status,
        user_notes=request.user_notes,
        user_id=current_user.id,
        db=db
    )
    
    return StatusUpdateResponse(
        success=True,
        updated_paper_id=request.paper_id,
        new_status=request.new_status,
        update_timestamp=datetime.utcnow()
    )
数据库设计
文献搜集记录表
sqlCREATE TABLE literature_collections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    requirements JSON NOT NULL,           -- 客户需求信息
    search_strategy JSON,                 -- 搜索策略
    total_papers_found INTEGER,           -- 总共找到的文献数
    supporting_papers JSON,               -- 支撑文献列表
    potential_papers JSON,                -- 潜在支撑文献
    rejected_papers JSON,                 -- 被拒绝的文献
    collection_summary JSON,              -- 搜集总结
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);

CREATE TABLE literature_evaluations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    collection_id INTEGER NOT NULL,
    paper_external_id VARCHAR(255),       -- 外部文献ID (PMID, DOI等)
    paper_info JSON,                      -- 文献基本信息
    relevance_analysis JSON,              -- AI相关性分析结果
    support_status VARCHAR(50),           -- confirmed, potential, rejected
    user_override BOOLEAN DEFAULT FALSE,  -- 用户是否手动修改过
    user_notes TEXT,                      -- 用户备注
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (collection_id) REFERENCES literature_collections (id)
);
智能优化策略
1. 搜索策略优化
pythonclass SmartSearchStrategy:
    def optimize_search_queries(self, requirements, initial_results):
        """基于初始结果优化搜索策略"""
        
        if len(initial_results) < 10:
            # 结果太少，扩大搜索范围
            return self.broaden_search_terms(requirements)
        elif len(initial_results) > 100:
            # 结果太多，缩小搜索范围  
            return self.narrow_search_terms(requirements)
        else:
            # 结果适中，优化质量
            return self.refine_search_quality(requirements, initial_results)
    
    def generate_alternative_queries(self, primary_query, requirements):
        """生成替代搜索查询"""
        alternatives = []
        
        # 同义词替换
        alternatives.extend(self.synonym_expansion(primary_query))
        
        # 概念扩展
        alternatives.extend(self.concept_expansion(primary_query, requirements))
        
        # 方法论聚焦
        alternatives.extend(self.methodology_focus(requirements))
        
        return alternatives
2. 相关性评估优化
pythonclass AdaptiveRelevanceEvaluator:
    def __init__(self):
        self.evaluation_history = []
    
    def learn_from_user_feedback(self, user_feedback):
        """从用户反馈中学习，优化评估标准"""
        
        # 分析用户确认/拒绝的文献特征
        confirmed_features = self.extract_features(user_feedback.confirmed_papers)
        rejected_features = self.extract_features(user_feedback.rejected_papers)
        
        # 调整评估权重
        self.adjust_evaluation_weights(confirmed_features, rejected_features)
    
    def dynamic_threshold_adjustment(self, collection_context):
        """动态调整相关性阈值"""
        
        if collection_context.urgency == "high":
            return 0.6  # 降低阈值，获取更多候选
        elif collection_context.precision_required == "high":
            return 0.8  # 提高阈值，确保质量
        else:
            return 0.7  # 标准阈值
3. 结果呈现优化
pythonclass ResultPresentation:
    def generate_collection_summary(self, collection_result):
        """生成文献搜集摘要"""
        
        return {
            "search_overview": {
                "total_sources_searched": 3,
                "total_papers_found": collection_result["total_found"],
                "high_relevance_papers": len(collection_result["supporting_papers"]["confirmed_supporting"]),
                "search_coverage": "comprehensive"
            },
            "quality_metrics": {
                "average_relevance_score": self.calculate_average_score(collection_result),
                "source_distribution": self.analyze_source_distribution(collection_result),
                "publication_year_range": self.analyze_temporal_distribution(collection_result)
            },
            "recommendations": {
                "next_steps": self.suggest_next_steps(collection_result),
                "search_gaps": self.identify_search_gaps(collection_result),
                "additional_keywords": self.suggest_additional_keywords(collection_result)
            }
        }
    
    def categorize_by_research_value(self, papers):
        """按研究价值分类文献"""
        
        return {
            "foundational_papers": [],      # 基础性文献
            "methodological_papers": [],    # 方法学文献
            "recent_advances": [],          # 最新进展
            "comparative_studies": [],      # 对比研究
            "clinical_applications": []     # 临床应用
        }
使用示例
API调用示例
python# 1. 基于客户需求搜集文献
collection_request = {
    "requirements": {
        "research_question": "如何识别心肌细胞亚型的基因表达特征",
        "tissue_type": "cardiac",
        "research_goal": "cell_type_identification",
        "sample_info": {
            "sample_size": 8,
            "sample_source": "human_heart_tissue"
        },
        "technical_requirements": {
            "sequencing_depth": "standard", 
            "analysis_focus": ["differential_expression", "cell_clustering"]
        }
    },
    "collection_preferences": {
        "max_papers": 50,
        "min_relevance_score": 0.7,
        "include_recent_only": False,
        "prioritize_high_impact": True
    }
}

response = await client.post("/api/v1/literature/collect-for-requirements", json=collection_request)

# 2. 获取支撑文献
supporting_papers = await client.get(f"/api/v1/literature/supporting-papers/{collection_id}")

# 3. 手动调整文献支撑状态
status_update = {
    "collection_id": collection_id,
    "paper_id": "pmid_12345678", 
    "new_status": "confirmed",
    "user_notes": "该文献的实验设计与我们的需求高度匹配"
}

await client.put("/api/v1/literature/update-support-status", json=status_update)
性能优化
1. 缓存策略

搜索结果缓存（24小时）
AI评估结果缓存（永久，除非文献更新）
用户偏好缓存（持久化）

2. 并发处理

并行搜索多个数据源
批量AI评估（每批5-10篇）
异步结果处理

3. 智能限流

API调用频率控制
用户级别限流
优雅降级机制

总结
这个智能文献搜集筛选系统将：

自动化文献搜集: 基于客户需求自动生成搜索策略，多源搜集文献
智能相关性判断: 使用AI分析文献与需求的匹配度，自动筛选支撑文献
灵活的管理机制: 支持手动调整、分类管理、状态更新
全程API驱动: 无需本地文献数据库，完全基于外部API实现
持续优化: 从用户反馈中学习，不断优化搜索和筛选策略

这样可以为客户提供精准、全面的文献支撑，大大提升研究方案的可信度和专业性。


# 🎯 CellForge文献搜集系统 - Cursor实施提示词

## 提示词1: 需求分析和搜索策略生成

你是一个Python后端专家，需要为CellForge项目实现基于客户需求的智能文献搜集系统。

### 项目背景
- 现有FastAPI + SQLAlchemy项目
- 客户已填写研究需求信息
- 需要基于需求搜集相关文献，AI判断相关性，自动筛选支撑文献
- 完全通过API实现，无本地文献数据库

### 核心任务
在`app/services/`目录下创建以下文件：

#### 1. `requirement_analyzer.py` - 需求分析器
```python
# 需要实现的核心功能：
class RequirementAnalyzer:
    """客户需求分析器 - 将客户需求转换为搜索策略"""
    
    def analyze_customer_requirements(self, requirements: dict) -> SearchStrategy:
        """
        分析客户需求，生成搜索策略
        
        输入示例：
        {
            "research_question": "如何识别心肌细胞亚型的基因表达差异",
            "tissue_type": "cardiac", 
            "research_goal": "cell_type_identification",
            "sample_info": {
                "sample_size": 8,
                "sample_source": "human_heart_tissue"
            },
            "technical_requirements": {
                "sequencing_depth": "standard",
                "analysis_focus": ["differential_expression", "cell_clustering"]
            },
            "constraints": {
                "budget_range": "medium",
                "timeline": "3_months"
            }
        }
        
        输出：SearchStrategy对象，包含：
        - primary_keywords: 主要关键词列表
        - secondary_keywords: 次要关键词列表  
        - must_include_terms: 必须包含的术语
        - exclude_terms: 排除的术语
        - filters: 搜索过滤条件
        - search_focus: 搜索重点（methodology, case_study, review等）
        """
    
    def extract_key_concepts(self, research_question: str) -> List[str]:
        """从研究问题中提取关键概念"""
        # 使用NLP技术或规则提取关键概念
        # 返回概念列表
        
    def generate_search_queries(self, strategy: SearchStrategy) -> List[SearchQuery]:
        """
        基于搜索策略生成多个搜索查询
        
        生成策略：
        1. 主查询：核心概念组合
        2. 方法学查询：聚焦技术方法
        3. 应用查询：聚焦应用场景
        4. 对比查询：相关对比研究
        
        每个查询包含：
        - query_text: 搜索文本
        - query_type: 查询类型
        - priority: 优先级
        - expected_results: 期望结果数
        """
    
    def optimize_for_data_source(self, query: str, source: str) -> str:
        """针对不同数据源优化查询"""
        # PubMed: 使用MeSH术语和字段限制
        # Semantic Scholar: 优化关键词组合  
        # bioRxiv: 适配预印本特点
        
    def determine_relevance_criteria(self, requirements: dict) -> RelevanceCriteria:
        """确定文献相关性评判标准"""
        return RelevanceCriteria(
            tissue_match_weight=0.3,
            method_match_weight=0.25,
            goal_match_weight=0.25,
            technical_match_weight=0.2,
            min_overall_score=0.7
        )
```

#### 2. `search_strategy.py` - 搜索策略类
```python
from pydantic import BaseModel
from typing import List, Dict, Optional
from enum import Enum

class SearchFocus(Enum):
    METHODOLOGY = "methodology"
    CASE_STUDY = "case_study" 
    REVIEW = "review"
    PROTOCOL = "protocol"
    COMPARATIVE = "comparative"

class SearchQuery(BaseModel):
    query_text: str
    query_type: SearchFocus
    priority: int  # 1-5, 5最高
    source_specific: Dict[str, str]  # 针对不同数据源的查询
    expected_results: int
    filters: Dict[str, any]

class SearchStrategy(BaseModel):
    primary_keywords: List[str]
    secondary_keywords: List[str]
    must_include_terms: List[str]
    exclude_terms: List[str]
    search_focus: List[SearchFocus]
    temporal_focus: str  # recent, comprehensive, historical
    quality_threshold: float
    max_results_per_source: int

class RelevanceCriteria(BaseModel):
    tissue_match_weight: float
    method_match_weight: float
    goal_match_weight: float
    technical_match_weight: float
    applicability_weight: float
    min_overall_score: float
    auto_confirm_threshold: float = 0.8
    auto_reject_threshold: float = 0.4
```

#### 3. 实现智能关键词生成
```python
class KeywordGenerator:
    """智能关键词生成器"""
    
    # 单细胞测序领域词汇库
    SINGLE_CELL_TERMS = {
        "techniques": [
            "single cell RNA sequencing", "scRNA-seq", "single cell transcriptomics",
            "drop-seq", "10x genomics", "smart-seq", "cell ranger"
        ],
        "tissues": {
            "cardiac": ["heart", "cardiomyocyte", "cardiac tissue", "myocardial"],
            "brain": ["neural", "neuron", "brain tissue", "cerebral"],
            "immune": ["PBMC", "T cell", "B cell", "immune cell"]
        },
        "analysis_methods": [
            "differential expression", "cell clustering", "trajectory analysis",
            "cell type identification", "gene regulatory network"
        ]
    }
    
    def expand_tissue_keywords(self, tissue_type: str) -> List[str]:
        """扩展组织类型关键词"""
        
    def expand_method_keywords(self, analysis_focus: List[str]) -> List[str]:
        """扩展分析方法关键词"""
        
    def generate_synonym_variations(self, base_terms: List[str]) -> List[str]:
        """生成同义词变体"""
        
    def create_boolean_queries(self, keywords: List[str]) -> List[str]:
        """创建布尔查询组合"""
        # 生成 AND, OR, NOT 的组合查询
```

### 数据模型
在`app/schemas/literature_collection.py`中定义：

```python
class CustomerRequirements(BaseModel):
    research_question: str
    tissue_type: str
    research_goal: str
    sample_info: dict
    technical_requirements: dict
    constraints: Optional[dict] = None
    user_preferences: Optional[dict] = None

class LiteratureCollectionRequest(BaseModel):
    requirements: CustomerRequirements
    collection_preferences: Optional[dict] = {
        "max_papers": 50,
        "min_relevance_score": 0.7,
        "include_recent_only": False,
        "prioritize_high_impact": True
    }

class SearchSummary(BaseModel):
    total_queries_executed: int
    sources_searched: List[str]
    search_strategy_used: dict
    optimization_applied: List[str]
    search_duration: float
```

### 集成要求
- 使用现有的配置管理系统
- 遵循现有的错误处理模式
- 支持异步操作
- 添加详细的日志记录
- 与现有数据库模型兼容

### 测试要求
创建`test_requirement_analyzer.py`：
```python
async def test_requirement_analysis():
    analyzer = RequirementAnalyzer()
    
    test_requirements = {
        "research_question": "如何识别心肌细胞亚型的基因表达差异",
        "tissue_type": "cardiac",
        "research_goal": "cell_type_identification"
    }
    
    strategy = analyzer.analyze_customer_requirements(test_requirements)
    
    assert len(strategy.primary_keywords) > 0
    assert "cardiac" in strategy.primary_keywords or "heart" in strategy.primary_keywords
    assert strategy.min_overall_score > 0
```

请实现完整的需求分析和搜索策略生成系统。

---

## 提示词2: 多源API客户端和文献搜集

你是一个API集成专家，需要实现多源文献搜索API客户端，并构建文献搜集聚合系统。

### 任务背景
- 已有需求分析器生成搜索策略
- 需要实现PubMed、Semantic Scholar、bioRxiv API客户端
- 构建搜索结果聚合和去重系统

### 核心任务
在`app/integrations/`目录下创建API客户端：

#### 1. `pubmed_client.py` - PubMed API客户端
```python
import aiohttp
import asyncio
from typing import List, Dict, Optional
from xml.etree import ElementTree as ET

class PubMedClient:
    """PubMed API客户端 - 使用NCBI E-utilities"""
    
    def __init__(self):
        self.base_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils"
        self.rate_limit = 3  # 每秒最多3个请求
        self.session = None
        
    async def search_literature(self, query: str, filters: dict = None) -> List[Dict]:
        """
        搜索PubMed文献
        
        参数：
        - query: 搜索查询字符串
        - filters: 过滤条件，包含：
          - max_results: 最大结果数 (默认50)
          - date_range: 时间范围 {"start": "2020/01/01", "end": "2024/01/01"}
          - journal_filter: 期刊过滤
          - language: 语言限制
          - publication_types: 文献类型
        
        返回：文献列表，每个包含：
        - pmid: PubMed ID
        - title: 标题
        - abstract: 摘要
        - authors: 作者列表
        - journal: 期刊名
        - publication_date: 发表日期
        - doi: DOI
        - mesh_terms: MeSH术语
        - citation_count: 引用数 (如果可获取)
        """
        
        # 1. 构建搜索查询
        search_term = self.build_pubmed_query(query, filters)
        
        # 2. 执行搜索获取PMID列表
        pmids = await self.search_pmids(search_term, filters.get('max_results', 50))
        
        if not pmids:
            return []
            
        # 3. 批量获取文献详细信息
        papers = await self.fetch_paper_details(pmids)
        
        return papers
    
    def build_pubmed_query(self, query: str, filters: dict) -> str:
        """构建PubMed搜索查询"""
        terms = [query]
        
        # 添加单细胞相关术语
        if not any(term in query.lower() for term in ['single cell', 'scrna', 'sc-rna']):
            terms.append('("single cell"[Title/Abstract] OR "single-cell"[Title/Abstract] OR "scRNA-seq"[Title/Abstract])')
        
        # 时间过滤
        if filters and 'date_range' in filters:
            date_filter = f'("{filters["date_range"]["start"]}"[PDAT] : "{filters["date_range"]["end"]}"[PDAT])'
            terms.append(date_filter)
        
        # 语言过滤
        if filters and filters.get('language') == 'english':
            terms.append('english[Language]')
            
        # 期刊影响因子过滤
        if filters and filters.get('high_impact_only'):
            high_impact_journals = [
                '"Nature"[Journal]', '"Science"[Journal]', '"Cell"[Journal]',
                '"Nature Methods"[Journal]', '"Nature Biotechnology"[Journal]'
            ]
            terms.append(f'({" OR ".join(high_impact_journals)})')
        
        return ' AND '.join(terms)
    
    async def search_pmids(self, search_term: str, max_results: int) -> List[str]:
        """搜索获取PMID列表"""
        search_url = f"{self.base_url}/esearch.fcgi"
        params = {
            'db': 'pubmed',
            'term': search_term,
            'retmax': max_results,
            'retmode': 'json',
            'sort': 'relevance'
        }
        
        async with aiohttp.ClientSession() as session:
            await asyncio.sleep(1 / self.rate_limit)  # 限流
            async with session.get(search_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('esearchresult', {}).get('idlist', [])
                else:
                    raise Exception(f"PubMed search failed: {response.status}")
    
    async def fetch_paper_details(self, pmids: List[str]) -> List[Dict]:
        """批量获取文献详细信息"""
        if not pmids:
            return []
            
        fetch_url = f"{self.base_url}/efetch.fcgi"
        params = {
            'db': 'pubmed',
            'id': ','.join(pmids),
            'retmode': 'xml',
            'rettype': 'abstract'
        }
        
        async with aiohttp.ClientSession() as session:
            await asyncio.sleep(1 / self.rate_limit)  # 限流
            async with session.get(fetch_url, params=params) as response:
                if response.status == 200:
                    xml_content = await response.text()
                    return self.parse_pubmed_xml(xml_content)
                else:
                    raise Exception(f"PubMed fetch failed: {response.status}")
    
    def parse_pubmed_xml(self, xml_content: str) -> List[Dict]:
        """解析PubMed XML响应"""
        papers = []
        root = ET.fromstring(xml_content)
        
        for article in root.findall('.//PubmedArticle'):
            try:
                paper = self.extract_paper_info(article)
                if paper:
                    papers.append(paper)
            except Exception as e:
                print(f"Error parsing article: {e}")
                continue
                
        return papers
    
    def extract_paper_info(self, article) -> Dict:
        """从XML元素提取文献信息"""
        # 提取PMID
        pmid = article.find('.//PMID').text if article.find('.//PMID') is not None else None
        
        # 提取标题
        title_elem = article.find('.//ArticleTitle')
        title = title_elem.text if title_elem is not None else "No title"
        
        # 提取摘要
        abstract_elem = article.find('.//AbstractText')
        abstract = abstract_elem.text if abstract_elem is not None else "No abstract available"
        
        # 提取作者
        authors = []
        for author in article.findall('.//Author'):
            lastname = author.find('.//LastName')
            firstname = author.find('.//ForeName')
            if lastname is not None:
                author_name = lastname.text
                if firstname is not None:
                    author_name = f"{firstname.text} {author_name}"
                authors.append(author_name)
        
        # 提取期刊信息
        journal_elem = article.find('.//Journal/Title')
        journal = journal_elem.text if journal_elem is not None else "Unknown journal"
        
        # 提取发表日期
        pub_date = self.extract_publication_date(article)
        
        # 提取DOI
        doi = None
        for article_id in article.findall('.//ArticleId'):
            if article_id.get('IdType') == 'doi':
                doi = article_id.text
                break
        
        # 提取MeSH术语
        mesh_terms = []
        for mesh in article.findall('.//MeshHeading/DescriptorName'):
            mesh_terms.append(mesh.text)
        
        return {
            'source': 'pubmed',
            'external_id': pmid,
            'pmid': pmid,
            'title': title,
            'abstract': abstract,
            'authors': authors,
            'journal': journal,
            'publication_date': pub_date,
            'doi': doi,
            'mesh_terms': mesh_terms,
            'url': f"https://pubmed.ncbi.nlm.nih.gov/{pmid}/" if pmid else None
        }
```

#### 2. `semantic_scholar_client.py` - Semantic Scholar API客户端
```python
class SemanticScholarClient:
    """Semantic Scholar API客户端"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.base_url = "https://api.semanticscholar.org/graph/v1"
        self.api_key = api_key
        self.rate_limit = 100 if api_key else 10  # 有API密钥时100次/秒，否则10次/秒
        
    async def search_literature(self, query: str, filters: dict = None) -> List[Dict]:
        """
        搜索Semantic Scholar文献
        
        返回包含以下字段的文献列表：
        - paperId: Semantic Scholar论文ID
        - title: 标题
        - abstract: 摘要  
        - authors: 作者列表
        - venue: 发表venue
        - year: 发表年份
        - citationCount: 引用数
        - url: 论文URL
        - openAccessPdf: 开放获取PDF链接
        - fields_of_study: 研究领域
        """
        
        search_url = f"{self.base_url}/paper/search"
        
        params = {
            'query': query,
            'limit': filters.get('max_results', 50) if filters else 50,
            'fields': 'title,abstract,authors,venue,year,citationCount,url,openAccessPdf,fieldsOfStudy,externalIds'
        }
        
        # 添加研究领域过滤
        if filters and 'fields_of_study' in filters:
            params['fieldsOfStudy'] = ','.join(filters['fields_of_study'])
        else:
            params['fieldsOfStudy'] = 'Biology,Medicine'
        
        # 时间过滤
        if filters and 'date_range' in filters:
            start_year = int(filters['date_range']['start'][:4])
            end_year = int(filters['date_range']['end'][:4])
            params['year'] = f"{start_year}-{end_year}"
        
        # 最小引用数过滤
        if filters and 'min_citations' in filters:
            params['minCitationCount'] = filters['min_citations']
        
        headers = {}
        if self.api_key:
            headers['x-api-key'] = self.api_key
            
        async with aiohttp.ClientSession() as session:
            await asyncio.sleep(1 / self.rate_limit)  # 限流
            async with session.get(search_url, params=params, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    return self.format_semantic_scholar_results(data.get('data', []))
                else:
                    raise Exception(f"Semantic Scholar search failed: {response.status}")
    
    def format_semantic_scholar_results(self, raw_results: List[Dict]) -> List[Dict]:
        """格式化Semantic Scholar搜索结果"""
        formatted_results = []
        
        for paper in raw_results:
            # 提取作者信息
            authors = []
            for author in paper.get('authors', []):
                authors.append(author.get('name', 'Unknown'))
            
            # 提取外部ID
            external_ids = paper.get('externalIds', {})
            pmid = external_ids.get('PubMed')
            doi = external_ids.get('DOI')
            
            formatted_paper = {
                'source': 'semantic_scholar',
                'external_id': paper.get('paperId'),
                'paper_id': paper.get('paperId'),
                'title': paper.get('title', 'No title'),
                'abstract': paper.get('abstract', 'No abstract available'),
                'authors': authors,
                'journal': paper.get('venue', 'Unknown venue'),
                'publication_date': f"{paper.get('year', 'Unknown')}-01-01",
                'year': paper.get('year'),
                'citation_count': paper.get('citationCount', 0),
                'url': paper.get('url'),
                'pdf_url': paper.get('openAccessPdf', {}).get('url') if paper.get('openAccessPdf') else None,
                'fields_of_study': paper.get('fieldsOfStudy', []),
                'pmid': pmid,
                'doi': doi
            }
            
            formatted_results.append(formatted_paper)
        
        return formatted_results
```

#### 3. `biorxiv_client.py` - bioRxiv API客户端
```python
class BioRxivClient:
    """bioRxiv预印本API客户端"""
    
    def __init__(self):
        self.base_url = "https://api.biorxiv.org"
        self.rate_limit = 10  # 每秒10个请求
        
    async def search_literature(self, query: str, filters: dict = None) -> List[Dict]:
        """
        搜索bioRxiv预印本
        注意：bioRxiv API功能有限，主要通过分类和时间范围获取文献
        """
        
        # bioRxiv搜索策略：通过分类获取最新文献，然后在标题/摘要中搜索关键词
        relevant_categories = self.determine_relevant_categories(query)
        
        all_papers = []
        for category in relevant_categories:
            papers = await self.search_by_category(category, filters)
            filtered_papers = self.filter_by_keywords(papers, query)
            all_papers.extend(filtered_papers)
        
        # 去重和排序
        unique_papers = self.deduplicate_papers(all_papers)
        sorted_papers = sorted(unique_papers, key=lambda x: x.get('date', ''), reverse=True)
        
        max_results = filters.get('max_results', 20) if filters else 20
        return sorted_papers[:max_results]
    
    def determine_relevant_categories(self, query: str) -> List[str]:
        """根据查询确定相关的bioRxiv分类"""
        categories = []
        
        if any(term in query.lower() for term in ['cell', 'molecular', 'gene', 'rna', 'dna']):
            categories.extend(['cell-biology', 'molecular-biology', 'genomics'])
            
        if any(term in query.lower() for term in ['neuro', 'brain', 'neural']):
            categories.append('neuroscience')
            
        if any(term in query.lower() for term in ['cancer', 'tumor', 'oncology']):
            categories.append('cancer-biology')
            
        if any(term in query.lower() for term in ['development', 'stem']):
            categories.append('developmental-biology')
        
        return categories if categories else ['cell-biology']  # 默认分类
    
    async def search_by_category(self, category: str, filters: dict) -> List[Dict]:
        """按分类搜索bioRxiv文献"""
        
        # 确定时间范围
        if filters and 'date_range' in filters:
            start_date = filters['date_range']['start']
            end_date = filters['date_range']['end']
        else:
            # 默认获取最近6个月的文献
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=180)).strftime('%Y-%m-%d')
        
        # bioRxiv API调用
        search_url = f"{self.base_url}/details/{category}/{start_date}/{end_date}/0/json"
        
        async with aiohttp.ClientSession() as session:
            await asyncio.sleep(1 / self.rate_limit)
            async with session.get(search_url) as response:
                if response.status == 200:
                    data = await response.json()
                    return self.format_biorxiv_results(data.get('collection', []))
                else:
                    return []
    
    def format_biorxiv_results(self, raw_results: List[Dict]) -> List[Dict]:
        """格式化bioRxiv搜索结果"""
        formatted_results = []
        
        for paper in raw_results:
            formatted_paper = {
                'source': 'biorxiv',
                'external_id': paper.get('doi'),
                'doi': paper.get('doi'),
                'title': paper.get('title', 'No title'),
                'abstract': paper.get('abstract', 'No abstract available'),
                'authors': paper.get('authors', '').split(';') if paper.get('authors') else [],
                'journal': 'bioRxiv (preprint)',
                'publication_date': paper.get('date'),
                'category': paper.get('category'),
                'url': f"https://doi.org/{paper.get('doi')}" if paper.get('doi') else None,
                'pdf_url': f"https://www.biorxiv.org/content/10.1101/{paper.get('doi').split('/')[-1]}v{paper.get('version', 1)}.full.pdf" if paper.get('doi') else None,
                'version': paper.get('version', 1),
                'is_preprint': True
            }
            
            formatted_results.append(formatted_paper)
        
        return formatted_results
    
    def filter_by_keywords(self, papers: List[Dict], query: str) -> List[Dict]:
        """根据关键词过滤文献"""
        keywords = query.lower().split()
        relevant_papers = []
        
        for paper in papers:
            title = paper.get('title', '').lower()
            abstract = paper.get('abstract', '').lower()
            
            # 计算关键词匹配度
            matches = 0
            for keyword in keywords:
                if keyword in title or keyword in abstract:
                    matches += 1
            
            # 如果匹配度超过50%，认为相关
            if matches / len(keywords) >= 0.5:
                paper['keyword_match_score'] = matches / len(keywords)
                relevant_papers.append(paper)
        
        return relevant_papers
```

#### 4. `literature_collector.py` - 文献搜集聚合器
```python
class LiteratureCollector:
    """文献搜集主控制器"""
    
    def __init__(self):
        self.pubmed_client = PubMedClient()
        self.semantic_scholar_client = SemanticScholarClient()
        self.biorxiv_client = BioRxivClient()
        
    async def collect_literature_for_requirements(
        self, 
        requirements: dict, 
        search_strategy: SearchStrategy
    ) -> Dict:
        """
        基于客户需求搜集文献
        
        流程：
        1. 并行搜索多个数据源
        2. 聚合和去重搜索结果
        3. 初步质量筛选
        4. 返回结构化结果
        """
        
        # 1. 生成针对性搜索查询
        queries = self.generate_targeted_queries(requirements, search_strategy)
        
        # 2. 并行搜索所有数据源
        all_papers = []
        search_tasks = []
        
        for query in queries:
            # 为每个数据源创建搜索任务
            search_tasks.extend([
                self.search_pubmed_with_query(query, search_strategy),
                self.search_semantic_scholar_with_query(query, search_strategy),
                self.search_biorxiv_with_query(query, search_strategy)
            ])
        
        # 执行并行搜索
        search_results = await asyncio.gather(*search_tasks, return_exceptions=True)
        
        # 3. 聚合结果
        for result in search_results:
            if isinstance(result, list):
                all_papers.extend(result)
            else:
                print(f"Search error: {result}")
        
        # 4. 去重和初步筛选
        unique_papers = self.deduplicate_papers(all_papers)
        quality_filtered = self.initial_quality_filter(unique_papers)
        
        # 5. 按相关性排序
        sorted_papers = self.sort_by_relevance(quality_filtered, requirements)
        
        return {
            'total_papers_found': len(all_papers),
            'unique_papers': len(unique_papers),
            'quality_filtered': len(quality_filtered),
            'papers': sorted_papers,
            'search_summary': self.generate_search_summary(queries, search_results)
        }
    
    def deduplicate_papers(self, papers: List[Dict]) -> List[Dict]:
        """文献去重"""
        seen_identifiers = set()
        unique_papers = []
        
        for paper in papers:
            # 使用多种标识符进行去重
            identifiers = []
            
            if paper.get('doi'):
                identifiers.append(('doi', paper['doi'].lower()))
            if paper.get('pmid'):
                identifiers.append(('pmid', paper['pmid']))
            if paper.get('title'):
                # 使用标题的哈希作为标识符
                title_hash = hashlib.md5(paper['title'].lower().encode()).hexdigest()
                identifiers.append(('title_hash', title_hash))
            
            # 检查是否已存在
            is_duplicate = False
            for id_type, id_value in identifiers:
                if (id_type, id_value) in seen_identifiers:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                # 添加所有标识符到已见集合
                for id_type, id_value in identifiers:
                    seen_identifiers.add((id_type, id_value))
                unique_papers.append(paper)
        
        return unique_papers
    
    def initial_quality_filter(self, papers: List[Dict]) -> List[Dict]:
        """初步质量筛选"""
        filtered_papers = []
        
        for paper in papers:
            # 基本质量检查
            if not paper.get('title') or len(paper['title']) < 10:
                continue
                
            if not paper.get('abstract') or len(paper['abstract']) < 50:
                continue
            
            # 排除明显不相关的文献类型
            title_lower = paper['title'].lower()
            if any(term in title_lower for term in ['retraction', 'erratum', 'correction']):
                continue
            
            # 语言过滤（如果需要）
            if self.is_non_english(paper['title']):
                continue
            
            filtered_papers.append(paper)
        
        return filtered_papers
    
    def sort_by_relevance(self, papers: List[Dict], requirements: dict) -> List[Dict]:
        """按相关性排序文献"""
        
        def calculate_basic_relevance(paper):
            score = 0
            title = paper.get('title', '').lower()
            abstract = paper.get('abstract', '').lower()
            
            # 组织类型匹配
            tissue_type = requirements.get('tissue_type', '').lower()
            if tissue_type in title:
                score += 2
            elif tissue_type in abstract:
                score += 1
            
            # 研究目标匹配
            research_goal = requirements.get('research_goal', '').lower()
            if research_goal.replace('_', ' ') in title:
                score += 2
            elif research_goal.replace('_', ' ') in abstract:
                score += 1
            
            # 单细胞相关术语
            sc_terms = ['single cell', 'single-cell', 'scrna', 'scrnaseq']
            for term in sc_terms:
                if term in title:
                    score += 1
                    break
            
            # 引用数加分（如果有）
            citation_count = paper.get('citation_count', 0)
            if citation_count > 100:
                score += 2
            elif citation_count > 50:
                score += 1
            
            # 发表年份加分（更新的文献）
            try:
                year = int(paper.get('year', 0)) if paper.get('year') else 0
                if year >= 2022:
                    score += 2
                elif year >= 2020:
                    score += 1
            except:
                pass
            
            return score
        
        # 计算每篇文献的相关性评分
        for paper in papers:
            paper['basic_relevance_score'] = calculate_basic_relevance(paper)
        
        # 按评分排序
        return sorted(papers, key=lambda x: x['basic_relevance_score'], reverse=True)
```

### 数据库模型
在`app/models/literature_collection.py`中创建：

```python
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Float, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from app.core.database import Base

class LiteratureCollection(Base):
    """文献搜集记录"""
    __tablename__ = "literature_collections"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 搜集配置
    requirements = Column(JSON, nullable=False)      # 客户需求
    search_strategy = Column(JSON)                   # 搜索策略
    collection_preferences = Column(JSON)            # 搜集偏好
    
    # 搜集结果统计
    total_papers_found = Column(Integer, default=0)
    unique_papers_count = Column(Integer, default=0)
    confirmed_supporting_count = Column(Integer, default=0)
    potential_supporting_count = Column(Integer, default=0)
    
    # 搜集元数据
    sources_searched = Column(JSON)                  # 搜索的数据源
    search_queries_used = Column(JSON)               # 使用的搜索查询
    collection_duration = Column(Float)              # 搜集耗时(秒)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="literature_collections")
    collected_papers = relationship("CollectedPaper", back_populates="collection", cascade="all, delete-orphan")

class CollectedPaper(Base):
    """搜集到的文献"""
    __tablename__ = "collected_papers"
    
    id = Column(Integer, primary_key=True)
    collection_id = Column(Integer, ForeignKey("literature_collections.id"), nullable=False)
    
    # 文献基本信息
    external_id = Column(String(255), nullable=False)  # 外部ID (PMID, DOI等)
    source = Column(String(50), nullable=False)        # 来源 (pubmed, semantic_scholar, biorxiv)
    title = Column(Text, nullable=False)
    abstract = Column(Text)
    authors = Column(JSON)                              # 作者列表
    journal = Column(String(255))
    publication_date = Column(String(50))
    doi = Column(String(255))
    pmid = Column(String(50))
    
    # 质量指标
    citation_count = Column(Integer, default=0)
    impact_factor = Column(Float)
    is_preprint = Column(Boolean, default=False)
    
    # 相关性评估
    basic_relevance_score = Column(Float, default=0.0)  # 基础相关性评分
    ai_relevance_analysis = Column(JSON)                 # AI相关性分析结果
    support_status = Column(String(50))                  # confirmed, potential, rejected, pending
    
    # 用户操作
    user_confirmed = Column(Boolean, default=False)
    user_notes = Column(Text)
    user_rating = Column(Integer)                        # 1-5星评分
    
    # 访问信息
    url = Column(String(500))
    pdf_url = Column(String(500))
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    collection = relationship("LiteratureCollection", back_populates="collected_papers")

测试要求
创建test_literature_collector.py：
pythonimport pytest
from app.integrations.literature_collector import LiteratureCollector
from app.services.requirement_analyzer import RequirementAnalyzer

async def test_literature_collection():
    """测试文献搜集功能"""
    collector = LiteratureCollector()
    analyzer = RequirementAnalyzer()
    
    test_requirements = {
        "research_question": "如何识别心肌细胞亚型的基因表达差异",
        "tissue_type": "cardiac",
        "research_goal": "cell_type_identification"
    }
    
    strategy = analyzer.analyze_customer_requirements(test_requirements)
    result = await collector.collect_literature_for_requirements(test_requirements, strategy)
    
    assert result['total_papers_found'] > 0
    assert len(result['papers']) > 0
    assert all(paper.get('title') for paper in result['papers'])

async def test_deduplication():
    """测试去重功能"""
    collector = LiteratureCollector()
    
    test_papers = [
        {"doi": "10.1000/test1", "title": "Test Paper 1"},
        {"doi": "10.1000/test1", "title": "Test Paper 1"},  # 重复
        {"doi": "10.1000/test2", "title": "Test Paper 2"}
    ]
    
    unique_papers = collector.deduplicate_papers(test_papers)
    assert len(unique_papers) == 2
请实现完整的多源API客户端和文献搜集系统，确保能够从PubMed、Semantic Scholar、bioRxiv获取文献并进行有效的聚合去重。

提示词3: AI相关性评估和智能筛选
你是一个AI应用专家，需要实现基于DeepSeek的文献相关性评估和智能筛选系统。
任务背景

已有多源文献搜集结果
需要使用AI评估文献与客户需求的相关性
自动筛选出支撑文献和潜在支撑文献

核心任务
在app/services/目录下创建：
1. literature_relevance_evaluator.py - 相关性评估器
pythonfrom typing import List, Dict, Optional
import asyncio
from app.services.ai_service import AIService

class LiteratureRelevanceEvaluator:
    """文献相关性评估器 - 使用AI判断文献与需求的匹配度"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.batch_size = 5  # 批量处理大小
        
    async def evaluate_papers_batch(
        self, 
        papers: List[Dict], 
        requirements: dict
    ) -> List[Dict]:
        """
        批量评估文献相关性
        
        参数：
        - papers: 文献列表
        - requirements: 客户需求
        
        返回：添加了AI评估结果的文献列表
        """
        
        evaluated_papers = []
        
        # 分批处理，避免AI服务过载
        for i in range(0, len(papers), self.batch_size):
            batch = papers[i:i + self.batch_size]
            batch_results = await self.process_batch(batch, requirements)
            evaluated_papers.extend(batch_results)
            
            # 批次间延迟，控制API调用频率
            if i + self.batch_size < len(papers):
                await asyncio.sleep(2)
        
        return evaluated_papers
    
    async def process_batch(self, batch: List[Dict], requirements: dict) -> List[Dict]:
        """处理单个批次的文献"""
        
        tasks = []
        for paper in batch:
            task = self.evaluate_single_paper(paper, requirements)
            tasks.append(task)
        
        # 并行处理批次内的文献
        evaluation_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        evaluated_papers = []
        for i, result in enumerate(evaluation_results):
            paper = batch[i].copy()
            
            if isinstance(result, dict):
                paper['ai_relevance_analysis'] = result
                paper['support_status'] = result.get('support_decision', 'pending')
            else:
                # 处理评估失败的情况
                print(f"Failed to evaluate paper: {batch[i].get('title', 'Unknown')}, Error: {result}")
                paper['ai_relevance_analysis'] = self.create_fallback_analysis()
                paper['support_status'] = 'pending'
            
            evaluated_papers.append(paper)
        
        return evaluated_papers
    
    async def evaluate_single_paper(self, paper: Dict, requirements: dict) -> Dict:
        """
        评估单篇文献的相关性
        
        返回详细的相关性分析结果
        """
        
        evaluation_prompt = self.create_evaluation_prompt(paper, requirements)
        
        try:
            # 调用AI服务进行评估
            ai_response = await self.ai_service.analyze_literature(evaluation_prompt)
            
            # 解析AI响应
            analysis_result = self.parse_ai_evaluation(ai_response)
            
            # 验证和修正评估结果
            validated_result = self.validate_evaluation_result(analysis_result, paper, requirements)
            
            return validated_result
            
        except Exception as e:
            print(f"AI evaluation failed for paper {paper.get('title', 'Unknown')}: {e}")
            return self.create_fallback_analysis()
    
    def create_evaluation_prompt(self, paper: Dict, requirements: dict) -> str:
        """创建文献评估提示词"""
        
        prompt = f"""
你是单细胞测序领域的顶级专家。请仔细评估以下文献与客户研究需求的匹配度。

客户研究需求：
研究问题: {requirements.get('research_question', 'Not specified')}
组织类型: {requirements.get('tissue_type', 'Not specified')}
研究目标: {requirements.get('research_goal', 'Not specified')}
样本信息: {requirements.get('sample_info', {})}
技术要求: {requirements.get('technical_requirements', {})}

待评估文献：
标题: {paper.get('title', 'No title')}
摘要: {paper.get('abstract', 'No abstract')[:1000]}...
期刊: {paper.get('journal', 'Unknown')}
发表年份: {paper.get('year', 'Unknown')}
引用数: {paper.get('citation_count', 'Unknown')}
来源: {paper.get('source', 'Unknown')}

请从以下维度进行深度分析：

1. 组织类型匹配度 (0-1)
   - 是否使用了相同或相似的组织类型？
   - 是否涉及相关的细胞类型？

2. 研究方法匹配度 (0-1)
   - 是否使用了单细胞RNA测序技术？
   - 技术平台和方法是否相似？
   - 数据分析方法是否相关？

3. 研究目标匹配度 (0-1)
   - 研究目标是否一致或相关？
   - 要解决的科学问题是否相似？

4. 应用价值评估 (0-1)
   - 该文献的方法能否应用到客户的研究中？
   - 结果和结论对客户研究有何启发？

5. 文献质量评估 (0-1)
   - 期刊影响因子和声誉
   - 实验设计的严谨性
   - 结果的可靠性

基于以上分析，判断该文献应该归类为：
- confirmed: 高度相关，强烈推荐作为支撑文献 (综合评分≥0.8)
- potential: 部分相关，可以考虑作为参考文献 (综合评分0.6-0.8)
- rejected: 相关性较低，不推荐 (综合评分<0.6)

严格按照以下JSON格式返回，不要包含任何其他内容：
{{
    "overall_score": 0.85,
    "dimension_scores": {{
        "tissue_match": 0.9,
        "method_match": 0.8,
        "goal_match": 0.85,
        "applicability": 0.8,
        "quality": 0.75
    }},
    "support_decision": "confirmed",
    "detailed_reasoning": "详细的分析理由，解释为什么给出这样的评分",
    "key_supporting_points": [
        "具体的支撑点1",
        "具体的支撑点2",
        "具体的支撑点3"
    ],
    "potential_limitations": [
        "可能的局限性1",
        "可能的局限性2"
    ],
    "relevance_quotes": [
        "摘要中最相关的片段1",
        "摘要中最相关的片段2"
    ],
    "recommendation_confidence": 0.9,
    "suggested_use_case": "建议如何在客户研究中使用这篇文献"
}}
"""
        
        return prompt
    
    def parse_ai_evaluation(self, ai_response: str) -> Dict:
        """解析AI评估响应"""
        try:
            import json
            import re
            
            # 尝试提取JSON内容
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                raise ValueError("No valid JSON found in AI response")
                
        except Exception as e:
            print(f"Failed to parse AI evaluation: {e}")
            return self.create_fallback_analysis()
    
    def validate_evaluation_result(self, analysis: Dict, paper: Dict, requirements: dict) -> Dict:
        """验证和修正评估结果"""
        
        # 确保所有必需字段存在
        required_fields = ['overall_score', 'dimension_scores', 'support_decision']
        for field in required_fields:
            if field not in analysis:
                return self.create_fallback_analysis()
        
        # 验证评分范围
        if not (0 <= analysis['overall_score'] <= 1):
            analysis['overall_score'] = max(0, min(1, analysis['overall_score']))
        
        # 验证决策一致性
        score = analysis['overall_score']
        decision = analysis['support_decision']
        
        if score >= 0.8 and decision != 'confirmed':
            analysis['support_decision'] = 'confirmed'
        elif 0.6 <= score < 0.8 and decision not in ['potential', 'confirmed']:
            analysis['support_decision'] = 'potential'
        elif score < 0.6 and decision not in ['rejected', 'potential']:
            analysis['support_decision'] = 'rejected'
        
        # 添加元数据
        analysis['evaluation_timestamp'] = datetime.utcnow().isoformat()
        analysis['evaluator_version'] = '1.0'
        
        return analysis
    
    def create_fallback_analysis(self) -> Dict:
        """创建备用分析结果（当AI评估失败时使用）"""
        return {
            'overall_score': 0.5,
            'dimension_scores': {
                'tissue_match': 0.5,
                'method_match': 0.5,
                'goal_match': 0.5,
                'applicability': 0.5,
                'quality': 0.5
            },
            'support_decision': 'pending',
            'detailed_reasoning': 'AI评估失败，需要人工审核',
            'key_supporting_points': [],
            'potential_limitations': ['AI评估失败'],
            'relevance_quotes': [],
            'recommendation_confidence': 0.0,
            'suggested_use_case': '需要人工审核',
            'evaluation_timestamp': datetime.utcnow().isoformat(),
            'evaluator_version': '1.0',
            'is_fallback': True
        }
2. supporting_literature_manager.py - 支撑文献管理器
pythonclass SupportingLiteratureManager:
    """支撑文献管理器 - 负责筛选和管理支撑文献"""
    
    def __init__(self):
        self.relevance_evaluator = LiteratureRelevanceEvaluator()
    
    async def process_collected_papers(
        self, 
        papers: List[Dict], 
        requirements: dict,
        collection_preferences: dict = None
    ) -> Dict:
        """
        处理搜集到的文献，进行AI评估和分类
        
        返回：
        {
            'confirmed_supporting': [...],
            'potential_supporting': [...], 
            'rejected_papers': [...],
            'processing_summary': {...}
        }
        """
        
        # 1. AI相关性评估
        evaluated_papers = await self.relevance_evaluator.evaluate_papers_batch(
            papers, requirements
        )
        
        # 2. 自动分类
        categorized_results = self.categorize_papers(evaluated_papers, collection_preferences)
        
        # 3. 质量排序
        categorized_results = self.sort_papers_by_quality(categorized_results)
        
        # 4. 应用用户偏好过滤
        if collection_preferences:
            categorized_results = self.apply_user_preferences(categorized_results, collection_preferences)
        
        # 5. 生成处理摘要
        processing_summary = self.generate_processing_summary(evaluated_papers, categorized_results)
        
        return {
            **categorized_results,
            'processing_summary': processing_summary
        }
    
    def categorize_papers(self, evaluated_papers: List[Dict], preferences: dict = None) -> Dict:
        """根据AI评估结果对文献进行分类"""
        
        confirmed_supporting = []
        potential_supporting = []
        rejected_papers = []
        
        # 获取用户偏好的阈值
        min_score = preferences.get('min_relevance_score', 0.7) if preferences else 0.7
        auto_confirm_threshold = preferences.get('auto_confirm_threshold', 0.8) if preferences else 0.8
        
        for paper in evaluated_papers:
            analysis = paper.get('ai_relevance_analysis', {})
            overall_score = analysis.get('overall_score', 0)
            support_decision = analysis.get('support_decision', 'pending')
            
            # 应用用户偏好的最低分数要求
            if overall_score < min_score:
                paper['support_status'] = 'rejected'
                paper['rejection_reason'] = f'评分 {overall_score:.2f} 低于最低要求 {min_score}'
                rejected_papers.append(paper)
                continue
            
            # 根据AI决策和评分进行分类
            if support_decision == 'confirmed' or overall_score >= auto_confirm_threshold:
                paper['support_status'] = 'confirmed'
                confirmed_supporting.append(paper)
            elif support_decision == 'potential' or overall_score >= 0.6:
                paper['support_status'] = 'potential'
                potential_supporting.append(paper)
            else:
                paper['support_status'] = 'rejected'
                paper['rejection_reason'] = f'AI评估结果: {support_decision}, 评分: {overall_score:.2f}'
                rejected_papers.append(paper)
        
        return {
            'confirmed_supporting': confirmed_supporting,
            'potential_supporting': potential_supporting,
            'rejected_papers': rejected_papers
        }
    
    def sort_papers_by_quality(self, categorized_results: Dict) -> Dict:
        """按质量对各类文献进行排序"""
        
        def quality_score(paper):
            analysis = paper.get('ai_relevance_analysis', {})
            overall_score = analysis.get('overall_score', 0)
            citation_count = paper.get('citation_count', 0)
            
            # 综合相关性评分和引用数
            normalized_citations = min(citation_count / 100, 1.0)  # 归一化引用数
            
            return overall_score * 0.7 + normalized_citations * 0.3
        
        # 对每个类别进行排序
        for category in ['confirmed_supporting', 'potential_supporting', 'rejected_papers']:
            if category in categorized_results:
                categorized_results[category].sort(key=quality_score, reverse=True)
        
        return categorized_results
    
    def apply_user_preferences(self, results: Dict, preferences: dict) -> Dict:
        """应用用户偏好过滤"""
        
        # 限制结果数量
        max_confirmed = preferences.get('max_confirmed_papers', 20)
        max_potential = preferences.get('max_potential_papers', 15)
        
        if len(results['confirmed_supporting']) > max_confirmed:
            results['confirmed_supporting'] = results['confirmed_supporting'][:max_confirmed]
        
        if len(results['potential_supporting']) > max_potential:
            results['potential_supporting'] = results['potential_supporting'][:max_potential]
        
        # 优先级过滤
        if preferences.get('prioritize_recent', False):
            results = self.prioritize_recent_papers(results)
        
        if preferences.get('prioritize_high_impact', False):
            results = self.prioritize_high_impact_papers(results)
        
        return results
    
    def prioritize_recent_papers(self, results: Dict) -> Dict:
        """优先考虑最近的文献"""
        current_year = datetime.now().year
        
        def recency_boost(paper):
            try:
                year = int(paper.get('year', 0))
                if year >= current_year - 2:  # 最近2年
                    return 0.1
                elif year >= current_year - 5:  # 最近5年
                    return 0.05
                else:
                    return 0
            except:
                return 0
        
        # 为每个类别的文献添加时效性加分
        for category in ['confirmed_supporting', 'potential_supporting']:
            if category in results:
                for paper in results[category]:
                    analysis = paper.get('ai_relevance_analysis', {})
                    original_score = analysis.get('overall_score', 0)
                    boosted_score = min(original_score + recency_boost(paper), 1.0)
                    analysis['overall_score'] = boosted_score
                    paper['ai_relevance_analysis'] = analysis
        
        return results
    
    def prioritize_high_impact_papers(self, results: Dict) -> Dict:
        """优先考虑高影响因子的文献"""
        
        high_impact_journals = {
            'nature': 3.0,
            'science': 3.0, 
            'cell': 2.5,
            'nature methods': 2.0,
            'nature biotechnology': 2.0,
            'genome research': 1.5,
            'nucleic acids research': 1.0
        }
        
        def impact_boost(paper):
            journal = paper.get('journal', '').lower()
            for high_journal, boost in high_impact_journals.items():
                if high_journal in journal:
                    return boost * 0.1  # 最大加分0.3
            return 0
        
        # 为高影响因子文献加分
        for category in ['confirmed_supporting', 'potential_supporting']:
            if category in results:
                for paper in results[category]:
                    analysis = paper.get('ai_relevance_analysis', {})
                    original_score = analysis.get('overall_score', 0)
                    boosted_score = min(original_score + impact_boost(paper), 1.0)
                    analysis['overall_score'] = boosted_score
                    paper['ai_relevance_analysis'] = analysis
        
        return results
    
    def generate_processing_summary(self, evaluated_papers: List[Dict], results: Dict) -> Dict:
        """生成处理摘要"""
        
        total_papers = len(evaluated_papers)
        confirmed_count = len(results['confirmed_supporting'])
        potential_count = len(results['potential_supporting'])
        rejected_count = len(results['rejected_papers'])
        
        # 计算平均评分
        total_score = sum(
            paper.get('ai_relevance_analysis', {}).get('overall_score', 0) 
            for paper in evaluated_papers
        )
        avg_score = total_score / total_papers if total_papers > 0 else 0
        
        # 分析评分分布
        score_distribution = {
            'high_relevance': len([p for p in evaluated_papers if p.get('ai_relevance_analysis', {}).get('overall_score', 0) >= 0.8]),
            'medium_relevance': len([p for p in evaluated_papers if 0.6 <= p.get('ai_relevance_analysis', {}).get('overall_score', 0) < 0.8]),
            'low_relevance': len([p for p in evaluated_papers if p.get('ai_relevance_analysis', {}).get('overall_score', 0) < 0.6])
        }
        
        # 来源分布
        source_distribution = {}
        for paper in evaluated_papers:
            source = paper.get('source', 'unknown')
            source_distribution[source] = source_distribution.get(source, 0) + 1
        
        return {
            'total_papers_processed': total_papers,
            'confirmed_supporting_count': confirmed_count,
            'potential_supporting_count': potential_count,
            'rejected_count': rejected_count,
            'average_relevance_score': round(avg_score, 3),
            'score_distribution': score_distribution,
            'source_distribution': source_distribution,
            'processing_success_rate': round((total_papers - len([p for p in evaluated_papers if p.get('ai_relevance_analysis', {}).get('is_fallback', False)])) / total_papers, 3) if total_papers > 0 else 0
        }
3. literature_categorizer.py - 文献分类器
pythonclass LiteratureCategorizer:
    """文献分类器 - 按研究价值和类型对文献进行分类"""
    
    def categorize_by_research_value(self, papers: List[Dict]) -> Dict:
        """按研究价值分类文献"""
        
        categories = {
            'foundational_papers': [],      # 基础性文献
            'methodological_papers': [],    # 方法学文献
            'recent_advances': [],          # 最新进展
            'comparative_studies': [],      # 对比研究
            'clinical_applications': [],    # 临床应用
            'review_papers': []            # 综述文献
        }
        
        current_year = datetime.now().year
        
        for paper in papers:
            title = paper.get('title', '').lower()
            abstract = paper.get('abstract', '').lower()
            year = paper.get('year', 0)
            citation_count = paper.get('citation_count', 0)
            
            # 判断文献类型
            paper_types = []
            
            # 综述文献
            if any(term in title for term in ['review', 'survey', 'overview', 'perspective']):
                paper_types.append('review_papers')
            
            # 方法学文献
            elif any(term in title for term in ['method', 'protocol', 'approach', 'technique', 'algorithm']):
                paper_types.append('methodological_papers')
            
            # 对比研究
            elif any(term in title for term in ['comparison', 'comparative', 'versus', 'benchmark']):
                paper_types.append('comparative_studies')
            
            # 临床应用
            elif any(term in title or abstract for term in ['clinical', 'patient', 'diagnosis', 'therapeutic', 'treatment']):
                paper_types.append('clinical_applications')
            
            # 最新进展（最近2年的高质量文献）
            if year >= current_year - 2 and citation_count > 10:
                paper_types.append('recent_advances')
            
            # 基础性文献（高引用、经典文献）
            if citation_count > 100:
                paper_types.append('foundational_papers')
            
            # 如果没有明确分类，根据内容进行分类
            if not paper_types:
                if 'single cell' in title or 'scrna' in title:
                    paper_types.append('methodological_papers')
                else:
                    paper_types.append('foundational_papers')
            
            # 添加到对应分类
            for paper_type in paper_types:
                categories[paper_type].append(paper)
        
        return categories
    
    def generate_category_summaries(self, categorized_papers: Dict) -> Dict:
        """生成各分类的摘要"""
        
        summaries = {}
        
        for category, papers in categorized_papers.items():
            if not papers:
                summaries[category] = {
                    'count': 0,
                    'description': self.get_category_description(category),
                    'top_papers': []
                }
                continue
            
            # 按相关性评分排序，取前3篇
            top_papers = sorted(
                papers, 
                key=lambda x: x.get('ai_relevance_analysis', {}).get('overall_score', 0), 
                reverse=True
            )[:3]
            
            # 计算平均评分
            avg_score = sum(
                p.get('ai_relevance_analysis', {}).get('overall_score', 0) 
                for p in papers
            ) / len(papers)
            
            summaries[category] = {
                'count': len(papers),
                'description': self.get_category_description(category),
                'average_relevance_score': round(avg_score, 3),
                'top_papers': [
                    {
                        'title': p.get('title'),
                        'journal': p.get('journal'),
                        'year': p.get('year'),
                        'citation_count': p.get('citation_count', 0),
                        'relevance_score': p.get('ai_relevance_analysis', {}).get('overall_score', 0)
                    } for p in top_papers
                ]
            }
        
        return summaries
    
    def get_category_description(self, category: str) -> str:
        """获取分类描述"""
        descriptions = {
            'foundational_papers': '高引用的经典基础文献，为研究领域奠定理论基础',
            'methodological_papers': '介绍新方法、技术或实验流程的文献',
            'recent_advances': '最近2年发表的高质量文献，代表最新研究进展',
            'comparative_studies': '对比不同方法或技术的研究文献',
            'clinical_applications': '具有临床应用价值或转化医学意义的文献',
            'review_papers': '综述性文献，提供领域概览和发展趋势'
        }
        return descriptions.get(category, '未分类文献')
API集成
更新app/api/endpoints/literature.py：
<EMAIL>("/collect-and-evaluate", response_model=LiteratureCollectionResponse)
async def collect_and_evaluate_literature(
    request: LiteratureCollectionRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    完整的文献搜集和AI评估流程
    """
    from app.services.requirement_analyzer import RequirementAnalyzer
    from app.integrations.literature_collector import LiteratureCollector
    from app.services.supporting_literature_manager import SupportingLiteratureManager
    
    # 1. 分析客户需求
    analyzer = RequirementAnalyzer()
    search_strategy = analyzer.analyze_customer_requirements(request.requirements)
    
    # 2. 搜集文献
    collector = LiteratureCollector()
    collection_result = await collector.collect_literature_for_requirements(
        request.requirements, search_strategy
    )
    
    # 3. AI评估和筛选
    manager = SupportingLiteratureManager()
    processed_result = await manager.process_collected_papers(
        collection_result['papers'],
        request.requirements,
        request.collection_preferences
    )
    
    # 4. 保存到数据库
    collection_record = await save_literature_collection_with_papers(
        user_id=current_user.id,
        requirements=request.requirements,
        search_strategy=search_strategy.dict(),
        collection_result=collection_result,
        processed_result=processed_result,
        db=db
    )
    
    return LiteratureCollectionResponse(
        collection_id=collection_record.id,
        total_papers_found=collection_result['total_papers_found'],
        confirmed_supporting=processed_result['confirmed_supporting'],
        potential_supporting=processed_result['potential_supporting'],
        processing_summary=processed_result['processing_summary'],
        search_strategy_used=search_strategy.dict(),
        collection_timestamp=datetime.utcnow()
    )
