"""
网页画像分析服务
专门用于从网页内容中提取和分析客户画像信息
"""
import re
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urlparse, urljoin
import httpx
try:
    from bs4 import BeautifulSoup
    HAS_BS4 = True
except ImportError:
    HAS_BS4 = False
    BeautifulSoup = None

try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False
    openai = None
from datetime import datetime, timezone

from .keyword_search_service import KeywordSearchService

logger = logging.getLogger(__name__)


class WebProfileAnalyzer:
    """网页画像分析器"""

    def __init__(self):
        self.session = httpx.AsyncClient(
            timeout=30.0,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        )

        # 初始化关键词搜索服务
        self.keyword_search_service = KeywordSearchService()

        # 学术网站模式识别
        self.academic_patterns = {
            'university_domains': [
                '.edu', '.ac.', 'university', 'college', 'institute', 'school'
            ],
            'academic_platforms': [
                'researchgate.net', 'scholar.google.', 'orcid.org',
                'linkedin.com', 'pubmed.ncbi.nlm.nih.gov'
            ],
            'research_keywords': [
                'research', 'publication', 'laboratory', 'department',
                'professor', 'phd', 'postdoc', 'graduate', 'faculty'
            ]
        }

        # 单细胞相关关键词
        self.single_cell_keywords = {
            'technologies': [
                'single cell', 'scRNA-seq', 'scATAC-seq', '10x genomics',
                'smart-seq', 'drop-seq', 'cel-seq', 'mars-seq',
                'spatial transcriptomics', 'single nucleus'
            ],
            'applications': [
                'cell atlas', 'development', 'differentiation', 'cancer',
                'immunology', 'neuroscience', 'stem cell', 'organoid'
            ],
            'analysis_tools': [
                'seurat', 'scanpy', 'cellranger', 'monocle', 'trajectory',
                'pseudotime', 'clustering', 'dimensionality reduction'
            ]
        }

    async def analyze_url(self, url: str, enable_keyword_search: bool = False) -> Dict[str, Any]:
        """分析单个URL并生成画像"""
        try:
            # 1. 抓取网页内容
            web_content = await self._fetch_web_content(url)
            if not web_content:
                return {"error": "无法获取网页内容"}

            # 2. 提取结构化信息
            structured_data = await self._extract_structured_data(web_content, url)

            # 3. 关键词增强搜索（可选）
            if enable_keyword_search:
                structured_data = await self._enhance_with_keyword_search(structured_data)

            # 4. AI分析生成画像
            profile_analysis = await self._ai_analyze_content(structured_data)

            # 5. 构建最终画像
            final_profile = await self._build_comprehensive_profile(
                url, structured_data, profile_analysis
            )

            return final_profile

        except Exception as e:
            logger.error(f"URL分析失败 {url}: {e}")
            return {"error": f"分析失败: {str(e)}"}

    async def analyze_multiple_sources(self, sources: List[Dict[str, str]]) -> Dict[str, Any]:
        """分析多个数据源并生成综合画像"""
        try:
            all_analyses = []

            for source in sources:
                if source['type'] == 'url':
                    analysis = await self.analyze_url(source['content'])
                elif source['type'] == 'text':
                    analysis = await self._analyze_text_input(source['content'])
                else:
                    continue

                if 'error' not in analysis:
                    all_analyses.append(analysis)

            # 综合多个分析结果
            if not all_analyses:
                return {"error": "没有有效的分析结果"}

            comprehensive_profile = await self._merge_multiple_analyses(all_analyses)
            return comprehensive_profile

        except Exception as e:
            logger.error(f"多源分析失败: {e}")
            return {"error": f"综合分析失败: {str(e)}"}

    async def _fetch_web_content(self, url: str) -> Optional[Dict[str, Any]]:
        """抓取网页内容"""
        try:
            if not HAS_BS4:
                # 简化版本，不使用BeautifulSoup
                response = await self.session.get(url)
                response.raise_for_status()

                return {
                    'url': url,
                    'title': '',
                    'text_content': response.text,
                    'meta_description': '',
                    'headings': [],
                    'links': [],
                    'structured_data': {}
                }

            response = await self.session.get(url)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # 移除脚本和样式标签
            for script in soup(["script", "style"]):
                script.decompose()

            return {
                'url': url,
                'title': soup.title.string if soup.title else '',
                'text_content': soup.get_text(),
                'meta_description': self._get_meta_description(soup),
                'headings': self._extract_headings(soup),
                'links': self._extract_links(soup, url),
                'structured_data': self._extract_json_ld(soup)
            }

        except Exception as e:
            logger.error(f"网页抓取失败 {url}: {e}")
            return None

    def _get_meta_description(self, soup: Any) -> str:
        """提取meta描述"""
        if not HAS_BS4 or soup is None:
            return ''
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        return meta_desc.get('content', '') if meta_desc else ''

    def _extract_headings(self, soup: Any) -> List[str]:
        """提取标题"""
        if not HAS_BS4 or soup is None:
            return []
        headings = []
        for tag in soup.find_all(['h1', 'h2', 'h3', 'h4']):
            text = tag.get_text().strip()
            if text:
                headings.append(text)
        return headings[:20]  # 限制数量

    def _extract_links(self, soup: Any, base_url: str) -> List[Dict[str, str]]:
        """提取相关链接"""
        if not HAS_BS4 or soup is None:
            return []
        links = []
        for link in soup.find_all('a', href=True)[:50]:  # 限制数量
            href = link['href']
            text = link.get_text().strip()

            # 转换相对链接为绝对链接
            if href.startswith('/'):
                href = urljoin(base_url, href)

            if text and href.startswith('http'):
                links.append({'text': text, 'url': href})

        return links

    def _extract_json_ld(self, soup: Any) -> Dict[str, Any]:
        """提取JSON-LD结构化数据"""
        if not HAS_BS4 or soup is None:
            return {}
        json_ld_data = {}
        for script in soup.find_all('script', type='application/ld+json'):
            try:
                data = json.loads(script.string)
                json_ld_data.update(data)
            except:
                continue
        return json_ld_data

    async def _extract_structured_data(self, web_content: Dict[str, Any], url: str) -> Dict[str, Any]:
        """从网页内容中提取结构化信息"""
        text_content = web_content['text_content']

        # 基础信息提取
        structured = {
            'url': url,
            'title': web_content['title'],
            'domain': urlparse(url).netloc,
            'is_academic': self._is_academic_site(url, text_content),
            'language': self._detect_language(text_content),
            'content_length': len(text_content),
            'headings': web_content['headings'],
            'meta_description': web_content['meta_description']
        }

        # 学术信息提取
        if structured['is_academic']:
            structured.update(await self._extract_academic_info(text_content, web_content))

        # 研究领域识别
        structured['research_areas'] = self._identify_research_areas(text_content)

        # 单细胞相关性分析
        structured['single_cell_relevance'] = self._analyze_single_cell_relevance(text_content)

        # 技术关键词提取
        structured['technical_keywords'] = self._extract_technical_keywords(text_content)

        return structured

    async def _enhance_with_keyword_search(self, structured_data: Dict[str, Any]) -> Dict[str, Any]:
        """使用关键词搜索增强结构化数据"""
        try:
            # 从结构化数据中提取文本内容
            text_content = structured_data.get('title', '') + ' ' + \
                          ' '.join(structured_data.get('headings', [])) + ' ' + \
                          ' '.join(structured_data.get('research_interests', []))

            # 提取关键词
            keywords = self.keyword_search_service.extract_keywords(text_content, 'web')

            # 执行学术信息搜索
            search_results = await self.keyword_search_service.search_academic_info(keywords)

            # 将搜索结果集成到结构化数据中
            structured_data['extracted_keywords'] = keywords
            structured_data['search_results'] = search_results
            structured_data['enhanced_analysis'] = True

            logger.info(f"关键词搜索完成，提取到 {len(keywords.get('persons', []))} 个人名，"
                       f"{len(keywords.get('institutions', []))} 个机构")

        except Exception as e:
            logger.error(f"关键词搜索增强失败: {e}")
            structured_data['enhanced_analysis'] = False
            structured_data['search_error'] = str(e)

        return structured_data

    def _is_academic_site(self, url: str, content: str) -> bool:
        """判断是否为学术网站"""
        domain = urlparse(url).netloc.lower()
        content_lower = content.lower()

        # 检查域名
        for pattern in self.academic_patterns['university_domains']:
            if pattern in domain:
                return True

        # 检查学术平台
        for platform in self.academic_patterns['academic_platforms']:
            if platform in domain:
                return True

        # 检查内容关键词
        academic_keyword_count = sum(
            1 for keyword in self.academic_patterns['research_keywords']
            if keyword in content_lower
        )

        return academic_keyword_count >= 3

    def _detect_language(self, text: str) -> str:
        """简单的语言检测"""
        # 简化的中英文检测
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))

        if chinese_chars > english_chars:
            return 'zh'
        else:
            return 'en'

    async def _extract_academic_info(self, text_content: str, web_content: Dict[str, Any]) -> Dict[str, Any]:
        """提取学术相关信息"""
        academic_info = {}

        # 提取职位信息
        academic_info['positions'] = self._extract_positions(text_content)

        # 提取机构信息
        academic_info['institutions'] = self._extract_institutions(text_content)

        # 提取教育背景
        academic_info['education'] = self._extract_education(text_content)

        # 提取研究兴趣
        academic_info['research_interests'] = self._extract_research_interests(text_content)

        # 提取发表信息
        academic_info['publications'] = self._extract_publication_info(text_content)

        # 提取联系信息
        academic_info['contact_info'] = self._extract_contact_info(text_content)

        return academic_info

    def _extract_positions(self, text: str) -> List[str]:
        """提取职位信息"""
        position_patterns = [
            r'(professor|prof\.?)\s+(?:of\s+)?([a-zA-Z\s]+)',
            r'(principal\s+investigator|PI)',
            r'(director|head)\s+(?:of\s+)?([a-zA-Z\s]+)',
            r'(assistant|associate|full)\s+professor',
            r'(postdoc|postdoctoral)\s+(?:fellow|researcher)',
            r'(graduate\s+student|phd\s+student)',
            r'(research\s+scientist|scientist)',
            r'(lab\s+manager|laboratory\s+manager)'
        ]

        positions = []
        text_lower = text.lower()

        for pattern in position_patterns:
            matches = re.findall(pattern, text_lower, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    position = ' '.join(filter(None, match)).strip()
                else:
                    position = match.strip()

                if position and len(position) > 2:
                    positions.append(position.title())

        return list(set(positions))[:5]  # 去重并限制数量

    def _extract_institutions(self, text: str) -> List[str]:
        """提取机构信息"""
        institution_patterns = [
            r'(university\s+of\s+[a-zA-Z\s]+)',
            r'([a-zA-Z\s]+\s+university)',
            r'([a-zA-Z\s]+\s+institute)',
            r'([a-zA-Z\s]+\s+college)',
            r'([a-zA-Z\s]+\s+hospital)',
            r'([a-zA-Z\s]+\s+medical\s+center)'
        ]

        institutions = []
        for pattern in institution_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                institution = match.strip()
                if len(institution) > 5 and len(institution) < 100:
                    institutions.append(institution.title())

        return list(set(institutions))[:3]

    def _extract_education(self, text: str) -> List[str]:
        """提取教育背景"""
        education_patterns = [
            r'(ph\.?d\.?|phd|doctorate)\s+(?:in\s+)?([a-zA-Z\s]+)',
            r'(master[\'s]?|m\.?s\.?|m\.?a\.?)\s+(?:in\s+)?([a-zA-Z\s]+)',
            r'(bachelor[\'s]?|b\.?s\.?|b\.?a\.?)\s+(?:in\s+)?([a-zA-Z\s]+)',
            r'(graduated|degree)\s+(?:from\s+)?([a-zA-Z\s]+)',
            r'(education|studied)\s+(?:at\s+)?([a-zA-Z\s]+)'
        ]

        education = []
        text_lower = text.lower()

        for pattern in education_patterns:
            matches = re.findall(pattern, text_lower, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    edu_info = ' '.join(filter(None, match)).strip()
                else:
                    edu_info = match.strip()

                if edu_info and len(edu_info) > 3 and len(edu_info) < 100:
                    education.append(edu_info.title())

        return list(set(education))[:5]  # 去重并限制数量

    def _extract_research_interests(self, text: str) -> List[str]:
        """提取研究兴趣"""
        # 查找研究兴趣相关段落
        interest_sections = re.findall(
            r'(?:research\s+interest|research\s+area|research\s+focus)[s]?[:\-]?\s*([^.]{10,200})',
            text, re.IGNORECASE
        )

        interests = []
        for section in interest_sections:
            # 分割并清理
            items = re.split(r'[,;]', section)
            for item in items:
                item = item.strip()
                if len(item) > 5 and len(item) < 100:
                    interests.append(item)

        return interests[:10]

    def _identify_research_areas(self, text: str) -> Dict[str, float]:
        """识别研究领域"""
        research_areas = {
            'single_cell_genomics': 0.0,
            'computational_biology': 0.0,
            'cancer_research': 0.0,
            'immunology': 0.0,
            'neuroscience': 0.0,
            'developmental_biology': 0.0,
            'systems_biology': 0.0,
            'bioinformatics': 0.0
        }

        text_lower = text.lower()

        # 单细胞基因组学
        sc_keywords = ['single cell', 'scrna', 'scatac', '10x genomics', 'single nucleus']
        research_areas['single_cell_genomics'] = sum(text_lower.count(kw) for kw in sc_keywords) / 10

        # 计算生物学
        comp_keywords = ['computational', 'bioinformatics', 'algorithm', 'machine learning', 'data analysis']
        research_areas['computational_biology'] = sum(text_lower.count(kw) for kw in comp_keywords) / 10

        # 癌症研究
        cancer_keywords = ['cancer', 'tumor', 'oncology', 'metastasis', 'chemotherapy']
        research_areas['cancer_research'] = sum(text_lower.count(kw) for kw in cancer_keywords) / 10

        # 免疫学
        immuno_keywords = ['immune', 'immunology', 'antibody', 't cell', 'b cell']
        research_areas['immunology'] = sum(text_lower.count(kw) for kw in immuno_keywords) / 10

        # 神经科学
        neuro_keywords = ['neuroscience', 'brain', 'neuron', 'neural', 'cognitive']
        research_areas['neuroscience'] = sum(text_lower.count(kw) for kw in neuro_keywords) / 10

        # 发育生物学
        dev_keywords = ['development', 'embryo', 'differentiation', 'stem cell', 'organoid']
        research_areas['developmental_biology'] = sum(text_lower.count(kw) for kw in dev_keywords) / 10

        # 归一化分数
        max_score = max(research_areas.values()) if research_areas.values() else 1
        if max_score > 0:
            research_areas = {k: min(v / max_score, 1.0) for k, v in research_areas.items()}

        return research_areas

    def _analyze_single_cell_relevance(self, text: str) -> Dict[str, Any]:
        """分析单细胞相关性"""
        text_lower = text.lower()

        relevance_analysis = {
            'overall_score': 0.0,
            'technology_mentions': [],
            'application_areas': [],
            'analysis_tools': [],
            'experience_level': 'unknown'
        }

        # 技术提及
        for tech in self.single_cell_keywords['technologies']:
            if tech.lower() in text_lower:
                relevance_analysis['technology_mentions'].append(tech)
                relevance_analysis['overall_score'] += 0.2

        # 应用领域
        for app in self.single_cell_keywords['applications']:
            if app.lower() in text_lower:
                relevance_analysis['application_areas'].append(app)
                relevance_analysis['overall_score'] += 0.1

        # 分析工具
        for tool in self.single_cell_keywords['analysis_tools']:
            if tool.lower() in text_lower:
                relevance_analysis['analysis_tools'].append(tool)
                relevance_analysis['overall_score'] += 0.15

        # 经验水平评估
        if relevance_analysis['overall_score'] > 1.0:
            relevance_analysis['experience_level'] = 'expert'
        elif relevance_analysis['overall_score'] > 0.5:
            relevance_analysis['experience_level'] = 'intermediate'
        elif relevance_analysis['overall_score'] > 0.2:
            relevance_analysis['experience_level'] = 'beginner'

        relevance_analysis['overall_score'] = min(relevance_analysis['overall_score'], 1.0)

        return relevance_analysis

    def _extract_technical_keywords(self, text: str) -> List[str]:
        """提取技术关键词"""
        # 生物技术相关关键词模式
        tech_patterns = [
            r'\b(RNA-seq|DNA-seq|ChIP-seq|ATAC-seq|Hi-C)\b',
            r'\b(CRISPR|PCR|qPCR|Western blot|ELISA)\b',
            r'\b(Python|R|MATLAB|Perl|Java|C\+\+)\b',
            r'\b(Docker|Kubernetes|AWS|GCP|Linux)\b',
            r'\b(TensorFlow|PyTorch|scikit-learn|pandas)\b'
        ]

        keywords = []
        for pattern in tech_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            keywords.extend(matches)

        return list(set(keywords))[:20]

    def _extract_publication_info(self, text: str) -> Dict[str, Any]:
        """提取发表信息"""
        pub_info = {
            'publication_count': 0,
            'recent_publications': [],
            'journals': [],
            'h_index': None
        }

        # 查找发表数量
        pub_count_patterns = [
            r'(\d+)\s+publications?',
            r'published\s+(\d+)\s+papers?',
            r'(\d+)\s+peer[- ]reviewed\s+articles?'
        ]

        for pattern in pub_count_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                pub_info['publication_count'] = max(int(m) for m in matches)
                break

        # 查找h-index
        h_index_match = re.search(r'h[- ]?index[:\s]*(\d+)', text, re.IGNORECASE)
        if h_index_match:
            pub_info['h_index'] = int(h_index_match.group(1))

        return pub_info

    def _extract_contact_info(self, text: str) -> Dict[str, str]:
        """提取联系信息"""
        contact = {}

        # 邮箱
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        if emails:
            contact['email'] = emails[0]

        # 电话
        phone_pattern = r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'
        phones = re.findall(phone_pattern, text)
        if phones:
            contact['phone'] = '-'.join(phones[0])

        return contact

    def _extract_education(self, text: str) -> List[str]:
        """提取教育背景信息"""
        education = []

        # 学位模式
        degree_patterns = [
            r'(Ph\.?D\.?|PhD|Doctor of Philosophy)\s+in\s+([^,\n\.]+)',
            r'(M\.?S\.?|MS|Master of Science)\s+in\s+([^,\n\.]+)',
            r'(B\.?S\.?|BS|Bachelor of Science)\s+in\s+([^,\n\.]+)',
            r'(M\.?A\.?|MA|Master of Arts)\s+in\s+([^,\n\.]+)',
            r'(B\.?A\.?|BA|Bachelor of Arts)\s+in\s+([^,\n\.]+)',
            r'(M\.?D\.?|MD|Doctor of Medicine)',
            r'(Postdoc|Postdoctoral)\s+(?:Fellow|Research|Training)?'
        ]

        for pattern in degree_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    if len(match) > 1:
                        education.append(f"{match[0]} in {match[1]}")
                    else:
                        education.append(match[0])
                else:
                    education.append(match)

        # 大学/机构模式
        university_patterns = [
            r'graduated from\s+([^,\n\.]+)',
            r'studied at\s+([^,\n\.]+)',
            r'educated at\s+([^,\n\.]+)',
            r'degree from\s+([^,\n\.]+)'
        ]

        for pattern in university_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                education.append(f"Education at {match}")

        return list(set(education))[:5]  # 最多返回5个教育背景

    async def _analyze_text_input(self, text_input: str) -> Dict[str, Any]:
        """分析文本输入"""
        structured_data = {
            'source_type': 'manual_input',
            'content': text_input,
            'language': self._detect_language(text_input),
            'content_length': len(text_input),
            'is_academic': 'research' in text_input.lower() or 'university' in text_input.lower()
        }

        # 提取学术信息
        if structured_data['is_academic']:
            structured_data.update(await self._extract_academic_info(text_input, {}))

        # 研究领域识别
        structured_data['research_areas'] = self._identify_research_areas(text_input)

        # 单细胞相关性分析
        structured_data['single_cell_relevance'] = self._analyze_single_cell_relevance(text_input)

        return structured_data

    async def _ai_analyze_content(self, structured_data: Dict[str, Any]) -> Dict[str, Any]:
        """使用AI分析内容并生成画像洞察"""
        try:
            # 构建AI分析提示
            analysis_prompt = self._build_analysis_prompt(structured_data)

            # 暂时跳过AI分析，直接使用基于规则的分析
            logger.info("暂时跳过AI分析，使用基于规则的分析")
            return self._fallback_analysis(structured_data)

        except Exception as e:
            logger.error(f"分析失败: {e}")
            return self._fallback_analysis(structured_data)

    def _build_analysis_prompt(self, structured_data: Dict[str, Any]) -> str:
        """构建AI分析提示"""
        prompt = f"""
请基于以下信息分析这个潜在客户的画像，重点关注单细胞测序业务的相关性：

## 基础信息
- 来源类型: {structured_data.get('source_type', '网页')}
- 是否学术机构: {structured_data.get('is_academic', False)}
- 内容语言: {structured_data.get('language', 'unknown')}

## 学术背景
- 职位: {structured_data.get('positions', [])}
- 机构: {structured_data.get('institutions', [])}
- 研究兴趣: {structured_data.get('research_interests', [])}

## 研究领域分析
{json.dumps(structured_data.get('research_areas', {}), indent=2, ensure_ascii=False)}

## 单细胞相关性
{json.dumps(structured_data.get('single_cell_relevance', {}), indent=2, ensure_ascii=False)}

## 技术关键词
{structured_data.get('technical_keywords', [])}

请从以下维度进行分析并以JSON格式返回：

1. **研究成熟度** (beginner/intermediate/expert)
2. **技术专长评估** (包括平台偏好、分析能力)
3. **预算能力推断** (low/medium/high/premium)
4. **决策影响力** (advisor/influencer/decision_maker)
5. **业务机会评估** (潜在需求、合作可能性)
6. **沟通策略建议** (技术深度、关注点)
7. **风险因素识别** (竞争对手、预算限制等)

请确保分析客观、专业，并提供具体的商业洞察。
"""
        return prompt

    def _parse_ai_analysis(self, ai_content: str) -> Dict[str, Any]:
        """解析AI分析结果"""
        try:
            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', ai_content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                # 如果没有JSON，解析文本内容
                return self._parse_text_analysis(ai_content)

        except Exception as e:
            logger.error(f"解析AI分析结果失败: {e}")
            return {"raw_analysis": ai_content}

    def _parse_text_analysis(self, text: str) -> Dict[str, Any]:
        """解析文本格式的分析结果"""
        analysis = {
            "research_maturity": "unknown",
            "technical_expertise": {},
            "budget_capacity": "unknown",
            "decision_influence": "unknown",
            "business_opportunity": {},
            "communication_strategy": {},
            "risk_factors": []
        }

        # 简单的文本解析逻辑
        text_lower = text.lower()

        # 研究成熟度
        if any(word in text_lower for word in ['expert', 'professor', 'senior', 'principal']):
            analysis["research_maturity"] = "expert"
        elif any(word in text_lower for word in ['intermediate', 'associate', 'experienced']):
            analysis["research_maturity"] = "intermediate"
        else:
            analysis["research_maturity"] = "beginner"

        # 预算能力
        if any(word in text_lower for word in ['high budget', 'well-funded', 'premium']):
            analysis["budget_capacity"] = "high"
        elif any(word in text_lower for word in ['medium budget', 'moderate']):
            analysis["budget_capacity"] = "medium"
        else:
            analysis["budget_capacity"] = "low"

        return analysis

    def _fallback_analysis(self, structured_data: Dict[str, Any]) -> Dict[str, Any]:
        """基于规则的后备分析"""
        analysis = {
            "research_maturity": "unknown",
            "technical_expertise": {},
            "budget_capacity": "unknown",
            "decision_influence": "unknown",
            "business_opportunity": {},
            "communication_strategy": {},
            "risk_factors": []
        }

        # 基于职位判断研究成熟度
        positions = structured_data.get('positions', [])
        position_text = ' '.join(positions).lower()

        if any(word in position_text for word in ['professor', 'principal investigator', 'director', 'head']):
            analysis["research_maturity"] = "expert"
        elif any(word in position_text for word in ['associate', 'assistant', 'senior']):
            analysis["research_maturity"] = "intermediate"
        else:
            analysis["research_maturity"] = "beginner"

        # 基于机构类型判断预算能力
        institutions = structured_data.get('institutions', [])
        institution_text = ' '.join(institutions).lower()

        if any(word in institution_text for word in ['harvard', 'mit', 'stanford', 'university']):
            analysis["budget_capacity"] = "high"
        elif any(word in institution_text for word in ['college', 'institute']):
            analysis["budget_capacity"] = "medium"
        else:
            analysis["budget_capacity"] = "low"

        # 基于单细胞相关性判断业务机会
        sc_relevance = structured_data.get('single_cell_relevance', {})
        sc_score = sc_relevance.get('overall_score', 0)

        if sc_score > 0.7:
            analysis["business_opportunity"] = {"level": "high", "potential": "immediate"}
        elif sc_score > 0.3:
            analysis["business_opportunity"] = {"level": "medium", "potential": "short_term"}
        else:
            analysis["business_opportunity"] = {"level": "low", "potential": "long_term"}

        return analysis

    def _generate_key_insights(self, structured_data: Dict[str, Any], ai_analysis: Dict[str, Any]) -> List[str]:
        """生成关键洞察"""
        insights = []

        # 基于研究成熟度的洞察
        maturity = ai_analysis.get('research_maturity', 'unknown')
        if maturity == 'expert':
            insights.append("该客户是领域专家，具有深厚的研究背景和技术理解能力")
        elif maturity == 'intermediate':
            insights.append("该客户具有中等研究经验，对新技术有一定了解")
        elif maturity == 'beginner':
            insights.append("该客户是研究新手，需要基础技术支持和教育")

        # 基于单细胞相关性的洞察
        sc_relevance = structured_data.get('single_cell_relevance', {})
        sc_score = sc_relevance.get('overall_score', 0)
        if sc_score > 0.7:
            insights.append("高度相关的单细胞研究背景，是理想的目标客户")
        elif sc_score > 0.3:
            insights.append("具有一定的单细胞研究相关性，有潜在合作机会")
        else:
            insights.append("单细胞研究相关性较低，需要教育和引导")

        # 基于预算能力的洞察
        budget = ai_analysis.get('budget_capacity', 'unknown')
        if budget == 'high':
            insights.append("预算充足，可以考虑高端解决方案")
        elif budget == 'medium':
            insights.append("预算适中，适合标准化产品和服务")
        elif budget == 'low':
            insights.append("预算有限，需要提供性价比高的解决方案")

        # 基于机构类型的洞察
        institutions = structured_data.get('institutions', [])
        if institutions:
            if any('university' in inst.lower() for inst in institutions):
                insights.append("来自知名学术机构，具有长期合作潜力")
            elif any('hospital' in inst.lower() for inst in institutions):
                insights.append("医疗机构背景，关注临床应用和转化研究")

        # 基于技术能力的洞察
        analysis_capability = self._assess_analysis_capability(structured_data)
        if analysis_capability == 'advanced':
            insights.append("具备高级数据分析能力，可以独立处理复杂项目")
        elif analysis_capability == 'intermediate':
            insights.append("具备中等数据分析能力，可能需要部分技术支持")
        else:
            insights.append("数据分析能力有限，需要全面的技术支持服务")

        return insights[:5]  # 最多返回5个关键洞察

    async def _build_comprehensive_profile(self, url: str, structured_data: Dict[str, Any],
                                         ai_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """构建综合画像"""
        # 生成关键洞察
        key_insights = self._generate_key_insights(structured_data, ai_analysis)

        # 生成行动建议
        action_recommendations = self._generate_action_recommendations(structured_data, ai_analysis)

        profile = {
            # 前端期望的字段
            "analysis_id": f"analysis_{int(datetime.now(timezone.utc).timestamp())}",
            "confidence_score": self._calculate_confidence_score(structured_data, ai_analysis),
            "key_insights": key_insights,
            "recommendations": [rec["action"] for rec in action_recommendations],

            # 详细的画像数据
            "profile_data": {
                "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
                "source_url": url,

                # 基础信息
                "basic_info": {
                    "source_type": structured_data.get('source_type', 'web'),
                    "is_academic": structured_data.get('is_academic', False),
                    "language": structured_data.get('language', 'unknown'),
                    "institutions": structured_data.get('institutions', []),
                    "positions": structured_data.get('positions', []),
                    "contact_info": structured_data.get('contact_info', {})
                },

                # 研究画像
                "research_profile": {
                    "maturity_level": ai_analysis.get('research_maturity', 'unknown'),
                    "research_areas": structured_data.get('research_areas', {}),
                    "research_interests": structured_data.get('research_interests', []),
                    "publication_info": structured_data.get('publications', {}),
                    "single_cell_relevance": structured_data.get('single_cell_relevance', {})
                },

                # 技术画像
                "technical_profile": {
                    "expertise_assessment": ai_analysis.get('technical_expertise', {}),
                    "technical_keywords": structured_data.get('technical_keywords', []),
                    "platform_experience": self._assess_platform_experience(structured_data),
                    "analysis_capability": self._assess_analysis_capability(structured_data)
                },

                # 商业画像
                "business_profile": {
                    "budget_capacity": ai_analysis.get('budget_capacity', 'unknown'),
                    "decision_influence": ai_analysis.get('decision_influence', 'unknown'),
                    "business_opportunity": ai_analysis.get('business_opportunity', {}),
                    "potential_value": self._calculate_potential_value(structured_data, ai_analysis)
                },

                # 沟通策略
                "communication_strategy": {
                    "recommended_approach": ai_analysis.get('communication_strategy', {}),
                    "technical_depth": self._recommend_technical_depth(structured_data, ai_analysis),
                    "key_focus_areas": self._identify_focus_areas(structured_data),
                    "preferred_channels": self._suggest_communication_channels(structured_data)
                },

                # 风险评估
                "risk_assessment": {
                    "identified_risks": ai_analysis.get('risk_factors', []),
                    "competition_risk": self._assess_competition_risk(structured_data),
                    "budget_risk": self._assess_budget_risk(ai_analysis),
                    "timeline_risk": self._assess_timeline_risk(structured_data)
                },

                # 行动建议详情
                "action_recommendations": action_recommendations
            }
        }

        return profile

    def _calculate_confidence_score(self, structured_data: Dict[str, Any],
                                  ai_analysis: Dict[str, Any]) -> float:
        """计算画像置信度"""
        confidence = 0.0

        # 基于数据完整性
        if structured_data.get('is_academic'):
            confidence += 0.2
        if structured_data.get('institutions'):
            confidence += 0.15
        if structured_data.get('positions'):
            confidence += 0.15
        if structured_data.get('research_interests'):
            confidence += 0.1
        if structured_data.get('contact_info'):
            confidence += 0.1

        # 基于单细胞相关性
        sc_relevance = structured_data.get('single_cell_relevance', {})
        if sc_relevance.get('overall_score', 0) > 0.5:
            confidence += 0.2
        elif sc_relevance.get('overall_score', 0) > 0.2:
            confidence += 0.1

        # 基于AI分析质量
        if 'error' not in ai_analysis:
            confidence += 0.1

        return min(confidence, 1.0)

    def _assess_platform_experience(self, structured_data: Dict[str, Any]) -> Dict[str, str]:
        """评估平台经验"""
        tech_keywords = structured_data.get('technical_keywords', [])
        sc_relevance = structured_data.get('single_cell_relevance', {})

        platform_exp = {}

        # 10x Genomics
        if any('10x' in kw.lower() for kw in tech_keywords) or \
           '10x genomics' in sc_relevance.get('technology_mentions', []):
            platform_exp['10x_genomics'] = 'experienced'

        # Smart-seq
        if any('smart' in kw.lower() for kw in tech_keywords) or \
           'smart-seq' in sc_relevance.get('technology_mentions', []):
            platform_exp['smart_seq'] = 'experienced'

        # 分析工具
        analysis_tools = sc_relevance.get('analysis_tools', [])
        if 'seurat' in analysis_tools:
            platform_exp['seurat'] = 'experienced'
        if 'scanpy' in analysis_tools:
            platform_exp['scanpy'] = 'experienced'

        return platform_exp

    def _assess_analysis_capability(self, structured_data: Dict[str, Any]) -> str:
        """评估分析能力"""
        tech_keywords = structured_data.get('technical_keywords', [])
        research_areas = structured_data.get('research_areas', {})

        # 编程技能
        programming_skills = [kw for kw in tech_keywords if kw.lower() in ['python', 'r', 'matlab']]

        # 计算生物学背景
        comp_bio_score = research_areas.get('computational_biology', 0)

        if programming_skills and comp_bio_score > 0.5:
            return 'advanced'
        elif programming_skills or comp_bio_score > 0.3:
            return 'intermediate'
        else:
            return 'basic'

    def _calculate_potential_value(self, structured_data: Dict[str, Any],
                                 ai_analysis: Dict[str, Any]) -> str:
        """计算潜在价值"""
        value_score = 0

        # 学术地位
        positions = structured_data.get('positions', [])
        if any('professor' in pos.lower() for pos in positions):
            value_score += 3
        elif any('director' in pos.lower() or 'head' in pos.lower() for pos in positions):
            value_score += 2

        # 单细胞相关性
        sc_score = structured_data.get('single_cell_relevance', {}).get('overall_score', 0)
        value_score += sc_score * 3

        # 预算能力
        budget = ai_analysis.get('budget_capacity', 'low')
        if budget == 'high':
            value_score += 3
        elif budget == 'medium':
            value_score += 2

        if value_score >= 6:
            return 'high'
        elif value_score >= 3:
            return 'medium'
        else:
            return 'low'

    def _recommend_technical_depth(self, structured_data: Dict[str, Any],
                                 ai_analysis: Dict[str, Any]) -> str:
        """推荐技术深度"""
        maturity = ai_analysis.get('research_maturity', 'unknown')
        analysis_capability = self._assess_analysis_capability(structured_data)

        if maturity == 'expert' and analysis_capability == 'advanced':
            return 'deep_technical'
        elif maturity in ['expert', 'intermediate'] or analysis_capability in ['advanced', 'intermediate']:
            return 'moderate_technical'
        else:
            return 'basic_overview'

    def _identify_focus_areas(self, structured_data: Dict[str, Any]) -> List[str]:
        """识别关注重点"""
        focus_areas = []

        research_areas = structured_data.get('research_areas', {})

        # 根据研究领域确定关注点
        for area, score in research_areas.items():
            if score > 0.5:
                if area == 'single_cell_genomics':
                    focus_areas.append('单细胞技术平台')
                elif area == 'computational_biology':
                    focus_areas.append('数据分析解决方案')
                elif area == 'cancer_research':
                    focus_areas.append('肿瘤研究应用')
                elif area == 'immunology':
                    focus_areas.append('免疫学应用')

        return focus_areas[:3]  # 最多3个重点

    def _suggest_communication_channels(self, structured_data: Dict[str, Any]) -> List[str]:
        """建议沟通渠道"""
        channels = []

        if structured_data.get('contact_info', {}).get('email'):
            channels.append('邮件联系')

        if structured_data.get('is_academic'):
            channels.extend(['学术会议', '研讨会', '技术讲座'])

        return channels

    def _assess_competition_risk(self, structured_data: Dict[str, Any]) -> str:
        """评估竞争风险"""
        # 简化的竞争风险评估
        tech_keywords = structured_data.get('technical_keywords', [])

        # 如果已经使用竞争对手技术
        competitor_tech = ['illumina', 'thermo fisher', 'bio-rad']
        if any(comp in ' '.join(tech_keywords).lower() for comp in competitor_tech):
            return 'high'
        else:
            return 'low'

    def _assess_budget_risk(self, ai_analysis: Dict[str, Any]) -> str:
        """评估预算风险"""
        budget_capacity = ai_analysis.get('budget_capacity', 'unknown')

        if budget_capacity == 'low':
            return 'high'
        elif budget_capacity == 'medium':
            return 'medium'
        else:
            return 'low'

    def _assess_timeline_risk(self, structured_data: Dict[str, Any]) -> str:
        """评估时间线风险"""
        # 基于学术周期评估
        if structured_data.get('is_academic'):
            return 'medium'  # 学术项目通常周期较长
        else:
            return 'low'

    def _generate_action_recommendations(self, structured_data: Dict[str, Any],
                                       ai_analysis: Dict[str, Any]) -> List[Dict[str, str]]:
        """生成行动建议"""
        recommendations = []

        # 基于研究成熟度的建议
        maturity = ai_analysis.get('research_maturity', 'unknown')
        if maturity == 'expert':
            recommendations.append({
                "action": "安排技术专家对接",
                "priority": "high",
                "timeline": "1周内"
            })
        elif maturity == 'intermediate':
            recommendations.append({
                "action": "提供技术方案演示",
                "priority": "medium",
                "timeline": "2周内"
            })
        else:
            recommendations.append({
                "action": "发送基础教育材料",
                "priority": "low",
                "timeline": "1个月内"
            })

        # 基于单细胞相关性的建议
        sc_relevance = structured_data.get('single_cell_relevance', {})
        if sc_relevance.get('overall_score', 0) > 0.7:
            recommendations.append({
                "action": "邀请参与单细胞技术研讨会",
                "priority": "high",
                "timeline": "即时"
            })

        # 基于预算能力的建议
        budget = ai_analysis.get('budget_capacity', 'unknown')
        if budget == 'high':
            recommendations.append({
                "action": "准备定制化解决方案",
                "priority": "high",
                "timeline": "1周内"
            })

        return recommendations

    async def _merge_multiple_analyses(self, analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """合并多个分析结果"""
        if not analyses:
            return {"error": "没有有效的分析结果"}

        if len(analyses) == 1:
            return analyses[0]

        # 合并逻辑
        merged_profile = {
            "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
            "source_count": len(analyses),
            "confidence_score": sum(a.get('confidence_score', 0) for a in analyses) / len(analyses),
            "merged_sources": [a.get('source_url', 'unknown') for a in analyses]
        }

        # 合并各个维度的信息
        for section in ['basic_info', 'research_profile', 'technical_profile', 'business_profile']:
            merged_profile[section] = self._merge_section_data(analyses, section)

        # 重新生成综合建议
        merged_profile['action_recommendations'] = self._generate_merged_recommendations(analyses)

        return merged_profile

    def _merge_section_data(self, analyses: List[Dict[str, Any]], section: str) -> Dict[str, Any]:
        """合并特定部分的数据"""
        merged_data = {}

        for analysis in analyses:
            section_data = analysis.get(section, {})
            for key, value in section_data.items():
                if key not in merged_data:
                    merged_data[key] = value
                elif isinstance(value, list):
                    if isinstance(merged_data[key], list):
                        merged_data[key].extend(value)
                        merged_data[key] = list(set(merged_data[key]))  # 去重
                elif isinstance(value, dict):
                    if isinstance(merged_data[key], dict):
                        merged_data[key].update(value)

        return merged_data

    def _generate_merged_recommendations(self, analyses: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """生成合并后的建议"""
        all_recommendations = []

        for analysis in analyses:
            recommendations = analysis.get('action_recommendations', [])
            all_recommendations.extend(recommendations)

        # 去重并按优先级排序
        unique_recommendations = []
        seen_actions = set()

        for rec in all_recommendations:
            action = rec.get('action', '')
            if action not in seen_actions:
                unique_recommendations.append(rec)
                seen_actions.add(action)

        # 按优先级排序
        priority_order = {'high': 0, 'medium': 1, 'low': 2}
        unique_recommendations.sort(key=lambda x: priority_order.get(x.get('priority', 'low'), 2))

        return unique_recommendations[:5]  # 最多返回5个建议
