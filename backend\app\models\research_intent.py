"""
研究意图分析数据模型
完全AI驱动的动态研究意图理解和用户画像构建
"""
from sqlalchemy import Column, Integer, String, Text, Float, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Dict, List, Any, Optional

from app.core.database import Base


class UserResearchProfile(Base):
    """用户研究画像模型 - 动态构建的个性化研究背景"""
    __tablename__ = "user_research_profiles"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # AI分析的用户特征
    research_experience_level = Column(String(50))  # novice, intermediate, expert
    primary_research_domains = Column(JSON)  # AI识别的主要研究领域
    technical_proficiency = Column(JSON)  # 技术能力评估
    budget_sensitivity = Column(String(50))  # high, medium, low
    timeline_preferences = Column(JSON)  # 时间偏好分析
    
    # 设备和资源情况 (AI从对话中推断)
    available_platforms = Column(JSON)  # 可用的技术平台
    lab_resources = Column(JSON)  # 实验室资源情况
    collaboration_network = Column(JSON)  # 合作网络
    
    # 学习和适应记录
    interaction_patterns = Column(JSON)  # 用户交互模式
    learning_preferences = Column(JSON)  # 学习偏好
    decision_making_style = Column(JSON)  # 决策风格
    
    # 动态更新字段
    confidence_score = Column(Float, default=0.7)  # 画像准确性评分
    last_analysis_date = Column(DateTime(timezone=True), default=func.now())
    profile_version = Column(Integer, default=1)  # 版本控制
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    research_sessions = relationship("ResearchIntentSession", back_populates="user_profile")


class ResearchIntentSession(Base):
    """研究意图分析会话 - 每次用户交互的完整意图分析"""
    __tablename__ = "research_intent_sessions"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    user_profile_id = Column(Integer, ForeignKey("user_research_profiles.id"))
    session_id = Column(String(100), unique=True, nullable=False)  # UUID
    
    # 用户输入信息
    original_query = Column(Text, nullable=False)  # 用户原始输入
    structured_requirements = Column(JSON)  # 结构化需求信息
    context_information = Column(JSON)  # 上下文信息
    
    # AI分析结果
    intent_analysis_result = Column(JSON, nullable=False)  # 完整的AI意图分析
    research_directions = Column(JSON)  # 生成的研究方向选项
    personalization_factors = Column(JSON)  # 个性化因素
    
    # 用户选择和反馈
    selected_direction_id = Column(String(50))  # 用户选择的研究方向
    user_feedback = Column(JSON)  # 用户反馈和调整
    satisfaction_score = Column(Float)  # 满意度评分
    
    # 分析质量指标
    analysis_confidence = Column(Float, default=0.8)
    processing_time_ms = Column(Integer)  # 处理时间毫秒
    ai_model_version = Column(String(50))  # 使用的AI模型版本
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))
    
    # 关联关系
    user_profile = relationship("UserResearchProfile", back_populates="research_sessions")
    research_directions_detail = relationship("ResearchDirectionAnalysis", back_populates="session")
    keyword_generations = relationship("DynamicKeywordGeneration", back_populates="session")


class ResearchDirectionAnalysis(Base):
    """研究方向详细分析 - AI生成的个性化研究方向"""
    __tablename__ = "research_direction_analyses"
    
    id = Column(Integer, primary_key=True)
    session_id = Column(Integer, ForeignKey("research_intent_sessions.id"), nullable=False)
    direction_id = Column(String(50), nullable=False)  # 方向唯一标识
    
    # 方向基本信息
    direction_title = Column(String(200), nullable=False)
    direction_description = Column(Text)
    research_focus = Column(String(500))  # 研究重点
    
    # AI生成的详细分析
    advantages = Column(JSON)  # 优势列表
    challenges = Column(JSON)  # 挑战和难点
    risk_factors = Column(JSON)  # 风险因素
    attention_points = Column(JSON)  # 注意事项
    
    # 成本和时间分析
    estimated_cost_range = Column(JSON)  # 成本范围分析
    timeline_estimation = Column(JSON)  # 时间线估算
    resource_requirements = Column(JSON)  # 资源需求
    
    # 技术路线
    recommended_platforms = Column(JSON)  # 推荐技术平台
    workflow_steps = Column(JSON)  # 工作流程步骤
    quality_control_points = Column(JSON)  # 质控要点
    
    # 个性化匹配
    user_suitability_score = Column(Float)  # 用户适合度评分
    customization_notes = Column(JSON)  # 个性化调整建议
    
    # 选择状态
    is_selected = Column(Boolean, default=False)
    selection_timestamp = Column(DateTime(timezone=True))
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联关系
    session = relationship("ResearchIntentSession", back_populates="research_directions_detail")
    keyword_generations = relationship("DynamicKeywordGeneration", back_populates="research_direction")


class DynamicKeywordGeneration(Base):
    """动态关键词生成记录 - 基于研究方向的AI关键词生成"""
    __tablename__ = "dynamic_keyword_generations"
    
    id = Column(Integer, primary_key=True)
    session_id = Column(Integer, ForeignKey("research_intent_sessions.id"), nullable=False)
    research_direction_id = Column(Integer, ForeignKey("research_direction_analyses.id"))
    generation_id = Column(String(50), nullable=False)  # 生成批次标识
    
    # 生成上下文
    generation_context = Column(JSON, nullable=False)  # 生成上下文信息
    user_expertise_level = Column(String(50))  # 用户专业水平
    search_depth_preference = Column(String(50))  # 搜索深度偏好
    
    # AI生成的关键词体系
    core_keywords = Column(JSON)  # 核心关键词
    biological_process_keywords = Column(JSON)  # 生物学过程关键词
    technical_method_keywords = Column(JSON)  # 技术方法关键词
    disease_specific_keywords = Column(JSON)  # 疾病特异性关键词
    trending_keywords = Column(JSON)  # 热点趋势关键词
    
    # 搜索策略
    optimized_search_queries = Column(JSON)  # 优化的搜索查询
    search_priority_ranking = Column(JSON)  # 搜索优先级排序
    platform_specific_queries = Column(JSON)  # 平台特定查询
    
    # 质量评估
    keyword_quality_score = Column(Float, default=0.8)
    relevance_confidence = Column(Float)  # 相关性置信度
    completeness_score = Column(Float)  # 完整性评分
    
    # 使用统计
    search_execution_count = Column(Integer, default=0)  # 搜索执行次数
    user_interaction_count = Column(Integer, default=0)  # 用户交互次数
    effectiveness_rating = Column(Float)  # 效果评分
    
    # 生成信息
    generation_method = Column(String(50))  # 生成方法
    ai_prompt_used = Column(Text)  # 使用的AI提示
    processing_time_ms = Column(Integer)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_used_at = Column(DateTime(timezone=True))
    
    # 关联关系
    session = relationship("ResearchIntentSession", back_populates="keyword_generations")
    research_direction = relationship("ResearchDirectionAnalysis", back_populates="keyword_generations")
    search_links = relationship("SmartSearchLink", back_populates="keyword_generation")


class SmartSearchLink(Base):
    """智能搜索链接 - 基于关键词生成的优化搜索链接"""
    __tablename__ = "smart_search_links"
    
    id = Column(Integer, primary_key=True)
    keyword_generation_id = Column(Integer, ForeignKey("dynamic_keyword_generations.id"), nullable=False)
    link_id = Column(String(50), nullable=False)  # 链接唯一标识
    
    # 搜索平台信息
    platform_name = Column(String(50), nullable=False)  # pubmed, google_scholar, semantic_scholar
    platform_display_name = Column(String(100))  # 显示名称
    search_url = Column(Text, nullable=False)  # 完整搜索URL
    
    # 链接优化信息
    search_strategy = Column(String(100))  # 搜索策略类型
    expected_result_count = Column(Integer)  # 预期结果数量
    search_syntax_type = Column(String(50))  # 搜索语法类型
    
    # 质量和效果指标
    relevance_prediction = Column(Float)  # 相关性预测
    link_quality_score = Column(Float)  # 链接质量评分
    user_click_count = Column(Integer, default=0)  # 用户点击次数
    success_rate = Column(Float)  # 成功率
    
    # 个性化调整
    user_preference_weight = Column(Float, default=1.0)  # 用户偏好权重
    customization_notes = Column(JSON)  # 个性化说明
    
    # 状态和维护
    is_active = Column(Boolean, default=True)
    last_tested_at = Column(DateTime(timezone=True))
    test_result_status = Column(String(50))  # 测试结果状态
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    keyword_generation = relationship("DynamicKeywordGeneration", back_populates="search_links")
    usage_analytics = relationship("SearchLinkUsageAnalytics", back_populates="search_link")


class SearchLinkUsageAnalytics(Base):
    """搜索链接使用分析 - 用于持续优化的用户行为数据"""
    __tablename__ = "search_link_usage_analytics"
    
    id = Column(Integer, primary_key=True)
    search_link_id = Column(Integer, ForeignKey("smart_search_links.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    session_id = Column(String(100))  # 会话标识
    
    # 用户行为数据
    click_timestamp = Column(DateTime(timezone=True), server_default=func.now())
    time_spent_on_results = Column(Integer)  # 结果页停留时间(秒)
    papers_accessed = Column(Integer)  # 访问的论文数量
    papers_saved = Column(Integer)  # 保存的论文数量
    
    # 用户反馈
    result_satisfaction = Column(Integer)  # 结果满意度(1-5)
    result_relevance_rating = Column(Integer)  # 相关性评分(1-5)
    found_target_literature = Column(Boolean)  # 是否找到目标文献
    
    # 后续行为
    return_to_system = Column(Boolean, default=False)  # 是否返回系统
    requested_refinement = Column(Boolean, default=False)  # 是否请求优化
    refinement_feedback = Column(JSON)  # 优化反馈内容
    
    # 分析标记
    is_processed = Column(Boolean, default=False)  # 是否已处理用于学习
    analysis_notes = Column(JSON)  # 分析备注
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联关系
    search_link = relationship("SmartSearchLink", back_populates="usage_analytics")


class AIModelPerformanceLog(Base):
    """AI模型性能日志 - 跟踪AI生成质量和持续优化"""
    __tablename__ = "ai_model_performance_logs"
    
    id = Column(Integer, primary_key=True)
    
    # 模型信息
    model_version = Column(String(50), nullable=False)
    model_type = Column(String(50))  # intent_analysis, keyword_generation, direction_generation
    operation_type = Column(String(100))  # 具体操作类型
    
    # 输入输出信息
    input_data_hash = Column(String(100))  # 输入数据哈希
    input_token_count = Column(Integer)  # 输入token数
    output_token_count = Column(Integer)  # 输出token数
    processing_time_ms = Column(Integer)  # 处理时间
    
    # 质量指标
    generation_quality_score = Column(Float)  # 生成质量评分
    user_acceptance_rate = Column(Float)  # 用户接受率
    error_rate = Column(Float)  # 错误率
    
    # 用户反馈统计
    positive_feedback_count = Column(Integer, default=0)
    negative_feedback_count = Column(Integer, default=0)
    improvement_suggestions = Column(JSON)  # 改进建议
    
    # 优化记录
    optimization_applied = Column(Boolean, default=False)
    optimization_notes = Column(JSON)  # 优化记录
    performance_improvement = Column(Float)  # 性能改进幅度
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 索引
    __table_args__ = (
        {'comment': 'AI模型性能跟踪和持续优化数据'}
    )