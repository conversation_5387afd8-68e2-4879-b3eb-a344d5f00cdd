"""
客户画像服务
"""
import json
import logging
from typing import List, Dict, Any, Optional, Tu<PERSON>
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func

from app.models.customer_profile import (
    CustomerProfile, ProfileDimension, BehaviorEvent, RequirementHistory,
    ProfileDimensionType, BehaviorEventType
)
from app.models.user import User
from app.models.conversation import Conversation, Message
from app.models.literature import LiteratureRecommendation
from app.schemas.customer_profile import (
    CustomerProfileCreate, CustomerProfileUpdate, ProfileAnalysisResponse,
    ProfileInsight, CustomerSegmentResponse
)
from app.core.database import get_db

logger = logging.getLogger(__name__)


class CustomerProfileService:
    """客户画像服务类"""

    def __init__(self):
        self.profile_weights = {
            "research_profile": 0.25,
            "technical_profile": 0.25,
            "business_profile": 0.25,
            "behavioral_profile": 0.15,
            "preference_profile": 0.10
        }

    async def get_or_create_profile(self, db: Session, user_id: int) -> CustomerProfile:
        """获取或创建客户画像"""
        try:
            # 尝试获取现有画像
            profile = db.query(CustomerProfile).filter(
                CustomerProfile.user_id == user_id
            ).first()

            if not profile:
                # 创建新画像
                profile = CustomerProfile(
                    user_id=user_id,
                    profile_version="1.0",
                    confidence_score=0.0,
                    completeness_score=0.0
                )
                db.add(profile)
                db.commit()
                db.refresh(profile)

                # 初始化基础画像数据
                await self._initialize_profile(db, profile)

            return profile

        except Exception as e:
            logger.error(f"获取或创建客户画像失败: {e}")
            db.rollback()
            raise

    async def _initialize_profile(self, db: Session, profile: CustomerProfile):
        """初始化画像基础数据"""
        try:
            # 从用户基础信息提取画像数据
            user = db.query(User).filter(User.id == profile.user_id).first()
            if not user:
                return

            # 研究画像初始化
            if user.research_interests:
                research_focus = self._extract_research_focus(user.research_interests)
                profile.research_focus = json.dumps(research_focus, ensure_ascii=False)

            if user.expertise_areas:
                technical_expertise = self._extract_technical_expertise(user.expertise_areas)
                profile.technical_expertise = json.dumps(technical_expertise, ensure_ascii=False)

            # 设置初始成熟度
            profile.research_maturity = "初学者"  # 默认值，后续通过行为分析调整
            profile.engagement_level = "中等"
            profile.learning_style = "文本"

            db.commit()

        except Exception as e:
            logger.error(f"初始化画像数据失败: {e}")
            db.rollback()

    def _extract_research_focus(self, research_interests: str) -> Dict[str, Any]:
        """从研究兴趣中提取研究焦点"""
        # 简化的关键词提取逻辑
        keywords = research_interests.lower()
        focus_areas = []

        # 单细胞技术相关
        if any(term in keywords for term in ["单细胞", "scRNA", "scrna", "single cell"]):
            focus_areas.append("单细胞转录组学")

        # 应用领域相关
        if any(term in keywords for term in ["肿瘤", "癌症", "tumor", "cancer"]):
            focus_areas.append("肿瘤研究")
        if any(term in keywords for term in ["免疫", "immune", "immunology"]):
            focus_areas.append("免疫学")
        if any(term in keywords for term in ["发育", "development", "embryo"]):
            focus_areas.append("发育生物学")
        if any(term in keywords for term in ["神经", "neuro", "brain"]):
            focus_areas.append("神经科学")

        return {
            "primary_areas": focus_areas[:3],  # 最多3个主要领域
            "keywords": research_interests.split(",")[:10],  # 最多10个关键词
            "confidence": 0.6  # 基于文本提取的置信度
        }

    def _extract_technical_expertise(self, expertise_areas: str) -> Dict[str, Any]:
        """从专业领域中提取技术专长"""
        keywords = expertise_areas.lower()
        expertise = {
            "platforms": [],
            "analysis_tools": [],
            "programming_skills": [],
            "level": "初级"
        }

        # 平台技术
        if any(term in keywords for term in ["10x", "chromium"]):
            expertise["platforms"].append("10x Genomics")
        if any(term in keywords for term in ["smart-seq", "smartseq"]):
            expertise["platforms"].append("Smart-seq")

        # 分析工具
        if any(term in keywords for term in ["seurat", "scanpy", "cellranger"]):
            expertise["analysis_tools"].extend(["Seurat", "Scanpy"])

        # 编程技能
        if any(term in keywords for term in ["r", "python", "bioinformatics"]):
            expertise["programming_skills"].extend(["R", "Python"])

        return expertise

    async def analyze_conversation_behavior(self, db: Session, user_id: int) -> Dict[str, Any]:
        """分析对话行为模式"""
        try:
            # 获取用户的对话历史
            conversations = db.query(Conversation).filter(
                Conversation.user_id == user_id
            ).order_by(desc(Conversation.created_at)).limit(50).all()

            if not conversations:
                return {"analysis": "insufficient_data"}

            # 分析对话模式
            total_conversations = len(conversations)
            total_messages = sum(conv.message_count for conv in conversations)
            avg_messages_per_conversation = total_messages / total_conversations if total_conversations > 0 else 0

            # 分析问题类型和复杂度
            question_complexity = self._analyze_question_complexity(conversations)
            communication_style = self._analyze_communication_style(conversations)

            return {
                "total_conversations": total_conversations,
                "avg_messages_per_conversation": avg_messages_per_conversation,
                "question_complexity": question_complexity,
                "communication_style": communication_style,
                "engagement_level": self._calculate_engagement_level(conversations),
                "learning_progression": self._analyze_learning_progression(conversations)
            }

        except Exception as e:
            logger.error(f"分析对话行为失败: {e}")
            return {"analysis": "error", "error": str(e)}

    def _analyze_question_complexity(self, conversations: List[Conversation]) -> str:
        """分析问题复杂度"""
        # 简化的复杂度分析逻辑
        # 实际实现中可以使用NLP技术分析问题的技术深度
        total_messages = sum(conv.message_count for conv in conversations)
        avg_length = total_messages / len(conversations) if conversations else 0

        if avg_length > 10:
            return "高复杂度"
        elif avg_length > 5:
            return "中等复杂度"
        else:
            return "基础复杂度"

    def _analyze_communication_style(self, conversations: List[Conversation]) -> str:
        """分析沟通风格"""
        # 基于对话频率和长度分析沟通风格
        if len(conversations) > 20:
            return "探索型"
        elif len(conversations) > 10:
            return "目标导向"
        else:
            return "谨慎型"

    def _calculate_engagement_level(self, conversations: List[Conversation]) -> str:
        """计算参与度水平"""
        if not conversations:
            return "低"

        # 基于最近活动和对话频率
        recent_conversations = [
            conv for conv in conversations
            if conv.created_at > datetime.utcnow() - timedelta(days=30)
        ]

        if len(recent_conversations) > 10:
            return "高"
        elif len(recent_conversations) > 3:
            return "中"
        else:
            return "低"

    def _analyze_learning_progression(self, conversations: List[Conversation]) -> Dict[str, Any]:
        """分析学习进展"""
        # 简化的学习进展分析
        if len(conversations) < 3:
            return {"stage": "初始探索", "confidence": 0.3}
        elif len(conversations) < 10:
            return {"stage": "深入学习", "confidence": 0.6}
        else:
            return {"stage": "专业应用", "confidence": 0.8}

    async def analyze_requirement_patterns(self, db: Session, user_id: int) -> Dict[str, Any]:
        """分析需求模式"""
        try:
            # 获取需求历史
            requirements = db.query(RequirementHistory).filter(
                RequirementHistory.user_id == user_id
            ).order_by(desc(RequirementHistory.submitted_at)).all()

            if not requirements:
                return {"analysis": "no_requirements"}

            # 分析需求演变
            budget_trend = self._analyze_budget_trend(requirements)
            complexity_trend = self._analyze_complexity_trend(requirements)
            focus_evolution = self._analyze_focus_evolution(requirements)

            return {
                "total_requirements": len(requirements),
                "budget_trend": budget_trend,
                "complexity_trend": complexity_trend,
                "focus_evolution": focus_evolution,
                "decision_pattern": self._analyze_decision_pattern(requirements)
            }

        except Exception as e:
            logger.error(f"分析需求模式失败: {e}")
            return {"analysis": "error", "error": str(e)}

    def _analyze_budget_trend(self, requirements: List[RequirementHistory]) -> Dict[str, Any]:
        """分析预算趋势"""
        budgets = [req.estimated_budget for req in requirements if req.estimated_budget]
        if not budgets:
            return {"trend": "unknown"}

        if len(budgets) == 1:
            return {"trend": "stable", "average": budgets[0]}

        # 简单的趋势分析
        recent_avg = sum(budgets[:3]) / min(3, len(budgets))
        overall_avg = sum(budgets) / len(budgets)

        if recent_avg > overall_avg * 1.2:
            trend = "increasing"
        elif recent_avg < overall_avg * 0.8:
            trend = "decreasing"
        else:
            trend = "stable"

        return {
            "trend": trend,
            "recent_average": recent_avg,
            "overall_average": overall_avg,
            "range": {"min": min(budgets), "max": max(budgets)}
        }

    def _analyze_complexity_trend(self, requirements: List[RequirementHistory]) -> str:
        """分析复杂度趋势"""
        complexities = [req.complexity_score for req in requirements if req.complexity_score]
        if len(complexities) < 2:
            return "insufficient_data"

        recent_complexity = sum(complexities[:3]) / min(3, len(complexities))
        overall_complexity = sum(complexities) / len(complexities)

        if recent_complexity > overall_complexity * 1.1:
            return "increasing_complexity"
        elif recent_complexity < overall_complexity * 0.9:
            return "decreasing_complexity"
        else:
            return "stable_complexity"

    def _analyze_focus_evolution(self, requirements: List[RequirementHistory]) -> Dict[str, Any]:
        """分析研究焦点演变"""
        # 提取所有需求中的研究目标关键词
        all_goals = []
        for req in requirements:
            if req.requirement_data and "researchGoal" in req.requirement_data:
                all_goals.append(req.requirement_data["researchGoal"])

        if not all_goals:
            return {"evolution": "no_data"}

        # 简化的焦点分析
        return {
            "evolution": "expanding" if len(set(all_goals)) > len(all_goals) * 0.7 else "focused",
            "primary_focus": max(set(all_goals), key=all_goals.count) if all_goals else None,
            "focus_diversity": len(set(all_goals)) / len(all_goals) if all_goals else 0
        }

    def _analyze_decision_pattern(self, requirements: List[RequirementHistory]) -> str:
        """分析决策模式"""
        if len(requirements) < 2:
            return "insufficient_data"

        # 基于提交频率分析决策模式
        time_intervals = []
        for i in range(1, len(requirements)):
            interval = (requirements[i-1].submitted_at - requirements[i].submitted_at).days
            time_intervals.append(interval)

        avg_interval = sum(time_intervals) / len(time_intervals)

        if avg_interval < 7:
            return "quick_decision"
        elif avg_interval < 30:
            return "deliberate_decision"
        else:
            return "slow_decision"

    async def update_profile_from_behavior(self, db: Session, user_id: int, behavior_data: Dict[str, Any]):
        """基于行为数据更新画像"""
        try:
            profile = await self.get_or_create_profile(db, user_id)

            # 记录行为事件
            behavior_event = BehaviorEvent(
                profile_id=profile.id,
                user_id=user_id,
                event_type=BehaviorEventType(behavior_data.get("event_type", "page_view")),
                event_name=behavior_data.get("event_name", ""),
                event_data=behavior_data.get("event_data", {}),
                session_id=behavior_data.get("session_id"),
                page_url=behavior_data.get("page_url"),
                session_duration=behavior_data.get("session_duration")
            )
            db.add(behavior_event)

            # 更新画像指标
            await self._update_engagement_metrics(db, profile, behavior_data)

            db.commit()

        except Exception as e:
            logger.error(f"基于行为更新画像失败: {e}")
            db.rollback()

    async def _update_engagement_metrics(self, db: Session, profile: CustomerProfile, behavior_data: Dict[str, Any]):
        """更新参与度指标"""
        # 获取最近30天的行为事件
        recent_events = db.query(BehaviorEvent).filter(
            and_(
                BehaviorEvent.profile_id == profile.id,
                BehaviorEvent.event_timestamp > datetime.utcnow() - timedelta(days=30)
            )
        ).count()

        # 更新参与度水平
        if recent_events > 50:
            profile.engagement_level = "高"
        elif recent_events > 20:
            profile.engagement_level = "中"
        else:
            profile.engagement_level = "低"

    async def update_profile_from_requirements(self, db: Session, user_id: int, requirement_data: Dict[str, Any]):
        """基于需求数据更新画像"""
        try:
            profile = await self.get_or_create_profile(db, user_id)

            # 分析需求复杂度
            complexity_score = self._calculate_requirement_complexity(requirement_data)

            # 提取预算信息
            estimated_budget = self._extract_budget_from_requirements(requirement_data)

            # 创建需求历史记录
            requirement_history = RequirementHistory(
                profile_id=profile.id,
                user_id=user_id,
                requirement_data=requirement_data,
                requirement_type="update",
                completeness_score=requirement_data.get("completeness", 0) / 100,
                complexity_score=complexity_score,
                estimated_budget=estimated_budget
            )
            db.add(requirement_history)

            # 更新画像
            await self._update_profile_from_requirement_analysis(db, profile, requirement_data, complexity_score)

            db.commit()

        except Exception as e:
            logger.error(f"基于需求更新画像失败: {e}")
            db.rollback()

    def _calculate_requirement_complexity(self, requirement_data: Dict[str, Any]) -> float:
        """计算需求复杂度"""
        complexity_score = 0.0

        # 基于填写的字段数量
        filled_fields = sum(1 for value in requirement_data.values() if value)
        complexity_score += min(filled_fields / 10, 0.3)  # 最多0.3分

        # 基于特殊需求
        if requirement_data.get("specialRequirements"):
            complexity_score += 0.2

        # 基于分析类型
        analysis_type = requirement_data.get("analysisType", "")
        if "高级" in analysis_type or "复杂" in analysis_type:
            complexity_score += 0.3
        elif "中级" in analysis_type:
            complexity_score += 0.2

        # 基于预算范围
        budget = requirement_data.get("budget", "")
        if "100万" in budget or "高预算" in budget:
            complexity_score += 0.2

        return min(complexity_score, 1.0)

    def _extract_budget_from_requirements(self, requirement_data: Dict[str, Any]) -> Optional[float]:
        """从需求中提取预算估算"""
        budget_str = requirement_data.get("budget", "")
        if not budget_str:
            return None

        # 简化的预算提取逻辑
        budget_mapping = {
            "5万以下": 30000,
            "5-10万": 75000,
            "10-20万": 150000,
            "20-50万": 350000,
            "50-100万": 750000,
            "100万以上": 1500000
        }

        return budget_mapping.get(budget_str, None)

    async def _update_profile_from_requirement_analysis(self, db: Session, profile: CustomerProfile,
                                                       requirement_data: Dict[str, Any], complexity_score: float):
        """基于需求分析更新画像"""
        # 更新技术复杂度偏好
        if complexity_score > 0.7:
            profile.analysis_complexity = "高级"
        elif complexity_score > 0.4:
            profile.analysis_complexity = "中级"
        else:
            profile.analysis_complexity = "基础"

        # 更新预算范围
        budget_str = requirement_data.get("budget", "")
        if budget_str:
            profile.budget_range = budget_str

        # 更新研究成熟度
        if complexity_score > 0.6 and profile.research_maturity == "初学者":
            profile.research_maturity = "中级"
        elif complexity_score > 0.8 and profile.research_maturity == "中级":
            profile.research_maturity = "专家"

        # 更新完整度评分
        profile.completeness_score = min(profile.completeness_score + 0.1, 1.0)

    async def generate_profile_insights(self, db: Session, user_id: int) -> List[ProfileInsight]:
        """生成画像洞察"""
        try:
            profile = await self.get_or_create_profile(db, user_id)
            insights = []

            # 分析对话行为
            conversation_analysis = await self.analyze_conversation_behavior(db, user_id)
            if conversation_analysis.get("engagement_level") == "高":
                insights.append(ProfileInsight(
                    insight_type="engagement",
                    title="高参与度用户",
                    description="该用户表现出很高的平台参与度，经常进行深度对话",
                    confidence=0.8,
                    supporting_data=conversation_analysis,
                    actionable_recommendations=[
                        "提供高级技术内容",
                        "邀请参与专家讨论",
                        "推荐定制化解决方案"
                    ]
                ))

            # 分析需求模式
            requirement_analysis = await self.analyze_requirement_patterns(db, user_id)
            if requirement_analysis.get("complexity_trend") == "increasing_complexity":
                insights.append(ProfileInsight(
                    insight_type="growth",
                    title="技术需求递增",
                    description="用户的技术需求复杂度呈上升趋势，显示出学习进步",
                    confidence=0.7,
                    supporting_data=requirement_analysis,
                    actionable_recommendations=[
                        "推荐进阶课程",
                        "提供技术咨询服务",
                        "介绍高端解决方案"
                    ]
                ))

            # 预算分析洞察
            budget_trend = requirement_analysis.get("budget_trend", {})
            if budget_trend.get("trend") == "increasing":
                insights.append(ProfileInsight(
                    insight_type="budget",
                    title="预算增长趋势",
                    description="用户的项目预算呈增长趋势，可能准备进行大型项目",
                    confidence=0.6,
                    supporting_data=budget_trend,
                    actionable_recommendations=[
                        "推荐高端服务包",
                        "提供项目管理支持",
                        "安排专属客户经理"
                    ]
                ))

            return insights

        except Exception as e:
            logger.error(f"生成画像洞察失败: {e}")
            return []

    async def get_personalized_recommendations(self, db: Session, user_id: int) -> Dict[str, Any]:
        """获取个性化推荐"""
        try:
            profile = await self.get_or_create_profile(db, user_id)

            recommendations = {
                "content_recommendations": [],
                "service_recommendations": [],
                "learning_path": [],
                "next_best_actions": []
            }

            # 基于研究成熟度推荐内容
            if profile.research_maturity == "初学者":
                recommendations["content_recommendations"].extend([
                    "单细胞测序基础教程",
                    "平台选择指南",
                    "样本制备最佳实践"
                ])
                recommendations["learning_path"] = [
                    "了解基础概念",
                    "学习平台差异",
                    "掌握样本处理",
                    "理解数据分析流程"
                ]
            elif profile.research_maturity == "中级":
                recommendations["content_recommendations"].extend([
                    "高级分析方法",
                    "多组学整合分析",
                    "自定义分析流程"
                ])

            # 基于预算范围推荐服务
            if profile.budget_range in ["50-100万", "100万以上"]:
                recommendations["service_recommendations"].extend([
                    "专属项目管理",
                    "定制化分析方案",
                    "一对一技术支持"
                ])

            # 基于参与度推荐行动
            if profile.engagement_level == "高":
                recommendations["next_best_actions"].extend([
                    "邀请参与beta测试",
                    "推荐专家咨询服务",
                    "提供早期访问权限"
                ])

            return recommendations

        except Exception as e:
            logger.error(f"获取个性化推荐失败: {e}")
            return {}
