'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { intelligentRecommendationApi } from '@/lib/api'
import { 
  ChevronDown, 
  ChevronUp, 
  BookOpen, 
  TrendingUp, 
  Lightbulb, 
  DollarSign,
  Calendar,
  AlertTriangle,
  Star,
  ExternalLink,
  Search,
  Zap,
  Target,
  Network,
  Award,
  Clock,
  Users,
  BarChart3,
  Download,
  Loader2
} from 'lucide-react'

interface ComprehensiveRecommendationPlatformProps {
  requirementData: any
  onRequirementChange?: (data: any) => void
}

export default function ComprehensiveRecommendationPlatform({
  requirementData,
  onRequirementChange
}: ComprehensiveRecommendationPlatformProps) {
  const [recommendation, setRecommendation] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['overview']))

  // 加载智能推荐数据
  const loadRecommendation = async () => {
    if (!requirementData) return
    
    setLoading(true)
    setError(null)
    
    try {
      const response = await intelligentRecommendationApi.getComprehensiveRecommendation(requirementData)
      if (response.success) {
        setRecommendation(response.data)
      } else {
        throw new Error('获取推荐失败')
      }
    } catch (err) {
      console.error('加载智能推荐失败:', err)
      setError(err instanceof Error ? err.message : '加载推荐数据失败，请稍后重试')
      
      // 在错误情况下使用模拟数据作为降级
      setRecommendation(getMockRecommendation())
    } finally {
      setLoading(false)
    }
  }

  // 模拟数据作为降级方案
  const getMockRecommendation = () => ({
    search_queries: {
      primary_query: "rat scRNA-seq heart developmental trajectory analysis",
      suggested_queries: [
        "rat single cell RNA sequencing cardiac development",
        "heart trajectory analysis scRNA-seq pseudotime",
        "cardiac progenitor cells developmental scRNA-seq rat",
        "single cell heart development Rattus norvegicus",
        "cardiomyocyte differentiation trajectory scRNA-seq",
        "rat cardiac development single cell transcriptomics"
      ]
    },
    literature_results: {
      combined_results: [
        {
          title: "Single-cell reconstruction of developmental trajectories during mammalian heart formation",
          authors: "Wang, L. et al.",
          journal: "Nature",
          impact_factor: 64.8,
          relevance_score: 0.95,
          source: "external",
          doi: "10.1038/s41586-2024-07543-x"
        },
        {
          title: "Enhanced single-cell trajectory inference with temporal dynamics", 
          authors: "Chen, M. et al.",
          journal: "Cell",
          impact_factor: 66.8,
          relevance_score: 0.92,
          source: "external",
          doi: "10.1016/j.cell.2024.06.012"
        }
      ]
    },
    expanded_keywords: {
      semantic_expansion: [
        "cardiac progenitor cells", "cardiomyocyte differentiation",
        "heart tube formation", "cardiac neural crest", "second heart field"
      ],
      cross_disciplinary: [
        "epigenetic regulation cardiac development",
        "mechanical forces heart morphogenesis",
        "metabolic reprogramming cardiogenesis"
      ],
      trending_terms: [
        "cardiac organoids 2024", "heart-on-chip development models",
        "AI-driven trajectory prediction", "spatial transcriptomics cardiac"
      ]
    },
    hot_papers: [
      {
        title: "Cardiac organoids reveal early human heart development",
        authors: "Zhang, Y. et al.",
        journal: "Science",
        impact_factor: 63.7,
        trend_score: 9.8,
        reason: "近30天引用激增300%，心脏发育领域突破性进展",
        publication_date: "2024-07-01"
      }
    ],
    tech_recommendations: [
      {
        platform: "10x Genomics Chromium",
        platform_id: "10x_genomics",
        total_estimated_cost: 25000,
        budget_match_score: 0.85,
        suitability_score: 0.92,
        cost_breakdown: {
          sample_preparation: {
            library_prep: 6000,
            reagents: 4000,
            description: "5个样本的文库构建和试剂成本"
          },
          sequencing: {
            sequencing_cost: 10000,
            depth: "50,000 reads/cell",
            description: "基于目标测序深度的测序成本"
          },
          additional_costs: {
            qc_validation: 2000,
            data_analysis: 5000,
            report_generation: 3000,
            description: "质控验证、数据分析和报告生成"
          },
          total_cost: 25000
        },
        timeline_estimate: {
          sample_prep: "5-7 days",
          library_construction: "3-5 days",
          sequencing: "7-10 days", 
          data_analysis: "10-14 days",
          total: "30-43 days"
        },
        advantages: [
          "标准化流程，重现性好",
          "高通量，单次可处理多个样本", 
          "完整的分析pipeline",
          "广泛的技术支持"
        ]
      }
    ],
    project_solution: {
      overview: "本项目旨在通过单细胞RNA测序技术对大鼠心脏组织进行发育轨迹分析研究...",
      expected_outcomes: {
        scientific_discoveries: [
          "构建大鼠心脏发育的高精度单细胞图谱",
          "识别心脏发育关键转录调控网络"
        ],
        publication_potential: {
          primary_paper: "预计发表在Impact Factor 8-15的期刊",
          follow_up_studies: "可衍生2-3个后续深入研究"
        }
      },
      collaboration_opportunities: [
        {
          institution: "中科院发育所",
          contact: "王红梅研究员团队",
          expertise: "心脏发育分子机制研究"
        }
      ]
    },
    risk_assessment: {
      technical_risks: [
        {
          risk: "大鼠心脏组织解离困难",
          probability: "中等",
          impact: "可能导致细胞回收率低或活力下降",
          mitigation: "优化酶解条件，进行预实验"
        }
      ],
      overall_risk_level: "低-中等",
      success_probability: "85-90%"
    }
  })

  useEffect(() => {
    if (requirementData) {
      loadRecommendation()
    }
  }, [requirementData])

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(section)) {
      newExpanded.delete(section)
    } else {
      newExpanded.add(section)
    }
    setExpandedSections(newExpanded)
  }

  const renderOverviewSection = () => (
    <div className="space-y-6">
      {/* 项目概览卡片 */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-blue-500" />
            项目概览
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {requirementData?.speciesType || "大鼠"}
              </div>
              <div className="text-sm text-gray-600">研究物种</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {requirementData?.researchGoal || "发育轨迹分析"}
              </div>
              <div className="text-sm text-gray-600">研究目标</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {requirementData?.sampleType || "心脏组织"}
              </div>
              <div className="text-sm text-gray-600">样本类型</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 推荐平台对比 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5 text-yellow-500" />
            推荐技术平台
          </CardTitle>
        </CardHeader>
        <CardContent>
          {recommendation?.tech_recommendations?.map((tech: any, index: number) => (
            <div key={index} className="border rounded-lg p-4 mb-4 last:mb-0">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="font-semibold text-lg">{tech.platform}</h3>
                  <div className="flex gap-2 mt-1">
                    <Badge variant="secondary">
                      匹配度 {Math.round(tech.suitability_score * 100)}%
                    </Badge>
                    <Badge variant="outline">
                      预算匹配 {Math.round(tech.budget_match_score * 100)}%
                    </Badge>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-green-600">
                    ¥{(tech.total_estimated_cost / 10000).toFixed(1)}万
                  </div>
                  <div className="text-sm text-gray-600">预估总成本</div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">主要优势</h4>
                  <ul className="text-sm space-y-1">
                    {tech.advantages?.slice(0, 3).map((advantage: any, i: number) => (
                      <li key={i} className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        {typeof advantage === 'string' ? advantage : advantage?.name || JSON.stringify(advantage)}
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">项目时间线</h4>
                  <div className="text-sm space-y-1">
                    <div>样本准备: {tech.timeline_estimate?.sample_prep}</div>
                    <div>测序分析: {tech.timeline_estimate?.sequencing}</div>
                    <div className="font-medium text-blue-600">
                      总周期: {tech.timeline_estimate?.total}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 预期成果 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-green-500" />
            预期研究成果
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">科学发现</h4>
              <ul className="space-y-2">
                {recommendation?.project_solution?.expected_outcomes?.scientific_discoveries?.map((discovery: any, index: number) => (
                  <li key={index} className="flex items-start gap-2">
                    <Lightbulb className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{typeof discovery === 'string' ? discovery : discovery?.name || JSON.stringify(discovery)}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-3">发表潜力</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-blue-500" />
                  <span className="text-sm">
                    {recommendation?.project_solution?.expected_outcomes?.publication_potential?.primary_paper}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Network className="h-4 w-4 text-purple-500" />
                  <span className="text-sm">
                    {recommendation?.project_solution?.expected_outcomes?.publication_potential?.follow_up_studies}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderLiteratureSection = () => (
    <div className="space-y-6">
      {/* 智能查询生成 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5 text-blue-500" />
            智能查询生成
          </CardTitle>
          <CardDescription>
            基于您的需求自动生成的优化搜索查询
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <h4 className="font-medium mb-2">主要查询</h4>
            <div className="p-3 bg-blue-50 rounded-lg font-mono text-sm">
              {recommendation?.search_queries?.primary_query}
            </div>
          </div>
          <div>
            <h4 className="font-medium mb-2">扩展查询</h4>
            <div className="grid grid-cols-1 gap-2">
              {recommendation?.search_queries?.suggested_queries?.slice(1).map((query: any, index: number) => (
                <div key={index} className="p-2 bg-gray-50 rounded text-sm font-mono">
                  {typeof query === 'string' ? query : query?.name || JSON.stringify(query)}
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 热点文献推荐 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-red-500" />
            热点文献推荐
          </CardTitle>
          <CardDescription>
            最新的高影响力相关文献
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recommendation?.hot_papers?.map((paper: any, index: number) => (
            <div key={index} className="border-l-4 border-l-red-500 pl-4 py-3 mb-4 last:mb-0">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-semibold text-lg leading-tight">{paper.title}</h3>
                <Badge variant="destructive" className="ml-2">
                  热度 {paper.trend_score}
                </Badge>
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                <span>{typeof paper.authors === 'string' ? paper.authors : paper.authors?.name || JSON.stringify(paper.authors)}</span>
                <span>•</span>
                <span className="font-medium">{paper.journal}</span>
                <span>•</span>
                <span>IF: {paper.impact_factor}</span>
              </div>
              <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                <TrendingUp className="h-4 w-4 inline mr-1" />
                {paper.reason}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 关键词扩展 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-yellow-500" />
            智能关键词扩展
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2 text-blue-600">语义扩展</h4>
              <div className="flex flex-wrap gap-2">
                {recommendation?.expanded_keywords?.semantic_expansion?.map((keyword: any, index: number) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {typeof keyword === 'string' ? keyword : keyword?.name || JSON.stringify(keyword)}
                  </Badge>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2 text-green-600">跨学科关键词</h4>
              <div className="flex flex-wrap gap-2">
                {recommendation?.expanded_keywords?.cross_disciplinary?.map((keyword: any, index: number) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {typeof keyword === 'string' ? keyword : keyword?.name || JSON.stringify(keyword)}
                  </Badge>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2 text-purple-600">热点趋势词</h4>
              <div className="flex flex-wrap gap-2">
                {recommendation?.expanded_keywords?.trending_terms?.map((keyword: any, index: number) => (
                  <Badge key={index} variant="default" className="text-xs bg-purple-100 text-purple-800">
                    {typeof keyword === 'string' ? keyword : keyword?.name || JSON.stringify(keyword)}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 权威文献推荐 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-blue-500" />
            权威文献推荐
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-96">
            {recommendation?.literature_results?.combined_results?.map((paper: any, index: number) => (
              <div key={index} className="border rounded-lg p-4 mb-3 last:mb-0">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium leading-tight">{paper.title}</h3>
                  <Badge variant="outline">
                    相关性 {Math.round(paper.relevance_score * 100)}%
                  </Badge>
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                  <span>{typeof paper.authors === 'string' ? paper.authors : paper.authors?.name || JSON.stringify(paper.authors)}</span>
                  <span>•</span>
                  <span className="font-medium">{paper.journal}</span>
                  <span>•</span>
                  <span>IF: {paper.impact_factor}</span>
                </div>
                <div className="flex justify-between items-center">
                  <Badge variant={paper.source === 'external' ? 'default' : 'secondary'}>
                    {paper.source === 'external' ? '外部数据库' : '本地文献'}
                  </Badge>
                  <Button variant="ghost" size="sm">
                    <ExternalLink className="h-4 w-4 mr-1" />
                    查看详情
                  </Button>
                </div>
              </div>
            ))}
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  )

  const renderTechSolutionSection = () => (
    <div className="space-y-6">
      {/* 详细技术对比 */}
      {recommendation?.tech_recommendations?.map((tech: any, index: number) => (
        <Card key={index} className="border-2">
          <CardHeader>
            <CardTitle className="flex justify-between items-center">
              <span className="flex items-center gap-2">
                <Award className="h-5 w-5 text-yellow-500" />
                {tech.platform}
              </span>
              <div className="flex gap-2">
                <Badge variant="default">
                  推荐度 {Math.round(tech.suitability_score * 100)}%
                </Badge>
                <Badge variant="secondary">
                  预算匹配 {Math.round(tech.budget_match_score * 100)}%
                </Badge>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="costs" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="costs">成本分析</TabsTrigger>
                <TabsTrigger value="timeline">时间规划</TabsTrigger>
                <TabsTrigger value="advantages">技术优势</TabsTrigger>
                <TabsTrigger value="specs">技术规格</TabsTrigger>
              </TabsList>
              
              <TabsContent value="costs" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">成本构成</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span>样本准备</span>
                        <span className="font-semibold">
                          ¥{(tech.cost_breakdown.sample_preparation.library_prep + tech.cost_breakdown.sample_preparation.reagents).toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>测序成本</span>
                        <span className="font-semibold">
                          ¥{tech.cost_breakdown.sequencing.sequencing_cost.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>附加服务</span>
                        <span className="font-semibold">
                          ¥{(tech.cost_breakdown.additional_costs.qc_validation + 
                              tech.cost_breakdown.additional_costs.data_analysis + 
                              tech.cost_breakdown.additional_costs.report_generation).toLocaleString()}
                        </span>
                      </div>
                      <Separator />
                      <div className="flex justify-between items-center text-lg font-bold text-green-600">
                        <span>总计</span>
                        <span>¥{tech.total_estimated_cost.toLocaleString()}</span>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">成本分布</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm">测序成本</span>
                            <span className="text-sm">40%</span>
                          </div>
                          <Progress value={40} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm">样本准备</span>
                            <span className="text-sm">35%</span>
                          </div>
                          <Progress value={35} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm">数据分析</span>
                            <span className="text-sm">25%</span>
                          </div>
                          <Progress value={25} className="h-2" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              <TabsContent value="timeline" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        项目时间线
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-semibold text-blue-600">1</div>
                        <div>
                          <div className="font-medium">样本准备</div>
                          <div className="text-sm text-gray-600">{tech.timeline_estimate.sample_prep}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-sm font-semibold text-green-600">2</div>
                        <div>
                          <div className="font-medium">文库构建</div>
                          <div className="text-sm text-gray-600">{tech.timeline_estimate.library_construction}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-sm font-semibold text-purple-600">3</div>
                        <div>
                          <div className="font-medium">测序分析</div>
                          <div className="text-sm text-gray-600">{tech.timeline_estimate.sequencing}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center text-sm font-semibold text-yellow-600">4</div>
                        <div>
                          <div className="font-medium">数据分析</div>
                          <div className="text-sm text-gray-600">{tech.timeline_estimate.data_analysis}</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">总周期概览</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-600 mb-2">
                          {tech.timeline_estimate.total}
                        </div>
                        <div className="text-gray-600 mb-4">预计总项目周期</div>
                        {tech.timeline_estimate.expedited && (
                          <Badge variant="destructive" className="mb-2">
                            <Clock className="h-3 w-3 mr-1" />
                            加急处理
                          </Badge>
                        )}
                        <div className="text-sm text-gray-500">
                          基于当前项目需求和平台处理能力估算
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              <TabsContent value="advantages" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg text-green-600">主要优势</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {tech.advantages?.map((advantage: any, i: number) => (
                          <li key={i} className="flex items-start gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-sm">
                              {typeof advantage === 'string' ? advantage : advantage?.name || JSON.stringify(advantage)}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg text-orange-600">注意事项</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {['成本相对较高', '需要专业设备和技能', '样本质量要求较高'].map((consideration: string, i: number) => (
                          <li key={i} className="flex items-start gap-2">
                            <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                            <span className="text-sm">{consideration}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              <TabsContent value="specs" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">技术规格</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <div>
                          <div className="font-medium text-sm text-gray-600">细胞通量</div>
                          <div className="font-semibold">500-80,000 cells</div>
                        </div>
                        <div>
                          <div className="font-medium text-sm text-gray-600">检测敏感性</div>
                          <div className="font-semibold">~6,000 genes/cell</div>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <div className="font-medium text-sm text-gray-600">细胞回收率</div>
                          <div className="font-semibold">>60%</div>
                        </div>
                        <div>
                          <div className="font-medium text-sm text-gray-600">支持应用</div>
                          <div className="flex flex-wrap gap-1">
                            {['scRNA-seq', 'scATAC-seq', 'Multiome', 'CITE-seq'].map((app, i) => (
                              <Badge key={i} variant="outline" className="text-xs">
                                {app}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      ))}
    </div>
  )

  const renderRiskAssessmentSection = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            风险评估与缓解策略
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">整体风险等级</span>
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                {recommendation?.risk_assessment?.overall_risk_level}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium">成功概率</span>
              <span className="font-semibold text-green-600">
                {recommendation?.risk_assessment?.success_probability}
              </span>
            </div>
          </div>

          <div className="space-y-4">
            {recommendation?.risk_assessment?.technical_risks?.map((risk: any, index: number) => (
              <Card key={index} className="border-l-4 border-l-orange-500">
                <CardContent className="pt-4">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-semibold">{risk.risk}</h4>
                    <Badge variant="outline">{risk.probability}</Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{risk.impact}</p>
                  <div className="bg-green-50 p-3 rounded">
                    <div className="font-medium text-green-800 text-sm mb-1">缓解策略</div>
                    <div className="text-green-700 text-sm">{risk.mitigation}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 合作建议 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-blue-500" />
            合作机会推荐
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {recommendation?.project_solution?.collaboration_opportunities?.map((collab: any, index: number) => (
              <Card key={index} className="border">
                <CardContent className="pt-4">
                  <h4 className="font-semibold mb-1">{collab.institution}</h4>
                  <div className="text-blue-600 font-medium text-sm mb-2">{collab.contact}</div>
                  <div className="text-sm text-gray-600 mb-2">{collab.expertise}</div>
                  <Badge variant="outline" className="text-xs">
                    {collab.collaboration_type || "研究合作"}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )

  if (loading) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="text-center text-gray-500">
            <Loader2 className="h-12 w-12 mx-auto mb-4 text-blue-500 animate-spin" />
            <p className="text-lg font-medium mb-2">正在生成智能推荐...</p>
            <p className="text-sm">请稍候，我们正在分析您的需求并匹配最佳方案</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error && !recommendation) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="text-center text-red-500">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-red-400" />
            <p className="text-lg font-medium mb-2">加载推荐数据失败</p>
            <p className="text-sm text-gray-600 mb-4">{error}</p>
            <Button onClick={loadRecommendation} variant="outline">
              重新加载
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!recommendation) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="text-center text-gray-500">
            <BookOpen className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>请先完成需求收集，以获取个性化推荐</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="w-full space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">CellForge AI 智能推荐平台</h1>
          <p className="text-gray-600">基于您的需求生成的综合技术方案与文献推荐</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-1" />
            导出报告
          </Button>
          <Button size="sm">
            <Star className="h-4 w-4 mr-1" />
            收藏方案
          </Button>
        </div>
      </div>

      {/* 主要内容标签页 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            项目概览
          </TabsTrigger>
          <TabsTrigger value="literature" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            文献推荐
          </TabsTrigger>
          <TabsTrigger value="technology" className="flex items-center gap-2">
            <Award className="h-4 w-4" />
            技术方案
          </TabsTrigger>
          <TabsTrigger value="risks" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            风险评估
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          {renderOverviewSection()}
        </TabsContent>

        <TabsContent value="literature" className="mt-6">
          {renderLiteratureSection()}
        </TabsContent>

        <TabsContent value="technology" className="mt-6">  
          {renderTechSolutionSection()}
        </TabsContent>

        <TabsContent value="risks" className="mt-6">
          {renderRiskAssessmentSection()}
        </TabsContent>
      </Tabs>
    </div>
  )
}