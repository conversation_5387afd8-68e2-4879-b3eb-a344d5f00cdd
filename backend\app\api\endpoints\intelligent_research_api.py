"""
智能研究API端点
整合IntelligentResearchAdvisor、SmartLiteratureLinker、DynamicKeywordGenerator、PersonalizedSolutionGenerator
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Query
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.intelligent_research_advisor import get_intelligent_research_advisor
from app.services.smart_literature_linker import get_smart_literature_linker
from app.services.dynamic_keyword_generator import get_dynamic_keyword_generator
from app.services.personalized_solution_generator import get_personalized_solution_generator

logger = logging.getLogger(__name__)

router = APIRouter()

# ===== 统一响应格式 =====

class APIResponse(BaseModel):
    """统一API响应格式"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    processing_time_ms: Optional[float] = None

# ===== 请求模型 =====

class UserIntentAnalysisRequest(BaseModel):
    """用户意图分析请求"""
    user_input: str = Field(..., description="用户原始输入")
    requirements: Dict[str, Any] = Field(..., description="结构化需求信息")
    user_id: int = Field(..., description="用户ID")
    user_context: Optional[Dict[str, Any]] = Field(None, description="用户上下文信息")

class ResearchDirectionsRequest(BaseModel):
    """研究方向生成请求"""
    session_id: str = Field(..., description="意图分析会话ID")
    user_id: int = Field(..., description="用户ID")
    intent_analysis: Dict[str, Any] = Field(..., description="意图分析结果")

class LiteratureLinksRequest(BaseModel):
    """文献链接生成请求"""
    research_direction: Dict[str, Any] = Field(..., description="研究方向信息")
    intent_analysis: Dict[str, Any] = Field(..., description="意图分析结果")
    user_profile: Optional[Dict[str, Any]] = Field(None, description="用户档案")

class DynamicKeywordsRequest(BaseModel):
    """动态关键词生成请求"""
    research_direction: Dict[str, Any] = Field(..., description="研究方向信息")
    intent_analysis: Dict[str, Any] = Field(..., description="意图分析结果")
    user_context: Optional[Dict[str, Any]] = Field(None, description="用户上下文")
    session_id: Optional[str] = Field(None, description="会话ID")

class ComprehensiveSolutionRequest(BaseModel):
    """综合解决方案生成请求"""
    user_input: str = Field(..., description="用户原始输入")
    requirements: Dict[str, Any] = Field(..., description="结构化需求信息")
    user_id: int = Field(..., description="用户ID")
    user_context: Optional[Dict[str, Any]] = Field(None, description="用户上下文信息")

# ===== 服务实例获取 =====

async def get_intelligent_advisor():
    """获取智能研究顾问服务"""
    try:
        return get_intelligent_research_advisor()
    except Exception as e:
        logger.error(f"获取智能研究顾问服务失败: {e}")
        raise HTTPException(status_code=500, detail="智能研究顾问服务不可用")

async def get_literature_linker():
    """获取智能文献链接服务"""
    try:
        return get_smart_literature_linker()
    except Exception as e:
        logger.error(f"获取智能文献链接服务失败: {e}")
        raise HTTPException(status_code=500, detail="智能文献链接服务不可用")

async def get_keyword_generator():
    """获取动态关键词生成服务"""
    try:
        service = get_dynamic_keyword_generator()
        await service.initialize()
        return service
    except Exception as e:
        logger.error(f"获取动态关键词生成服务失败: {e}")
        raise HTTPException(status_code=500, detail="动态关键词生成服务不可用")

async def get_solution_generator():
    """获取个性化方案生成服务"""
    try:
        service = get_personalized_solution_generator()
        await service.initialize()
        return service
    except Exception as e:
        logger.error(f"获取个性化方案生成服务失败: {e}")
        raise HTTPException(status_code=500, detail="个性化方案生成服务不可用")

# ===== API端点 =====

@router.post("/intent-analysis", response_model=APIResponse)
async def analyze_user_intent(
    request: UserIntentAnalysisRequest,
    background_tasks: BackgroundTasks
):
    """
    用户意图分析
    深度理解用户的研究需求和意图
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"接收用户意图分析请求 - 用户ID: {request.user_id}")
        
        # 获取服务
        advisor = await get_intelligent_advisor()
        
        # 执行意图分析
        result = await advisor.understand_user_intent(
            user_input=request.user_input,
            requirements=request.requirements,
            user_id=request.user_id,
            user_context=request.user_context
        )
        
        # 计算处理时间
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        # 检查结果
        if "error" in result:
            logger.warning(f"意图分析出现警告: {result['error']}")
            return APIResponse(
                success=True,
                message="意图分析完成(使用降级模式)",
                data=result,
                processing_time_ms=processing_time
            )
        
        logger.info(f"用户意图分析成功 - 会话ID: {result.get('session_id')}")
        
        return APIResponse(
            success=True,
            message="用户意图分析完成",
            data=result,
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        logger.error(f"用户意图分析失败: {str(e)}")
        
        return APIResponse(
            success=False,
            message="用户意图分析失败",
            error=str(e),
            processing_time_ms=processing_time
        )

@router.post("/research-directions", response_model=APIResponse)
async def generate_research_directions(
    request: ResearchDirectionsRequest,
    background_tasks: BackgroundTasks
):
    """
    生成个性化研究方向
    基于意图分析结果生成定制化的研究方向建议
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"生成研究方向 - 会话ID: {request.session_id}")
        
        # 获取服务
        advisor = await get_intelligent_advisor()
        
        # 生成研究方向
        result = await advisor.generate_personalized_directions(
            session_id=request.session_id,
            user_id=request.user_id,
            intent_analysis=request.intent_analysis
        )
        
        # 计算处理时间
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        # 检查结果
        if "error" in result:
            logger.warning(f"研究方向生成出现警告: {result['error']}")
            return APIResponse(
                success=True,
                message="研究方向生成完成(使用降级模式)",
                data=result,
                processing_time_ms=processing_time
            )
        
        logger.info(f"研究方向生成成功 - 生成{result.get('total_directions', 0)}个方向")
        
        return APIResponse(
            success=True,
            message=f"成功生成{result.get('total_directions', 0)}个个性化研究方向",
            data=result,
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        logger.error(f"研究方向生成失败: {str(e)}")
        
        return APIResponse(
            success=False,
            message="研究方向生成失败",
            error=str(e),
            processing_time_ms=processing_time
        )

@router.post("/literature-links", response_model=APIResponse)
async def generate_literature_links(
    request: LiteratureLinksRequest,
    background_tasks: BackgroundTasks
):
    """
    生成智能文献搜索链接
    为研究方向生成优化的文献搜索链接
    """
    start_time = datetime.now()
    
    try:
        logger.info("生成智能文献搜索链接")
        
        # 获取服务
        linker = await get_literature_linker()
        
        # 生成文献链接
        result = await linker.generate_research_links(
            research_direction=request.research_direction,
            intent_analysis=request.intent_analysis,
            user_profile=request.user_profile
        )
        
        # 计算处理时间
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        logger.info(f"文献链接生成成功 - 生成{result.total_links}个链接")
        
        return APIResponse(
            success=True,
            message=f"成功生成{result.total_links}个智能文献搜索链接",
            data={
                "research_focus": result.research_focus,
                "total_links": result.total_links,
                "links": [
                    {
                        "platform": link.platform,
                        "title": link.title,
                        "url": link.url,
                        "description": link.description,
                        "relevance_score": link.relevance_score,
                        "search_strategy": link.search_strategy,
                        "expected_results": link.expected_results
                    } for link in result.links
                ],
                "generation_timestamp": result.generation_timestamp,
                "optimization_notes": result.optimization_notes
            },
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        logger.error(f"文献链接生成失败: {str(e)}")
        
        return APIResponse(
            success=False,
            message="智能文献链接生成失败",
            error=str(e),
            processing_time_ms=processing_time
        )

@router.post("/dynamic-keywords", response_model=APIResponse)
async def generate_dynamic_keywords(
    request: DynamicKeywordsRequest,
    background_tasks: BackgroundTasks
):
    """
    生成动态关键词策略
    基于研究方向和用户意图生成优化的关键词策略
    """
    start_time = datetime.now()
    
    try:
        logger.info("生成动态关键词策略")
        
        # 获取服务
        generator = await get_keyword_generator()
        
        # 生成动态关键词
        result = await generator.generate_dynamic_keywords(
            research_direction=request.research_direction,
            intent_analysis=request.intent_analysis,
            user_context=request.user_context,
            session_id=request.session_id
        )
        
        # 计算处理时间
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        logger.info(f"动态关键词生成成功 - 生成{result.total_keywords}个关键词")
        
        return APIResponse(
            success=True,
            message=f"成功生成{result.total_keywords}个动态关键词",
            data={
                "session_id": result.session_id,
                "research_focus": result.research_focus,
                "keyword_clusters": [
                    {
                        "cluster_name": cluster.cluster_name,
                        "primary_keywords": cluster.primary_keywords,
                        "secondary_keywords": cluster.secondary_keywords,
                        "weight": cluster.weight,
                        "relevance_score": cluster.relevance_score,
                        "search_priority": cluster.search_priority
                    } for cluster in result.keyword_clusters
                ],
                "adaptive_keywords": result.adaptive_keywords,
                "context_keywords": result.context_keywords,
                "trending_keywords": result.trending_keywords,
                "total_keywords": result.total_keywords,
                "confidence_score": result.confidence_score,
                "generation_strategy": result.generation_strategy,
                "optimization_notes": result.optimization_notes,
                "generation_timestamp": result.generation_timestamp
            },
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        logger.error(f"动态关键词生成失败: {str(e)}")
        
        return APIResponse(
            success=False,
            message="动态关键词生成失败",
            error=str(e),
            processing_time_ms=processing_time
        )

@router.post("/comprehensive-solution", response_model=APIResponse)
async def generate_comprehensive_solution(
    request: ComprehensiveSolutionRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    生成综合个性化解决方案
    整合所有AI服务，生成完整的个性化研究解决方案
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"开始生成综合个性化解决方案 - 用户ID: {request.user_id}")
        
        # 获取服务
        generator = await get_solution_generator()
        
        # 生成综合解决方案
        result = await generator.generate_comprehensive_solution(
            user_input=request.user_input,
            requirements=request.requirements,
            user_id=request.user_id,
            user_context=request.user_context
        )
        
        # 计算处理时间
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        # 构建响应数据
        response_data = {
            "solution_id": result.solution_id,
            "session_id": result.session_id,
            "user_id": result.user_id,
            "generation_timestamp": result.generation_timestamp,
            
            # 核心分析结果
            "user_profile_summary": result.user_profile_summary,
            "intent_analysis": result.intent_analysis,
            "research_directions": result.research_directions,
            
            # 资源整合结果
            "literature_resources": result.literature_resources,
            "keyword_strategies": result.keyword_strategies,
            
            # 个性化方案组件
            "personalized_recommendations": [
                {
                    "recommendation_id": rec.recommendation_id,
                    "category": rec.category,
                    "title": rec.title,
                    "description": rec.description,
                    "priority": rec.priority,
                    "rationale": rec.rationale,
                    "estimated_impact": rec.estimated_impact,
                    "implementation_complexity": rec.implementation_complexity,
                    "estimated_cost": rec.estimated_cost,
                    "timeline_estimate": rec.timeline_estimate
                } for rec in result.personalized_recommendations
            ],
            "learning_paths": [
                {
                    "path_id": path.path_id,
                    "path_name": path.path_name,
                    "target_audience": path.target_audience,
                    "total_duration": path.total_duration,
                    "difficulty_level": path.difficulty_level,
                    "prerequisites": path.prerequisites,
                    "stages": [
                        {
                            "stage_number": stage.stage_number,
                            "stage_name": stage.stage_name,
                            "learning_objectives": stage.learning_objectives,
                            "key_concepts": stage.key_concepts,
                            "recommended_resources": stage.recommended_resources,
                            "practical_exercises": stage.practical_exercises,
                            "duration": stage.duration,
                            "success_criteria": stage.success_criteria
                        } for stage in path.stages
                    ],
                    "final_outcomes": path.final_outcomes,
                    "certification_opportunities": path.certification_opportunities
                } for path in result.learning_paths
            ],
            
            # 执行指导
            "immediate_next_steps": result.immediate_next_steps,
            "long_term_roadmap": result.long_term_roadmap,
            "decision_support": result.decision_support,
            
            # 元数据
            "confidence_score": result.confidence_score,
            "personalization_level": result.personalization_level,
            "solution_complexity": result.solution_complexity,
            "estimated_success_rate": result.estimated_success_rate,
            "generation_metadata": result.generation_metadata
        }
        
        logger.info(f"综合解决方案生成成功 - 方案ID: {result.solution_id}")
        
        return APIResponse(
            success=True,
            message=f"成功生成综合个性化解决方案",
            data=response_data,
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        logger.error(f"综合解决方案生成失败: {str(e)}")
        
        return APIResponse(
            success=False,
            message="综合个性化解决方案生成失败",
            error=str(e),
            processing_time_ms=processing_time
        )

@router.post("/keyword-platform-optimization", response_model=APIResponse)
async def optimize_keywords_for_platforms(
    keyword_result: Dict[str, Any],
    target_platforms: List[str] = Query(..., description="目标平台列表")
):
    """
    为特定搜索平台优化关键词
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"为{len(target_platforms)}个平台优化关键词")
        
        # 获取服务
        generator = await get_keyword_generator()
        
        # 将字典转换为DynamicKeywordResult对象 (简化版本)
        from app.services.dynamic_keyword_generator import DynamicKeywordResult, KeywordCluster
        
        # 这里简化处理，实际可能需要更复杂的转换逻辑
        mock_result = DynamicKeywordResult(
            session_id=keyword_result.get("session_id", "mock"),
            research_focus=keyword_result.get("research_focus", ""),
            keyword_clusters=[],  # 简化
            adaptive_keywords=keyword_result.get("adaptive_keywords", []),
            context_keywords=keyword_result.get("context_keywords", []),
            trending_keywords=keyword_result.get("trending_keywords", []),
            total_keywords=keyword_result.get("total_keywords", 0),
            confidence_score=keyword_result.get("confidence_score", 0.5),
            generation_strategy=keyword_result.get("generation_strategy", "standard"),
            optimization_notes=keyword_result.get("optimization_notes", ""),
            generation_timestamp=keyword_result.get("generation_timestamp", datetime.now().isoformat())
        )
        
        # 执行平台优化
        result = await generator.optimize_keywords_for_platforms(
            keyword_result=mock_result,
            target_platforms=target_platforms
        )
        
        # 计算处理时间
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        logger.info(f"关键词平台优化成功 - 优化{len(result)}个平台")
        
        return APIResponse(
            success=True,
            message=f"成功为{len(result)}个平台优化关键词",
            data=result,
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        logger.error(f"关键词平台优化失败: {str(e)}")
        
        return APIResponse(
            success=False,
            message="关键词平台优化失败",
            error=str(e),
            processing_time_ms=processing_time
        )

# ===== 批量处理端点 =====

@router.post("/batch-literature-links", response_model=APIResponse)
async def generate_batch_literature_links(
    research_directions: List[Dict[str, Any]],
    intent_analysis: Dict[str, Any],
    user_profile: Optional[Dict[str, Any]] = None,
    background_tasks: BackgroundTasks = None
):
    """
    批量生成多个研究方向的文献链接
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"批量生成{len(research_directions)}个研究方向的文献链接")
        
        # 获取服务
        linker = await get_literature_linker()
        
        # 批量生成文献链接
        results = await linker.generate_multi_direction_links(
            research_directions=research_directions,
            intent_analysis=intent_analysis,
            user_profile=user_profile
        )
        
        # 计算处理时间
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        # 构建响应数据
        response_data = {}
        for direction_id, link_package in results.items():
            response_data[direction_id] = {
                "research_focus": link_package.research_focus,
                "total_links": link_package.total_links,
                "links": [
                    {
                        "platform": link.platform,
                        "title": link.title,
                        "url": link.url,
                        "description": link.description,
                        "relevance_score": link.relevance_score
                    } for link in link_package.links
                ],
                "generation_timestamp": link_package.generation_timestamp,
                "optimization_notes": link_package.optimization_notes
            }
        
        logger.info(f"批量文献链接生成成功 - 处理{len(results)}个方向")
        
        return APIResponse(
            success=True,
            message=f"成功为{len(results)}个研究方向生成文献链接",
            data=response_data,
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        logger.error(f"批量文献链接生成失败: {str(e)}")
        
        return APIResponse(
            success=False,
            message="批量文献链接生成失败",
            error=str(e),
            processing_time_ms=processing_time
        )

# ===== 健康检查和系统状态 =====

@router.get("/health", response_model=APIResponse)
async def health_check():
    """
    智能研究服务健康检查
    """
    try:
        # 检查各个服务的状态
        service_status = {}
        
        try:
            advisor = await get_intelligent_advisor()
            service_status["intelligent_research_advisor"] = "healthy"
        except Exception:
            service_status["intelligent_research_advisor"] = "unhealthy"
        
        try:
            linker = await get_literature_linker()
            service_status["smart_literature_linker"] = "healthy"
        except Exception:
            service_status["smart_literature_linker"] = "unhealthy"
        
        try:
            generator = await get_keyword_generator()
            service_status["dynamic_keyword_generator"] = "healthy"
        except Exception:
            service_status["dynamic_keyword_generator"] = "unhealthy"
        
        try:
            solution_gen = await get_solution_generator()
            service_status["personalized_solution_generator"] = "healthy"
        except Exception:
            service_status["personalized_solution_generator"] = "unhealthy"
        
        # 计算整体健康状态
        healthy_services = sum(1 for status in service_status.values() if status == "healthy")
        total_services = len(service_status)
        health_percentage = (healthy_services / total_services) * 100
        
        overall_status = "healthy" if health_percentage >= 75 else "degraded" if health_percentage >= 50 else "unhealthy"
        
        return APIResponse(
            success=True,
            message=f"系统状态: {overall_status} ({healthy_services}/{total_services} 服务正常)",
            data={
                "overall_status": overall_status,
                "health_percentage": health_percentage,
                "services": service_status,
                "healthy_services": healthy_services,
                "total_services": total_services,
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return APIResponse(
            success=False,
            message="健康检查失败",
            error=str(e)
        )

@router.get("/service-info", response_model=APIResponse)
async def get_service_info():
    """
    获取智能研究服务信息
    """
    try:
        service_info = {
            "intelligent_research_advisor": {
                "name": "智能研究顾问",
                "description": "AI驱动的研究意图理解和个性化方案生成",
                "capabilities": [
                    "深度意图分析",
                    "个性化研究方向推荐",
                    "用户档案管理",
                    "智能方案定制"
                ],
                "version": "1.0.0"
            },
            "smart_literature_linker": {
                "name": "智能文献链接生成器",
                "description": "基于研究意图动态生成最相关的文献搜索链接",
                "capabilities": [
                    "多平台文献搜索",
                    "智能链接优化",
                    "搜索策略生成",
                    "相关性评分"
                ],
                "supported_platforms": ["PubMed", "Google Scholar", "PMC", "bioRxiv", "ScienceDirect", "Semantic Scholar"],
                "version": "1.0.0"
            },
            "dynamic_keyword_generator": {
                "name": "动态关键词生成器",
                "description": "基于用户研究意图和上下文动态生成最相关的搜索关键词",
                "capabilities": [
                    "AI驱动关键词扩展",
                    "关键词聚类分析",
                    "自适应关键词生成",
                    "趋势关键词识别",
                    "平台优化策略"
                ],
                "version": "1.0.0"
            },
            "personalized_solution_generator": {
                "name": "个性化方案生成器",
                "description": "整合所有AI服务生成完整的个性化研究解决方案",
                "capabilities": [
                    "综合方案整合",
                    "学习路径设计",
                    "资源配置规划",
                    "风险评估分析",
                    "成功指标制定"
                ],
                "version": "1.0.0"
            }
        }
        
        return APIResponse(
            success=True,
            message="智能研究服务信息",
            data={
                "services": service_info,
                "api_version": "1.0.0",
                "last_updated": "2024-01-01T00:00:00Z"
            }
        )
        
    except Exception as e:
        logger.error(f"获取服务信息失败: {str(e)}")
        return APIResponse(
            success=False,
            message="获取服务信息失败",
            error=str(e)
        )