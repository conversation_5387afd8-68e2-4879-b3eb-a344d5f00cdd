/**
 * 智能方案生成器
 * 基于用户输入动态生成技术方案，而非硬编码数据
 */

interface RequirementData {
  speciesType?: string
  experimentType?: string  
  researchGoal?: string
  sampleType?: string
  sampleCount?: string
  sampleCondition?: string
  sampleProcessing?: string
  cellCount?: string
  cellViability?: string
  budget?: string
  timeline?: string
  urgencyLevel?: string
  sequencingDepth?: string
  analysisType?: string
  dataAnalysisNeeds?: string
  specialRequirements?: string
  needsCellSorting?: string
  [key: string]: any
}

interface TechPlatform {
  name: string
  suitability: number
  cost: number
  timeframe: string
  advantages: string[]
  limitations: string[]
  specifications: Record<string, string>
}

interface DynamicSolution {
  solution_id: string
  generated_at: string
  client_requirements: RequirementData
  recommended_solution: {
    platform: string
    reasoning: string
    specifications: Record<string, string>
  }
  cost_analysis: {
    total_cost: number
    sample_preparation: { cost: number; description: string }
    sequencing: { cost: number; description: string }
    data_analysis: { cost: number; description: string }
    breakdown: string
  }
  risk_assessment: {
    overall_risk_level: string
    success_probability: string
    technical_risks: Array<{
      risk: string
      probability: string
      impact: string
      mitigation: string
    }>
  }
  timeline: {
    total: string
    sample_prep: string
    sequencing: string
    analysis: string
  }
  deliverables: string[]
  next_steps: string[]
  contact_info: {
    email: string
    phone: string
  }
  intelligent_features: any
}

class IntelligentSolutionGenerator {
  private techPlatforms: Record<string, TechPlatform> = {
    '10x_genomics': {
      name: '10x Genomics Chromium',
      suitability: 0,
      cost: 25000,
      timeframe: '30-45天',
      advantages: ['高通量', '单细胞分辨率高', '数据质量佳'],
      limitations: ['成本较高', '需要新鲜样本'],
      specifications: {
        cell_throughput: '500-80,000 cells',
        sensitivity: '~6,000 genes/cell',
        efficiency: '>60% cells recovered'
      }
    },
    'bd_rhapsody': {
      name: 'BD Rhapsody',
      suitability: 0,
      cost: 20000,
      timeframe: '25-35天',
      advantages: ['成本效益好', '操作简便', '适合中等通量'],
      limitations: ['基因检测数量较少', '细胞回收率中等'],
      specifications: {
        cell_throughput: '200-20,000 cells',
        sensitivity: '~3,000 genes/cell',
        efficiency: '>40% cells recovered'
      }
    },
    'parse_biosciences': {
      name: 'Parse Biosciences',
      suitability: 0,
      cost: 18000,
      timeframe: '20-30天',
      advantages: ['成本最低', '处理速度快', '适合大规模筛选'],
      limitations: ['基因覆盖度有限', '数据深度较浅'],
      specifications: {
        cell_throughput: '1,000-1,000,000 cells',
        sensitivity: '~2,500 genes/cell',
        efficiency: '>35% cells recovered'
      }
    }
  }

  /**
   * 分析用户需求并计算平台适配度
   */
  private analyzePlatformSuitability(requirements: RequirementData): Record<string, TechPlatform> {
    const platforms = { ...this.techPlatforms }
    
    // 基于预算调整适配度
    const budgetNum = this.extractBudgetNumber(requirements.budget || requirements.budgetRange || '')
    
    // 基于细胞数量调整适配度
    const cellCountNum = this.extractCellCount(requirements.cellCount || '')
    
    // 基于紧急程度调整
    const urgency = requirements.urgencyLevel || requirements.urgency || ''
    
    // 基于实验类型调整
    const experimentType = requirements.experimentType || ''
    
    Object.keys(platforms).forEach(key => {
      let score = 0.5 // 基础分数
      
      // 预算匹配度 (0-0.3)
      if (budgetNum > 0) {
        const costDiff = Math.abs(platforms[key].cost - budgetNum) / budgetNum
        score += Math.max(0, 0.3 - costDiff * 0.3)
      }
      
      // 细胞数量匹配度 (0-0.25)
      if (cellCountNum > 0) {
        if (key === '10x_genomics' && cellCountNum >= 1000) score += 0.25
        else if (key === 'bd_rhapsody' && cellCountNum >= 500 && cellCountNum <= 15000) score += 0.25
        else if (key === 'parse_biosciences' && cellCountNum >= 10000) score += 0.25
        else score += 0.1
      }
      
      // 紧急程度匹配度 (0-0.15)
      if (urgency.includes('紧急') || urgency.includes('急')) {
        if (key === 'parse_biosciences') score += 0.15 // 最快
        else if (key === 'bd_rhapsody') score += 0.1
        else score += 0.05
      } else {
        score += 0.1
      }
      
      // 实验类型匹配度 (0-0.2)
      if (experimentType.includes('单细胞RNA')) {
        score += 0.2 // 所有平台都适合
      } else if (experimentType.includes('ATAC')) {
        if (key === '10x_genomics') score += 0.2
        else score += 0.1
      }
      
      // 样本类型匹配度 (0-0.1)
      const sampleType = requirements.sampleType || ''
      if (sampleType.includes('心脏') || sampleType.includes('脑') || sampleType.includes('复杂组织')) {
        if (key === '10x_genomics') score += 0.1 // 最适合复杂样本
        else score += 0.05
      }
      
      platforms[key].suitability = Math.min(1, score)
    })
    
    return platforms
  }

  /**
   * 从预算字符串中提取数字
   */
  private extractBudgetNumber(budget: string): number {
    if (!budget) return 0
    
    // 匹配 "10-20万" 或 "25万" 等格式
    const match = budget.match(/([\d.]+)(?:[-~]([\d.]+))?万/)
    if (match) {
      const min = parseFloat(match[1]) * 10000
      const max = match[2] ? parseFloat(match[2]) * 10000 : min
      return (min + max) / 2
    }
    
    // 匹配纯数字
    const numMatch = budget.match(/([\d.]+)/)
    if (numMatch) {
      return parseFloat(numMatch[1])
    }
    
    return 0
  }

  /**
   * 从细胞数量字符串中提取数字
   */
  private extractCellCount(cellCount: string): number {
    if (!cellCount) return 0
    
    // 匹配 "5000-10000" 或 "1万" 等格式
    const match = cellCount.match(/([\d.]+)([万千]?)(?:[-~]([\d.]+)([万千]?))?/)
    if (match) {
      let min = parseFloat(match[1])
      if (match[2] === '万') min *= 10000
      else if (match[2] === '千') min *= 1000
      
      let max = min
      if (match[3]) {
        max = parseFloat(match[3])
        if (match[4] === '万') max *= 10000
        else if (match[4] === '千') max *= 1000
      }
      
      return (min + max) / 2
    }
    
    return 0
  }

  /**
   * 根据需求动态计算成本
   */
  private calculateDynamicCost(requirements: RequirementData, platform: TechPlatform): any {
    const baseCost = platform.cost
    
    // 根据样本数量调整成本
    const sampleCount = this.extractSampleCount(requirements.sampleCount || '')
    const sampleMultiplier = Math.max(1, sampleCount * 0.8)
    
    // 根据细胞数量调整成本
    const cellCount = this.extractCellCount(requirements.cellCount || '')
    const cellMultiplier = cellCount > 50000 ? 1.2 : cellCount > 10000 ? 1.1 : 1.0
    
    // 根据紧急程度调整成本
    const urgency = requirements.urgencyLevel || requirements.urgency || ''
    const urgencyMultiplier = urgency.includes('紧急') ? 1.3 : urgency.includes('急') ? 1.15 : 1.0
    
    // 根据特殊需求调整成本
    const needsSorting = (requirements.needsCellSorting || '').includes('是')
    const sortingCost = needsSorting ? 5000 : 0
    
    const adjustedBaseCost = baseCost * sampleMultiplier * cellMultiplier * urgencyMultiplier
    const totalCost = Math.round(adjustedBaseCost + sortingCost)
    
    return {
      total_cost: totalCost,
      sample_preparation: { 
        cost: Math.round(totalCost * 0.4), 
        description: "文库构建、质检和试剂" 
      },
      sequencing: { 
        cost: Math.round(totalCost * 0.4), 
        description: `${platform.specifications.sensitivity || '标准深度'}测序` 
      },
      data_analysis: { 
        cost: Math.round(totalCost * 0.2), 
        description: "生信分析和报告生成" 
      },
      breakdown: this.generateCostBreakdown(requirements, urgency, needsSorting)
    }
  }

  private extractSampleCount(sampleCount: string): number {
    if (!sampleCount) return 1
    const match = sampleCount.match(/([\d.]+)/)
    return match ? Math.max(1, parseFloat(match[1])) : 1
  }

  private generateCostBreakdown(requirements: RequirementData, urgency: string, needsSorting: boolean): string {
    const factors = []
    
    if (urgency.includes('紧急')) factors.push("加急处理费用")
    if (needsSorting) factors.push("细胞分选费用")
    
    const budgetRange = requirements.budget || requirements.budgetRange || ''
    if (budgetRange) factors.push(`已针对您的${budgetRange}预算优化`)
    
    return factors.length > 0 ? factors.join("，") : "标准处理流程费用"
  }

  /**
   * 动态生成时间规划
   */
  private calculateDynamicTimeline(requirements: RequirementData, platform: TechPlatform): any {
    const baseTimeframe = platform.timeframe
    const [minDays, maxDays] = baseTimeframe.match(/(\d+)-(\d+)天/)?.slice(1).map(Number) || [30, 45]
    
    // 根据紧急程度调整时间
    const urgency = requirements.urgencyLevel || requirements.urgency || ''
    let timeMultiplier = 1.0
    if (urgency.includes('非常紧急')) timeMultiplier = 0.7
    else if (urgency.includes('紧急') || urgency.includes('急')) timeMultiplier = 0.8
    
    // 根据样本复杂度调整时间
    const sampleType = requirements.sampleType || ''
    if (sampleType.includes('冷冻') || sampleType.includes('固定')) timeMultiplier *= 1.2
    
    const adjustedMin = Math.round(minDays * timeMultiplier)
    const adjustedMax = Math.round(maxDays * timeMultiplier)
    
    return {
      total: `${adjustedMin}-${adjustedMax}天`,
      sample_prep: `${Math.round(adjustedMin * 0.2)}-${Math.round(adjustedMax * 0.25)}天`,
      sequencing: `${Math.round(adjustedMin * 0.3)}-${Math.round(adjustedMax * 0.35)}天`,
      analysis: `${Math.round(adjustedMin * 0.4)}-${Math.round(adjustedMax * 0.4)}天`
    }
  }

  /**
   * 动态生成风险评估
   */
  private generateDynamicRiskAssessment(requirements: RequirementData, platform: TechPlatform): any {
    const risks = []
    
    // 基于样本类型的风险
    const sampleType = requirements.sampleType || ''
    if (sampleType.includes('心脏') || sampleType.includes('脑') || sampleType.includes('复杂')) {
      risks.push({
        risk: `${sampleType}组织解离难度`,
        probability: "中等",
        impact: "可能影响细胞回收率和RNA质量",
        mitigation: "优化酶解条件，温和处理，预实验验证"
      })
    }
    
    // 基于样本状态的风险
    const sampleCondition = requirements.sampleCondition || ''
    if (sampleCondition.includes('冷冻')) {
      risks.push({
        risk: "冷冻样本RNA降解风险",
        probability: "中-高",
        impact: "可能导致基因检测灵敏度下降",
        mitigation: "快速处理，优选RNA保护试剂，质量预检"
      })
    }
    
    // 基于细胞数量的风险
    const cellCount = this.extractCellCount(requirements.cellCount || '')
    if (cellCount < 1000) {
      risks.push({
        risk: "低细胞数量技术挑战",
        probability: "中等",
        impact: "可能影响数据统计功效",
        mitigation: "选择高灵敏度平台，优化细胞捕获协议"
      })
    }
    
    // 基于紧急程度的风险
    const urgency = requirements.urgencyLevel || requirements.urgency || ''
    if (urgency.includes('紧急')) {
      risks.push({
        risk: "加急处理质量风险",
        probability: "低-中",
        impact: "可能影响实验优化时间",
        mitigation: "预先准备，专人负责，质量检查点"
      })
    }
    
    // 计算整体风险等级和成功概率
    const riskCount = risks.length
    const overallRisk = riskCount <= 1 ? "低" : riskCount <= 2 ? "低-中等" : "中等"
    const successProb = riskCount <= 1 ? "90-95%" : riskCount <= 2 ? "85-90%" : "80-85%"
    
    return {
      overall_risk_level: overallRisk,
      success_probability: successProb,
      technical_risks: risks
    }
  }

  /**
   * 主函数：生成智能解决方案
   */
  generateIntelligentSolution(requirements: RequirementData): DynamicSolution {
    // 分析平台适配度
    const analyzedPlatforms = this.analyzePlatformSuitability(requirements)
    
    // 选择最佳平台
    const bestPlatformKey = Object.keys(analyzedPlatforms).reduce((best, current) => 
      analyzedPlatforms[current].suitability > analyzedPlatforms[best].suitability ? current : best
    )
    const bestPlatform = analyzedPlatforms[bestPlatformKey]
    
    // 生成动态成本分析
    const costAnalysis = this.calculateDynamicCost(requirements, bestPlatform)
    
    // 生成动态时间规划
    const timeline = this.calculateDynamicTimeline(requirements, bestPlatform)
    
    // 生成动态风险评估
    const riskAssessment = this.generateDynamicRiskAssessment(requirements, bestPlatform)
    
    // 生成推荐理由
    const reasoning = this.generateRecommendationReasoning(requirements, bestPlatform)
    
    return {
      solution_id: `intelligent_dynamic_${Date.now()}`,
      generated_at: new Date().toISOString(),
      client_requirements: requirements,
      recommended_solution: {
        platform: `${bestPlatform.name} (智能推荐)`,
        reasoning,
        specifications: bestPlatform.specifications
      },
      cost_analysis: costAnalysis,
      risk_assessment: riskAssessment,
      timeline,
      deliverables: this.generateDeliverables(requirements),
      next_steps: this.generateNextSteps(requirements),
      contact_info: {
        email: "<EMAIL>",
        phone: "400-AI-CELL (************)"
      },
      intelligent_features: {} // Will be populated by calling code
    }
  }

  private generateRecommendationReasoning(requirements: RequirementData, platform: TechPlatform): string {
    const reasons = []
    
    // 基于研究目标
    const researchGoal = requirements.researchGoal || ''
    if (researchGoal) {
      reasons.push(`基于您的${researchGoal}研究需求`)
    }
    
    // 基于样本类型
    const sampleType = requirements.sampleType || ''
    if (sampleType) {
      reasons.push(`该平台在处理${sampleType}样本方面具有显著优势`)
    }
    
    // 基于细胞数量
    const cellCount = requirements.cellCount || ''
    if (cellCount) {
      reasons.push(`特别适合${cellCount}规模的细胞分析`)
    }
    
    // 基于预算
    const budget = requirements.budget || requirements.budgetRange || ''
    if (budget) {
      reasons.push(`在您的${budget}预算范围内提供最佳性价比`)
    }
    
    // 基于时间要求
    const urgency = requirements.urgencyLevel || requirements.urgency || ''
    if (urgency.includes('紧急')) {
      reasons.push(`能够满足您的紧急时间需求`)
    }
    
    return reasons.length > 0 
      ? `我们推荐使用${platform.name}平台。${reasons.join('，')}。`
      : `基于综合评估，我们推荐使用${platform.name}平台，该平台能够为您的项目提供最佳的技术支持。`
  }

  private generateDeliverables(requirements: RequirementData): string[] {
    const baseDeliverables = [
      "🧬 智能文献推荐报告 (基于AI关键词扩展)",
      "📊 多平台技术对比分析",
      "⚠️ 项目风险评估与缓解策略", 
      "🎯 个性化实验方案设计"
    ]
    
    // 基于分析需求添加交付物
    const analysisNeeds = requirements.dataAnalysisNeeds || requirements.analysisType || ''
    if (analysisNeeds.includes('轨迹') || analysisNeeds.includes('发育')) {
      baseDeliverables.push("🔄 细胞轨迹分析报告")
    }
    if (analysisNeeds.includes('免疫') || analysisNeeds.includes('功能')) {
      baseDeliverables.push("🛡️ 功能富集分析报告")
    }
    
    baseDeliverables.push("📈 预期成果与发表策略规划")
    
    return baseDeliverables
  }

  private generateNextSteps(requirements: RequirementData): string[] {
    const steps = [
      "📚 查看智能文献推荐和热点论文发现",
      "🏆 对比多个技术平台的详细规格和成本",
      "🤝 查看专家合作机会和资源推荐",
      "🚀 制定基于AI洞察的项目实施计划"
    ]
    
    const urgency = requirements.urgencyLevel || requirements.urgency || ''
    if (urgency.includes('紧急')) {
      steps.unshift("⚡ 启动加急处理流程")
    }
    
    return steps
  }
}

// 导出单例实例
export const intelligentSolutionGenerator = new IntelligentSolutionGenerator()