"""
Pytest configuration and fixtures for CellForge AI Backend tests
"""
import pytest
import asyncio
from typing import Async<PERSON>enerator, Dict, Any
from unittest.mock import Mock, AsyncMock

from app.config.keyword_config import KeywordConfig, TerminologyConfig
from app.utils.validation import InputValidator
from app.utils.cache_manager import <PERSON>acheManager
from app.utils.rate_limiter import RedisRateLimiter
from app.utils.metrics import MetricsCollector


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_keyword_config():
    """Test configuration for keyword generation"""
    return KeywordConfig(
        MAX_QUERY_LENGTH=500,
        MAX_KEYWORDS_PER_QUERY=20,
        MAX_PROCESSING_TIME=10.0,
        ENABLE_CACHING=False,  # Disable for tests
        ENABLE_RATE_LIMITING=False,  # Disable for tests
        AI_TIMEOUT=5.0,
        MIN_CONFIDENCE_SCORE=0.1,
        ENABLE_FUZZY_MATCHING=True,
        FUZZY_MATCH_THRESHOLD=0.7,
        PRIMARY_SEARCH_TERMS_COUNT=3,
        SECONDARY_SEARCH_TERMS_COUNT=3,
        MAX_COMBINED_QUERIES=2,
        MAX_BOOLEAN_QUERIES=2
    )


@pytest.fixture
def test_terminology_config():
    """Test terminology configuration"""
    return TerminologyConfig(
        TECH_PLATFORM_WEIGHT=2.0,
        CELL_TYPE_WEIGHT=1.5,
        BIOLOGICAL_PROCESS_WEIGHT=1.0,
        DISEASE_WEIGHT=1.2,
        DOMAIN_WEIGHTS={
            "immunology": 1.0,
            "oncology": 1.0,
            "neuroscience": 0.9
        },
        PLATFORM_PRIORITIES={
            "10x Genomics": 1,
            "Smart-seq": 2,
            "Drop-seq": 3
        }
    )


@pytest.fixture
def mock_validator():
    """Mock input validator"""
    validator = Mock(spec=InputValidator)
    validator.validate_query.return_value = "test query"
    validator.validate_context.return_value = {}
    validator.validate_max_keywords.return_value = 10
    return validator


@pytest.fixture
def mock_cache_manager():
    """Mock cache manager"""
    cache_manager = AsyncMock(spec=CacheManager)
    cache_manager.is_available.return_value = False
    cache_manager.get.return_value = None
    cache_manager.set.return_value = True
    cache_manager.generate_cache_key.return_value = "test_cache_key"
    return cache_manager


@pytest.fixture
def mock_rate_limiter():
    """Mock rate limiter"""
    rate_limiter = AsyncMock(spec=RedisRateLimiter)
    rate_limiter.check_rate_limit.return_value = (True, {"remaining": 99})
    return rate_limiter


@pytest.fixture
def mock_metrics_collector():
    """Mock metrics collector"""
    metrics = Mock(spec=MetricsCollector)
    metrics.record_performance.return_value = None
    metrics.record_quality.return_value = None
    metrics.record_cache_hit.return_value = None
    metrics.record_cache_miss.return_value = None
    metrics.record_error.return_value = None
    return metrics


@pytest.fixture
def sample_query_data():
    """Sample query data for testing"""
    return {
        "simple_chinese": "单细胞RNA测序",
        "complex_chinese": "T细胞和B细胞在肿瘤微环境中的单细胞转录组分析",
        "english": "single cell RNA sequencing analysis",
        "mixed": "10x Genomics单细胞测序数据分析",
        "technical": "Smart-seq2 PBMC scRNA-seq",
        "empty": "",
        "whitespace": "   \n\t  ",
        "special_chars": "单细胞<script>alert('test')</script>",
        "very_long": "单细胞" * 200,
        "numbers_only": "12345",
        "symbols_only": "!@#$%^&*()"
    }


@pytest.fixture
def expected_keywords():
    """Expected keyword extraction results"""
    return {
        "chinese_keywords": [
            "单细胞", "RNA测序", "T细胞", "B细胞", "肿瘤微环境", "转录组分析"
        ],
        "english_keywords": [
            "single cell", "RNA sequencing", "T cell", "B cell", 
            "tumor microenvironment", "transcriptome analysis"
        ],
        "domain_classification": {
            "immunology": ["T cell", "B cell"],
            "oncology": ["tumor microenvironment"],
            "methodology": ["single cell", "RNA sequencing"]
        }
    }


@pytest.fixture
async def mock_ai_service():
    """Mock AI service for testing"""
    ai_service = AsyncMock()
    ai_service.use_real_ai = False
    ai_service.client = None
    return ai_service


@pytest.fixture
def performance_test_data():
    """Data for performance testing"""
    return {
        "test_queries": [
            "单细胞RNA测序分析",
            "T细胞免疫反应研究", 
            "10x Genomics PBMC数据分析",
            "肿瘤微环境单细胞图谱",
            "神经元发育轨迹分析"
        ],
        "expected_durations": {
            "max_duration": 5.0,  # Maximum acceptable duration
            "avg_duration": 2.0   # Average expected duration
        },
        "expected_quality": {
            "min_keywords": 3,
            "min_confidence": 0.3,
            "min_domains": 1
        }
    }


@pytest.fixture
def error_test_cases():
    """Test cases for error handling"""
    return [
        {
            "name": "empty_query",
            "query": "",
            "expected_error": "InvalidInputError"
        },
        {
            "name": "too_long_query", 
            "query": "x" * 2000,
            "expected_error": "InvalidInputError"
        },
        {
            "name": "malicious_query",
            "query": "<script>alert('xss')</script>",
            "expected_error": "InvalidInputError"
        },
        {
            "name": "invalid_context",
            "query": "test",
            "context": "not a dict",
            "expected_error": "ValidationError"
        },
        {
            "name": "invalid_max_keywords",
            "query": "test",
            "max_keywords": -1,
            "expected_error": "InvalidInputError"
        }
    ]


@pytest.fixture
def cache_test_scenarios():
    """Test scenarios for cache functionality"""
    return [
        {
            "name": "cache_hit",
            "query": "单细胞测序",
            "cached_result": {
                "user_query": "单细胞测序",
                "extracted_keywords": {"chinese": ["单细胞", "测序"], "english": ["single cell", "sequencing"]},
                "domain_classification": {},
                "keyword_analysis": {"total_keywords": 2, "confidence_score": 0.8},
                "search_strategies": {},
                "timestamp": "2024-01-01T00:00:00"
            }
        },
        {
            "name": "cache_miss",
            "query": "新查询内容",
            "cached_result": None
        }
    ]


# Helper functions for tests
def assert_valid_keyword_result(result: Dict[str, Any]):
    """Assert that a keyword generation result has the expected structure"""
    required_keys = [
        "user_query", "extracted_keywords", "domain_classification",
        "keyword_analysis", "search_strategies", "timestamp"
    ]
    
    for key in required_keys:
        assert key in result, f"Missing required key: {key}"
    
    # Check extracted_keywords structure
    assert "chinese" in result["extracted_keywords"]
    assert "english" in result["extracted_keywords"]
    assert isinstance(result["extracted_keywords"]["chinese"], list)
    assert isinstance(result["extracted_keywords"]["english"], list)
    
    # Check keyword_analysis structure
    analysis = result["keyword_analysis"]
    assert "total_keywords" in analysis
    assert "confidence_score" in analysis
    assert isinstance(analysis["total_keywords"], int)
    assert isinstance(analysis["confidence_score"], (int, float))
    assert 0.0 <= analysis["confidence_score"] <= 1.0


def assert_performance_within_limits(duration: float, max_duration: float = 5.0):
    """Assert that performance is within acceptable limits"""
    assert duration >= 0, "Duration should be non-negative"
    assert duration <= max_duration, f"Duration {duration}s exceeds limit {max_duration}s"


def create_test_terminology_mapping():
    """Create a minimal terminology mapping for testing"""
    return {
        "单细胞": "single cell",
        "RNA测序": "RNA sequencing", 
        "T细胞": "T cell",
        "B细胞": "B cell",
        "基因表达": "gene expression",
        "细胞分化": "cell differentiation",
        "免疫细胞": "immune cells",
        "肿瘤": "tumor",
        "神经元": "neuron",
        "10x基因组学": "10x Genomics"
    }


def create_test_domain_keywords():
    """Create minimal domain keywords for testing"""
    return {
        "immunology": ["immune", "T cell", "B cell", "antibody"],
        "oncology": ["cancer", "tumor", "malignant"],
        "neuroscience": ["neuron", "brain", "neural"],
        "methodology": ["sequencing", "analysis", "single cell"]
    }