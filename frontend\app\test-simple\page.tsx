"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function TestSimplePage() {
  const [count, setCount] = useState(0)

  return (
    <div className="p-6">
      <Card>
        <CardHeader>
          <CardTitle>简单测试页面</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>这是一个简单的测试页面，用于验证系统是否正常工作。</p>
            <div className="flex items-center gap-4">
              <Button onClick={() => setCount(count + 1)}>
                点击计数: {count}
              </Button>
              <Button variant="outline" onClick={() => setCount(0)}>
                重置
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
