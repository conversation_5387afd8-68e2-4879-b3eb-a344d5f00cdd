"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  History, 
  Search, 
  Trash2, 
  Download, 
  MessageSquare, 
  Calendar,
  Filter,
  MoreVertical,
  Star,
  Archive
} from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "sonner"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface Message {
  id: string
  type: "user" | "ai"
  content: string
  timestamp: Date
  status?: "sending" | "sent" | "read" | "error"
  confidence?: number
  sources?: string[]
}

interface ConversationSession {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
  updatedAt: Date
  isStarred: boolean
  isArchived: boolean
  tags: string[]
  messageCount: number
  lastMessage: string
}

interface ConversationHistoryProps {
  onLoadConversation: (session: ConversationSession) => void
  currentMessages: Message[]
  onSaveCurrentConversation: () => void
}

export function ConversationHistory({
  onLoadConversation,
  currentMessages,
  onSaveCurrentConversation
}: ConversationHistoryProps) {
  const { user } = useAuth()
  const [sessions, setSessions] = useState<ConversationSession[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [filterType, setFilterType] = useState<"all" | "starred" | "archived">("all")
  const [isLoading, setIsLoading] = useState(true)

  // 生成对话标题
  const generateConversationTitle = (messages: Message[]): string => {
    if (messages.length === 0) return "新对话"
    
    const firstUserMessage = messages.find(m => m.type === "user")
    if (firstUserMessage) {
      const content = firstUserMessage.content.trim()
      if (content.length > 30) {
        return content.substring(0, 30) + "..."
      }
      return content
    }
    
    return "新对话"
  }

  // 保存对话到本地存储
  const saveConversation = (messages: Message[], title?: string): string => {
    if (messages.length === 0) return ""
    
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const now = new Date()
    
    const session: ConversationSession = {
      id: sessionId,
      title: title || generateConversationTitle(messages),
      messages,
      createdAt: now,
      updatedAt: now,
      isStarred: false,
      isArchived: false,
      tags: [],
      messageCount: messages.length,
      lastMessage: messages[messages.length - 1]?.content.substring(0, 100) || ""
    }

    try {
      const storageKey = `cellforge_conversations_${user?.id || 'anonymous'}`
      const existingSessions = JSON.parse(localStorage.getItem(storageKey) || '[]')
      const updatedSessions = [session, ...existingSessions].slice(0, 50) // 最多保存50个对话
      
      localStorage.setItem(storageKey, JSON.stringify(updatedSessions))
      setSessions(updatedSessions)
      
      toast.success("对话已保存")
      return sessionId
    } catch (error) {
      console.error('保存对话失败:', error)
      toast.error("保存对话失败")
      return ""
    }
  }

  // 加载对话历史
  const loadConversations = () => {
    try {
      setIsLoading(true)
      const storageKey = `cellforge_conversations_${user?.id || 'anonymous'}`
      const savedSessions = localStorage.getItem(storageKey)
      
      if (savedSessions) {
        const sessions: ConversationSession[] = JSON.parse(savedSessions).map((session: any) => ({
          ...session,
          createdAt: new Date(session.createdAt),
          updatedAt: new Date(session.updatedAt),
          messages: session.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }))
        }))
        
        setSessions(sessions)
      }
    } catch (error) {
      console.error('加载对话历史失败:', error)
      toast.error("加载对话历史失败")
    } finally {
      setIsLoading(false)
    }
  }

  // 删除对话
  const deleteConversation = (sessionId: string) => {
    try {
      const storageKey = `cellforge_conversations_${user?.id || 'anonymous'}`
      const updatedSessions = sessions.filter(s => s.id !== sessionId)
      localStorage.setItem(storageKey, JSON.stringify(updatedSessions))
      setSessions(updatedSessions)
      toast.success("对话已删除")
    } catch (error) {
      console.error('删除对话失败:', error)
      toast.error("删除对话失败")
    }
  }

  // 切换收藏状态
  const toggleStar = (sessionId: string) => {
    try {
      const storageKey = `cellforge_conversations_${user?.id || 'anonymous'}`
      const updatedSessions = sessions.map(session => 
        session.id === sessionId 
          ? { ...session, isStarred: !session.isStarred, updatedAt: new Date() }
          : session
      )
      localStorage.setItem(storageKey, JSON.stringify(updatedSessions))
      setSessions(updatedSessions)
    } catch (error) {
      console.error('更新收藏状态失败:', error)
    }
  }

  // 导出对话
  const exportConversation = (session: ConversationSession) => {
    try {
      const content = session.messages.map(msg => 
        `[${msg.timestamp.toLocaleString()}] ${msg.type === 'user' ? '用户' : 'AI'}: ${msg.content}`
      ).join('\n\n')
      
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${session.title}_${session.createdAt.toISOString().split('T')[0]}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      toast.success("对话已导出")
    } catch (error) {
      console.error('导出对话失败:', error)
      toast.error("导出对话失败")
    }
  }

  // 保存当前对话
  const handleSaveCurrentConversation = () => {
    if (currentMessages.length > 0) {
      saveConversation(currentMessages)
      onSaveCurrentConversation()
    } else {
      toast.error("没有可保存的对话内容")
    }
  }

  // 过滤对话
  const filteredSessions = sessions.filter(session => {
    // 类型过滤
    if (filterType === "starred" && !session.isStarred) return false
    if (filterType === "archived" && !session.isArchived) return false
    if (filterType === "all" && session.isArchived) return false
    
    // 搜索过滤
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return session.title.toLowerCase().includes(query) ||
             session.lastMessage.toLowerCase().includes(query)
    }
    
    return true
  })

  useEffect(() => {
    loadConversations()
  }, [user])

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <History className="h-5 w-5 text-blue-600" />
          对话历史
        </CardTitle>
        
        {/* 搜索和过滤 */}
        <div className="space-y-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="搜索对话..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 h-9"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant={filterType === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterType("all")}
              className="h-7 text-xs"
            >
              全部
            </Button>
            <Button
              variant={filterType === "starred" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterType("starred")}
              className="h-7 text-xs"
            >
              <Star className="h-3 w-3 mr-1" />
              收藏
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleSaveCurrentConversation}
              className="h-7 text-xs ml-auto"
            >
              保存当前对话
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-y-auto p-0">
        {isLoading ? (
          <div className="p-4 text-center text-slate-500">
            加载中...
          </div>
        ) : filteredSessions.length === 0 ? (
          <div className="p-4 text-center text-slate-500">
            {searchQuery ? "没有找到匹配的对话" : "暂无对话历史"}
          </div>
        ) : (
          <div className="space-y-2 p-3">
            {filteredSessions.map((session) => (
              <div
                key={session.id}
                className="p-3 border border-slate-200 rounded-lg hover:bg-slate-50 cursor-pointer transition-colors group"
                onClick={() => onLoadConversation(session)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="text-sm font-medium text-slate-900 truncate">
                        {session.title}
                      </h4>
                      {session.isStarred && (
                        <Star className="h-3 w-3 text-amber-500 fill-current" />
                      )}
                    </div>
                    
                    <p className="text-xs text-slate-500 line-clamp-2 mb-2">
                      {session.lastMessage}
                    </p>
                    
                    <div className="flex items-center gap-2 text-xs text-slate-400">
                      <Calendar className="h-3 w-3" />
                      <span>{session.createdAt.toLocaleDateString()}</span>
                      <MessageSquare className="h-3 w-3 ml-2" />
                      <span>{session.messageCount} 条消息</span>
                    </div>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreVertical className="h-3 w-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation()
                        toggleStar(session.id)
                      }}>
                        <Star className="h-3 w-3 mr-2" />
                        {session.isStarred ? "取消收藏" : "收藏"}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation()
                        exportConversation(session)
                      }}>
                        <Download className="h-3 w-3 mr-2" />
                        导出
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={(e) => {
                          e.stopPropagation()
                          deleteConversation(session.id)
                        }}
                        className="text-red-600"
                      >
                        <Trash2 className="h-3 w-3 mr-2" />
                        删除
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
