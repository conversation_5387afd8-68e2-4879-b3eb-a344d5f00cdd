"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { useAuth } from "@/contexts/auth-context"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Eye, EyeOff, Loader2 } from "lucide-react"
import { toast } from "sonner"

// 表单验证schema
const loginSchema = z.object({
  email: z
    .string()
    .min(1, "请输入邮箱地址")
    .email("请输入有效的邮箱地址"),
  password: z
    .string()
    .min(1, "请输入密码")
    .min(6, "密码至少6位字符"),
})

type LoginFormValues = z.infer<typeof loginSchema>

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState("")
  const { login, isLoading, user } = useAuth()
  const router = useRouter()

  // 如果用户已经登录，重定向到主页
  useEffect(() => {
    if (user) {
      console.log("用户已登录，重定向到主页")
      router.push("/")
    }
  }, [user, router])

  // 初始化表单
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  // 处理表单提交
  const onSubmit = async (values: LoginFormValues) => {
    setError("")
    console.log("开始登录流程:", values.email)

    try {
      console.log("调用登录API...")
      await login(values.email, values.password)
      console.log("登录API调用成功")

      toast.success("登录成功！正在跳转...")

      // 延迟跳转，让用户看到成功消息，并确保认证状态已更新
      console.log("准备跳转到主页...")
      setTimeout(() => {
        console.log("执行页面跳转")
        try {
          router.push("/")
          console.log("router.push 调用完成")
        } catch (routerError) {
          console.error("路由跳转失败:", routerError)
          // 如果路由跳转失败，尝试使用 window.location
          window.location.href = "/"
        }
      }, 1500)

    } catch (err: any) {
      console.error("登录错误:", err)

      // 根据错误类型显示不同的错误信息
      let errorMessage = "登录失败，请稍后重试"

      if (err?.message) {
        if (err.message.includes("邮箱或密码错误")) {
          errorMessage = "邮箱或密码错误，请检查后重试"
        } else if (err.message.includes("账户已被暂停")) {
          errorMessage = "您的账户已被暂停，请联系管理员"
        } else if (err.message.includes("账户不存在")) {
          errorMessage = "账户不存在，请检查邮箱地址"
        } else if (err.message.includes("网络")) {
          errorMessage = "网络连接失败，请检查网络后重试"
        } else {
          errorMessage = err.message
        }
      }

      setError(errorMessage)
      toast.error(errorMessage)
    }
  }

  // 切换密码显示状态
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1 text-center">
          <div className="flex justify-center mb-4">
            <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-lg">CF</span>
            </div>
          </div>
          <CardTitle className="text-2xl font-bold">CellForge AI</CardTitle>
          <CardDescription>单细胞测序方案咨询系统</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>邮箱地址</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="请输入您的邮箱地址"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center justify-between">
                      <FormLabel>密码</FormLabel>
                      <a
                        href="#"
                        className="text-sm text-blue-600 hover:underline"
                        onClick={(e) => {
                          e.preventDefault()
                          toast.info("密码重置功能即将上线")
                        }}
                      >
                        忘记密码?
                      </a>
                    </div>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="请输入您的密码"
                          {...field}
                          disabled={isLoading}
                          className="pr-10"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={togglePasswordVisibility}
                          disabled={isLoading}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4 text-gray-500" />
                          ) : (
                            <Eye className="h-4 w-4 text-gray-500" />
                          )}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    登录中...
                  </>
                ) : (
                  "登录"
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="text-center text-sm text-slate-600">
          <div className="w-full">
            <p>测试账户:</p>
            <p className="mt-1">
              <code className="bg-slate-100 px-1 py-0.5 rounded"><EMAIL></code> (超级管理员)
            </p>
            <p className="mt-1">
              <code className="bg-slate-100 px-1 py-0.5 rounded"><EMAIL></code> (销售人员)
            </p>
            <p className="mt-1">
              <code className="bg-slate-100 px-1 py-0.5 rounded"><EMAIL></code> (客户)
            </p>
            <p className="mt-1">密码分别为: Admin123456, Sales123456, Test123456</p>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
