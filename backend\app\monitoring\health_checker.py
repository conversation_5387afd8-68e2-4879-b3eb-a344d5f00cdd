"""
健康检查器
检查系统各组件的健康状态，包括数据库、外部API、服务等
"""

import time
import json
import asyncio
import aiohttp
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """健康状态"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """健康检查结果"""
    component: str
    status: HealthStatus
    response_time: float
    timestamp: float
    message: str
    details: Dict[str, Any] = None


@dataclass
class ServiceHealth:
    """服务健康状态"""
    service_name: str
    overall_status: HealthStatus
    checks: List[HealthCheckResult]
    last_check_time: float
    uptime_percentage: float
    issues: List[str] = None


class HealthChecker:
    """健康检查器"""
    
    def __init__(self, config_path: str = None):
        self.config = self._load_config(config_path)
        self.health_history = {}
        self.check_results = {}
        self.is_running = False
        self.check_thread = None
        self.executor = ThreadPoolExecutor(max_workers=10)
        self.alert_cache = {}  # 防止重复告警
        
        # 初始化健康检查历史数据库表
        self._init_health_database()
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置"""
        if not config_path:
            config_path = "/mnt/c/Users/<USER>/Desktop/Dev/CellForge AI/backend/app/monitoring/monitoring_config.json"
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get("monitoring", {}).get("health_checks", {})
        except Exception as e:
            logger.error(f"加载健康检查配置失败: {e}")
            return self._get_default_health_config()
    
    def _get_default_health_config(self) -> Dict:
        """获取默认健康检查配置"""
        return {
            "enabled": True,
            "check_interval": 30,
            "endpoints": [
                {
                    "name": "database",
                    "type": "database_connection",
                    "timeout": 5
                },
                {
                    "name": "self_api",
                    "type": "http_endpoint",
                    "url": "http://localhost:8000/health",
                    "timeout": 10
                }
            ]
        }
    
    def _init_health_database(self):
        """初始化健康检查数据库表"""
        try:
            conn = sqlite3.connect('/mnt/c/Users/<USER>/Desktop/Dev/CellForge AI/backend/cellforge.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS health_checks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    component TEXT,
                    status TEXT,
                    response_time REAL,
                    timestamp REAL,
                    message TEXT,
                    details TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_health_component ON health_checks(component)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_health_timestamp ON health_checks(timestamp)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_health_status ON health_checks(status)
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"初始化健康检查数据库失败: {e}")
    
    def start_health_checks(self):
        """开始健康检查"""
        if self.is_running:
            return
            
        self.is_running = True
        self.check_thread = threading.Thread(target=self._health_check_loop, daemon=True)
        self.check_thread.start()
        logger.info("健康检查器已启动")
    
    def stop_health_checks(self):
        """停止健康检查"""
        self.is_running = False
        if self.check_thread:
            self.check_thread.join(timeout=10)
        self.executor.shutdown(wait=True)
        logger.info("健康检查器已停止")
    
    def _health_check_loop(self):
        """健康检查循环"""
        check_interval = self.config.get("check_interval", 30)
        
        while self.is_running:
            try:
                self._perform_all_health_checks()
                self._update_health_history()
                self._persist_health_results()
                time.sleep(check_interval)
            except Exception as e:
                logger.error(f"健康检查循环错误: {e}")
                time.sleep(10)
    
    def _perform_all_health_checks(self):
        """执行所有健康检查"""
        endpoints = self.config.get("endpoints", [])
        
        # 使用线程池并行执行健康检查
        futures = {}
        for endpoint in endpoints:
            future = self.executor.submit(self._perform_single_health_check, endpoint)
            futures[future] = endpoint
        
        # 收集结果
        current_results = {}
        for future in as_completed(futures, timeout=30):
            endpoint = futures[future]
            try:
                result = future.result()
                current_results[endpoint['name']] = result
            except Exception as e:
                logger.error(f"健康检查失败 {endpoint['name']}: {e}")
                current_results[endpoint['name']] = HealthCheckResult(
                    component=endpoint['name'],
                    status=HealthStatus.UNKNOWN,
                    response_time=0.0,
                    timestamp=time.time(),
                    message=f"检查失败: {str(e)}",
                    details={"error": str(e)}
                )
        
        self.check_results = current_results
    
    def _perform_single_health_check(self, endpoint: Dict) -> HealthCheckResult:
        """执行单个健康检查"""
        start_time = time.time()
        component_name = endpoint['name']
        check_type = endpoint['type']
        timeout = endpoint.get('timeout', 10)
        
        try:
            if check_type == "database_connection":
                return self._check_database_health(component_name, timeout, start_time)
            elif check_type == "http_endpoint":
                return self._check_http_endpoint_health(endpoint, start_time)
            elif check_type == "external_api":
                return self._check_external_api_health(endpoint, start_time)
            else:
                return HealthCheckResult(
                    component=component_name,
                    status=HealthStatus.UNKNOWN,
                    response_time=0.0,
                    timestamp=start_time,
                    message=f"不支持的检查类型: {check_type}"
                )
                
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component=component_name,
                status=HealthStatus.UNHEALTHY,
                response_time=response_time,
                timestamp=start_time,
                message=f"健康检查异常: {str(e)}",
                details={"error": str(e), "check_type": check_type}
            )
    
    def _check_database_health(self, component_name: str, timeout: int, start_time: float) -> HealthCheckResult:
        """检查数据库健康状态"""
        try:
            conn = sqlite3.connect(
                '/mnt/c/Users/<USER>/Desktop/Dev/CellForge AI/backend/cellforge.db',
                timeout=timeout
            )
            cursor = conn.cursor()
            
            # 执行简单查询测试连接
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            # 检查数据库大小
            cursor.execute("PRAGMA database_list")
            db_info = cursor.fetchall()
            
            conn.close()
            
            response_time = (time.time() - start_time) * 1000
            
            if result and result[0] == 1:
                return HealthCheckResult(
                    component=component_name,
                    status=HealthStatus.HEALTHY,
                    response_time=response_time,
                    timestamp=start_time,
                    message="数据库连接正常",
                    details={
                        "table_count": len(tables),
                        "database_info": db_info,
                        "connection_time_ms": response_time
                    }
                )
            else:
                return HealthCheckResult(
                    component=component_name,
                    status=HealthStatus.UNHEALTHY,
                    response_time=response_time,
                    timestamp=start_time,
                    message="数据库查询失败"
                )
                
        except sqlite3.OperationalError as e:
            response_time = (time.time() - start_time) * 1000
            if "database is locked" in str(e).lower():
                return HealthCheckResult(
                    component=component_name,
                    status=HealthStatus.DEGRADED,
                    response_time=response_time,
                    timestamp=start_time,
                    message="数据库锁定，性能降级",
                    details={"error": str(e)}
                )
            else:
                return HealthCheckResult(
                    component=component_name,
                    status=HealthStatus.UNHEALTHY,
                    response_time=response_time,
                    timestamp=start_time,
                    message=f"数据库操作错误: {str(e)}",
                    details={"error": str(e)}
                )
    
    def _check_http_endpoint_health(self, endpoint: Dict, start_time: float) -> HealthCheckResult:
        """检查HTTP端点健康状态"""
        url = endpoint['url']
        timeout = endpoint.get('timeout', 10)
        expected_status = endpoint.get('expected_status', [200, 404])  # 404也可能是正常的
        
        try:
            import requests
            response = requests.get(url, timeout=timeout)
            response_time = (time.time() - start_time) * 1000
            
            if response.status_code in expected_status:
                status = HealthStatus.HEALTHY
                message = f"HTTP端点响应正常 (状态码: {response.status_code})"
            elif response.status_code < 500:
                status = HealthStatus.DEGRADED
                message = f"HTTP端点响应异常但可用 (状态码: {response.status_code})"
            else:
                status = HealthStatus.UNHEALTHY
                message = f"HTTP端点服务器错误 (状态码: {response.status_code})"
            
            return HealthCheckResult(
                component=endpoint['name'],
                status=status,
                response_time=response_time,
                timestamp=start_time,
                message=message,
                details={
                    "status_code": response.status_code,
                    "response_size": len(response.content),
                    "url": url
                }
            )
            
        except requests.exceptions.Timeout:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component=endpoint['name'],
                status=HealthStatus.UNHEALTHY,
                response_time=response_time,
                timestamp=start_time,
                message="HTTP端点响应超时",
                details={"error": "timeout", "url": url, "timeout": timeout}
            )
        except requests.exceptions.ConnectionError:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component=endpoint['name'],
                status=HealthStatus.UNHEALTHY,
                response_time=response_time,
                timestamp=start_time,
                message="HTTP端点连接失败",
                details={"error": "connection_error", "url": url}
            )
    
    def _check_external_api_health(self, endpoint: Dict, start_time: float) -> HealthCheckResult:
        """检查外部API健康状态"""
        url = endpoint['url']
        timeout = endpoint.get('timeout', 10)
        
        try:
            import requests
            
            # 对于不同的API，使用不同的检查策略
            if "openai.com" in url:
                headers = {"Authorization": "Bearer invalid-key-for-health-check"}
                response = requests.get(url, headers=headers, timeout=timeout)
            else:
                response = requests.get(url, timeout=timeout)
            
            response_time = (time.time() - start_time) * 1000
            
            # 对于API健康检查，通常任何响应都表示服务可用
            if response.status_code < 500:
                if response.status_code == 401:  # 认证错误，但服务可用
                    status = HealthStatus.HEALTHY
                    message = f"外部API服务可用 (认证错误，状态码: {response.status_code})"
                elif response.status_code < 400:
                    status = HealthStatus.HEALTHY
                    message = f"外部API服务正常 (状态码: {response.status_code})"
                else:
                    status = HealthStatus.DEGRADED
                    message = f"外部API服务部分可用 (状态码: {response.status_code})"
            else:
                status = HealthStatus.UNHEALTHY
                message = f"外部API服务器错误 (状态码: {response.status_code})"
            
            return HealthCheckResult(
                component=endpoint['name'],
                status=status,
                response_time=response_time,
                timestamp=start_time,
                message=message,
                details={
                    "status_code": response.status_code,
                    "url": url,
                    "api_type": self._detect_api_type(url)
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component=endpoint['name'],
                status=HealthStatus.UNHEALTHY,
                response_time=response_time,
                timestamp=start_time,
                message=f"外部API检查失败: {str(e)}",
                details={"error": str(e), "url": url}
            )
    
    def _detect_api_type(self, url: str) -> str:
        """检测API类型"""
        if "openai.com" in url:
            return "openai"
        elif "openalex.org" in url:
            return "openalex"
        elif "anthropic.com" in url:
            return "anthropic"
        else:
            return "unknown"
    
    def _update_health_history(self):
        """更新健康历史"""
        current_time = time.time()
        
        for component_name, result in self.check_results.items():
            if component_name not in self.health_history:
                self.health_history[component_name] = {
                    'checks': [],
                    'uptime_stats': {
                        'total_checks': 0,
                        'healthy_checks': 0,
                        'degraded_checks': 0,
                        'unhealthy_checks': 0
                    }
                }
            
            history = self.health_history[component_name]
            history['checks'].append(result)
            
            # 保持最近1000个检查结果
            if len(history['checks']) > 1000:
                history['checks'] = history['checks'][-1000:]
            
            # 更新统计
            stats = history['uptime_stats']
            stats['total_checks'] += 1
            
            if result.status == HealthStatus.HEALTHY:
                stats['healthy_checks'] += 1
            elif result.status == HealthStatus.DEGRADED:
                stats['degraded_checks'] += 1
            elif result.status == HealthStatus.UNHEALTHY:
                stats['unhealthy_checks'] += 1
    
    def _persist_health_results(self):
        """持久化健康检查结果"""
        try:
            conn = sqlite3.connect('/mnt/c/Users/<USER>/Desktop/Dev/CellForge AI/backend/cellforge.db')
            cursor = conn.cursor()
            
            for component_name, result in self.check_results.items():
                cursor.execute('''
                    INSERT INTO health_checks (component, status, response_time, timestamp, message, details)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    result.component,
                    result.status.value,
                    result.response_time,
                    result.timestamp,
                    result.message,
                    json.dumps(result.details) if result.details else None
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"持久化健康检查结果失败: {e}")
    
    def get_current_health_status(self) -> Dict[str, ServiceHealth]:
        """获取当前健康状态"""
        health_status = {}
        
        for component_name, result in self.check_results.items():
            history = self.health_history.get(component_name, {})
            stats = history.get('uptime_stats', {})
            
            # 计算可用性百分比
            total_checks = stats.get('total_checks', 1)
            healthy_checks = stats.get('healthy_checks', 0)
            degraded_checks = stats.get('degraded_checks', 0)
            
            uptime_percentage = ((healthy_checks + degraded_checks * 0.5) / total_checks) * 100
            
            # 检查是否有问题
            issues = []
            if result.status == HealthStatus.UNHEALTHY:
                issues.append(f"服务不健康: {result.message}")
            elif result.status == HealthStatus.DEGRADED:
                issues.append(f"服务性能降级: {result.message}")
            
            if result.response_time > 5000:  # 响应时间超过5秒
                issues.append(f"响应时间过长: {result.response_time:.2f}ms")
            
            health_status[component_name] = ServiceHealth(
                service_name=component_name,
                overall_status=result.status,
                checks=[result],
                last_check_time=result.timestamp,
                uptime_percentage=round(uptime_percentage, 2),
                issues=issues if issues else None
            )
        
        return health_status
    
    def get_health_summary(self) -> Dict:
        """获取健康状态摘要"""
        current_status = self.get_current_health_status()
        
        healthy_count = len([s for s in current_status.values() if s.overall_status == HealthStatus.HEALTHY])
        degraded_count = len([s for s in current_status.values() if s.overall_status == HealthStatus.DEGRADED])
        unhealthy_count = len([s for s in current_status.values() if s.overall_status == HealthStatus.UNHEALTHY])
        unknown_count = len([s for s in current_status.values() if s.overall_status == HealthStatus.UNKNOWN])
        
        total_services = len(current_status)
        
        # 计算整体健康状态
        if unhealthy_count > 0:
            overall_status = HealthStatus.UNHEALTHY
        elif degraded_count > 0:
            overall_status = HealthStatus.DEGRADED
        elif healthy_count == total_services:
            overall_status = HealthStatus.HEALTHY
        else:
            overall_status = HealthStatus.UNKNOWN
        
        # 计算平均响应时间
        avg_response_time = 0
        if current_status:
            response_times = [s.checks[0].response_time for s in current_status.values() if s.checks]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        return {
            "overall_status": overall_status.value,
            "total_services": total_services,
            "healthy_services": healthy_count,
            "degraded_services": degraded_count,
            "unhealthy_services": unhealthy_count,
            "unknown_services": unknown_count,
            "average_response_time": round(avg_response_time, 2),
            "last_check_time": max([s.last_check_time for s in current_status.values()]) if current_status else 0,
            "uptime_percentage": round(sum([s.uptime_percentage for s in current_status.values()]) / max(total_services, 1), 2)
        }
    
    def get_health_history(self, component: str = None, hours: int = 24) -> Dict:
        """获取健康检查历史"""
        end_time = time.time()
        start_time = end_time - (hours * 3600)
        
        try:
            conn = sqlite3.connect('/mnt/c/Users/<USER>/Desktop/Dev/CellForge AI/backend/cellforge.db')
            cursor = conn.cursor()
            
            if component:
                cursor.execute('''
                    SELECT component, status, response_time, timestamp, message
                    FROM health_checks 
                    WHERE component = ? AND timestamp BETWEEN ? AND ?
                    ORDER BY timestamp DESC
                    LIMIT 1000
                ''', (component, start_time, end_time))
            else:
                cursor.execute('''
                    SELECT component, status, response_time, timestamp, message
                    FROM health_checks 
                    WHERE timestamp BETWEEN ? AND ?
                    ORDER BY timestamp DESC
                    LIMIT 1000
                ''', (start_time, end_time))
            
            history_data = []
            for row in cursor.fetchall():
                component_name, status, response_time, timestamp, message = row
                history_data.append({
                    'component': component_name,
                    'status': status,
                    'response_time': response_time,
                    'timestamp': timestamp,
                    'datetime': datetime.fromtimestamp(timestamp).isoformat(),
                    'message': message
                })
            
            conn.close()
            
            return {
                'history': history_data,
                'time_range': {
                    'start': datetime.fromtimestamp(start_time).isoformat(),
                    'end': datetime.fromtimestamp(end_time).isoformat(),
                    'hours': hours
                },
                'total_records': len(history_data)
            }
            
        except Exception as e:
            logger.error(f"获取健康历史失败: {e}")
            return {}
    
    def check_alerts(self) -> List[Dict]:
        """检查是否需要发送告警"""
        alerts = []
        current_time = time.time()
        
        current_status = self.get_current_health_status()
        
        for component_name, service_health in current_status.items():
            # 检查是否需要告警
            alert_key = f"{component_name}_{service_health.overall_status.value}"
            
            # 防止重复告警（5分钟内不重复）
            if alert_key in self.alert_cache:
                if current_time - self.alert_cache[alert_key] < 300:
                    continue
            
            if service_health.overall_status == HealthStatus.UNHEALTHY:
                alerts.append({
                    'type': 'health_alert',
                    'severity': 'critical',
                    'component': component_name,
                    'status': service_health.overall_status.value,
                    'message': f"{component_name} 服务不健康",
                    'details': service_health.issues,
                    'response_time': service_health.checks[0].response_time if service_health.checks else 0,
                    'uptime_percentage': service_health.uptime_percentage,
                    'timestamp': current_time
                })
                self.alert_cache[alert_key] = current_time
                
            elif service_health.overall_status == HealthStatus.DEGRADED:
                alerts.append({
                    'type': 'health_alert',
                    'severity': 'warning',
                    'component': component_name,
                    'status': service_health.overall_status.value,
                    'message': f"{component_name} 服务性能降级",
                    'details': service_health.issues,
                    'response_time': service_health.checks[0].response_time if service_health.checks else 0,
                    'uptime_percentage': service_health.uptime_percentage,
                    'timestamp': current_time
                })
                self.alert_cache[alert_key] = current_time
        
        return alerts
    
    def force_health_check(self, component: str = None) -> Dict:
        """强制执行健康检查"""
        if component:
            # 检查特定组件
            endpoints = [ep for ep in self.config.get("endpoints", []) if ep['name'] == component]
            if not endpoints:
                return {"error": f"未找到组件 {component}"}
        else:
            # 检查所有组件
            endpoints = self.config.get("endpoints", [])
        
        results = {}
        for endpoint in endpoints:
            try:
                result = self._perform_single_health_check(endpoint)
                results[endpoint['name']] = asdict(result)
            except Exception as e:
                results[endpoint['name']] = {
                    "error": str(e),
                    "timestamp": time.time()
                }
        
        return results


# 全局健康检查器实例
health_checker = HealthChecker()