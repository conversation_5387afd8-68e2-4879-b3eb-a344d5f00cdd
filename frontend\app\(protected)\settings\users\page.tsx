"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { RoleBadge } from "@/components/role-badge"
import { PermissionGate } from "@/components/permission-gate"
import { Search, Edit, Trash2, UserPlus, Mail, Lock, MoreHorizontal } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import type { UserRole } from "@/contexts/auth-context"

interface User {
  id: string
  name: string
  email: string
  role: UserRole
  department?: string
  status: "active" | "inactive" | "pending"
  lastActive?: string
  createdAt: string
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([
    {
      id: "1",
      name: "张三",
      email: "<EMAIL>",
      role: "super_admin",
      department: "管理层",
      status: "active",
      lastActive: "2024-05-20 10:30",
      createdAt: "2023-01-15",
    },
    {
      id: "2",
      name: "李四",
      email: "<EMAIL>",
      role: "sales",
      department: "销售部",
      status: "active",
      lastActive: "2024-05-19 16:45",
      createdAt: "2023-02-20",
    },
    {
      id: "3",
      name: "王五",
      email: "<EMAIL>",
      role: "operations",
      department: "技术运维部",
      status: "active",
      lastActive: "2024-05-20 09:15",
      createdAt: "2023-03-10",
    },
    {
      id: "4",
      name: "赵六",
      email: "<EMAIL>",
      role: "sales",
      department: "销售部",
      status: "inactive",
      lastActive: "2024-05-10 14:20",
      createdAt: "2023-04-05",
    },
    {
      id: "5",
      name: "钱七",
      email: "<EMAIL>",
      role: "customer",
      status: "active",
      lastActive: "2024-05-18 11:30",
      createdAt: "2023-05-12",
    },
    {
      id: "6",
      name: "孙八",
      email: "<EMAIL>",
      role: "customer",
      status: "pending",
      createdAt: "2024-05-15",
    },
  ])

  const [searchQuery, setSearchQuery] = useState("")
  const [isCreating, setIsCreating] = useState(false)
  const [newUser, setNewUser] = useState<Partial<User>>({
    name: "",
    email: "",
    role: "customer",
    department: "",
  })

  // 过滤用户
  const filteredUsers = users.filter((user) => {
    if (!searchQuery) return true
    return (
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.department?.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })

  // 创建用户
  const handleCreateUser = () => {
    if (!newUser.name || !newUser.email || !newUser.role) return

    const newUserObj: User = {
      id: Date.now().toString(),
      name: newUser.name,
      email: newUser.email,
      role: newUser.role,
      department: newUser.department,
      status: "pending",
      createdAt: new Date().toISOString().split("T")[0],
    }

    setUsers([...users, newUserObj])
    setNewUser({
      name: "",
      email: "",
      role: "customer",
      department: "",
    })
    setIsCreating(false)
  }

  // 删除用户
  const handleDeleteUser = (userId: string) => {
    setUsers(users.filter((user) => user.id !== userId))
  }

  // 激活/停用用户
  const handleToggleUserStatus = (userId: string) => {
    setUsers(
      users.map((user) => {
        if (user.id === userId) {
          return {
            ...user,
            status: user.status === "active" ? "inactive" : "active",
          }
        }
        return user
      }),
    )
  }

  // 重置密码
  const handleResetPassword = (userId: string) => {
    // 实际应用中这里会调用API发送重置密码邮件
    alert(`已向用户 ID: ${userId} 发送密码重置邮件`)
  }

  return (
    <div className="container py-10">
      <PermissionGate permission="view_users" fallback={<div>您没有权限访问此页面</div>}>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">用户管理</h1>
            <p className="text-muted-foreground">管理系统用户账户</p>
          </div>
          <PermissionGate permission="add_users">
            <Dialog open={isCreating} onOpenChange={setIsCreating}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="h-4 w-4 mr-2" />
                  添加用户
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>添加新用户</DialogTitle>
                  <DialogDescription>创建新用户账户并设置基本信息</DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="user-name">姓名</Label>
                    <Input
                      id="user-name"
                      placeholder="输入用户姓名"
                      value={newUser.name}
                      onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="user-email">邮箱</Label>
                    <Input
                      id="user-email"
                      type="email"
                      placeholder="输入用户邮箱"
                      value={newUser.email}
                      onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="user-role">角色</Label>
                    <select
                      id="user-role"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={newUser.role}
                      onChange={(e) => setNewUser({ ...newUser, role: e.target.value as UserRole })}
                    >
                      <option value="super_admin">超级管理员</option>
                      <option value="sales">销售人员</option>
                      <option value="operations">运维人员</option>
                      <option value="customer">客户</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="user-department">部门</Label>
                    <Input
                      id="user-department"
                      placeholder="输入用户部门"
                      value={newUser.department}
                      onChange={(e) => setNewUser({ ...newUser, department: e.target.value })}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreating(false)}>
                    取消
                  </Button>
                  <Button onClick={handleCreateUser} disabled={!newUser.name || !newUser.email || !newUser.role}>
                    创建用户
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </PermissionGate>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>用户列表</CardTitle>
                <CardDescription>管理系统中的所有用户账户</CardDescription>
              </div>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="搜索用户..."
                  className="pl-8 w-[250px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="all">所有用户</TabsTrigger>
                <TabsTrigger value="active">活跃用户</TabsTrigger>
                <TabsTrigger value="inactive">非活跃用户</TabsTrigger>
                <TabsTrigger value="pending">待激活用户</TabsTrigger>
              </TabsList>

              <TabsContent value="all">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>用户</TableHead>
                      <TableHead>角色</TableHead>
                      <TableHead>部门</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>上次活动</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                          未找到匹配的用户
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <Avatar>
                                <AvatarImage src={`/placeholder.svg?height=40&width=40&query=${user.name}`} />
                                <AvatarFallback>{user.name[0]}</AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium">{user.name}</div>
                                <div className="text-sm text-muted-foreground">{user.email}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <RoleBadge role={user.role} />
                          </TableCell>
                          <TableCell>{user.department || "-"}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                user.status === "active"
                                  ? "default"
                                  : user.status === "inactive"
                                    ? "secondary"
                                    : "outline"
                              }
                            >
                              {user.status === "active" ? "活跃" : user.status === "inactive" ? "非活跃" : "待激活"}
                            </Badge>
                          </TableCell>
                          <TableCell>{user.lastActive || "-"}</TableCell>
                          <TableCell>{user.createdAt}</TableCell>
                          <TableCell className="text-right">
                            <PermissionGate permission="edit_users">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>用户操作</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => alert("编辑用户: " + user.id)}>
                                    <Edit className="h-4 w-4 mr-2" />
                                    编辑用户
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleResetPassword(user.id)}>
                                    <Lock className="h-4 w-4 mr-2" />
                                    重置密码
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleToggleUserStatus(user.id)}>
                                    {user.status === "active" ? "停用账户" : "激活账户"}
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-red-600" onClick={() => handleDeleteUser(user.id)}>
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    删除用户
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </PermissionGate>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TabsContent>

              <TabsContent value="active">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>用户</TableHead>
                      <TableHead>角色</TableHead>
                      <TableHead>部门</TableHead>
                      <TableHead>上次活动</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.filter((user) => user.status === "active").length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                          未找到活跃用户
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers
                        .filter((user) => user.status === "active")
                        .map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <Avatar>
                                  <AvatarImage src={`/placeholder.svg?height=40&width=40&query=${user.name}`} />
                                  <AvatarFallback>{user.name[0]}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium">{user.name}</div>
                                  <div className="text-sm text-muted-foreground">{user.email}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <RoleBadge role={user.role} />
                            </TableCell>
                            <TableCell>{user.department || "-"}</TableCell>
                            <TableCell>{user.lastActive}</TableCell>
                            <TableCell>{user.createdAt}</TableCell>
                            <TableCell className="text-right">
                              <PermissionGate permission="edit_users">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="icon">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>用户操作</DropdownMenuLabel>
                                    <DropdownMenuItem onClick={() => alert("编辑用户: " + user.id)}>
                                      <Edit className="h-4 w-4 mr-2" />
                                      编辑用户
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleResetPassword(user.id)}>
                                      <Lock className="h-4 w-4 mr-2" />
                                      重置密码
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleToggleUserStatus(user.id)}>
                                      停用账户
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      className="text-red-600"
                                      onClick={() => handleDeleteUser(user.id)}
                                    >
                                      <Trash2 className="h-4 w-4 mr-2" />
                                      删除用户
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </PermissionGate>
                            </TableCell>
                          </TableRow>
                        ))
                    )}
                  </TableBody>
                </Table>
              </TabsContent>

              <TabsContent value="inactive">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>用户</TableHead>
                      <TableHead>角色</TableHead>
                      <TableHead>部门</TableHead>
                      <TableHead>上次活动</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.filter((user) => user.status === "inactive").length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                          未找到非活跃用户
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers
                        .filter((user) => user.status === "inactive")
                        .map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <Avatar>
                                  <AvatarImage src={`/placeholder.svg?height=40&width=40&query=${user.name}`} />
                                  <AvatarFallback>{user.name[0]}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium">{user.name}</div>
                                  <div className="text-sm text-muted-foreground">{user.email}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <RoleBadge role={user.role} />
                            </TableCell>
                            <TableCell>{user.department || "-"}</TableCell>
                            <TableCell>{user.lastActive}</TableCell>
                            <TableCell>{user.createdAt}</TableCell>
                            <TableCell className="text-right">
                              <PermissionGate permission="edit_users">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="icon">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>用户操作</DropdownMenuLabel>
                                    <DropdownMenuItem onClick={() => alert("编辑用户: " + user.id)}>
                                      <Edit className="h-4 w-4 mr-2" />
                                      编辑用户
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleResetPassword(user.id)}>
                                      <Lock className="h-4 w-4 mr-2" />
                                      重置密码
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleToggleUserStatus(user.id)}>
                                      激活账户
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      className="text-red-600"
                                      onClick={() => handleDeleteUser(user.id)}
                                    >
                                      <Trash2 className="h-4 w-4 mr-2" />
                                      删除用户
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </PermissionGate>
                            </TableCell>
                          </TableRow>
                        ))
                    )}
                  </TableBody>
                </Table>
              </TabsContent>

              <TabsContent value="pending">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>用户</TableHead>
                      <TableHead>角色</TableHead>
                      <TableHead>部门</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.filter((user) => user.status === "pending").length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-6 text-muted-foreground">
                          未找到待激活用户
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers
                        .filter((user) => user.status === "pending")
                        .map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <Avatar>
                                  <AvatarImage src={`/placeholder.svg?height=40&width=40&query=${user.name}`} />
                                  <AvatarFallback>{user.name[0]}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium">{user.name}</div>
                                  <div className="text-sm text-muted-foreground">{user.email}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <RoleBadge role={user.role} />
                            </TableCell>
                            <TableCell>{user.department || "-"}</TableCell>
                            <TableCell>{user.createdAt}</TableCell>
                            <TableCell className="text-right">
                              <PermissionGate permission="edit_users">
                                <div className="flex justify-end gap-2">
                                  <Button size="sm" onClick={() => handleToggleUserStatus(user.id)}>
                                    激活账户
                                  </Button>
                                  <Button variant="outline" size="sm" onClick={() => handleResetPassword(user.id)}>
                                    <Mail className="h-4 w-4 mr-2" />
                                    发送邀请
                                  </Button>
                                </div>
                              </PermissionGate>
                            </TableCell>
                          </TableRow>
                        ))
                    )}
                  </TableBody>
                </Table>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </PermissionGate>
    </div>
  )
}
