from typing import Dict, List, Optional, AsyncGenerator, Any
from datetime import datetime
import openai
from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage, AIMessage
from langchain.prompts import ChatPromptTemplate
import asyncio
import logging
from enum import Enum

from app.core.config import settings
from app.services.legacy_interfaces.prompt_manager import SecurePromptManager, ConversationStage

logger = logging.getLogger(__name__)


class AIResponse:
    def __init__(self, content: str, confidence: float, sources: List[str] = None, suggestions: List[str] = None):
        self.content = content
        self.confidence = confidence
        self.sources = sources or []
        self.suggestions = suggestions or []


class AIService:
    def __init__(self):
        # 初始化安全提示词管理器
        self.prompt_manager = SecurePromptManager()

        # 检查是否有API密钥和是否启用真实AI
        self.use_real_ai = settings.USE_REAL_AI and bool(settings.get_api_key())

        if self.use_real_ai:
            try:
                self.client = ChatOpenAI(
                    model=settings.get_api_model(),
                    temperature=0.3,
                    openai_api_key=settings.get_api_key(),
                    openai_api_base=settings.get_api_base()
                )
                print(f"✅ 使用真实AI服务: {settings.get_api_model()} @ {settings.get_api_base()}")
            except Exception as e:
                print(f"❌ AI服务初始化失败: {e}")
                print("🔄 切换到模拟AI响应模式")
                self.client = None
                self.use_real_ai = False
        else:
            # 使用模拟AI服务
            self.client = None
            if not settings.get_api_key():
                print("⚠️  警告: 未配置API密钥，使用模拟AI响应")
            else:
                print("ℹ️  信息: USE_REAL_AI=False，使用模拟AI响应模式")

        # 单细胞测序专业提示词模板
        self.system_prompt = """
你是 CellForge AI，一个专业的单细胞测序方案咨询专家。你拥有深厚的单细胞生物学、基因组学和生物信息学专业知识。

## 核心专业能力：

### 🔬 技术平台专精
- **10x Genomics Chromium**: 高通量单细胞转录组、表观组、多组学分析
- **Smart-seq系列**: 全长转录本测序，适合深度分析
- **Drop-seq/inDrop**: 成本效益优化的大规模细胞分析
- **MARS-seq/CEL-seq**: 特定应用场景的技术选择
- **Spatial Transcriptomics**: 空间转录组学技术

### 🧬 应用场景精通
- **发育生物学**: 细胞分化轨迹、发育时序分析
- **肿瘤研究**: 肿瘤微环境、耐药机制、免疫浸润
- **免疫学**: T/B细胞亚群、免疫应答、自身免疫疾病
- **神经科学**: 神经元分类、神经发育、神经退行性疾病
- **药物研发**: 药物筛选、毒性评估、机制研究

### 📊 数据分析专长
- **质量控制**: 细胞过滤、基因过滤、批次效应校正
- **降维聚类**: PCA、t-SNE、UMAP、Leiden/Louvain聚类
- **差异分析**: DEG分析、功能富集、通路分析
- **轨迹分析**: Pseudotime、RNA velocity、细胞命运决定
- **细胞通讯**: 配体-受体分析、细胞间相互作用

### 💰 成本效益优化
- **预算规划**: 基于研究目标的成本预估
- **方案比较**: 不同平台的性价比分析
- **资源配置**: 样本数量、测序深度优化建议

## 用户背景信息：
- 机构：{organization}
- 专业领域：{expertise}
- 研究兴趣：{research_interests}

## 相关知识库内容：
{relevant_knowledge}

## 近期对话历史：
{conversation_history}

## 回答要求：
1. **专业准确**: 基于最新科学文献和技术发展
2. **个性化**: 根据用户背景定制建议
3. **实用性**: 提供具体可操作的方案
4. **成本意识**: 考虑预算限制和性价比
5. **风险提示**: 指出潜在技术难点
6. **结构清晰**: 使用标题、列表、重点标记

请始终用中文回答，保持专业、友好、耐心的态度。
        """

    async def generate_response(
        self,
        message: str,
        context: Dict,
        conversation_type: str = "general",
        enable_literature_search: bool = False
    ) -> AIResponse:
        """
        生成 AI 回复（带安全检查）
        """
        try:
            # 安全检查：检测提示词套取尝试
            security_response = self.prompt_manager.get_security_response(message)
            if security_response:
                return AIResponse(
                    content=security_response,
                    confidence=1.0,
                    sources=["安全系统"],
                    suggestions=["请告诉我您的研究目标", "您需要什么技术支持？", "有什么具体的实验需求吗？"]
                )

            # 确定对话阶段
            conversation_stage = self._determine_conversation_stage(context)

            # 检查是否有结构化需求信息
            requirements = context.get("requirements")
            if requirements and conversation_type == "requirement_based":
                return await self._generate_requirement_based_response(message, requirements, context)

            # 构建安全的提示词
            conversation_history = context.get("conversation_history", [])
            user_context = context.get("user_profile", {})

            secure_prompt = self.prompt_manager.build_secure_prompt(
                stage=conversation_stage,
                user_context=user_context,
                conversation_history=conversation_history,
                user_message=message
            )

            if not secure_prompt:
                # 如果提示词构建失败（安全威胁），返回安全响应
                return AIResponse(
                    content="让我们专注于您的单细胞测序需求。请告诉我您的具体研究目标。",
                    confidence=1.0,
                    sources=["安全系统"],
                    suggestions=["研究目标是什么？", "样本类型是什么？", "预算范围如何？"]
                )

            if self.use_real_ai and self.client:
                # 使用真实AI服务
                messages = [SystemMessage(content=secure_prompt)]

                # 添加对话历史
                if conversation_history:
                    for msg in conversation_history[-5:]:  # 只保留最近5轮对话
                        if msg["role"] == "user":
                            messages.append(HumanMessage(content=msg["content"]))
                        else:
                            messages.append(AIMessage(content=msg["content"]))

                # 添加当前用户消息
                messages.append(HumanMessage(content=message))

                # 调用 AI 模型
                response = await self.client.ainvoke(messages)
                ai_content = response.content
            else:
                # 使用模拟AI响应
                ai_content = self._generate_mock_response(message, context)

            # 可选的文献推荐功能
            literature_recommendations = []
            if enable_literature_search:
                literature_recommendations = await self._get_literature_recommendations_optional(message, context)

            # 如果有文献推荐，将其整合到回复中
            if literature_recommendations:
                ai_content = await self._integrate_literature_citations(ai_content, literature_recommendations)

            # 分析回复质量和置信度
            confidence = self._calculate_confidence(ai_content, context)

            # 提取相关来源（包括文献来源）
            sources = self._extract_sources(context.get("relevant_knowledge", []))
            if literature_recommendations:
                literature_sources = [f"{lit['literature']['journal']} ({lit['literature']['publication_year']})"
                                    for lit in literature_recommendations[:2]]
                sources.extend(literature_sources)

            # 生成建议
            suggestions = await self._generate_suggestions(message, ai_content)

            return AIResponse(
                content=ai_content,
                confidence=confidence,
                sources=sources,
                suggestions=suggestions
            )

        except Exception as e:
            return AIResponse(
                content=f"抱歉，处理您的请求时出现错误：{str(e)}",
                confidence=0.0
            )

    async def generate_streaming_response(
        self,
        message: str,
        context: Dict
    ) -> AsyncGenerator[str, None]:
        """
        生成流式回复
        """
        try:
            system_message = self._build_system_message(context)

            messages = [
                {"role": "system", "content": system_message},
                {"role": "user", "content": message}
            ]

            # 使用 OpenAI 的流式 API
            stream = await openai.ChatCompletion.acreate(
                model=settings.OPENAI_MODEL,
                messages=messages,
                stream=True,
                temperature=0.3
            )

            async for chunk in stream:
                if chunk.choices[0].delta.get("content"):
                    yield chunk.choices[0].delta["content"]

        except Exception as e:
            yield f"流式响应错误：{str(e)}"

    def _build_system_message(self, context: Dict) -> str:
        """
        构建系统提示词
        """
        user_profile = context.get("user_profile", {})
        relevant_knowledge = context.get("relevant_knowledge", [])
        conversation_history = context.get("conversation_history", [])

        # 格式化知识库内容
        knowledge_text = "\n".join([
            f"- {item.get('title', '')}: {item.get('content', '')}"
            for item in relevant_knowledge[:3]  # 只包含前3个最相关的
        ])

        # 格式化对话历史
        history_text = "\n".join([
            f"{msg['role']}: {msg['content']}"
            for msg in conversation_history[-3:]  # 只包含最近3轮
        ])

        return self.system_prompt.format(
            organization=user_profile.get("organization", "未知"),
            expertise=user_profile.get("expertise", "未指定"),
            research_interests=user_profile.get("research_interests", "未指定"),
            relevant_knowledge=knowledge_text,
            conversation_history=history_text
        )

    def _calculate_confidence(self, response: str, context: Dict) -> float:
        """
        计算回复的置信度
        """
        # 简单的置信度计算逻辑
        confidence = 0.8  # 基础置信度

        # 如果有相关知识库支撑，提高置信度
        if context.get("relevant_knowledge"):
            confidence += 0.1

        # 如果回复包含具体的技术细节，提高置信度
        technical_keywords = ["测序", "文库", "细胞", "基因", "表达", "分析"]
        keyword_count = sum(1 for keyword in technical_keywords if keyword in response)
        confidence += keyword_count * 0.02

        return min(confidence, 1.0)

    def _extract_sources(self, knowledge_items: List[Dict]) -> List[str]:
        """
        提取回复的来源
        """
        return [
            item.get("source", item.get("title", "知识库"))
            for item in knowledge_items[:3]
        ]

    async def _generate_suggestions(self, user_message: str, ai_response: str) -> List[str]:
        """
        基于对话内容生成相关建议
        """
        suggestions = []

        # 基于关键词生成建议
        if "成本" in user_message or "费用" in user_message:
            suggestions.append("查看详细的成本预算分析")
            suggestions.append("比较不同测序平台的性价比")

        if "时间" in user_message or "周期" in user_message:
            suggestions.append("了解项目时间规划建议")
            suggestions.append("查看加急处理方案")

        if "样本" in user_message:
            suggestions.append("获取样本制备最佳实践")
            suggestions.append("了解样本质量控制要点")

        return suggestions[:3]  # 最多返回3个建议

    async def _get_literature_recommendations_optional(self, message: str, context: Dict) -> List[Dict]:
        """
        可选的文献推荐功能 - 已迁移到新的智能研究系统
        """
        # 该功能已迁移到 intelligent_research_advisor 中的新架构
        # 返回空列表以保持向后兼容性
        return []

    async def _get_literature_recommendations(self, message: str, context: Dict) -> List[Dict]:
        """
        获取相关文献推荐（保持向后兼容）
        """
        try:
            # 构建文献推荐的上下文
            literature_context = {
                "user_message": message,
                "user_profile": context.get("user_profile", {}),
                "requirements": context.get("requirements", {}),
                "conversation_history": context.get("conversation_history", [])
            }

            # 获取文献推荐
            recommendations = await literature_service.get_literature_recommendations(
                context=literature_context,
                recommendation_type="mixed",
                top_k=3
            )

            return recommendations

        except Exception as e:
            print(f"获取文献推荐失败: {e}")
            return []

    def _extract_requirements_from_message(self, message: str, context: Dict) -> Dict[str, Any]:
        """
        从消息中提取基础需求信息
        """
        requirements = {}

        # 简单的关键词提取
        message_lower = message.lower()

        # 组织类型识别
        tissue_keywords = {
            "心脏": "cardiac",
            "心肌": "cardiac",
            "脑": "brain",
            "神经": "neural",
            "免疫": "immune",
            "血液": "blood",
            "肿瘤": "tumor",
            "癌": "cancer"
        }

        for keyword, tissue_type in tissue_keywords.items():
            if keyword in message:
                requirements["tissue_type"] = tissue_type
                break

        # 研究目标识别
        if any(word in message_lower for word in ["分型", "分类", "识别"]):
            requirements["research_goal"] = "cell_type_identification"
        elif any(word in message_lower for word in ["轨迹", "发育", "分化"]):
            requirements["research_goal"] = "trajectory_analysis"
        elif any(word in message_lower for word in ["差异", "表达"]):
            requirements["research_goal"] = "differential_expression"

        # 研究问题
        requirements["research_question"] = message[:100]  # 取前100个字符作为研究问题

        return requirements

    async def _integrate_literature_citations(self, ai_content: str, literature_recommendations: List[Dict]) -> str:
        """
        将文献引用整合到AI回复中
        """
        try:
            if not literature_recommendations:
                return ai_content

            # 在回复末尾添加文献支持部分
            citations_section = "\n\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n📚 **相关文献支持**\n\n"

            for i, rec in enumerate(literature_recommendations, 1):
                lit = rec["literature"]
                citations_section += f"**[{i}] {lit['title']}**\n"
                citations_section += f"*{', '.join(lit['authors'][:3])}{'等' if len(lit['authors']) > 3 else ''}*\n"
                citations_section += f"📖 {lit['journal']} ({lit['publication_year']})\n"

                if lit.get('impact_factor'):
                    citations_section += f"🏆 影响因子: {lit['impact_factor']}\n"

                citations_section += f"💡 **相关性**: {rec['relevance_explanation']}\n"

                if rec.get('key_support_points'):
                    citations_section += f"🔑 **支持要点**: {rec['key_support_points'][0]}\n"

                if lit.get('doi'):
                    citations_section += f"🔗 DOI: {lit['doi']}\n"

                citations_section += "\n"

            return ai_content + citations_section

        except Exception as e:
            print(f"整合文献引用失败: {e}")
            return ai_content

    def _determine_conversation_stage(self, context: Dict) -> ConversationStage:
        """
        根据上下文确定当前对话阶段
        """
        conversation_history = context.get("conversation_history", [])
        user_profile = context.get("user_profile", {})

        # 如果是第一次对话
        if not conversation_history:
            return ConversationStage.INITIAL_CONTACT

        # 简单的阶段判断逻辑（可以根据需要扩展）
        message_count = len(conversation_history)

        if message_count <= 2:
            return ConversationStage.INITIAL_CONTACT
        elif message_count <= 6:
            return ConversationStage.REQUIREMENT_COLLECTION
        elif message_count <= 10:
            return ConversationStage.DEEP_DIVE_QUESTIONS
        else:
            return ConversationStage.REQUIREMENT_VALIDATION

    async def _generate_requirement_based_response(
        self,
        message: str,
        requirements: Dict,
        context: Dict
    ) -> AIResponse:
        """
        基于结构化需求生成专业回复（集成意图分析）
        """
        try:
            # 第1步：分析用户意图
            intent_analysis = await self.analyze_user_intent(requirements, message)

            # 第2步：构建增强的专业提示词
            enhanced_prompt = self._build_intent_aware_prompt(requirements, intent_analysis, context)

            if self.use_real_ai and self.client:
                messages = [
                    SystemMessage(content=enhanced_prompt),
                    HumanMessage(content=f"用户需求：{message}\n\n请基于意图分析结果生成个性化的专业建议。")
                ]

                response = await self.client.ainvoke(messages)
                ai_content = response.content

                # 使用真实AI时的高置信度
                confidence = 0.95
                sources = ["AI意图分析引擎", "专业知识库", "技术规格数据库"]
            else:
                # 使用增强的模拟响应（基于意图分析）
                ai_content = self._generate_intent_based_mock_response(requirements, intent_analysis)
                confidence = 0.85
                sources = ["意图分析系统", "规则引擎", "技术知识库"]

            # 基于意图分析生成专业建议
            suggestions = self._generate_intent_based_suggestions(requirements, intent_analysis)

            return AIResponse(
                content=ai_content,
                confidence=confidence,
                sources=sources,
                suggestions=suggestions
            )

        except Exception as e:
            return AIResponse(
                content=f"基于您的需求分析时出现错误：{str(e)}",
                confidence=0.0
            )

    def _build_requirement_analysis_prompt(self, requirements: Dict, context: Dict) -> str:
        """
        构建基于需求分析的提示词
        """
        user_profile = context.get("user_profile", {})

        prompt = f"""
你是CellForge AI专业顾问，现在需要基于用户的结构化需求信息生成专业的单细胞测序方案建议。

## 用户需求信息：
- 研究目标：{requirements.get('researchGoal', '未指定')}
- 样本类型：{requirements.get('sampleType', '未指定')}
- 细胞数量：{requirements.get('cellCount', '未指定')}
- 预算范围：{requirements.get('budget', '未指定')}
- 项目周期：{requirements.get('timeline', '未指定')}
- 测序深度：{requirements.get('sequencingDepth', '未指定')}
- 分析类型：{requirements.get('analysisType', '未指定')}
- 特殊要求：{requirements.get('specialRequirements', '无')}

## 用户背景：
- 机构：{user_profile.get('organization', '未知')}
- 角色：{user_profile.get('role', '未知')}
- 专业领域：{user_profile.get('expertise', '未知')}

## 任务要求：
请基于以上信息生成一个详细的技术方案建议，包括：

1. **技术平台推荐**：基于研究目标和样本类型推荐最适合的技术平台
2. **实验设计**：详细的实验流程和关键步骤
3. **成本分析**：预算分解和成本优化建议
4. **时间规划**：项目里程碑和时间安排
5. **风险评估**：潜在技术难点和解决方案
6. **质量保证**：质控标准和数据验证方法

请确保建议专业、实用、可操作，并考虑用户的实际条件和限制。
        """

        return prompt

    def _build_intent_aware_prompt(self, requirements: Dict, intent_analysis: Dict, context: Dict) -> str:
        """
        构建基于意图分析的增强提示词
        """
        research_domain = intent_analysis.get('research_domain', '通用研究')
        budget_category = intent_analysis.get('budget_category', '标准型')
        urgency_level = intent_analysis.get('urgency_level', '常规')
        technical_preference = intent_analysis.get('technical_preference', '10x Genomics')

        prompt = f"""
你是CellForge AI的高级单细胞测序专家，专门为{research_domain}领域提供个性化技术方案。

用户画像分析：
- 研究领域：{research_domain}
- 预算类别：{budget_category}
- 紧急程度：{urgency_level}
- 技术偏好：{technical_preference}
- 样本复杂度：{intent_analysis.get('sample_complexity', '中等')}
- 经验水平：{intent_analysis.get('experience_level', '中级')}

核心需求信息：
- 研究目标：{requirements.get('researchGoal', '未指定')}
- 样本类型：{requirements.get('sampleType', '未指定')}
- 细胞数量：{requirements.get('cellCount', '未指定')}
- 预算范围：{requirements.get('budget', '未指定')}
- 项目周期：{requirements.get('timeline', '未指定')}

请基于以上分析，生成高度个性化的技术方案建议，包括：

1. **针对{research_domain}的专业方案**
   - 推荐最适合的技术平台和理由
   - 考虑{budget_category}预算的成本优化策略
   - 适应{urgency_level}时间要求的项目规划

2. **个性化技术建议**
   - 基于样本复杂度的预处理方案
   - 针对用户经验水平的操作指导
   - 特定研究领域的分析重点

3. **风险评估和质量保证**
   - 识别潜在技术风险
   - 提供质量控制措施
   - 给出成功率预估

请确保建议专业、实用、个性化，避免通用化的模板回复。
        """

        return prompt

    def _generate_intent_based_mock_response(self, requirements: Dict, intent_analysis: Dict) -> str:
        """
        基于意图分析生成个性化模拟响应
        """
        research_domain = intent_analysis.get('research_domain', '通用研究')
        budget_category = intent_analysis.get('budget_category', '标准型')
        urgency_level = intent_analysis.get('urgency_level', '常规')

        # 根据研究领域定制响应
        if research_domain == "癌症研究":
            return self._generate_cancer_research_response(requirements, intent_analysis)
        elif research_domain == "发育生物学":
            return self._generate_developmental_biology_response(requirements, intent_analysis)
        elif research_domain == "免疫学研究":
            return self._generate_immunology_response(requirements, intent_analysis)
        else:
            return self._generate_general_research_response(requirements, intent_analysis)

    def _generate_cancer_research_response(self, requirements: Dict, intent_analysis: Dict) -> str:
        """
        生成癌症研究专用响应
        """
        sample_type = requirements.get('sampleType', '')
        budget_category = intent_analysis.get('budget_category', '标准型')

        response = f"""🎯 **CellForge AI** - 癌症研究专业方案

感谢您选择CellForge AI进行癌症单细胞研究！基于您的{sample_type}样本和研究需求，我为您制定了以下**癌症生物学专业方案**：

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🔬 **癌症研究专业技术方案**

🌟 **推荐平台：10x Genomics Chromium + Spatial Transcriptomics**

**癌症研究优势：**
✅ **肿瘤异质性分析**：精确识别癌细胞亚群和正常细胞
✅ **微环境解析**：深入分析肿瘤微环境中的免疫浸润
✅ **空间信息保留**：结合空间转录组学，保留细胞位置信息
✅ **耐药机制研究**：识别耐药相关的细胞亚群和通路

**技术规格：**
• 细胞通量：`10,000-20,000 cells/sample`（适合肿瘤样本）
• 基因检测：`~25,000 genes`（全转录组覆盖）
• 空间分辨率：`55μm spots`（组织结构保留）
• 数据质量：**低双联率(<3%)**，高基因检测数

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

💰 **癌症研究专业成本分析**
"""

        # 根据预算类别添加不同的成本方案
        if budget_category == "经济型":
            response += """
💡 **经济型癌症研究方案**

**总预算：6-8万元**
• 样本制备：¥8,000-12,000（肿瘤组织解离优化）
• 文库构建：¥20,000-25,000（标准10x流程）
• 测序服务：¥15,000-20,000（中等深度测序）
• 癌症专业分析：¥12,000-18,000（肿瘤细胞注释+微环境分析）

**优化策略：**
🎯 聚焦核心癌症标志物分析
📊 使用成熟的癌症细胞图谱进行注释
🔄 优化样本数量（建议3-4个样本对比）
"""
        else:
            response += """
🚀 **标准癌症研究方案**

**总预算：12-18万元**
• 样本制备：¥15,000-20,000（专业肿瘤样本处理）
• 文库构建：¥35,000-45,000（10x + Spatial组合）
• 测序服务：¥30,000-40,000（高深度测序）
• 癌症深度分析：¥25,000-35,000（多组学整合+通路分析）

**高端服务：**
🔬 肿瘤异质性深度解析
📊 免疫微环境全景分析
🧬 癌症驱动基因识别
👨‍🔬 肿瘤生物学专家指导
"""

        response += """
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🎯 **癌症研究专业分析内容**

🔍 **肿瘤细胞分析：**
• 癌细胞亚群识别和分类
• 肿瘤干细胞标志物检测
• 增殖和凋亡状态评估
• 上皮-间质转化(EMT)分析

🛡️ **免疫微环境分析：**
• T细胞亚群功能状态分析
• 巨噬细胞极化状态检测
• 免疫检查点分子表达
• 细胞间通讯网络构建

📊 **通路和功能分析：**
• 癌症相关信号通路激活
• 代谢重编程分析
• DNA损伤修复通路评估
• 血管生成相关基因表达

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CellForge AI - 您的癌症研究专业伙伴 🧬🔬"""

        return response

    def _generate_developmental_biology_response(self, requirements: Dict, intent_analysis: Dict) -> str:
        """
        生成发育生物学专用响应
        """
        response = f"""🎯 **CellForge AI** - 发育生物学专业方案

专为发育轨迹研究设计！基于您的研究需求，我为您制定了以下**发育生物学专业方案**：

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🔬 **发育轨迹专业技术方案**

🌟 **推荐平台：10x Genomics Chromium + RNA Velocity**

**发育研究优势：**
✅ **时序分析能力**：高时间分辨率捕获发育过程
✅ **轨迹重构**：精确重建细胞分化轨迹
✅ **RNA Velocity**：预测细胞命运和分化方向
✅ **伪时间分析**：构建发育时间轴

**技术规格：**
• 细胞通量：`5,000-15,000 cells/timepoint`
• 时间点建议：`4-6个关键发育阶段`
• 基因检测：`~30,000 genes`（包含发育调控因子）
• 数据深度：**高UMI计数**，支持轨迹分析

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🎯 **发育生物学专业分析**

🌱 **轨迹分析：**
• Monocle3伪时间分析
• PAGA连接性分析
• CellRank命运预测
• 分支点识别和验证

🧬 **发育调控分析：**
• 转录因子活性推断
• 发育相关通路激活
• 表观遗传修饰预测
• 细胞周期状态分析

📊 **时序基因表达：**
• 发育阶段特异性基因
• 早期响应基因识别
• 晚期分化标志物
• 瞬时表达基因检测

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CellForge AI - 您的发育生物学研究专家 🌱🔬"""

        return response

    def _generate_immunology_response(self, requirements: Dict, intent_analysis: Dict) -> str:
        """
        生成免疫学研究专用响应
        """
        response = f"""🎯 **CellForge AI** - 免疫学研究专业方案

专为免疫细胞分析设计！基于您的PBMC/免疫样本，我为您制定了以下**免疫学专业方案**：

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🔬 **免疫学专业技术方案**

🌟 **推荐平台：10x Genomics Chromium + VDJ分析**

**免疫研究优势：**
✅ **免疫细胞全景**：14种主要免疫细胞类型精确识别
✅ **TCR/BCR分析**：免疫受体序列重构和克隆性分析
✅ **功能状态评估**：激活、耗竭、记忆状态精确判定
✅ **免疫应答分析**：细胞间通讯和免疫网络构建

**技术规格：**
• 细胞通量：`10,000-20,000 PBMC cells`
• 免疫细胞覆盖：`T/B/NK/DC/Monocyte全覆盖`
• VDJ检测：`TCRα/β + BCR重链/轻链`
• 功能基因：`~2,000个免疫相关基因`

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🛡️ **免疫学专业分析内容**

🔍 **免疫细胞分型：**
• CD4+/CD8+ T细胞亚群
• B细胞成熟阶段分析
• NK细胞功能状态
• 树突状细胞激活程度

⚔️ **免疫功能分析：**
• 细胞毒性评分
• 免疫检查点表达
• 细胞因子产生能力
• 抗原呈递功能评估

🧬 **免疫组库分析：**
• TCR/BCR多样性计算
• 克隆扩增检测
• V(D)J重排分析
• 抗原特异性预测

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CellForge AI - 您的免疫学研究专业伙伴 🛡️🔬"""

        return response

    def _generate_general_research_response(self, requirements: Dict, intent_analysis: Dict) -> str:
        """
        生成通用研究响应（保留原有逻辑但增强个性化）
        """
        # 这里可以调用原有的_generate_requirement_mock_response逻辑
        # 但增加基于意图分析的个性化元素
        return self._generate_requirement_mock_response(requirements)

    def _generate_intent_based_suggestions(self, requirements: Dict, intent_analysis: Dict) -> List[str]:
        """
        基于意图分析生成个性化建议
        """
        suggestions = []
        research_domain = intent_analysis.get('research_domain', '通用研究')

        # 基于研究领域的专业建议
        if research_domain == "癌症研究":
            suggestions.extend([
                "查看肿瘤异质性分析案例",
                "了解免疫微环境解析方案",
                "获取癌症标志物检测指南"
            ])
        elif research_domain == "发育生物学":
            suggestions.extend([
                "查看轨迹分析最佳实践",
                "了解RNA Velocity分析流程",
                "获取发育时序采样建议"
            ])
        elif research_domain == "免疫学研究":
            suggestions.extend([
                "查看免疫细胞分型标准",
                "了解TCR/BCR分析流程",
                "获取PBMC处理最佳实践"
            ])

        # 基于预算类别的建议
        budget_category = intent_analysis.get('budget_category', '标准型')
        if budget_category == "经济型":
            suggestions.append("了解成本优化策略")
        elif budget_category == "高端型":
            suggestions.append("查看多组学整合方案")

        # 通用建议
        suggestions.extend([
            "预约技术专家一对一咨询",
            "获取详细项目时间规划"
        ])

        return suggestions[:5]

    def _generate_requirement_mock_response(self, requirements: Dict) -> str:
        """
        生成基于需求的模拟回复 - 优化格式和用户体验
        """
        research_goal = requirements.get('researchGoal', '')
        sample_type = requirements.get('sampleType', '')
        cell_count = requirements.get('cellCount', '')
        budget = requirements.get('budget', '')
        timeline = requirements.get('timeline', '')
        experiment_type = requirements.get('experimentType', '')
        analysis_type = requirements.get('analysisType', '')

        # 开始构建专业回复 - 使用markdown格式
        response = f"""🎯 **CellForge AI** 专业方案建议

感谢您提供详细的需求信息！基于您的研究目标和实际条件，我为您制定了以下**个性化技术方案**：

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📊 需求分析总结

🎯 研究目标：{research_goal or '待确认'}
🧪 样本类型：{sample_type or '待确认'}
🔢 细胞数量：{cell_count or '待确认'}
💰 预算范围：{budget or '待确认'}
⏰ 项目周期：{timeline or '待确认'}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🔬 推荐技术方案

"""

        # 基于研究目标和样本类型推荐平台
        if "发育轨迹" in research_goal or "时序" in research_goal:
            response += """🌟 推荐平台：**10x Genomics Chromium + RNA Velocity**

**选择理由：**
✅ 高时间分辨率，完美适配轨迹分析需求
✅ 支持伪时间分析和细胞命运预测
✅ 成熟的RNA Velocity算法支持

**技术优势：**
• 单细胞分辨率：`~3,000-10,000 cells/sample`
• 基因检测数量：`~20,000-30,000 genes`
• 数据质量：**UMI去重**，低噪音

"""
        elif "肿瘤" in research_goal or "癌" in research_goal:
            response += """🌟 推荐平台：**10x Genomics + Spatial Transcriptomics**

**选择理由：**
✅ 结合空间信息和单细胞分辨率
✅ 深入解析肿瘤微环境异质性
✅ 支持免疫浸润和细胞通讯分析

**技术优势：**
• 空间分辨率：`55μm spot size`
• 细胞类型识别：支持**稀有细胞亚群**检测
• 多组学整合：*转录组+蛋白质组*

"""
        elif "免疫" in research_goal:
            response += """🌟 推荐平台：**10x Genomics Chromium + VDJ分析**

**选择理由：**
✅ 专业的免疫细胞分析解决方案
✅ 支持T/B细胞受体序列分析
✅ 免疫细胞亚群精确分类

**技术优势：**
• 免疫组库分析：`TCR/BCR序列重构`
• 克隆性分析：**克隆扩增检测**
• 功能注释：*免疫应答状态评估*

"""
        else:
            response += """🌟 推荐平台：**10x Genomics Chromium**

**选择理由：**
✅ 行业金标准，技术成熟稳定
✅ 高通量、高质量数据产出
✅ 完善的分析流程和工具支持

**技术优势：**
• 细胞通量：`500-10,000 cells/sample`
• 基因覆盖：*全转录组检测*
• 数据质量：低双联率(**<5%**)，高基因检测数

"""

        # 基于预算给出详细的成本分析
        response += "\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n💰 成本分析与优化\n\n"

        if "5万以下" in budget or "5万" in budget:
            response += """💡 **经济型方案**

**总预算：4-6万元**

**费用明细：**
• 样本制备：¥5,000-8,000（组织解离、细胞分选）
• 文库构建：¥15,000-20,000（`Smart-seq3平台`）
• 测序服务：¥12,000-18,000（*NovaSeq 6000*）
• 数据分析：¥8,000-12,000（标准分析流程）

**优化建议：**
🔄 使用**Smart-seq3**降低单细胞成本
📊 优化样本数量(建议`3-4个样本`)
🎯 聚焦核心分析内容

"""
        elif "10万" in budget or "5-10万" in budget:
            response += """🎯 标准方案

总预算：8-12万元

费用明细：
• 样本制备：¥8,000-12,000（专业细胞分选）
• 文库构建：¥25,000-35,000（10x Genomics标准流程）
• 测序服务：¥20,000-30,000（高深度测序）
• 数据分析：¥15,000-25,000（深度分析+可视化）

包含服务：
✅ 完整的实验设计咨询
✅ 专业的数据分析报告
✅ 3个月技术支持

"""
        else:
            response += """🚀 高端定制方案

总预算：15-25万元

费用明细：
• 样本制备：¥12,000-18,000（高级细胞分选+质控）
• 文库构建：¥40,000-60,000（多组学平台）
• 测序服务：¥35,000-50,000（超高深度测序）
• 数据分析：¥25,000-40,000（定制化深度分析）

高端服务：
🔬 多组学整合分析
📊 个性化数据挖掘
👨‍🔬 专家一对一指导

"""

        # 项目时间规划
        response += "\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n⏰ 项目时间规划\n\n"

        if "1个月" in timeline:
            response += """⚡ 加急方案 (3-4周)

时间安排：
🧪 第1周：样本接收、质控、细胞分选
🔬 第2周：单细胞捕获、cDNA合成
📊 第3周：高通量测序、数据产出
📋 第4周：数据分析、报告撰写

⚠️ 加急注意事项：
• 需要样本质量优异
• 建议预先沟通实验细节
• 可能需要加急费用(+20%)

"""
        else:
            response += """📅 标准时间安排 (6-8周)

时间安排：
🧪 第1-2周：样本接收、质控、优化
🔬 第3-4周：单细胞捕获、质量检测
📊 第5-6周：测序、数据质控
📋 第7-8周：生物信息学分析、报告

✅ 充足时间优势：
• 充分的质量控制时间
• 可进行实验优化调整
• 深度的数据挖掘分析

"""

        # 风险评估和质量保证
        response += """\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

⚠️ 风险评估与质量保证

🔍 潜在技术风险：
• 样本质量风险：细胞活力<80%可能影响数据质量
• 技术风险：双联率过高(>10%)需要重新优化
• 分析风险：批次效应可能影响结果解读

🛡️ 质量控制措施：
✅ 样本预检：细胞活力、浓度、形态学检查
✅ 过程监控：每步骤质量检测和记录
✅ 数据验证：多重质控指标验证
✅ 结果确认：生物学意义验证

📞 技术支持承诺：
🔬 实验阶段：实时技术指导和问题解决
📊 分析阶段：专业生物信息学家支持
📋 后续服务：3个月免费技术咨询

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🎯 下一步行动建议

1. 📋 确认方案细节：请确认技术平台和预算方案
2. 🧪 样本准备指导：我们提供详细的样本制备指南
3. 📅 时间安排协调：确定项目启动时间和关键节点
4. 🤝 签署服务协议：明确服务内容和质量标准

💡 温馨提示：
我们建议在正式启动前进行一次技术交流会，确保方案完全符合您的研究需求。

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CellForge AI - 您的单细胞测序专业伙伴 🧬✨"""

        return response

    def _generate_requirement_suggestions(self, requirements: Dict) -> List[str]:
        """
        基于需求生成后续建议
        """
        suggestions = []

        if not requirements.get('sequencingDepth'):
            suggestions.append("需要确定测序深度要求")

        if not requirements.get('analysisType'):
            suggestions.append("请明确数据分析重点")

        if requirements.get('budget') and "5万" in requirements['budget']:
            suggestions.append("了解成本优化方案")

        suggestions.extend([
            "查看相似案例研究",
            "获取详细实验方案",
            "预约技术专家咨询"
        ])

        return suggestions[:5]

    async def analyze_user_intent(self, requirements: Dict, user_message: str = "") -> Dict:
        """
        智能分析用户意图，提取关键信息用于个性化推荐
        """
        try:
            # 构建意图分析提示词
            intent_analysis_prompt = f"""
作为单细胞测序专家，请分析用户的研究需求并提取关键意图信息。

用户需求信息：
- 研究目标：{requirements.get('researchGoal', '未指定')}
- 样本类型：{requirements.get('sampleType', '未指定')}
- 细胞数量：{requirements.get('cellCount', '未指定')}
- 预算范围：{requirements.get('budget', '未指定')}
- 项目周期：{requirements.get('timeline', '未指定')}
- 实验类型：{requirements.get('experimentType', '未指定')}
- 分析需求：{requirements.get('analysisType', '未指定')}
- 特殊要求：{requirements.get('specialRequirements', '未指定')}

用户消息：{user_message}

请分析并返回JSON格式的意图分析结果：
{{
    "research_domain": "研究领域（如：癌症研究、发育生物学、免疫学等）",
    "primary_objective": "主要研究目标",
    "sample_complexity": "样本复杂度（简单/中等/复杂）",
    "budget_category": "预算类别（经济型/标准型/高端型）",
    "urgency_level": "紧急程度（常规/紧急/加急）",
    "technical_preference": "技术偏好（10x Genomics/Smart-seq/其他）",
    "analysis_focus": "分析重点（细胞类型鉴定/轨迹分析/差异表达等）",
    "experience_level": "用户经验水平（初学者/中级/专家）",
    "key_concerns": ["主要关注点列表"],
    "recommended_approach": "推荐方法",
    "confidence_score": 0.95
}}
"""

            if self.use_real_ai and self.client:
                messages = [
                    SystemMessage(content="你是一个专业的单细胞测序技术专家，擅长分析用户需求和意图。"),
                    HumanMessage(content=intent_analysis_prompt)
                ]

                response = await self.client.ainvoke(messages)

                # 尝试解析JSON响应
                try:
                    import json
                    import re

                    # 提取JSON部分
                    json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
                    if json_match:
                        intent_data = json.loads(json_match.group())
                        return intent_data
                    else:
                        # 如果无法解析JSON，返回基于规则的分析
                        return self._rule_based_intent_analysis(requirements)

                except (json.JSONDecodeError, Exception) as e:
                    print(f"JSON解析失败，使用规则分析: {e}")
                    return self._rule_based_intent_analysis(requirements)
            else:
                # 使用基于规则的意图分析
                return self._rule_based_intent_analysis(requirements)

        except Exception as e:
            print(f"意图分析失败: {e}")
            return self._rule_based_intent_analysis(requirements)

    def _rule_based_intent_analysis(self, requirements: Dict) -> Dict:
        """
        基于规则的意图分析（备用方案）
        """
        research_goal = requirements.get('researchGoal', '').lower()
        sample_type = requirements.get('sampleType', '').lower()
        budget = requirements.get('budget', '').lower()
        timeline = requirements.get('timeline', '').lower()

        # 研究领域识别
        research_domain = "通用研究"
        if any(keyword in research_goal for keyword in ['癌', '肿瘤', 'cancer', 'tumor']):
            research_domain = "癌症研究"
        elif any(keyword in research_goal for keyword in ['发育', '轨迹', 'development', 'trajectory']):
            research_domain = "发育生物学"
        elif any(keyword in research_goal for keyword in ['免疫', 'immune', 'pbmc']):
            research_domain = "免疫学研究"
        elif any(keyword in research_goal for keyword in ['神经', 'brain', 'neuron']):
            research_domain = "神经科学"

        # 预算类别
        budget_category = "标准型"
        if "5万以下" in budget or "5万" in budget:
            budget_category = "经济型"
        elif "10万以上" in budget or "15万" in budget:
            budget_category = "高端型"

        # 紧急程度
        urgency_level = "常规"
        if "1个月" in timeline or "紧急" in timeline:
            urgency_level = "紧急"
        elif "2个月" in timeline:
            urgency_level = "加急"

        # 样本复杂度
        sample_complexity = "中等"
        if any(keyword in sample_type for keyword in ['pbmc', '血液', 'blood']):
            sample_complexity = "简单"
        elif any(keyword in sample_type for keyword in ['脑', 'brain', '心脏', 'heart', '组织']):
            sample_complexity = "复杂"

        return {
            "research_domain": research_domain,
            "primary_objective": requirements.get('researchGoal', '细胞类型鉴定和功能分析'),
            "sample_complexity": sample_complexity,
            "budget_category": budget_category,
            "urgency_level": urgency_level,
            "technical_preference": "10x Genomics",  # 默认推荐
            "analysis_focus": requirements.get('analysisType', '细胞类型鉴定'),
            "experience_level": "中级",
            "key_concerns": ["数据质量", "成本控制", "时间管理"],
            "recommended_approach": "标准单细胞RNA测序流程",
            "confidence_score": 0.85
        }

    async def generate_keyword_enhanced_response(
        self,
        message: str,
        context: Dict,
        use_keyword_enhancement: bool = True,
        enable_literature_search: bool = False
    ) -> AIResponse:
        """
        生成关键词增强的AI回复
        集成关键词提取和文献推荐
        """
        try:
            # 第一步：提取关键词（如果启用）
            keyword_data = None
            enhanced_context = context.copy()
            
            if use_keyword_enhancement:
                try:
                    # 动态导入避免循环依赖
                    from app.services.ai_keyword_generator import get_keyword_generator
                    
                    keyword_generator = await get_keyword_generator()
                    keyword_data = await keyword_generator.generate_keywords_from_query(
                        user_query=message,
                        context=context,
                        max_keywords=10
                    )
                    
                    # 将关键词信息添加到上下文中
                    if "extracted_keywords" in keyword_data:
                        enhanced_context["extracted_keywords"] = keyword_data["extracted_keywords"]
                        enhanced_context["domain_classification"] = keyword_data["domain_classification"]
                        
                        # 将英文关键词添加到相关知识中用于增强响应
                        english_keywords = keyword_data["extracted_keywords"].get("english", [])
                        if english_keywords:
                            keyword_knowledge = {
                                "title": "提取的关键概念",
                                "content": f"相关科学术语: {', '.join(english_keywords[:8])}",
                                "source": "AI关键词分析"
                            }
                            
                            if "relevant_knowledge" not in enhanced_context:
                                enhanced_context["relevant_knowledge"] = []
                            enhanced_context["relevant_knowledge"].insert(0, keyword_knowledge)
                            
                except Exception as e:
                    logger.warning(f"关键词增强失败，使用标准模式: {e}")
            
            # 第二步：生成AI回复
            ai_response = await self.generate_response(
                message=message,
                context=enhanced_context,
                enable_literature_search=enable_literature_search
            )
            
            # 第三步：如果有关键词数据，添加到回复中
            if keyword_data and use_keyword_enhancement:
                keyword_summary = self._generate_keyword_summary(keyword_data)
                if keyword_summary:
                    ai_response.content = ai_response.content + "\n\n" + keyword_summary
                
                # 更新建议
                keyword_suggestions = self._generate_keyword_suggestions(keyword_data)
                ai_response.suggestions.extend(keyword_suggestions)
            
            return ai_response
            
        except Exception as e:
            logger.error(f"关键词增强回复生成失败: {e}")
            # 回退到标准响应
            return await self.generate_response(message, context, enable_literature_search=enable_literature_search)
    
    def _generate_keyword_summary(self, keyword_data: Dict) -> str:
        """生成关键词摘要"""
        try:
            if not keyword_data or "extracted_keywords" not in keyword_data:
                return ""
            
            english_keywords = keyword_data["extracted_keywords"].get("english", [])
            domain_classification = keyword_data.get("domain_classification", {})
            
            if not english_keywords:
                return ""
            
            summary = "\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
            summary += "🔍 **关键词分析**\n\n"
            
            # 主要关键词
            if english_keywords:
                summary += f"**识别的科学术语**: {', '.join(english_keywords[:6])}\n\n"
            
            # 领域分类
            if domain_classification:
                summary += "**研究领域分布**:\n"
                for domain, keywords in domain_classification.items():
                    domain_name = {
                        "immunology": "免疫学",
                        "oncology": "肿瘤学", 
                        "neuroscience": "神经科学",
                        "developmental_biology": "发育生物学",
                        "metabolism": "代谢学",
                        "stem_cell": "干细胞研究"
                    }.get(domain, domain)
                    summary += f"• {domain_name}: {', '.join(keywords[:3])}\n"
            
            summary += "\n💡 这些关键词可用于精确的文献搜索和技术咨询。"
            
            return summary
            
        except Exception as e:
            logger.error(f"生成关键词摘要失败: {e}")
            return ""
    
    def _generate_keyword_suggestions(self, keyword_data: Dict) -> List[str]:
        """基于关键词数据生成建议"""
        suggestions = []
        
        try:
            domain_classification = keyword_data.get("domain_classification", {})
            
            # 基于领域生成建议
            if "immunology" in domain_classification:
                suggestions.append("查看免疫细胞分析专业方案")
            if "oncology" in domain_classification:
                suggestions.append("了解肿瘤单细胞研究案例")
            if "neuroscience" in domain_classification:
                suggestions.append("获取神经科学研究指导")
            if "developmental_biology" in domain_classification:
                suggestions.append("查看发育轨迹分析方法")
            
            # 通用建议
            suggestions.extend([
                "使用这些关键词进行精确文献搜索",
                "获取基于关键词的技术方案推荐"
            ])
            
        except Exception as e:
            logger.warning(f"生成关键词建议失败: {e}")
        
        return suggestions[:3]  # 最多返回3个建议

    def _generate_mock_response(self, message: str, context: Dict) -> str:
        """
        生成模拟AI响应（当没有配置真实API时使用）
        """
        user_profile = context.get("user_profile", {})
        organization = user_profile.get("organization", "您的机构")

        # 基于关键词生成不同的响应
        if any(keyword in message.lower() for keyword in ["单细胞", "scrna", "测序"]):
            return f"""感谢您咨询单细胞测序方案！

基于您的需求，我为{organization}推荐以下单细胞RNA测序方案：

**技术平台推荐：**
- 10x Genomics Chromium：适合大规模细胞分析，通量高
- Smart-seq3：适合需要全长转录本信息的研究
- Drop-seq：成本效益较高的选择

**样本制备要点：**
1. 确保细胞活力 >80%
2. 细胞浓度控制在 500-1000 cells/μL
3. 避免细胞聚集和碎片

**预估成本：**
- 10x Genomics: 约 ¥800-1200/样本
- Smart-seq3: 约 ¥1500-2000/样本

**项目周期：**
- 样本制备：1-2天
- 文库构建：2-3天
- 测序分析：5-7天

如需更详细的方案设计，请提供具体的研究目标和样本信息。

*注：当前为演示模式，实际使用时将连接专业AI模型提供更精准的建议。*"""

        elif any(keyword in message.lower() for keyword in ["成本", "费用", "价格"]):
            return f"""关于单细胞测序的成本分析：

**主要费用构成：**
1. **样本制备费用：** ¥200-500/样本
2. **文库构建费用：** ¥400-800/样本
3. **测序费用：** ¥300-600/样本
4. **数据分析费用：** ¥200-400/样本

**不同平台成本对比：**
- **10x Genomics：** ¥800-1200/样本（推荐）
- **Smart-seq3：** ¥1500-2000/样本
- **Drop-seq：** ¥600-900/样本

**成本优化建议：**
- 批量处理可降低10-15%成本
- 选择合适的测序深度
- 考虑多样本复用策略

为{organization}制定具体预算方案，请告知样本数量和研究需求。"""

        elif any(keyword in message.lower() for keyword in ["时间", "周期", "多久"]):
            return f"""单细胞测序项目时间规划：

**标准流程时间：**
1. **项目咨询：** 1-2天
2. **样本制备：** 1-3天
3. **质量检测：** 1天
4. **文库构建：** 2-4天
5. **测序运行：** 3-5天
6. **数据分析：** 5-10天
7. **报告交付：** 2-3天

**总计：** 15-28天（标准流程）

**加急服务：**
- 可缩短至10-15天
- 需额外支付20-30%费用

**影响因素：**
- 样本数量和复杂度
- 测序深度要求
- 分析复杂程度

为{organization}制定详细时间计划，请提供具体项目需求。"""

        else:
            return f"""您好！我是CellForge AI单细胞测序咨询专家。

我可以为{organization}提供以下服务：

🔬 **技术咨询**
- 单细胞RNA测序方案设计
- 样本制备指导
- 平台选择建议

💰 **成本分析**
- 详细费用预算
- 性价比分析
- 优化建议

⏰ **项目规划**
- 时间安排
- 流程优化
- 质量控制

📊 **数据分析**
- 分析流程设计
- 结果解读
- 可视化方案

请告诉我您的具体需求，比如：
- 研究目标和样本类型
- 预期细胞数量
- 预算范围
- 时间要求

我将为您提供专业的个性化建议！

*注：当前为演示模式，配置API密钥后将提供更智能的专业咨询。*"""