"use client"

import { SmartLiteratureResearchPlatform } from '@/components/smart-literature-research-platform'

export default function SmartLiteratureResearchPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto py-8">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-slate-900 mb-2">
            CellForge AI 智能文献研究平台
          </h1>
          <p className="text-slate-600 max-w-2xl mx-auto">
            结合问卷调研和AI技术，为您提供类似Perplexity的智能文献搜索体验。
            基于您的研究需求自动生成优化的搜索策略，发现最相关的科学文献。
          </p>
        </div>
        
        <SmartLiteratureResearchPlatform />
      </div>
    </div>
  )
}