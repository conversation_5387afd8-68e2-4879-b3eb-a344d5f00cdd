"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface CircularProgressProps {
  value: number
  size?: number
  strokeWidth?: number
  className?: string
  showValue?: boolean
  children?: React.ReactNode
}

export function CircularProgress({
  value,
  size = 120,
  strokeWidth = 8,
  className,
  showValue = true,
  children
}: CircularProgressProps) {
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (value / 100) * circumference

  return (
    <div className={cn("relative inline-flex items-center justify-center", className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
        viewBox={`0 0 ${size} ${size}`}
      >
        {/* 背景圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-slate-200"
        />
        {/* 进度圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="text-blue-600 transition-all duration-500 ease-in-out"
        />
      </svg>
      
      {/* 中心内容 */}
      <div className="absolute inset-0 flex items-center justify-center">
        {children || (showValue && (
          <div className="text-center">
            <div className="text-2xl font-bold text-slate-900">
              {Math.round(value)}%
            </div>
            <div className="text-xs text-slate-500 mt-1">
              完成度
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// 小尺寸版本
export function CircularProgressMini({
  value,
  size = 60,
  strokeWidth = 4,
  className,
  showValue = true
}: CircularProgressProps) {
  return (
    <CircularProgress
      value={value}
      size={size}
      strokeWidth={strokeWidth}
      className={className}
      showValue={false}
    >
      {showValue && (
        <div className="text-center">
          <div className="text-sm font-semibold text-slate-900">
            {Math.round(value)}%
          </div>
        </div>
      )}
    </CircularProgress>
  )
}

// 带动画的进度环
export function AnimatedCircularProgress({
  value,
  size = 120,
  strokeWidth = 8,
  className,
  showValue = true,
  children
}: CircularProgressProps) {
  const [animatedValue, setAnimatedValue] = React.useState(0)

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedValue(value)
    }, 100)

    return () => clearTimeout(timer)
  }, [value])

  return (
    <CircularProgress
      value={animatedValue}
      size={size}
      strokeWidth={strokeWidth}
      className={className}
      showValue={showValue}
    >
      {children}
    </CircularProgress>
  )
}
