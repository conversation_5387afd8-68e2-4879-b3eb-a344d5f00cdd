"use client"

import { useAuth } from "@/contexts/auth-context"
import { CustomerDashboard } from "@/components/customer-dashboard"
import { AccessDenied } from "@/components/access-denied"

export default function CustomersPage() {
  const { hasPermission } = useAuth()

  return (
    <div className="min-h-[calc(100vh-64px)]">
      {hasPermission("view_customers") ? <CustomerDashboard /> : <AccessDenied />}
    </div>
  )
}
