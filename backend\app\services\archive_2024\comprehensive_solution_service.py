"""
综合解决方案服务 - 兼容性接口
将综合解决方案功能重定向到现有的增强解决方案生成器
"""
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

from app.services.enhanced_solution_generator import get_enhanced_solution_generator
from app.services.research_intent_service import get_research_intent_service

logger = logging.getLogger(__name__)


class ComprehensiveSolutionService:
    """综合解决方案服务 - 基于增强解决方案生成器的兼容性实现"""
    
    def __init__(self):
        self.solution_generator = get_enhanced_solution_generator()
        self.research_intent_service = get_research_intent_service()
    
    async def generate_comprehensive_framework(
        self,
        requirements: Dict[str, Any],
        user_message: str = "",
        framework_template: str = "standard",
        enable_literature_search: bool = True,
        user_preferences: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        生成综合解决方案框架
        使用增强解决方案生成器的功能
        """
        try:
            logger.info(f"🚀 生成综合解决方案框架 - 模板: {framework_template}")
            
            # 使用增强解决方案生成器
            solution = await self.solution_generator.generate_comprehensive_solution(
                requirements=requirements,
                user_message=user_message
            )
            
            # 转换为框架格式
            framework = {
                "framework_id": solution.get("solution_id", ""),
                "generated_at": solution.get("generated_at", datetime.now().isoformat()),
                "framework_template": framework_template,
                "client_requirements": requirements,
                "solution_overview": {
                    "recommended_solution": solution.get("recommended_solution", {}),
                    "technical_analysis": solution.get("technical_analysis", {}),
                    "cost_analysis": solution.get("cost_analysis", {}),
                    "timeline": solution.get("timeline", {}),
                    "risk_assessment": solution.get("risk_assessment", {})
                },
                "literature_integration": solution.get("literature_analysis", {}) if enable_literature_search else {},
                "next_steps": solution.get("next_steps", []),
                "contact_info": solution.get("contact_info", {}),
                "framework_metadata": {
                    "template_used": framework_template,
                    "literature_enabled": enable_literature_search,
                    "user_preferences": user_preferences or {},
                    "generation_method": "enhanced_solution_generator"
                }
            }
            
            return framework
            
        except Exception as e:
            logger.error(f"综合框架生成失败: {str(e)}")
            return {
                "error": f"框架生成失败: {str(e)}",
                "fallback_framework": {
                    "framework_id": f"fallback_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    "message": "请联系专家获取详细方案",
                    "contact_info": {"email": "<EMAIL>"}
                }
            }
    
    async def analyze_research_intent(
        self,
        requirements: Dict[str, Any],
        user_message: str = ""
    ) -> Dict[str, Any]:
        """
        分析研究意图
        """
        try:
            # 使用研究意图服务
            intent_analysis = await self.research_intent_service.analyze_research_intent_and_generate_keywords(
                requirements, user_message
            )
            
            return {
                "research_intent": intent_analysis,
                "analysis_timestamp": datetime.now().isoformat(),
                "confidence_level": "high"
            }
            
        except Exception as e:
            logger.error(f"研究意图分析失败: {str(e)}")
            return {
                "error": f"意图分析失败: {str(e)}",
                "fallback_analysis": {
                    "basic_intent": requirements.get("researchGoal", "未明确"),
                    "experiment_type": requirements.get("experimentType", "未明确")
                }
            }
    
    async def generate_solution_with_literature(
        self,
        requirements: Dict[str, Any],
        user_message: str = ""
    ) -> Dict[str, Any]:
        """
        生成包含文献搜索的解决方案
        """
        try:
            # 使用研究意图服务的文献搜索功能
            literature_solution = await self.research_intent_service.generate_literature_search_with_ai_analysis(
                requirements, user_message
            )
            
            return {
                "solution_with_literature": literature_solution,
                "generated_at": datetime.now().isoformat(),
                "includes_literature": True
            }
            
        except Exception as e:
            logger.error(f"文献集成解决方案生成失败: {str(e)}")
            return {
                "error": f"文献集成失败: {str(e)}",
                "fallback_message": "请单独搜索相关文献"
            }
    
    async def get_framework_templates(self) -> Dict[str, Any]:
        """
        获取可用的框架模板
        """
        templates = {
            "standard": {
                "name": "标准版方案",
                "description": "适合大多数研究项目的标准化方案",
                "components": ["solution_overview", "technical_analysis", "cost_analysis"],
                "complexity": "medium",
                "target_users": "研究人员、实验室"
            },
            "detailed": {
                "name": "详细版方案",
                "description": "包含深度分析的详细方案",
                "components": ["solution_overview", "technical_analysis", "cost_analysis", "risk_assessment", "literature_review"],
                "complexity": "high",
                "target_users": "项目负责人、技术专家"
            },
            "simplified": {
                "name": "简化版方案",
                "description": "快速决策用的简化方案",
                "components": ["solution_overview", "basic_cost_estimate"],
                "complexity": "low",
                "target_users": "初学者、快速决策"
            }
        }
        
        return {
            "available_templates": templates,
            "default_template": "standard",
            "customization_available": True
        }
    
    async def validate_framework_requirements(
        self,
        requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        验证框架生成所需的需求完整性
        """
        try:
            # 检查必要字段
            required_fields = ["researchGoal", "experimentType", "sampleType"]
            missing_fields = []
            
            for field in required_fields:
                if not requirements.get(field):
                    missing_fields.append(field)
            
            # 计算完整度
            all_fields = ["researchGoal", "experimentType", "sampleType", "budget", "timeline", "sampleCount"]
            completed_fields = [f for f in all_fields if requirements.get(f)]
            completeness = len(completed_fields) / len(all_fields) * 100
            
            can_generate = len(missing_fields) == 0 and completeness >= 50
            
            return {
                "validation_result": {
                    "is_valid": can_generate,
                    "completeness_percentage": round(completeness, 1),
                    "missing_required_fields": missing_fields,
                    "completed_fields": completed_fields,
                    "recommended_template": "simplified" if completeness < 70 else "standard"
                },
                "next_action": "generate_framework" if can_generate else "complete_requirements"
            }
            
        except Exception as e:
            logger.error(f"需求验证失败: {str(e)}")
            return {
                "error": f"验证失败: {str(e)}",
                "fallback_validation": {"is_valid": False, "message": "请完善基础信息"}
            }


# 全局服务实例
comprehensive_solution_service = ComprehensiveSolutionService()


def get_comprehensive_solution_service() -> ComprehensiveSolutionService:
    """获取综合解决方案服务实例"""
    return comprehensive_solution_service
