"""
Production-ready AI关键词生成器 
基于用户查询生成科学关键词并翻译为英文

优化特性:
- 全面的错误处理和输入验证
- Redis缓存支持
- 速率限制
- 性能监控
- 模糊匹配
- 配置化参数
"""
import logging
import re
import asyncio
import hashlib
import time
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime
import json
from functools import wraps

from app.core.config import settings
from app.services.ai_service import AIService
from app.services.enhanced_prompt_manager import get_enhanced_prompt_manager, PromptType
from app.services.ai_metrics_collector import get_metrics_collector, monitor_performance
from app.services.smart_retry_manager import get_smart_retry_manager
from app.config.config_manager import get_config
from app.config.keyword_config import (
    get_keyword_config, 
    get_terminology_config,
    KeywordConfig,
    TerminologyConfig
)
from app.exceptions.keyword_exceptions import (
    KeywordGenerationError,
    InvalidInputError,
    TerminologyMappingError,
    DomainClassificationError,
    TranslationError,
    AIServiceError,
    CacheError,
    PerformanceError
)
from app.utils.validation import get_input_validator, InputValidator
from app.utils.cache_manager import get_cache_manager, CacheManager
from app.utils.rate_limiter import get_rate_limiter
from app.utils.metrics import get_metrics_collector, monitor_performance, monitor_function
from fuzzywuzzy import fuzz, process

logger = logging.getLogger(__name__)


class AIKeywordGenerator:
    """Production-ready AI驱动的关键词生成器
    
    Features:
    - Comprehensive input validation
    - Redis caching with TTL
    - Rate limiting
    - Performance monitoring
    - Fuzzy matching for terminology
    - Configurable parameters
    - Robust error handling
    """
    
    def __init__(
        self, 
        config: Optional[KeywordConfig] = None,
        terminology_config: Optional[TerminologyConfig] = None,
        validator: Optional[InputValidator] = None,
        cache_manager: Optional[CacheManager] = None
    ):
        # Configuration
        self.config = config or get_keyword_config()
        self.terminology_config = terminology_config or get_terminology_config()
        
        # Services
        self.ai_service = AIService()
        self.enhanced_prompt_manager = get_enhanced_prompt_manager()
        self.metrics_collector = get_metrics_collector()
        self.retry_manager = get_smart_retry_manager()
        self.validator = validator
        self.cache_manager = cache_manager
        self.rate_limiter = None
        self.metrics_collector = get_metrics_collector()
        
        # Lazy initialization flags
        self._initialized = False
        
        # 术语映射表 - 从配置文件加载
        self.terminology_mapping = {}
        self.domain_keywords = {}
        
        # 加载配置
        self._load_configurations()
    
    def _load_configurations(self):
        """从配置文件加载术语映射和领域关键词"""
        try:
            # 加载术语映射配置
            terminology_config = get_config('terminology_mapping')
            terminology_data = terminology_config.get('terminology_mapping', {})
            
            # 合并所有映射类型
            self.terminology_mapping = {}
            for mapping_type, mappings in terminology_data.items():
                if isinstance(mappings, dict):
                    self.terminology_mapping.update(mappings)
            
            # 加载领域关键词配置
            domain_config = get_config('domain_keywords')
            domain_data = domain_config.get('domain_keywords', {})
            
            # 提取每个领域的核心关键词
            self.domain_keywords = {}
            for domain, domain_info in domain_data.items():
                if isinstance(domain_info, dict):
                    self.domain_keywords[domain] = domain_info.get('core_keywords', [])
                    
            logger.info(f"Loaded {len(self.terminology_mapping)} terminology mappings and {len(self.domain_keywords)} domain keyword sets")
            
        except Exception as e:
            logger.warning(f"Failed to load configurations, using defaults: {e}")
            # 使用默认配置作为回退
            self._load_default_configurations()
    
    def _load_default_configurations(self):
        """加载默认配置作为回退"""
        # 默认术语映射
        self.terminology_mapping = {
            "单细胞": "single cell",
            "单细胞测序": "single cell sequencing",
            "转录组": "transcriptome",
            "基因表达": "gene expression",
            "细胞类型": "cell type",
            "细胞分型": "cell typing",
            "细胞亚群": "cell subpopulation",
            "差异表达": "differential expression",
            "发育轨迹": "developmental trajectory",
            "细胞分化": "cell differentiation",
            "免疫细胞": "immune cells",
            "肿瘤微环境": "tumor microenvironment",
            "空间转录组": "spatial transcriptomics",
            "多组学": "multi-omics",
            "生物标志物": "biomarker",
            "通路分析": "pathway analysis",
            "功能富集": "functional enrichment",
            "细胞通讯": "cell communication",
            "细胞周期": "cell cycle",
            "凋亡": "apoptosis",
            "增殖": "proliferation",
            "干细胞": "stem cell",
            "神经元": "neuron",
            "巨噬细胞": "macrophage",
            "T细胞": "T cell",
            "B细胞": "B cell",
            "NK细胞": "NK cell",
            "树突状细胞": "dendritic cell",
            "成纤维细胞": "fibroblast",
            "上皮细胞": "epithelial cell",
            "内皮细胞": "endothelial cell",
            "10x基因组学": "10x Genomics",
            "Smart-seq": "Smart-seq",
            "Drop-seq": "Drop-seq",
            "聚类分析": "clustering analysis",
            "降维": "dimensionality reduction",
            "主成分分析": "principal component analysis",
            "t-SNE": "t-SNE",
            "UMAP": "UMAP",
            "伪时间": "pseudotime",
            "RNA速度": "RNA velocity",
            "轨迹推断": "trajectory inference",
            "癌症": "cancer",
            "肿瘤": "tumor",
            "糖尿病": "diabetes",
            "自身免疫": "autoimmune",
            "炎症": "inflammation",
            "纤维化": "fibrosis",
            "代谢": "metabolism",
            "心血管": "cardiovascular",
            "神经退行性": "neurodegenerative",
            "血液": "blood",
            "骨髓": "bone marrow",
            "脾脏": "spleen",
            "淋巴结": "lymph node",
            "肺": "lung",
            "肝脏": "liver",
            "肾脏": "kidney",
            "心脏": "heart",
            "大脑": "brain",
            "皮肤": "skin",
            "肠道": "intestine",
            "胰腺": "pancreas"
        }
        
        # 默认学科领域关键词
        self.domain_keywords = {
            "immunology": ["immune", "immunity", "immunology", "T cell", "B cell", "NK cell", "dendritic", "macrophage", "cytokine", "antibody"],
            "oncology": ["cancer", "tumor", "oncology", "metastasis", "carcinoma", "malignant", "chemotherapy", "radiotherapy"],
            "neuroscience": ["neuron", "brain", "neural", "neuroscience", "synapse", "neurotransmitter", "cognitive"],
            "developmental_biology": ["development", "differentiation", "embryonic", "developmental", "morphogenesis", "organogenesis"],
            "metabolism": ["metabolism", "metabolic", "glucose", "insulin", "lipid", "metabolome", "energy"],
            "stem_cell": ["stem cell", "pluripotent", "multipotent", "regenerative", "self-renewal"]
        }
    
    def reload_configurations(self):
        """重新加载配置文件"""
        logger.info("Reloading configurations...")
        self._load_configurations()
    
    async def initialize(self) -> bool:
        """Initialize all services asynchronously"""
        if self._initialized:
            return True
        
        try:
            # Initialize validator
            if not self.validator:
                self.validator = get_input_validator()
            
            # Initialize cache manager
            if not self.cache_manager:
                self.cache_manager = await get_cache_manager()
            
            # Initialize rate limiter
            if not self.rate_limiter:
                self.rate_limiter = await get_rate_limiter()
            
            self._initialized = True
            logger.info("AIKeywordGenerator initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize AIKeywordGenerator: {e}")
            return False
    
    @property
    def terminology_mapping(self) -> Dict[str, str]:
        """Get terminology mapping with caching"""
        return self._terminology_mapping
    
    @property 
    def domain_keywords(self) -> Dict[str, List[str]]:
        """Get domain keywords with caching"""
        return self._domain_keywords
    
    @monitor_function("keyword_generation_full")
    async def generate_keywords_from_query(
        self, 
        user_query: str, 
        context: Optional[Dict[str, Any]] = None,
        max_keywords: int = 15,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Production-ready keyword generation with full validation and caching
        
        Args:
            user_query: 用户输入的查询
            context: 附加上下文信息
            max_keywords: 最大关键词数量
            user_id: 用户ID (for rate limiting)
            
        Returns:
            包含关键词分析结果的字典
            
        Raises:
            InvalidInputError: Input validation failed
            RateLimitError: Rate limit exceeded
            KeywordGenerationError: Generation failed
        """
        # Ensure initialization
        if not self._initialized:
            await self.initialize()
        
        start_time = datetime.now()
        
        try:
            # 1. Input validation
            await self._validate_inputs(user_query, context, max_keywords)
            
            # 2. Rate limiting check
            if user_id and self.rate_limiter:
                await self.rate_limiter.check_rate_limit(user_id)
            
            # 3. Check cache first
            cache_key = self._generate_cache_key(user_query, context, max_keywords)
            cached_result = await self._get_cached_result(cache_key)
            if cached_result:
                self.metrics_collector.record_cache_hit("query_result")
                logger.debug(f"Cache hit for query: {user_query[:50]}...")
                return cached_result
            
            self.metrics_collector.record_cache_miss("query_result")
            
            # 4. Generate keywords
            result = await self._generate_keywords_internal(user_query, context, max_keywords)
            
            # 5. Cache the result
            await self._cache_result(cache_key, result)
            
            # 6. Record quality metrics
            self._record_quality_metrics(result, start_time)
            
            return result
            
        except (InvalidInputError, KeywordGenerationError) as e:
            logger.warning(f"Keyword generation failed for query '{user_query[:50]}...': {e}")
            self.metrics_collector.record_error(type(e).__name__, "generate_keywords_from_query")
            raise
            
        except Exception as e:
            logger.error(f"Unexpected error in keyword generation: {e}")
            self.metrics_collector.record_error("UnexpectedError", "generate_keywords_from_query")
            
            # Return safe fallback result
            return self._create_fallback_result(user_query, str(e))
    
    async def _validate_inputs(
        self, 
        user_query: str, 
        context: Optional[Dict[str, Any]], 
        max_keywords: int
    ) -> None:
        """Comprehensive input validation"""
        try:
            # Validate query
            if not self.validator:
                raise InvalidInputError("Validator not initialized")
            
            validated_query = self.validator.validate_query(user_query, context)
            
            # Validate context
            if context is not None:
                self.validator.validate_context(context)
            
            # Validate max_keywords
            self.validator.validate_max_keywords(max_keywords)
            
        except Exception as e:
            if isinstance(e, InvalidInputError):
                raise
            raise InvalidInputError(f"Input validation failed: {str(e)}")
    
    def _generate_cache_key(
        self, 
        user_query: str, 
        context: Optional[Dict[str, Any]], 
        max_keywords: int
    ) -> str:
        """Generate cache key for query result"""
        if not self.cache_manager:
            return ""
        
        # Create consistent hash from inputs
        components = [
            user_query.strip().lower(),
            json.dumps(context, sort_keys=True) if context else "null",
            str(max_keywords)
        ]
        
        content = "|".join(components)
        content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
        
        return self.cache_manager.generate_cache_key("query_result", query_hash=content_hash)
    
    async def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached result if available"""
        if not self.cache_manager or not self.cache_manager.is_available():
            return None
        
        try:
            return await self.cache_manager.get(
                cache_key, 
                default=None,
                decompress=True
            )
        except Exception as e:
            logger.warning(f"Cache get failed: {e}")
            return None
    
    async def _cache_result(self, cache_key: str, result: Dict[str, Any]) -> None:
        """Cache the generation result"""
        if not self.cache_manager or not self.cache_manager.is_available():
            return
        
        try:
            await self.cache_manager.set(
                cache_key,
                result,
                ttl=self.config.CACHE_TTL_KEYWORDS,
                compress=True
            )
        except Exception as e:
            logger.warning(f"Cache set failed: {e}")
    
    async def _generate_keywords_internal(
        self, 
        user_query: str, 
        context: Optional[Dict[str, Any]], 
        max_keywords: int
    ) -> Dict[str, Any]:
        """Internal keyword generation logic"""
        try:
            # Performance monitoring for each stage
            async with monitor_performance("rule_based_extraction"):
                rule_based_keywords = self._extract_rule_based_keywords(user_query)
            
            async with monitor_performance("ai_enhanced_generation"):
                ai_enhanced_keywords = await self._generate_ai_enhanced_keywords(user_query, context)
            
            async with monitor_performance("keyword_optimization"):
                combined_keywords = self._combine_and_optimize_keywords(
                    rule_based_keywords, 
                    ai_enhanced_keywords, 
                    max_keywords
                )
            
            async with monitor_performance("translation"):
                english_keywords = await self._translate_to_english(combined_keywords)
            
            async with monitor_performance("domain_classification"):
                domain_classification = await self._classify_domain(english_keywords)
            
            # Calculate confidence and generate strategies
            confidence_score = self._calculate_confidence_score(english_keywords)
            search_strategies = self._generate_search_strategies(english_keywords)
            
            return {
                "user_query": user_query,
                "extracted_keywords": {
                    "chinese": combined_keywords,
                    "english": english_keywords
                },
                "domain_classification": domain_classification,
                "keyword_analysis": {
                    "total_keywords": len(english_keywords),
                    "confidence_score": confidence_score,
                    "coverage_domains": list(domain_classification.keys()),
                    "processing_metadata": {
                        "rule_based_count": len(rule_based_keywords),
                        "ai_enhanced_count": len(ai_enhanced_keywords),
                        "combined_count": len(combined_keywords),
                        "final_count": len(english_keywords)
                    }
                },
                "search_strategies": search_strategies,
                "timestamp": datetime.now().isoformat(),
                "version": "2.0"  # Production version
            }
            
        except Exception as e:
            logger.error(f"Internal keyword generation failed: {e}")
            raise KeywordGenerationError(
                f"Failed to generate keywords: {str(e)}",
                details={"query": user_query[:100], "max_keywords": max_keywords}
            )
    
    def _create_fallback_result(self, user_query: str, error_msg: str) -> Dict[str, Any]:
        """Create safe fallback result when generation fails"""
        return {
            "user_query": user_query,
            "extracted_keywords": {"chinese": [], "english": []},
            "domain_classification": {},
            "keyword_analysis": {
                "total_keywords": 0, 
                "confidence_score": 0.0,
                "coverage_domains": [],
                "processing_metadata": {"error": error_msg}
            },
            "search_strategies": {
                "primary_search_terms": [],
                "secondary_search_terms": [],
                "combined_queries": [],
                "boolean_queries": [],
                "field_specific_queries": {}
            },
            "timestamp": datetime.now().isoformat(),
            "version": "2.0",
            "error": error_msg
        }
    
    def _record_quality_metrics(self, result: Dict[str, Any], start_time: datetime) -> None:
        """Record quality metrics for monitoring"""
        try:
            processing_time = (datetime.now() - start_time).total_seconds()
            
            keywords_count = result.get("keyword_analysis", {}).get("total_keywords", 0)
            confidence_score = result.get("keyword_analysis", {}).get("confidence_score", 0.0)
            domains_covered = len(result.get("domain_classification", {}))
            query = result.get("user_query", "")
            
            self.metrics_collector.record_quality(
                query=query,
                keywords_count=keywords_count,
                confidence_score=confidence_score,
                domains_covered=domains_covered,
                processing_time=processing_time
            )
            
        except Exception as e:
            logger.warning(f"Failed to record quality metrics: {e}")
    
    def _extract_rule_based_keywords(self, query: str) -> List[str]:
        """基于规则提取关键词"""
        keywords = []
        query_lower = query.lower()
        
        # 直接匹配术语映射表
        for chinese_term, english_term in self.terminology_mapping.items():
            if chinese_term in query:
                keywords.append(chinese_term)
        
        # 提取技术平台关键词
        platform_patterns = [
            r'10x\s*genomics?', r'smart-?seq', r'drop-?seq', 
            r'mars-?seq', r'cel-?seq', r'fluidigm'
        ]
        for pattern in platform_patterns:
            matches = re.findall(pattern, query_lower)
            keywords.extend(matches)
        
        # 提取细胞类型
        cell_patterns = [
            r'([A-Z]\w*细胞)', r'(T细胞|B细胞|NK细胞)',
            r'(干细胞|免疫细胞|神经元|巨噬细胞)'
        ]
        for pattern in cell_patterns:
            matches = re.findall(pattern, query)
            keywords.extend([match for match in matches if isinstance(match, str)])
        
        # 提取生物学过程
        process_patterns = [
            r'(分化|发育|增殖|凋亡|激活|抑制)',
            r'(表达|调控|信号|通路|网络)',
            r'(分析|检测|鉴定|筛选|比较)'
        ]
        for pattern in process_patterns:
            matches = re.findall(pattern, query)
            keywords.extend(matches)
        
        return list(set(keywords))  # 去重
    
    async def _generate_ai_enhanced_keywords(
        self, 
        query: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """使用AI增强关键词生成（使用增强提示词管理器）"""
        try:
            # 使用增强提示词管理器
            template_id = "keyword_generation"
            template_variables = {
                "user_query": query,
                "context": json.dumps(context, ensure_ascii=False) if context else "无特定上下文",
                "max_keywords": str(15)
            }
            
            keyword_prompt = self.enhanced_prompt_manager.render_prompt(
                template_id,
                template_variables
            )
            
            if not keyword_prompt:
                # 回退到原始提示词
                keyword_prompt = f"""
作为单细胞生物学专家，请从用户查询中提取关键的科学术语和概念。

用户查询："{query}"

请提取以下类型的关键词：
1. 细胞类型和亚群
2. 生物学过程和功能
3. 技术平台和方法
4. 疾病和病理状态
5. 组织和器官
6. 分子标志物
7. 分析方法

请以JSON格式返回，包含以下字段：
{{
    "cell_types": ["提取的细胞类型"],
    "biological_processes": ["生物学过程"],
    "technologies": ["技术平台"],
    "diseases": ["疾病相关"],
    "tissues": ["组织器官"],
    "biomarkers": ["分子标志物"],
    "analysis_methods": ["分析方法"],
    "additional_terms": ["其他重要术语"]
}}

注意：
- 尽量提取具体的、科学准确的术语
- 避免过于宽泛的词汇
- 关注单细胞测序和生物学相关的专业术语
"""

            if self.ai_service.use_real_ai and self.ai_service.client:
                # 使用真实AI服务
                from langchain.schema import HumanMessage, SystemMessage
                
                messages = [
                    SystemMessage(content="你是一个专业的单细胞生物学关键词提取专家。"),
                    HumanMessage(content=keyword_prompt)
                ]
                
                response = await self.ai_service.client.ainvoke(messages)
                
                # 尝试解析JSON响应
                try:
                    import json
                    json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
                    if json_match:
                        keyword_data = json.loads(json_match.group())
                        
                        # 提取所有关键词
                        all_keywords = []
                        for category, terms in keyword_data.items():
                            if isinstance(terms, list):
                                all_keywords.extend(terms)
                        
                        return all_keywords
                        
                except (json.JSONDecodeError, Exception) as e:
                    logger.warning(f"AI关键词解析失败，使用备用方案: {e}")
                    return self._fallback_keyword_extraction(query)
            else:
                # 使用规则基础的备用方案
                return self._fallback_keyword_extraction(query)
                
        except Exception as e:
            logger.error(f"AI增强关键词生成失败: {e}")
            return self._fallback_keyword_extraction(query)
    
    def _fallback_keyword_extraction(self, query: str) -> List[str]:
        """备用关键词提取方案"""
        keywords = []
        
        # 基于模式匹配提取关键词
        patterns = {
            "细胞类型": [r'(\w*细胞)', r'(T细胞|B细胞|NK细胞|神经元|成纤维细胞)'],
            "技术": [r'(测序|分析|检测)', r'(10x|Smart-seq|Drop-seq)'],
            "过程": [r'(分化|发育|表达|调控|激活)'],
            "疾病": [r'(癌症|肿瘤|糖尿病|炎症)'],
            "组织": [r'(血液|肺|肝脏|心脏|大脑)']
        }
        
        for category, pattern_list in patterns.items():
            for pattern in pattern_list:
                matches = re.findall(pattern, query)
                keywords.extend([match for match in matches if isinstance(match, str)])
        
        return keywords
    
    def _combine_and_optimize_keywords(
        self, 
        rule_keywords: List[str], 
        ai_keywords: List[str], 
        max_keywords: int
    ) -> List[str]:
        """合并和优化关键词"""
        # 合并关键词
        all_keywords = list(set(rule_keywords + ai_keywords))
        
        # 按重要性排序
        scored_keywords = []
        for keyword in all_keywords:
            score = self._calculate_keyword_importance_score(keyword)
            scored_keywords.append((keyword, score))
        
        # 排序并截取
        scored_keywords.sort(key=lambda x: x[1], reverse=True)
        
        return [keyword for keyword, score in scored_keywords[:max_keywords]]
    
    def _calculate_keyword_importance_score(self, keyword: str) -> float:
        """Calculate keyword importance score with configurable weights"""
        score = 1.0
        keyword_lower = keyword.lower()
        
        # Technical platform keywords
        tech_terms = ['10x', 'smart-seq', 'drop-seq', '测序', '单细胞', 'genomics']
        if any(term in keyword_lower for term in tech_terms):
            score += self.terminology_config.TECH_PLATFORM_WEIGHT
        
        # Cell type keywords
        cell_indicators = ['细胞', 'cell', 'neuron', 'macrophage']
        if any(indicator in keyword_lower for indicator in cell_indicators):
            score += self.terminology_config.CELL_TYPE_WEIGHT
        
        # Biological process keywords
        process_terms = ['分化', '发育', '表达', '调控', '分析', 'differentiation', 'expression']
        if any(term in keyword_lower for term in process_terms):
            score += self.terminology_config.BIOLOGICAL_PROCESS_WEIGHT
        
        # Disease-related keywords
        disease_terms = ['癌症', '肿瘤', '糖尿病', '炎症', 'cancer', 'tumor', 'diabetes']
        if any(term in keyword_lower for term in disease_terms):
            score += self.terminology_config.DISEASE_WEIGHT
        
        # Terminology mapping presence
        if keyword in self.terminology_mapping:
            score += 1.0
        
        # Technology platform priority boost
        for platform, priority in self.terminology_config.PLATFORM_PRIORITIES.items():
            if platform.lower() in keyword_lower:
                # Higher priority = lower number = higher boost
                priority_boost = (len(self.terminology_config.PLATFORM_PRIORITIES) - priority + 1) * 0.1
                score += priority_boost
                break
        
        return score
    
    async def _translate_to_english(self, chinese_keywords: List[str]) -> List[str]:
        """Enhanced translation with fuzzy matching and caching"""
        if not chinese_keywords:
            return []
        
        english_keywords = []
        
        for keyword in chinese_keywords:
            try:
                # Check cache first
                translation = await self._get_cached_translation(keyword)
                if translation:
                    english_keywords.append(translation)
                    continue
                
                # Direct mapping
                if keyword in self.terminology_mapping:
                    translation = self.terminology_mapping[keyword]
                    english_keywords.append(translation)
                    await self._cache_translation(keyword, translation)
                    continue
                
                # Fuzzy matching if enabled
                if self.config.ENABLE_FUZZY_MATCHING:
                    fuzzy_translation = self._fuzzy_match_translation(keyword)
                    if fuzzy_translation:
                        english_keywords.append(fuzzy_translation)
                        await self._cache_translation(keyword, fuzzy_translation)
                        continue
                
                # Partial match mapping
                partial_translation = self._partial_match_translation(keyword)
                if partial_translation:
                    english_keywords.append(partial_translation)
                    await self._cache_translation(keyword, partial_translation)
                    continue
                
                # Keep original (might be English already)
                english_keywords.append(keyword)
                await self._cache_translation(keyword, keyword)
                
            except Exception as e:
                logger.warning(f"Translation failed for keyword '{keyword}': {e}")
                # Fallback to original
                english_keywords.append(keyword)
        
        return list(set(english_keywords))  # Remove duplicates
    
    async def _get_cached_translation(self, keyword: str) -> Optional[str]:
        """Get cached translation"""
        if not self.cache_manager or not self.cache_manager.is_available():
            return None
        
        try:
            cache_key = self.cache_manager.generate_cache_key(
                "terminology", 
                term=keyword
            )
            return await self.cache_manager.get(cache_key)
        except Exception:
            return None
    
    async def _cache_translation(self, keyword: str, translation: str) -> None:
        """Cache translation result"""
        if not self.cache_manager or not self.cache_manager.is_available():
            return
        
        try:
            cache_key = self.cache_manager.generate_cache_key(
                "terminology", 
                term=keyword
            )
            await self.cache_manager.set(
                cache_key, 
                translation, 
                ttl=self.config.CACHE_TTL_TERMINOLOGY
            )
        except Exception as e:
            logger.warning(f"Failed to cache translation for '{keyword}': {e}")
    
    def _fuzzy_match_translation(self, keyword: str) -> Optional[str]:
        """Find translation using fuzzy matching"""
        try:
            # Get best match from terminology mapping keys
            best_match = process.extractOne(
                keyword,
                self.terminology_mapping.keys(),
                scorer=fuzz.ratio
            )
            
            if (best_match and 
                best_match[1] >= self.config.FUZZY_MATCH_THRESHOLD * 100):
                
                matched_term = best_match[0]
                logger.debug(f"Fuzzy match: '{keyword}' -> '{matched_term}' (score: {best_match[1]})")
                return self.terminology_mapping[matched_term]
                
        except Exception as e:
            logger.warning(f"Fuzzy matching failed for '{keyword}': {e}")
        
        return None
    
    def _partial_match_translation(self, keyword: str) -> Optional[str]:
        """部分匹配翻译"""
        for chinese_term, english_term in self.terminology_mapping.items():
            if chinese_term in keyword or keyword in chinese_term:
                return english_term
        return None
    
    async def _classify_domain(self, keywords: List[str]) -> Dict[str, List[str]]:
        """Enhanced domain classification with caching and weights"""
        if not keywords:
            return {}
        
        # Check cache first
        cache_key = await self._get_domain_classification_cache_key(keywords)
        cached_result = await self._get_cached_domain_classification(cache_key)
        if cached_result:
            self.metrics_collector.record_cache_hit("domain_classification")
            return cached_result
        
        self.metrics_collector.record_cache_miss("domain_classification")
        
        try:
            classification = {}
            
            for domain, domain_terms in self.domain_keywords.items():
                matching_keywords = []
                domain_weight = self.terminology_config.DOMAIN_WEIGHTS.get(domain, 1.0)
                
                for keyword in keywords:
                    keyword_lower = keyword.lower()
                    
                    # Exact match
                    for term in domain_terms:
                        term_lower = term.lower()
                        if (term_lower in keyword_lower or 
                            keyword_lower in term_lower or
                            keyword_lower == term_lower):
                            
                            matching_keywords.append({
                                "keyword": keyword,
                                "matched_term": term,
                                "weight": domain_weight,
                                "match_type": "exact"
                            })
                            break
                    else:
                        # Fuzzy match if enabled
                        if self.config.ENABLE_FUZZY_MATCHING:
                            best_match = process.extractOne(
                                keyword, 
                                domain_terms,
                                scorer=fuzz.partial_ratio
                            )
                            
                            if (best_match and 
                                best_match[1] >= self.config.FUZZY_MATCH_THRESHOLD * 100):
                                
                                matching_keywords.append({
                                    "keyword": keyword,
                                    "matched_term": best_match[0],
                                    "weight": domain_weight * 0.8,  # Slightly lower weight for fuzzy
                                    "match_type": "fuzzy",
                                    "score": best_match[1]
                                })
                
                if matching_keywords:
                    # Sort by weight and take unique keywords
                    unique_keywords = {}
                    for match in matching_keywords:
                        kw = match["keyword"]
                        if kw not in unique_keywords or match["weight"] > unique_keywords[kw]["weight"]:
                            unique_keywords[kw] = match
                    
                    classification[domain] = list(unique_keywords.keys())
            
            # Cache the result
            await self._cache_domain_classification(cache_key, classification)
            
            return classification
            
        except Exception as e:
            logger.error(f"Domain classification failed: {e}")
            raise DomainClassificationError(
                f"Failed to classify domains: {str(e)}",
                keywords=keywords
            )
    
    async def _get_domain_classification_cache_key(self, keywords: List[str]) -> str:
        """Generate cache key for domain classification"""
        if not self.cache_manager:
            return ""
        
        # Sort keywords for consistent key
        sorted_keywords = sorted(keywords)
        keywords_hash = hashlib.md5(
            json.dumps(sorted_keywords, ensure_ascii=False).encode('utf-8')
        ).hexdigest()
        
        return self.cache_manager.generate_cache_key(
            "domain_classification", 
            keywords_hash=keywords_hash
        )
    
    async def _get_cached_domain_classification(self, cache_key: str) -> Optional[Dict[str, List[str]]]:
        """Get cached domain classification"""
        if not self.cache_manager or not self.cache_manager.is_available():
            return None
        
        try:
            return await self.cache_manager.get(cache_key)
        except Exception:
            return None
    
    async def _cache_domain_classification(
        self, 
        cache_key: str, 
        classification: Dict[str, List[str]]
    ) -> None:
        """Cache domain classification result"""
        if not self.cache_manager or not self.cache_manager.is_available():
            return
        
        try:
            await self.cache_manager.set(
                cache_key,
                classification,
                ttl=self.config.CACHE_TTL_DOMAIN_CLASSIFICATION
            )
        except Exception as e:
            logger.warning(f"Failed to cache domain classification: {e}")
    
    def _calculate_confidence_score(self, keywords: List[str]) -> float:
        """计算关键词提取的置信度分数"""
        if not keywords:
            return 0.0
        
        # 基础置信度
        confidence = 0.5
        
        # 关键词数量加分
        confidence += min(len(keywords) * 0.05, 0.3)
        
        # 术语映射覆盖率加分
        mapped_count = sum(1 for kw in keywords if kw in self.terminology_mapping.values())
        coverage_ratio = mapped_count / len(keywords)
        confidence += coverage_ratio * 0.2
        
        return min(confidence, 1.0)
    
    def _generate_search_strategies(self, keywords: List[str]) -> Dict[str, Any]:
        """Generate optimized search strategies with configurable parameters"""
        if not keywords:
            return {
                "primary_search_terms": [],
                "secondary_search_terms": [],
                "combined_queries": [],
                "boolean_queries": [],
                "field_specific_queries": {}
            }
        
        # Use configuration for counts
        primary_count = min(len(keywords), self.config.PRIMARY_SEARCH_TERMS_COUNT)
        secondary_count = min(len(keywords) - primary_count, self.config.SECONDARY_SEARCH_TERMS_COUNT)
        
        strategies = {
            "primary_search_terms": keywords[:primary_count],
            "secondary_search_terms": keywords[primary_count:primary_count + secondary_count],
            "combined_queries": [],
            "boolean_queries": [],
            "field_specific_queries": {},
            "optimization_metadata": {
                "total_keywords": len(keywords),
                "primary_count": primary_count,
                "secondary_count": secondary_count
            }
        }
        
        # Generate combined queries with limits
        combined_queries = []
        if len(keywords) >= 2:
            for i in range(min(len(keywords) - 1, self.config.MAX_COMBINED_QUERIES)):
                combined_queries.extend([
                    f'"{keywords[i]}" AND "{keywords[i+1]}"',
                    f'({keywords[i]} OR {keywords[i+1]}) AND "single cell"'
                ])
        
        strategies["combined_queries"] = combined_queries[:self.config.MAX_COMBINED_QUERIES]
        
        # Generate boolean queries with limits
        boolean_queries = []
        if len(keywords) >= 3:
            for i in range(min(len(keywords) - 2, self.config.MAX_BOOLEAN_QUERIES)):
                boolean_queries.extend([
                    f'({keywords[i]} AND {keywords[i+1]}) OR {keywords[i+2]}',
                    f'{keywords[i]} AND ("single cell" OR "scRNA-seq")',
                    f'"{keywords[i]}" AND ("{keywords[i+1]}" OR "{keywords[i+2]}")'
                ])
        
        strategies["boolean_queries"] = boolean_queries[:self.config.MAX_BOOLEAN_QUERIES]
        
        # Generate field-specific queries
        strategies["field_specific_queries"] = {
            "title": keywords[:3],
            "abstract": keywords[:5],
            "keywords_field": keywords[:8],
            "author": self._extract_potential_authors(keywords),
            "journal": self._extract_potential_journals(keywords)
        }
        
        # Add advanced search patterns
        strategies["advanced_patterns"] = self._generate_advanced_search_patterns(keywords)
        
        return strategies
    
    def _extract_potential_authors(self, keywords: List[str]) -> List[str]:
        """Extract potential author names from keywords"""
        potential_authors = []
        
        # Look for patterns that might be author names
        import re
        for keyword in keywords:
            # Pattern for "FirstName LastName" or "A. LastName"
            if re.match(r'^[A-Z][a-z]+\s+[A-Z][a-z]+$', keyword):
                potential_authors.append(keyword)
            elif re.match(r'^[A-Z]\.\s+[A-Z][a-z]+$', keyword):
                potential_authors.append(keyword)
        
        return potential_authors[:3]  # Limit to 3 potential authors
    
    def _extract_potential_journals(self, keywords: List[str]) -> List[str]:
        """Extract potential journal names from keywords"""
        journal_indicators = [
            'nature', 'science', 'cell', 'journal', 'proceedings', 
            'genomics', 'bioinformatics', 'methods', 'biotechnology'
        ]
        
        potential_journals = []
        for keyword in keywords:
            if any(indicator in keyword.lower() for indicator in journal_indicators):
                potential_journals.append(keyword)
        
        return potential_journals[:3]
    
    def _generate_advanced_search_patterns(self, keywords: List[str]) -> Dict[str, List[str]]:
        """Generate advanced search patterns"""
        patterns = {
            "proximity_searches": [],
            "wildcard_searches": [],
            "phrase_searches": [],
            "field_combinations": []
        }
        
        # Proximity searches (terms within N words of each other)
        if len(keywords) >= 2:
            patterns["proximity_searches"] = [
                f'"{keywords[0]}" NEAR/5 "{keywords[1]}"',
                f'"{keywords[0]}" ADJ3 "{keywords[1]}"'
            ]
        
        # Wildcard searches for variant terms
        patterns["wildcard_searches"] = [
            f"{kw}*" for kw in keywords[:3] if len(kw) > 4
        ]
        
        # Phrase searches
        patterns["phrase_searches"] = [
            f'"{kw}"' for kw in keywords if ' ' in kw
        ]
        
        # Field combinations
        if len(keywords) >= 2:
            patterns["field_combinations"] = [
                f'title:"{keywords[0]}" AND abstract:"{keywords[1]}"',
                f'keywords:"{keywords[0]}" AND title:"{keywords[1]}"'
            ]
        
        return patterns


# Production-ready factory and utilities

class KeywordGeneratorFactory:
    """Factory for creating optimized keyword generator instances"""
    
    _instance: Optional[AIKeywordGenerator] = None
    _lock = asyncio.Lock()
    
    @classmethod
    async def get_instance(cls) -> AIKeywordGenerator:
        """Get singleton instance with lazy initialization"""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = AIKeywordGenerator()
                    await cls._instance.initialize()
        
        return cls._instance
    
    @classmethod
    async def create_new_instance(
        cls,
        config: Optional[KeywordConfig] = None,
        terminology_config: Optional[TerminologyConfig] = None
    ) -> AIKeywordGenerator:
        """Create new instance with custom configuration"""
        instance = AIKeywordGenerator(
            config=config,
            terminology_config=terminology_config
        )
        await instance.initialize()
        return instance
    
    @classmethod
    async def reset_instance(cls):
        """Reset the singleton instance (for testing)"""
        async with cls._lock:
            cls._instance = None


# Health check and diagnostics
class KeywordGeneratorDiagnostics:
    """Diagnostic utilities for keyword generator"""
    
    @staticmethod
    async def health_check(generator: AIKeywordGenerator) -> Dict[str, Any]:
        """Comprehensive health check"""
        health_status = {
            "status": "healthy",
            "checks": {},
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            # Check initialization
            health_status["checks"]["initialized"] = generator._initialized
            
            # Check validator
            health_status["checks"]["validator"] = generator.validator is not None
            
            # Check cache manager
            if generator.cache_manager:
                cache_available = generator.cache_manager.is_available()
                health_status["checks"]["cache"] = {
                    "available": cache_available,
                    "stats": await generator.cache_manager.get_stats() if cache_available else None
                }
            else:
                health_status["checks"]["cache"] = {"available": False}
            
            # Check rate limiter
            health_status["checks"]["rate_limiter"] = generator.rate_limiter is not None
            
            # Check AI service
            health_status["checks"]["ai_service"] = generator.ai_service is not None
            
            # Test basic functionality
            try:
                test_result = await generator.generate_keywords_from_query(
                    "test query",
                    max_keywords=3
                )
                health_status["checks"]["basic_functionality"] = "working"
            except Exception as e:
                health_status["checks"]["basic_functionality"] = f"failed: {str(e)}"
                health_status["status"] = "degraded"
            
        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["error"] = str(e)
        
        return health_status
    
    @staticmethod
    async def performance_benchmark(
        generator: AIKeywordGenerator,
        test_queries: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Run performance benchmark"""
        if test_queries is None:
            test_queries = [
                "单细胞RNA测序分析",
                "T细胞免疫反应研究",
                "10x Genomics PBMC数据分析",
                "肿瘤微环境单细胞图谱",
                "神经元发育轨迹分析"
            ]
        
        benchmark_results = {
            "test_queries_count": len(test_queries),
            "results": [],
            "summary": {},
            "timestamp": datetime.now().isoformat()
        }
        
        durations = []
        keyword_counts = []
        confidence_scores = []
        
        for query in test_queries:
            start_time = time.time()
            
            try:
                result = await generator.generate_keywords_from_query(query, max_keywords=10)
                duration = time.time() - start_time
                
                durations.append(duration)
                keyword_counts.append(result.get("keyword_analysis", {}).get("total_keywords", 0))
                confidence_scores.append(result.get("keyword_analysis", {}).get("confidence_score", 0.0))
                
                benchmark_results["results"].append({
                    "query": query,
                    "duration": duration,
                    "keywords_count": result.get("keyword_analysis", {}).get("total_keywords", 0),
                    "confidence": result.get("keyword_analysis", {}).get("confidence_score", 0.0),
                    "success": True
                })
                
            except Exception as e:
                duration = time.time() - start_time
                benchmark_results["results"].append({
                    "query": query,
                    "duration": duration,
                    "error": str(e),
                    "success": False
                })
        
        # Calculate summary statistics
        if durations:
            benchmark_results["summary"] = {
                "avg_duration": sum(durations) / len(durations),
                "min_duration": min(durations),
                "max_duration": max(durations),
                "avg_keywords": sum(keyword_counts) / len(keyword_counts) if keyword_counts else 0,
                "avg_confidence": sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0,
                "success_rate": len([r for r in benchmark_results["results"] if r["success"]]) / len(test_queries)
            }
        
        return benchmark_results


# Performance optimization utilities
async def optimize_terminology_mapping(generator: AIKeywordGenerator) -> Dict[str, Any]:
    """Optimize terminology mapping for better performance"""
    optimization_results = {
        "original_size": len(generator.terminology_mapping),
        "optimizations_applied": [],
        "final_size": 0,
        "performance_improvement": 0.0
    }
    
    # Add reverse mappings for faster lookup
    reverse_mapping = {}
    for chinese, english in generator.terminology_mapping.items():
        if english not in reverse_mapping:
            reverse_mapping[english] = []
        reverse_mapping[english].append(chinese)
    
    # Cache frequently accessed terms
    if generator.cache_manager and generator.cache_manager.is_available():
        try:
            # Pre-populate cache with common terms
            common_terms = [
                "单细胞", "T细胞", "B细胞", "基因表达", "细胞分化",
                "10x Genomics", "Smart-seq", "PBMC", "肿瘤微环境"
            ]
            
            for term in common_terms:
                if term in generator.terminology_mapping:
                    await generator._cache_translation(term, generator.terminology_mapping[term])
            
            optimization_results["optimizations_applied"].append("cached_common_terms")
            
        except Exception as e:
            logger.warning(f"Failed to pre-populate cache: {e}")
    
    optimization_results["final_size"] = len(generator.terminology_mapping)
    
    return optimization_results


# Global factory instance
keyword_generator_factory = KeywordGeneratorFactory()


async def get_keyword_generator() -> AIKeywordGenerator:
    """Get optimized keyword generator instance"""
    return await keyword_generator_factory.get_instance()


async def create_keyword_generator(
    config: Optional[KeywordConfig] = None,
    terminology_config: Optional[TerminologyConfig] = None
) -> AIKeywordGenerator:
    """Create new keyword generator with custom configuration"""
    return await keyword_generator_factory.create_new_instance(config, terminology_config)


# Add performance timeout wrapper
async def generate_keywords_with_timeout(
    generator: AIKeywordGenerator,
    user_query: str,
    context: Optional[Dict[str, Any]] = None,
    max_keywords: int = 15,
    user_id: Optional[str] = None,
    timeout: Optional[float] = None
) -> Dict[str, Any]:
    """Generate keywords with timeout protection"""
    if timeout is None:
        timeout = generator.config.MAX_PROCESSING_TIME
    
    try:
        return await asyncio.wait_for(
            generator.generate_keywords_from_query(
                user_query=user_query,
                context=context,
                max_keywords=max_keywords,
                user_id=user_id
            ),
            timeout=timeout
        )
    except asyncio.TimeoutError:
        logger.error(f"Keyword generation timed out after {timeout}s for query: {user_query[:50]}...")
        
        # Record performance error
        generator.metrics_collector.record_error("TimeoutError", "generate_keywords_from_query")
        
        raise PerformanceError(
            f"Keyword generation exceeded timeout of {timeout} seconds",
            operation="generate_keywords_from_query",
            duration=timeout,
            threshold=timeout
        )