"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { PermissionGate } from "@/components/permission-gate"
import { Search, Download, Calendar, Filter, Eye } from "lucide-react"

interface AuditLog {
  id: string
  action: string
  user: string
  userRole: string
  timestamp: string
  ip: string
  details: string
  status: "success" | "warning" | "error"
}

export default function AuditLogsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [dateRange, setDateRange] = useState<[string, string]>(["", ""])

  // 模拟审计日志数据
  const auditLogs: AuditLog[] = [
    {
      id: "1",
      action: "用户登录",
      user: "张三",
      userRole: "超级管理员",
      timestamp: "2024-05-20 10:30:15",
      ip: "***********",
      details: "成功登录系统",
      status: "success",
    },
    {
      id: "2",
      action: "修改权限",
      user: "张三",
      userRole: "超级管理员",
      timestamp: "2024-05-20 10:45:22",
      ip: "***********",
      details: "修改了销售人员角色的权限",
      status: "success",
    },
    {
      id: "3",
      action: "添加用户",
      user: "张三",
      userRole: "超级管理员",
      timestamp: "2024-05-20 11:15:30",
      ip: "***********",
      details: "添加了新用户：李四",
      status: "success",
    },
    {
      id: "4",
      action: "删除文档",
      user: "王五",
      userRole: "运维人员",
      timestamp: "2024-05-20 14:22:10",
      ip: "***********",
      details: "删除了知识库文档：单细胞测序操作指南",
      status: "warning",
    },
    {
      id: "5",
      action: "访问敏感数据",
      user: "李四",
      userRole: "销售人员",
      timestamp: "2024-05-20 15:30:45",
      ip: "************",
      details: "尝试访问未授权的客户财务数据",
      status: "error",
    },
    {
      id: "6",
      action: "系统配置修改",
      user: "张三",
      userRole: "超级管理员",
      timestamp: "2024-05-20 16:05:12",
      ip: "***********",
      details: "修改了系统API集成配置",
      status: "success",
    },
    {
      id: "7",
      action: "生成方案",
      user: "李四",
      userRole: "销售人员",
      timestamp: "2024-05-20 16:30:20",
      ip: "************",
      details: "为客户生成了新的测序方案",
      status: "success",
    },
    {
      id: "8",
      action: "密码重置",
      user: "系统",
      userRole: "系统",
      timestamp: "2024-05-20 17:15:33",
      ip: "127.0.0.1",
      details: "为用户赵六重置了密码",
      status: "success",
    },
  ]

  // 过滤日志
  const filteredLogs = auditLogs.filter((log) => {
    if (
      searchQuery &&
      !log.action.includes(searchQuery) &&
      !log.user.includes(searchQuery) &&
      !log.details.includes(searchQuery)
    ) {
      return false
    }
    return true
  })

  return (
    <div className="container py-10">
      <PermissionGate permission="view_logs" fallback={<div>您没有权限访问此页面</div>}>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">审计日志</h1>
            <p className="text-muted-foreground">查看系统操作记录和安全审计</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              选择日期范围
            </Button>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              筛选
            </Button>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              导出日志
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>系统审计日志</CardTitle>
                <CardDescription>记录所有用户操作和系统事件</CardDescription>
              </div>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="搜索日志..."
                  className="pl-8 w-[250px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>操作</TableHead>
                  <TableHead>用户</TableHead>
                  <TableHead>时间</TableHead>
                  <TableHead>IP地址</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLogs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                      未找到匹配的日志记录
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <div className="font-medium">{log.action}</div>
                        <div className="text-sm text-muted-foreground">{log.details}</div>
                      </TableCell>
                      <TableCell>
                        <div>{log.user}</div>
                        <div className="text-xs text-muted-foreground">{log.userRole}</div>
                      </TableCell>
                      <TableCell>{log.timestamp}</TableCell>
                      <TableCell>{log.ip}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            log.status === "success"
                              ? "default"
                              : log.status === "warning"
                                ? "secondary"
                                : "destructive"
                          }
                        >
                          {log.status === "success" ? "成功" : log.status === "warning" ? "警告" : "错误"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </PermissionGate>
    </div>
  )
}
