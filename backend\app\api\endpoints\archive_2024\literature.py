"""
文献资源API端点
"""
import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel

logger = logging.getLogger(__name__)

from app.models.user import User
from app.core.auth import get_current_active_user
from app.services.literature_service import literature_service
from app.services.unified_literature_api_service import get_unified_literature_api_service
from app.services.research_intent_service import get_research_intent_service
from app.services.enhanced_solution_generator import get_enhanced_solution_generator

router = APIRouter()


class LiteratureSearchRequest(BaseModel):
    """文献搜索请求"""
    query: str
    category: Optional[str] = None
    technology_tags: Optional[List[str]] = None
    top_k: int = 10


class LiteratureRecommendationRequest(BaseModel):
    """文献推荐请求"""
    context: dict
    recommendation_type: str = "mixed"  # authority, methodology, application, mixed
    top_k: int = 5


class LiteratureResponse(BaseModel):
    """文献响应"""
    id: Optional[int] = None
    title: str
    authors: List[str]
    journal: str
    publication_year: int
    doi: Optional[str] = None
    pubmed_id: Optional[str] = None
    abstract: str
    category: str
    technology_tags: List[str]
    application_tags: List[str]
    impact_factor: Optional[float] = None
    citation_count: int
    relevance_score: float
    key_findings: str
    methodology_summary: str
    business_value: str


class LiteratureRecommendationResponse(BaseModel):
    """文献推荐响应"""
    literature: LiteratureResponse
    recommendation_type: str
    relevance_explanation: str
    business_value: str
    key_support_points: List[str]


class KeywordRecommendationRequest(BaseModel):
    """关键词推荐请求"""
    user_query: str
    context: Optional[Dict[str, Any]] = None
    max_keywords: int = 15


class KeywordRecommendationResponse(BaseModel):
    """关键词推荐响应"""
    user_query: str
    extracted_keywords: Dict[str, List[str]]
    domain_classification: Dict[str, List[str]]
    keyword_analysis: Dict[str, Any]
    search_strategies: Dict[str, Any]
    timestamp: str


class EnhancedSearchRequest(BaseModel):
    """增强搜索请求"""
    user_query: str
    context: Optional[Dict[str, Any]] = None
    use_keyword_enhancement: bool = True
    max_keywords: int = 10
    top_k: int = 10


@router.post("/search", response_model=List[LiteratureResponse])
async def search_literature(
    request: LiteratureSearchRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    搜索文献资源
    """
    try:
        results = await literature_service.search_literature(
            query=request.query,
            category=request.category,
            technology_tags=request.technology_tags,
            top_k=request.top_k
        )

        # 转换为响应格式
        literature_responses = []
        for result in results:
            literature_response = LiteratureResponse(
                title=result["title"],
                authors=result["authors"],
                journal=result["journal"],
                publication_year=result["publication_year"],
                doi=result.get("doi"),
                pubmed_id=result.get("pubmed_id"),
                abstract=result["abstract"],
                category=result["category"],
                technology_tags=result["technology_tags"],
                application_tags=result["application_tags"],
                impact_factor=result.get("impact_factor"),
                citation_count=result["citation_count"],
                relevance_score=result["relevance_score"],
                key_findings=result["key_findings"],
                methodology_summary=result["methodology_summary"],
                business_value=result["business_value"]
            )
            literature_responses.append(literature_response)

        return literature_responses

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文献搜索失败: {str(e)}")


@router.post("/recommendations", response_model=List[LiteratureRecommendationResponse])
async def get_literature_recommendations(
    request: LiteratureRecommendationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    获取文献推荐
    """
    try:
        recommendations = await literature_service.get_literature_recommendations(
            context=request.context,
            recommendation_type=request.recommendation_type,
            top_k=request.top_k
        )

        # 转换为响应格式
        recommendation_responses = []
        for rec in recommendations:
            lit = rec["literature"]
            recommendation_response = LiteratureRecommendationResponse(
                literature=LiteratureResponse(
                    title=lit["title"],
                    authors=lit["authors"],
                    journal=lit["journal"],
                    publication_year=lit["publication_year"],
                    doi=lit.get("doi"),
                    pubmed_id=lit.get("pubmed_id"),
                    abstract=lit["abstract"],
                    category=lit["category"],
                    technology_tags=lit["technology_tags"],
                    application_tags=lit["application_tags"],
                    impact_factor=lit.get("impact_factor"),
                    citation_count=lit["citation_count"],
                    relevance_score=lit["relevance_score"],
                    key_findings=lit["key_findings"],
                    methodology_summary=lit["methodology_summary"],
                    business_value=lit["business_value"]
                ),
                recommendation_type=rec["recommendation_type"],
                relevance_explanation=rec["relevance_explanation"],
                business_value=rec["business_value"],
                key_support_points=rec["key_support_points"]
            )
            recommendation_responses.append(recommendation_response)

        return recommendation_responses

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文献推荐失败: {str(e)}")


@router.get("/categories")
async def get_literature_categories(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取文献分类列表
    """
    return {
        "categories": [
            {"value": "methodology", "label": "方法学研究"},
            {"value": "application", "label": "应用案例"},
            {"value": "technology", "label": "技术开发"},
            {"value": "review", "label": "综述文章"},
            {"value": "protocol", "label": "实验协议"}
        ],
        "technology_tags": [
            {"value": "scRNA-seq", "label": "单细胞RNA测序"},
            {"value": "scATAC-seq", "label": "单细胞ATAC测序"},
            {"value": "scDNA-seq", "label": "单细胞DNA测序"},
            {"value": "multiome", "label": "多组学测序"},
            {"value": "spatial", "label": "空间转录组"},
            {"value": "10x_genomics", "label": "10x Genomics平台"},
            {"value": "smart_seq", "label": "Smart-seq技术"},
            {"value": "droplet_seq", "label": "Droplet-seq技术"}
        ],
        "application_tags": [
            {"value": "immunology", "label": "免疫学"},
            {"value": "oncology", "label": "肿瘤学"},
            {"value": "neuroscience", "label": "神经科学"},
            {"value": "development", "label": "发育生物学"},
            {"value": "stem_cell", "label": "干细胞研究"},
            {"value": "drug_discovery", "label": "药物发现"},
            {"value": "clinical", "label": "临床研究"}
        ]
    }


@router.get("/trending")
async def get_trending_literature(
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取热门文献
    """
    try:
        # 获取高影响因子和高引用的文献
        results = await literature_service.search_literature(
            query="single cell",
            top_k=limit * 2
        )

        # 按影响因子和引用数排序
        trending = sorted(results,
                         key=lambda x: (x.get("impact_factor", 0) * 0.3 +
                                      x.get("citation_count", 0) * 0.0001),
                         reverse=True)

        return {
            "trending_literature": trending[:limit],
            "total_count": len(trending)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取热门文献失败: {str(e)}")


@router.get("/api-status")
async def get_api_status(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取所有外部API的详细状态
    """
    from app.core.external_apis import get_api_manager
    from app.core.config import settings
    import aiohttp
    
    manager = get_api_manager()
    api_status = {}
    
    # 检查每个API的连接状态
    for name, config in manager.apis.items():
        status_info = {
            "enabled": config.enabled,
            "has_api_key": config.api_key is not None,
            "requires_key": config.requires_key(),
            "is_available": config.is_available(),
            "base_url": config.base_url,
            "rate_limit": config.rate_limit
        }
        
        # 测试连接
        if config.is_available():
            try:
                if name == "pubmed":
                    test_url = f"{config.base_url}/esearch.fcgi"
                    params = {"db": "pubmed", "term": "test", "retmax": "1", "retmode": "json"}
                    if config.api_key:
                        params["api_key"] = config.api_key
                elif name == "google_scholar":
                    test_url = config.base_url
                    params = {"engine": "google_scholar", "q": "test", "num": 1, "api_key": config.api_key}
                elif name == "semantic_scholar":
                    test_url = f"{config.base_url}/paper/search"
                    params = {"query": "test", "limit": 1}
                else:
                    status_info["connection_test"] = "skipped"
                    api_status[name] = status_info
                    continue
                
                # 执行连接测试
                timeout = aiohttp.ClientTimeout(total=5)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    headers = config.get_headers() if name == "semantic_scholar" else {}
                    async with session.get(test_url, params=params, headers=headers) as response:
                        if response.status == 200:
                            status_info["connection_test"] = "success"
                        else:
                            status_info["connection_test"] = f"failed (HTTP {response.status})"
                            
            except Exception as e:
                status_info["connection_test"] = f"error: {str(e)}"
        else:
            status_info["connection_test"] = "not_available"
        
        api_status[name] = status_info
    
    return {
        "literature_search_enabled": settings.LITERATURE_SEARCH_ENABLED,
        "apis": api_status,
        "summary": {
            "total_apis": len(manager.apis),
            "available_apis": len([api for api in api_status.values() if api["is_available"]]),
            "working_apis": len([api for api in api_status.values() if api.get("connection_test") == "success"])
        },
        "recommendations": _generate_api_recommendations(api_status)
    }


@router.post("/keyword-recommendation", response_model=KeywordRecommendationResponse)
async def get_keyword_recommendation(
    request: KeywordRecommendationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    基于用户查询生成科学关键词推荐
    集成到研究意图分析中
    """
    try:
        # 使用研究意图服务来生成关键词
        research_intent_service = get_research_intent_service()
        
        # 构建虚拟需求来进行意图分析
        requirements = {
            "researchGoal": request.user_query,
            "sampleType": request.context.get("sample_type", "") if request.context else "",
            "speciesType": request.context.get("species_type", "") if request.context else ""
        }
        
        intent_analysis = await research_intent_service.analyze_research_intent_and_generate_keywords(
            requirements=requirements,
            user_message=request.user_query
        )
        
        # 提取关键词信息
        terminology = intent_analysis.get("dynamic_terminology", {})
        
        return KeywordRecommendationResponse(
            user_query=request.user_query,
            extracted_keywords=terminology.get("species_terminology", {}).get("common_variants", []),
            domain_classification=terminology.get("domain_specific_terms", {}),
            keyword_analysis={"method": "research_intent_analysis"},
            search_strategies={"comprehensive_queries": terminology.get("comprehensive_search_queries", [])},
            timestamp=intent_analysis.get("generation_metadata", {}).get("generated_at", "")
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"关键词推荐失败: {str(e)}")


@router.post("/search-enhanced", response_model=Dict[str, Any])
async def enhanced_literature_search(
    request: EnhancedSearchRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    增强文献搜索（集成研究意图分析）
    """
    try:
        # 使用统一文献API服务进行搜索
        unified_service = await get_unified_literature_api_service()
        search_results = await unified_service.comprehensive_search(
            query=request.user_query,
            max_results_per_source=max(3, request.top_k // 3)
        )
        
        papers = search_results.get("papers", [])
        final_results = papers[:request.top_k]
        
        # 转换为响应格式
        literature_responses = []
        for result in final_results:
            try:
                literature_response = LiteratureResponse(
                    title=result.get("title", ""),
                    authors=result.get("authors", []),
                    journal=result.get("journal", ""),
                    publication_year=result.get("publication_year", 2024),
                    doi=result.get("doi"),
                    pubmed_id=result.get("pubmed_id"),
                    abstract=result.get("abstract", ""),
                    category="external",  # 外部来源统一分类
                    technology_tags=result.get("technology_tags", []),
                    application_tags=result.get("application_tags", []),
                    impact_factor=result.get("impact_factor"),
                    citation_count=result.get("citation_count", 0),
                    relevance_score=result.get("relevance_score", 0.5),
                    key_findings=result.get("key_findings", ""),
                    methodology_summary=result.get("methodology_summary", ""),
                    business_value=result.get("business_value", "")
                )
                literature_responses.append(literature_response)
            except Exception as e:
                logger.warning(f"转换文献响应失败: {e}")
                continue
        
        return {
            "search_request": {
                "user_query": request.user_query,
                "use_keyword_enhancement": request.use_keyword_enhancement,
                "search_method": "unified_multi_source"
            },
            "search_metadata": search_results,
            "literature_results": literature_responses,
            "search_statistics": {
                "total_results_found": search_results.get("total_found", 0),
                "returned_results": len(literature_responses),
                "sources_used": search_results.get("sources_used", []),
                "search_time": search_results.get("search_time", 0)
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"增强搜索失败: {str(e)}")


@router.get("/keyword-domains")
async def get_keyword_domains(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取支持的学科领域和术语映射
    """
    return {
        "supported_domains": {
            "immunology": {
                "name": "免疫学",
                "description": "免疫细胞、免疫应答、免疫疾病相关研究",
                "sample_keywords": ["T cell", "B cell", "immunity", "cytokine"]
            },
            "oncology": {
                "name": "肿瘤学",
                "description": "癌症、肿瘤微环境、肿瘤生物学研究",
                "sample_keywords": ["cancer", "tumor", "metastasis", "oncology"]
            },
            "neuroscience": {
                "name": "神经科学",
                "description": "神经元、大脑功能、神经系统疾病研究",
                "sample_keywords": ["neuron", "brain", "neural", "synapse"]
            },
            "developmental_biology": {
                "name": "发育生物学",
                "description": "细胞分化、发育轨迹、胚胎发育研究",
                "sample_keywords": ["development", "differentiation", "embryonic"]
            },
            "metabolism": {
                "name": "代谢学",
                "description": "代谢过程、能量代谢、代谢疾病研究",
                "sample_keywords": ["metabolism", "glucose", "insulin", "energy"]
            },
            "stem_cell": {
                "name": "干细胞研究",
                "description": "干细胞生物学、再生医学研究",
                "sample_keywords": ["stem cell", "pluripotent", "regenerative"]
            }
        },
        "terminology_examples": {
            "cell_types": {
                "单细胞": "single cell",
                "T细胞": "T cell",
                "神经元": "neuron",
                "巨噬细胞": "macrophage"
            },
            "technologies": {
                "10x基因组学": "10x Genomics",
                "Smart-seq": "Smart-seq",
                "空间转录组": "spatial transcriptomics"
            },
            "processes": {
                "细胞分化": "cell differentiation",
                "基因表达": "gene expression",
                "通路分析": "pathway analysis"
            }
        },
        "search_tips": [
            "使用具体的细胞类型或组织名称",
            "包含技术平台信息（如10x Genomics）",
            "描述具体的研究目标或生物过程",
            "提及疾病或病理状态",
            "使用英文科学术语会获得更精确的结果"
        ]
    }


def _generate_api_recommendations(api_status: dict) -> list:
    """生成API配置建议"""
    recommendations = []
    
    # 检查PubMed
    if not api_status.get("pubmed", {}).get("has_api_key"):
        recommendations.append({
            "type": "missing_key",
            "api": "pubmed",
            "message": "建议配置PUBMED_API_KEY以获得更好的搜索性能",
            "url": "https://ncbiinsights.ncbi.nlm.nih.gov/2017/11/02/new-api-keys-for-the-e-utilities/"
        })
    
    # 检查Google Scholar
    if not api_status.get("google_scholar", {}).get("has_api_key"):
        recommendations.append({
            "type": "enhancement",
            "api": "google_scholar", 
            "message": "配置SERPAPI_KEY可以启用Google Scholar搜索",
            "url": "https://serpapi.com/"
        })
    
    # 检查连接问题
    for name, status in api_status.items():
        if status.get("connection_test") and status["connection_test"] != "success" and status["connection_test"] != "not_available":
            recommendations.append({
                "type": "connection_issue",
                "api": name,
                "message": f"{name} API连接有问题: {status['connection_test']}"
            })
    
    return recommendations


@router.post("/comprehensive-search")
async def comprehensive_literature_search(
    request: dict
):
    """
    基于研究意图的综合文献搜索
    集成意图分析、多源搜索和AI解析
    """
    try:
        # 提取参数 - 兼容不同的请求格式
        if "query" in request:
            user_message = request["query"]
            requirements = {}
        else:
            requirements = request.get("requirements", {})
            user_message = request.get("user_message", "")
        max_results = request.get("max_results", 15)
        
        # 使用增强解决方案生成器
        solution_generator = get_enhanced_solution_generator()
        comprehensive_solution = await solution_generator.generate_comprehensive_solution(
            requirements=requirements,
            user_message=user_message
        )
        
        return {
            "status": "success",
            "comprehensive_analysis": comprehensive_solution,
            "search_metadata": {
                "search_type": "comprehensive_intent_based",
                "total_papers": comprehensive_solution.get("literature_insights", {}).get("total_papers", 0),
                "analysis_modules": comprehensive_solution.get("generation_metadata", {}).get("processing_modules", [])
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"综合文献搜索失败: {str(e)}")


@router.post("/intent-analysis")
async def analyze_research_intent(
    request: dict
):
    """
    研究意图分析和关键词生成
    """
    try:
        # 兼容不同的请求格式
        if "query" in request:
            user_message = request["query"]
            requirements = {}
        else:
            requirements = request.get("requirements", {})
            user_message = request.get("user_message", "")

        research_intent_service = get_research_intent_service()
        intent_analysis = await research_intent_service.analyze_research_intent_and_generate_keywords(
            requirements=requirements,
            user_message=user_message
        )
        
        return {
            "status": "success",
            "intent_analysis": intent_analysis,
            "clickable_search_links": research_intent_service._generate_clickable_search_links(intent_analysis)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"研究意图分析失败: {str(e)}")


@router.post("/unified-search")
async def unified_literature_search(
    request: dict
):
    """
    统一多源文献搜索
    """
    try:
        query = request.get("query", "")
        max_results_per_source = request.get("max_results_per_source", 5)
        enabled_sources = request.get("enabled_sources")  # None表示使用所有可用源
        
        if not query:
            raise HTTPException(status_code=400, detail="搜索查询不能为空")
        
        unified_service = await get_unified_literature_api_service()
        search_results = await unified_service.comprehensive_search(
            query=query,
            max_results_per_source=max_results_per_source,
            enabled_sources=enabled_sources
        )
        
        return {
            "status": "success",
            "search_results": search_results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"统一文献搜索失败: {str(e)}")


@router.get("/stats")
async def get_literature_stats(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取文献服务统计信息
    """
    try:
        # 获取统一文献API服务状态
        unified_service = await get_unified_literature_api_service()
        
        # 检查各API的可用性
        available_apis = []
        for api_name, config in unified_service.api_configs.items():
            if config.get("enabled", False):
                available_apis.append(api_name)
        
        return {
            "service_status": "active",
            "available_apis": available_apis,
            "total_api_sources": len(unified_service.api_configs),
            "search_capabilities": {
                "multi_source_search": True,
                "concurrent_search": True,
                "result_deduplication": True,
                "ai_analysis": True
            },
            "supported_platforms": [
                {"name": "PubMed", "description": "生物医学文献数据库"},
                {"name": "OpenAlex", "description": "开放学术文献数据库"},
                {"name": "Semantic Scholar", "description": "AI驱动的学术搜索"},
                {"name": "Google Scholar", "description": "学术搜索引擎"}
            ],
            "service_features": [
                "研究意图分析",
                "多语言关键词生成", 
                "并发多源搜索",
                "智能结果去重",
                "AI风险分析",
                "可点击搜索链接生成"
            ],
            "last_updated": "2024-01-15T10:00:00Z"
        }
        
    except Exception as e:
        return {
            "service_status": "error",
            "error_message": str(e),
            "available_apis": [],
            "total_api_sources": 0
        }
