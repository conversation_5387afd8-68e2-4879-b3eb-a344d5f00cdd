"""
监控中间件
自动收集API请求的性能指标和业务指标
"""

import time
import logging
from typing import Dict, Any, Optional
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from ..monitoring.metrics_collector import metrics_collector

logger = logging.getLogger(__name__)


class MonitoringMiddleware(BaseHTTPMiddleware):
    """监控中间件，自动收集API性能指标"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.excluded_paths = {
            "/docs", "/redoc", "/openapi.json", "/favicon.ico",
            "/static", "/health", "/metrics"  # 排除静态文件和监控端点
        }
    
    async def dispatch(self, request: Request, call_next):
        """处理请求并收集指标"""
        start_time = time.time()
        
        # 获取请求信息
        method = request.method
        path = request.url.path
        user_id = self._extract_user_id(request)
        
        # 检查是否需要跳过监控
        if self._should_skip_monitoring(path):
            return await call_next(request)
        
        # 记录请求开始
        logger.debug(f"开始处理请求: {method} {path}")
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算响应时间
            response_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            # 记录API指标
            metrics_collector.record_api_metric(
                endpoint=self._normalize_endpoint(path),
                method=method,
                response_time=response_time,
                status_code=response.status_code,
                user_id=user_id
            )
            
            # 记录业务指标
            self._record_business_metrics(path, method, response.status_code, response_time)
            
            logger.debug(f"完成请求处理: {method} {path} - {response.status_code} - {response_time:.2f}ms")
            
            return response
            
        except Exception as e:
            # 记录错误
            response_time = (time.time() - start_time) * 1000
            
            metrics_collector.record_api_metric(
                endpoint=self._normalize_endpoint(path),
                method=method,
                response_time=response_time,
                status_code=500,
                user_id=user_id
            )
            
            logger.error(f"请求处理异常: {method} {path} - {str(e)}")
            raise
    
    def _extract_user_id(self, request: Request) -> Optional[str]:
        """从请求中提取用户ID"""
        try:
            # 尝试从JWT token中提取用户ID
            auth_header = request.headers.get("authorization")
            if auth_header and auth_header.startswith("Bearer "):
                # 这里可以解析JWT token获取用户ID
                # 为了简化，我们使用请求IP作为标识
                return request.client.host if request.client else None
            
            # 从查询参数或其他地方获取用户标识
            return request.query_params.get("user_id")
            
        except Exception as e:
            logger.debug(f"提取用户ID失败: {e}")
            return None
    
    def _should_skip_monitoring(self, path: str) -> bool:
        """判断是否需要跳过监控"""
        return any(excluded in path for excluded in self.excluded_paths)
    
    def _normalize_endpoint(self, path: str) -> str:
        """标准化端点路径（处理路径参数）"""
        # 替换动态路径参数为固定标识符
        import re
        
        # 替换UUID/ID参数
        path = re.sub(r'/[0-9a-f-]{36}', '/{id}', path)  # UUID
        path = re.sub(r'/\d+', '/{id}', path)  # 数字ID
        
        # 替换其他常见参数模式
        path = re.sub(r'/[^/]+\.(jpg|png|gif|css|js|ico)$', '/static_file', path)
        
        return path
    
    def _record_business_metrics(self, path: str, method: str, status_code: int, response_time: float):
        """记录业务相关指标"""
        try:
            # 根据不同的API端点记录相应的业务指标
            
            # 文献搜索相关指标
            if "/literature" in path:
                if status_code == 200:
                    metrics_collector.record_business_metric(
                        "literature_search_success_rate", 1.0,
                        {"endpoint": path, "response_time": response_time}
                    )
                else:
                    metrics_collector.record_business_metric(
                        "literature_search_success_rate", 0.0,
                        {"endpoint": path, "status_code": status_code, "response_time": response_time}
                    )
            
            # AI服务相关指标
            if any(ai_path in path for ai_path in ["/conversation", "/ai", "/generate", "/solution"]):
                if status_code == 200 and response_time < 10000:  # 10秒内完成认为是高质量
                    quality_score = min(1.0, max(0.5, 1.0 - (response_time / 20000)))  # 基于响应时间计算质量分
                    metrics_collector.record_business_metric(
                        "ai_service_quality_score", quality_score,
                        {"endpoint": path, "response_time": response_time}
                    )
                else:
                    metrics_collector.record_business_metric(
                        "ai_service_quality_score", 0.3,  # 低质量分数
                        {"endpoint": path, "status_code": status_code, "response_time": response_time}
                    )
            
            # 用户满意度指标（基于响应时间和成功率推断）
            if status_code == 200:
                if response_time < 1000:  # 1秒内
                    satisfaction_score = 1.0
                elif response_time < 3000:  # 3秒内
                    satisfaction_score = 0.8
                elif response_time < 5000:  # 5秒内
                    satisfaction_score = 0.6
                else:
                    satisfaction_score = 0.4
                
                metrics_collector.record_business_metric(
                    "user_satisfaction_score", satisfaction_score,
                    {"endpoint": path, "response_time": response_time}
                )
            else:
                metrics_collector.record_business_metric(
                    "user_satisfaction_score", 0.2,
                    {"endpoint": path, "status_code": status_code}
                )
                
        except Exception as e:
            logger.debug(f"记录业务指标失败: {e}")


class CacheMetricsMiddleware(BaseHTTPMiddleware):
    """缓存指标中间件，记录缓存命中率"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.cache_hit_header = "X-Cache-Status"
    
    async def dispatch(self, request: Request, call_next):
        """处理请求并记录缓存指标"""
        response = await call_next(request)
        
        try:
            # 检查缓存状态
            cache_status = response.headers.get(self.cache_hit_header)
            
            if cache_status:
                if cache_status.lower() == "hit":
                    metrics_collector.record_business_metric(
                        "cache_hit_rate", 1.0,
                        {
                            "endpoint": request.url.path,
                            "cache_type": "application_cache"
                        }
                    )
                elif cache_status.lower() == "miss":
                    metrics_collector.record_business_metric(
                        "cache_hit_rate", 0.0,
                        {
                            "endpoint": request.url.path,
                            "cache_type": "application_cache"
                        }
                    )
            
            return response
            
        except Exception as e:
            logger.debug(f"记录缓存指标失败: {e}")
            return response


class ErrorTrackingMiddleware(BaseHTTPMiddleware):
    """错误跟踪中间件，记录和分析错误模式"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.error_patterns = {}
    
    async def dispatch(self, request: Request, call_next):
        """处理请求并跟踪错误"""
        try:
            response = await call_next(request)
            
            # 记录错误模式
            if response.status_code >= 400:
                self._track_error_pattern(
                    request.url.path,
                    request.method,
                    response.status_code
                )
            
            return response
            
        except Exception as e:
            # 记录异常
            self._track_error_pattern(
                request.url.path,
                request.method,
                500,
                str(e)
            )
            raise
    
    def _track_error_pattern(self, path: str, method: str, status_code: int, error_msg: str = None):
        """跟踪错误模式"""
        try:
            error_key = f"{method}:{path}:{status_code}"
            
            if error_key not in self.error_patterns:
                self.error_patterns[error_key] = {
                    "count": 0,
                    "first_seen": time.time(),
                    "last_seen": time.time(),
                    "examples": []
                }
            
            pattern = self.error_patterns[error_key]
            pattern["count"] += 1
            pattern["last_seen"] = time.time()
            
            if error_msg and len(pattern["examples"]) < 5:
                pattern["examples"].append({
                    "message": error_msg,
                    "timestamp": time.time()
                })
            
            # 记录错误率指标
            metrics_collector.record_business_metric(
                "error_pattern_frequency", 1.0,
                {
                    "error_key": error_key,
                    "status_code": status_code,
                    "path": path,
                    "method": method
                }
            )
            
        except Exception as e:
            logger.debug(f"跟踪错误模式失败: {e}")
    
    def get_error_patterns(self) -> Dict[str, Any]:
        """获取错误模式统计"""
        return self.error_patterns