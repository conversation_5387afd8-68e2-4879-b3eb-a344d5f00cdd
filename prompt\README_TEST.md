# CellForge AI Backend API 测试指南

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install requests colorama
```

### 2. 启动后端服务
确保你的后端服务正在运行：
```bash
# 在backend目录下
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 3. 运行测试
```bash
# 运行所有测试
python test_api.py

# 或者指定特定测试类型
python test_api.py --test intent     # 仅测试研究意图分析
python test_api.py --test search     # 仅测试综合文献搜索
python test_api.py --test unified    # 仅测试统一多源搜索
python test_api.py --test all        # 运行所有测试（默认）

# 指定特定测试案例
python test_api.py --case 0          # 测试第一个案例（单细胞转录组分析）
python test_api.py --case 1          # 测试第二个案例（免疫细胞分化研究）
```

## 📋 测试案例

测试脚本包含4个真实的科研场景：

1. **单细胞转录组分析** - 胚胎发育过程中心脏细胞的基因表达变化研究
2. **免疫细胞分化研究** - T细胞在肿瘤微环境中的分化和功能状态变化
3. **神经发育研究** - 大脑皮层神经元的发育轨迹分析
4. **干细胞重编程** - iPSC重编程过程中的关键转录因子网络研究

## 🧪 测试的API端点

### 1. 研究意图分析 `/literature/intent-analysis`
- **功能**: 解析客户研究需求，提取研究意图和关键词
- **输入**: 客户的研究描述
- **输出**: 
  - 研究可能性列表
  - 英文关键词组合
  - 可点击的文献搜索链接

### 2. 综合文献搜索 `/literature/comprehensive-search`
- **功能**: 基于研究意图进行全面的文献检索和分析
- **输入**: 研究查询和最大结果数
- **输出**:
  - 研究分析结果
  - 按类别分组的文献结果
  - AI生成的风险评估和建议

### 3. 统一多源搜索 `/literature/unified-search`
- **功能**: 并发查询多个文献数据库
- **输入**: 搜索查询、数据源列表、每源最大结果数
- **输出**: 各数据源的搜索结果汇总

## 📊 测试结果解读

### 成功指标
- ✅ **服务器健康检查通过**
- ✅ **API响应状态码200**
- ✅ **返回预期的数据结构**
- ✅ **研究意图成功解析**
- ✅ **文献检索返回结果**

### 可能的错误
- ❌ **连接错误**: 后端服务未启动或端口错误
- ❌ **超时错误**: API响应时间过长（超过设定阈值）
- ❌ **API错误**: 服务内部错误或配置问题
- ❌ **数据错误**: 返回的数据结构不符合预期

### 性能监控
测试脚本会显示每个API调用的响应时间：
- **< 5秒**: 优秀
- **5-15秒**: 良好
- **15-30秒**: 可接受
- **> 30秒**: 需要优化

## 🔧 故障排除

### 1. 连接错误
```
✗ 无法连接到服务器，请确保后端服务已启动
```
**解决方案**: 
- 检查后端服务是否在 http://localhost:8000 运行
- 确认防火墙设置
- 验证端口8000未被占用

### 2. API错误
```
✗ API调用失败，状态码: 500
```
**解决方案**:
- 检查后端服务日志
- 验证数据库连接
- 确认环境变量配置
- 检查外部API密钥设置

### 3. 超时错误
```
✗ 请求超时，API响应时间过长
```
**解决方案**:
- 检查网络连接
- 验证外部API服务状态
- 调整超时设置
- 考虑优化查询复杂度

### 4. 依赖问题
```
ModuleNotFoundError: No module named 'requests'
```
**解决方案**:
```bash
pip install requests colorama
```

## 📈 测试报告示例

```
============================================================
                    测试结果汇总                    
============================================================
总测试数: 12
成功数: 10
失败数: 2
成功率: 83.3%
🎉 大部分测试通过！
```

## 🎨 输出说明

测试脚本使用彩色输出提高可读性：
- 🔵 **蓝色**: 信息提示
- 🟢 **绿色**: 成功状态
- 🔴 **红色**: 错误信息
- 🟡 **黄色**: 数据输出
- 🟣 **紫色**: 重要内容
- ⚪ **白色**: 分隔线和标题

## 💡 高级用法

### 自定义测试数据
你可以修改 `test_api.py` 中的 `TEST_CASES` 列表来添加自己的测试案例：

```python
TEST_CASES.append({
    "name": "你的测试案例名称",
    "query": "你的研究问题描述",
    "expected_keywords": ["关键词1", "关键词2", "关键词3"]
})
```

### 修改API地址
如果你的后端服务运行在不同地址，修改 `BASE_URL` 变量：

```python
BASE_URL = "http://your-server:port"
```

### 调整超时设置
在各个测试函数中修改 `timeout` 参数来调整超时时间。

---

**注意**: 确保在运行测试前已经正确配置了所有必要的环境变量和API密钥。