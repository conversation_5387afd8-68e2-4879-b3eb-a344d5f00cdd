"use client"

import { useAuth } from "@/contexts/auth-context"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function DebugPage() {
  const { user, isLoading, logout } = useAuth()
  const router = useRouter()

  const handleGoToLogin = () => {
    router.push("/login")
  }

  const handleGoToHome = () => {
    router.push("/")
  }

  const handleClearStorage = () => {
    localStorage.clear()
    window.location.reload()
  }

  const tokenInfo = typeof window !== 'undefined' ? {
    token: localStorage.getItem('cellforge_access_token'),
    userInfo: localStorage.getItem('cellforge_user_info')
  } : { token: null, userInfo: null }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold text-center">CellForge AI - 调试页面</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 认证状态 */}
          <Card>
            <CardHeader>
              <CardTitle>认证状态</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <strong>加载状态:</strong> {isLoading ? "加载中..." : "已完成"}
              </div>
              <div>
                <strong>用户状态:</strong> {user ? "已登录" : "未登录"}
              </div>
              {user && (
                <div className="space-y-2">
                  <div><strong>用户ID:</strong> {user.id}</div>
                  <div><strong>用户名:</strong> {user.name}</div>
                  <div><strong>邮箱:</strong> {user.email}</div>
                  <div><strong>角色:</strong> {user.role}</div>
                  <div><strong>状态:</strong> {user.status}</div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 本地存储 */}
          <Card>
            <CardHeader>
              <CardTitle>本地存储</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <strong>Token:</strong>
                <div className="text-xs bg-gray-100 p-2 rounded mt-1 break-all">
                  {tokenInfo.token || "无"}
                </div>
              </div>
              <div>
                <strong>用户信息:</strong>
                <div className="text-xs bg-gray-100 p-2 rounded mt-1 break-all">
                  {tokenInfo.userInfo || "无"}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <Card>
            <CardHeader>
              <CardTitle>操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={handleGoToLogin} className="w-full">
                前往登录页
              </Button>
              <Button onClick={handleGoToHome} className="w-full">
                前往主页
              </Button>
              <Button onClick={handleClearStorage} variant="destructive" className="w-full">
                清除本地存储
              </Button>
              {user && (
                <Button onClick={logout} variant="outline" className="w-full">
                  登出
                </Button>
              )}
            </CardContent>
          </Card>

          {/* 系统信息 */}
          <Card>
            <CardHeader>
              <CardTitle>系统信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div><strong>当前URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</div>
              <div><strong>User Agent:</strong> {typeof window !== 'undefined' ? navigator.userAgent : 'N/A'}</div>
              <div><strong>时间:</strong> {new Date().toLocaleString()}</div>
            </CardContent>
          </Card>
        </div>

        {/* 实时日志 */}
        <Card>
          <CardHeader>
            <CardTitle>实时日志</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-gray-600">
              请打开浏览器开发者工具的Console标签页查看详细日志信息
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
