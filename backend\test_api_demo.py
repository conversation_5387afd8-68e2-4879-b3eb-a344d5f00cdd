#!/usr/bin/env python3
"""
CellForge AI API 测试脚本
基于前端RequirementData框架生成演示数据并测试所有新创建的API
"""

import requests
import json
from datetime import datetime
from typing import Dict, Any, List
import time

# API 基础URL
BASE_URL = "http://localhost:8000/api"

# 基于前端RequirementData结构的演示数据样本
DEMO_REQUIREMENTS = [
    {
        "sample_name": "人类PBMC单细胞RNA测序项目",
        "requirements": {
            # 第1步：基础分类信息
            "speciesType": "人类 (Homo sapiens)",
            "experimentType": "单细胞RNA测序 (scRNA-seq)",
            "researchGoal": "免疫细胞功能分析",
            
            # 第2步：样本详细信息
            "sampleType": "PBMC (外周血单核细胞)",
            "sampleCount": "4-5个样本",
            "sampleCondition": "新鲜样本",
            "sampleProcessing": "机械+酶解离",
            "cellCount": "10,000 cells/sample",
            "cellViability": "> 90%",
            
            # 第3步：项目规划
            "budget": "10-20万",
            "timeline": "3-6个月",
            "urgencyLevel": "较为紧急",
            
            # 第4步：技术细节和高级选项
            "sequencingDepth": "50,000 reads/cell",
            "analysisType": "细胞类型注释",
            "dataAnalysisNeeds": "高级生物信息学分析",
            "specialRequirements": "需要同时进行流式细胞术验证",
            "needsCellSorting": "需要细胞分选",
            
            # 自动推断信息
            "recommendedPlatform": "10x Genomics",
            "estimatedCost": "150,000",
            "riskFactors": ["样本质量控制", "细胞活力维持"],
            
            # 完成度
            "completeness": 95,
            "collectedFields": ["speciesType", "experimentType", "researchGoal", "sampleType", "budget", "timeline"]
        }
    },
    {
        "sample_name": "小鼠脑组织空间转录组学研究",
        "requirements": {
            # 第1步：基础分类信息
            "speciesType": "小鼠 (Mus musculus)",
            "experimentType": "空间转录组学 (Spatial)",
            "researchGoal": "神经发育与功能",
            
            # 第2步：样本详细信息
            "sampleType": "脑组织",
            "sampleCount": "2-3个样本",
            "sampleCondition": "冷冻切片",
            "sampleProcessing": "激光显微切割",
            "cellCount": "5,000 cells/section",
            "cellViability": "80-90%",
            
            # 第3步：项目规划
            "budget": "20-50万",
            "timeline": "6个月以上",
            "urgencyLevel": "正常进度",
            
            # 第4步：技术细节和高级选项
            "sequencingDepth": "100,000 reads/spot",
            "analysisType": "发育轨迹分析",
            "dataAnalysisNeeds": "个性化分析方案",
            "specialRequirements": "需要组织形态学分析配合",
            "needsCellSorting": "不需要细胞分选",
            
            # 自动推断信息
            "recommendedPlatform": "Visium",
            "estimatedCost": "300,000",
            "riskFactors": ["空间分辨率限制", "组织完整性"],
            
            # 完成度
            "completeness": 88,
            "collectedFields": ["speciesType", "experimentType", "researchGoal", "sampleType", "budget"]
        }
    },
    {
        "sample_name": "肿瘤异质性单细胞多组学研究",
        "requirements": {
            # 第1步：基础分类信息
            "speciesType": "人类 (Homo sapiens)",
            "experimentType": "单细胞多组学 (Multiome)",
            "researchGoal": "肿瘤异质性研究",
            
            # 第2步：样本详细信息
            "sampleType": "肿瘤组织",
            "sampleCount": "6-10个样本",
            "sampleCondition": "新鲜样本",
            "sampleProcessing": "酶解离",
            "cellCount": "20,000 cells/sample",
            "cellViability": "> 90%",
            
            # 第3步：项目规划
            "budget": "50万以上",
            "timeline": "6个月以上",
            "urgencyLevel": "正常进度",
            
            # 第4步：技术细节和高级选项
            "sequencingDepth": "25,000 RNA reads + 15,000 ATAC reads/cell",
            "analysisType": "细胞通讯分析",
            "dataAnalysisNeeds": "高级生物信息学分析",
            "specialRequirements": "需要配合免疫组化和流式细胞术",
            "needsCellSorting": "需要细胞分选",
            
            # 自动推断信息
            "recommendedPlatform": "10x Multiome",
            "estimatedCost": "600,000",
            "riskFactors": ["肿瘤细胞异质性", "样本获取难度", "数据复杂度"],
            
            # 完成度
            "completeness": 100,
            "collectedFields": ["speciesType", "experimentType", "researchGoal", "sampleType", "sampleCount", "budget", "timeline", "analysisType"]
        }
    }
]

class APITester:
    """API测试类"""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.test_results = []
    
    def log_test_result(self, test_name: str, success: bool, response_data: Any = None, error: str = None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "timestamp": datetime.now().isoformat(),
            "success": success,
            "response_data": response_data,
            "error": error
        }
        self.test_results.append(result)
        
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{status} {test_name}")
        if error:
            print(f"   错误: {error}")
        if response_data and success:
            # 只显示响应的前200字符以避免输出过长
            response_str = json.dumps(response_data, ensure_ascii=False, indent=2)
            if len(response_str) > 200:
                response_str = response_str[:200] + "..."
            print(f"   响应: {response_str}")
        print()
    
    def test_health_check(self):
        """测试健康检查接口"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.log_test_result("健康检查", True, data)
            else:
                self.log_test_result("健康检查", False, error=f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test_result("健康检查", False, error=str(e))
    
    def test_intent_analysis(self, requirements: Dict[str, Any]):
        """测试意图分析API"""
        try:
            payload = {
                "user_input": f"我需要进行{requirements.get('experimentType', '单细胞测序')}实验",
                "requirements": requirements,
                "user_id": 1,  # 添加必需的user_id字段
                "user_context": {
                    "research_background": "单细胞测序研究",
                    "technical_level": "中级"
                }
            }
            
            response = requests.post(
                f"{self.base_url}/intent-analysis",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                self.log_test_result("意图分析API", True, data)
            else:
                self.log_test_result("意图分析API", False, error=f"HTTP {response.status_code}: {response.text}")
        except Exception as e:
            self.log_test_result("意图分析API", False, error=str(e))
    
    def test_research_directions(self, requirements: Dict[str, Any]):
        """测试研究方向API"""
        try:
            payload = {
                "session_id": "test_session_001",
                "user_id": 1,
                "intent_analysis": {
                    "research_query": f"{requirements.get('researchGoal', '细胞分析')}相关的研究方向",
                    "domain_focus": requirements.get('experimentType', 'scRNA-seq'),
                    "user_profile": {
                        "expertise_level": "intermediate", 
                        "research_interests": [requirements.get('researchGoal', '')]
                    }
                }
            }
            
            response = requests.post(
                f"{self.base_url}/research-directions",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                self.log_test_result("研究方向API", True, data)
            else:
                self.log_test_result("研究方向API", False, error=f"HTTP {response.status_code}: {response.text}")
        except Exception as e:
            self.log_test_result("研究方向API", False, error=str(e))
    
    def test_literature_links(self, requirements: Dict[str, Any]):
        """测试文献链接API"""
        try:
            payload = {
                "research_direction": {
                    "research_topic": requirements.get('researchGoal', '单细胞测序'),
                    "experiment_type": requirements.get('experimentType', 'scRNA-seq'),
                    "species": requirements.get('speciesType', 'human')
                },
                "intent_analysis": {
                    "domain_focus": requirements.get('experimentType', 'scRNA-seq'),
                    "research_goals": [requirements.get('researchGoal', '')]
                },
                "user_profile": {
                    "expertise_level": "intermediate",
                    "preferences": {
                        "primary_databases": ["pubmed", "google_scholar"],
                        "focus_areas": ["methodology", "analysis"]
                    }
                }
            }
            
            response = requests.post(
                f"{self.base_url}/literature-links",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                self.log_test_result("文献链接API", True, data)
            else:
                self.log_test_result("文献链接API", False, error=f"HTTP {response.status_code}: {response.text}")
        except Exception as e:
            self.log_test_result("文献链接API", False, error=str(e))
    
    def test_dynamic_keywords(self, requirements: Dict[str, Any]):
        """测试动态关键词API"""
        try:
            payload = {
                "research_direction": {
                    "main_topic": requirements.get('researchGoal', ''),
                    "experiment_type": requirements.get('experimentType', ''),
                    "species": requirements.get('speciesType', '')
                },
                "intent_analysis": {
                    "research_domain": "single_cell_genomics",
                    "technical_focus": requirements.get('analysisType', ''),
                    "urgency_level": requirements.get('urgencyLevel', 'normal')
                },
                "user_context": {
                    "expertise_level": "intermediate",
                    "optimization_level": "enhanced"
                },
                "session_id": "test_session_001"
            }
            
            response = requests.post(
                f"{self.base_url}/dynamic-keywords",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                self.log_test_result("动态关键词API", True, data)
            else:
                self.log_test_result("动态关键词API", False, error=f"HTTP {response.status_code}: {response.text}")
        except Exception as e:
            self.log_test_result("动态关键词API", False, error=str(e))
    
    def test_comprehensive_solution(self, requirements: Dict[str, Any], sample_name: str):
        """测试综合方案API"""
        try:
            payload = {
                "user_input": f"请为我的{sample_name}生成综合解决方案",
                "requirements": requirements,
                "user_id": 1,
                "user_context": {
                    "framework_template": "standard",
                    "enable_literature_search": False
                }
            }
            
            response = requests.post(
                f"{self.base_url}/comprehensive-solution",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                self.log_test_result(f"综合方案API - {sample_name}", True, data)
            else:
                self.log_test_result(f"综合方案API - {sample_name}", False, error=f"HTTP {response.status_code}: {response.text}")
        except Exception as e:
            self.log_test_result(f"综合方案API - {sample_name}", False, error=str(e))
    
    def run_comprehensive_test(self):
        """运行完整的API测试套件"""
        print("🚀 开始CellForge AI API测试")
        print("=" * 50)
        
        # 1. 健康检查
        self.test_health_check()
        
        # 2. 对每个演示样本进行全面测试
        for i, demo_data in enumerate(DEMO_REQUIREMENTS):
            sample_name = demo_data["sample_name"]
            requirements = demo_data["requirements"]
            
            print(f"\n📋 测试样本 {i+1}: {sample_name}")
            print("-" * 40)
            
            # 测试所有API端点
            self.test_intent_analysis(requirements)
            time.sleep(1)  # 添加延迟避免请求过快
            
            self.test_research_directions(requirements)
            time.sleep(1)
            
            self.test_literature_links(requirements)
            time.sleep(1)
            
            self.test_dynamic_keywords(requirements)
            time.sleep(1)
            
            self.test_comprehensive_solution(requirements, sample_name)
            time.sleep(2)  # 综合方案需要更多时间
    
    def generate_test_report(self):
        """生成测试报告"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result["success"])
        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": total_tests - successful_tests,
                "success_rate": f"{success_rate:.1f}%",
                "test_timestamp": datetime.now().isoformat()
            },
            "detailed_results": self.test_results
        }
        
        print("\n" + "=" * 50)
        print("📊 测试报告")
        print("=" * 50)
        print(f"总测试数: {total_tests}")
        print(f"成功测试: {successful_tests}")
        print(f"失败测试: {total_tests - successful_tests}")
        print(f"成功率: {success_rate:.1f}%")
        
        # 保存详细报告到文件
        with open("api_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细测试报告已保存到: api_test_report.json")
        
        return report

def main():
    """主测试函数"""
    try:
        tester = APITester()
        tester.run_comprehensive_test()
        tester.generate_test_report()
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()