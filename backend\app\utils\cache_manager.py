"""
Cache manager for keyword generation service
Support both Redis and in-memory fallback
"""
import json
import gzip
import hashlib
import asyncio
import logging
from typing import Any, Optional, Dict, List, Union
from datetime import datetime, timedelta

# 尝试导入redis，如果失败则使用内存缓存
try:
    import redis.asyncio as redis
    from redis.asyncio import ConnectionPool
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None
    ConnectionPool = None

try:
    from app.config.keyword_config import get_cache_config, get_keyword_config
    from app.exceptions.keyword_exceptions import CacheError
except ImportError:
    # 提供默认配置
    def get_cache_config():
        return {
            'host': 'localhost',
            'port': 6379,
            'db': 0,
            'default_ttl': 3600,
            'max_memory_items': 1000
        }
    
    def get_keyword_config():
        return {'cache_enabled': True}
    
    class CacheError(Exception):
        pass


logger = logging.getLogger(__name__)


class CacheManager:
    """Redis-based cache manager with compression and error handling"""
    
    def __init__(self):
        self.cache_config = get_cache_config()
        self.keyword_config = get_keyword_config()
        self.redis_client: Optional[redis.Redis] = None
        self.connection_pool: Optional[ConnectionPool] = None
        self._connected = False
    
    async def initialize(self) -> bool:
        """
        Initialize Redis connection
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create connection pool
            self.connection_pool = ConnectionPool(
                host=self.cache_config.REDIS_HOST,
                port=self.cache_config.REDIS_PORT,
                db=self.cache_config.REDIS_DB,
                password=self.cache_config.REDIS_PASSWORD,
                max_connections=self.cache_config.CONNECTION_POOL_SIZE,
                socket_timeout=self.cache_config.SOCKET_TIMEOUT,
                socket_connect_timeout=self.cache_config.CONNECTION_TIMEOUT,
                decode_responses=False  # We'll handle encoding ourselves
            )
            
            # Create Redis client
            self.redis_client = redis.Redis(connection_pool=self.connection_pool)
            
            # Test connection
            await self.redis_client.ping()
            self._connected = True
            
            logger.info("Cache manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize cache manager: {e}")
            self._connected = False
            return False
    
    async def close(self):
        """Close Redis connections"""
        try:
            if self.redis_client:
                await self.redis_client.aclose()
            if self.connection_pool:
                await self.connection_pool.aclose()
            self._connected = False
            logger.info("Cache manager closed")
        except Exception as e:
            logger.error(f"Error closing cache manager: {e}")
    
    def is_available(self) -> bool:
        """Check if cache is available"""
        return self._connected and self.keyword_config.ENABLE_CACHING
    
    async def get(
        self, 
        key: str, 
        default: Any = None,
        decompress: bool = True
    ) -> Any:
        """
        Get value from cache
        
        Args:
            key: Cache key
            default: Default value if key not found
            decompress: Whether to decompress the data
            
        Returns:
            Cached value or default
            
        Raises:
            CacheError: If cache operation fails
        """
        if not self.is_available():
            return default
        
        try:
            cached_data = await self.redis_client.get(key)
            
            if cached_data is None:
                return default
            
            # Decompress if needed
            if decompress and self.cache_config.ENABLE_COMPRESSION:
                try:
                    cached_data = gzip.decompress(cached_data)
                except gzip.BadGzipFile:
                    # Data might not be compressed (backward compatibility)
                    pass
            
            # Deserialize
            try:
                return json.loads(cached_data.decode('utf-8'))
            except (json.JSONDecodeError, UnicodeDecodeError):
                logger.warning(f"Failed to deserialize cached data for key: {key}")
                return default
                
        except Exception as e:
            logger.error(f"Cache get operation failed for key {key}: {e}")
            if isinstance(e, (redis.ConnectionError, redis.TimeoutError)):
                self._connected = False
            raise CacheError(f"Failed to get cache key: {key}", cache_key=key, operation="get")
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        compress: bool = True
    ) -> bool:
        """
        Set value in cache
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            compress: Whether to compress the data
            
        Returns:
            True if successful, False otherwise
            
        Raises:
            CacheError: If cache operation fails
        """
        if not self.is_available():
            return False
        
        try:
            # Serialize
            serialized_data = json.dumps(value, ensure_ascii=False).encode('utf-8')
            
            # Compress if enabled
            if compress and self.cache_config.ENABLE_COMPRESSION:
                serialized_data = gzip.compress(
                    serialized_data, 
                    compresslevel=self.cache_config.COMPRESSION_LEVEL
                )
            
            # Set with TTL
            if ttl:
                await self.redis_client.setex(key, ttl, serialized_data)
            else:
                await self.redis_client.set(key, serialized_data)
            
            return True
            
        except Exception as e:
            logger.error(f"Cache set operation failed for key {key}: {e}")
            if isinstance(e, (redis.ConnectionError, redis.TimeoutError)):
                self._connected = False
            raise CacheError(f"Failed to set cache key: {key}", cache_key=key, operation="set")
    
    async def delete(self, key: str) -> bool:
        """
        Delete key from cache
        
        Args:
            key: Cache key to delete
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_available():
            return False
        
        try:
            result = await self.redis_client.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"Cache delete operation failed for key {key}: {e}")
            if isinstance(e, (redis.ConnectionError, redis.TimeoutError)):
                self._connected = False
            return False
    
    async def exists(self, key: str) -> bool:
        """
        Check if key exists in cache
        
        Args:
            key: Cache key to check
            
        Returns:
            True if key exists, False otherwise
        """
        if not self.is_available():
            return False
        
        try:
            result = await self.redis_client.exists(key)
            return result > 0
        except Exception as e:
            logger.error(f"Cache exists operation failed for key {key}: {e}")
            if isinstance(e, (redis.ConnectionError, redis.TimeoutError)):
                self._connected = False
            return False
    
    async def invalidate_pattern(self, pattern: str) -> int:
        """
        Invalidate keys matching pattern
        
        Args:
            pattern: Pattern to match (Redis glob pattern)
            
        Returns:
            Number of keys deleted
        """
        if not self.is_available():
            return 0
        
        try:
            keys = []
            async for key in self.redis_client.scan_iter(match=pattern):
                keys.append(key)
            
            if keys:
                return await self.redis_client.delete(*keys)
            return 0
            
        except Exception as e:
            logger.error(f"Cache invalidate pattern operation failed for pattern {pattern}: {e}")
            if isinstance(e, (redis.ConnectionError, redis.TimeoutError)):
                self._connected = False
            return 0
    
    def generate_cache_key(self, cache_type: str, **kwargs) -> str:
        """
        Generate standardized cache key
        
        Args:
            cache_type: Type of cache (terminology, query_result, etc.)
            **kwargs: Key components
            
        Returns:
            Generated cache key
        """
        # Get pattern template
        pattern = self.cache_config.KEY_PATTERNS.get(cache_type, f"{self.cache_config.CACHE_PREFIX}{cache_type}:{{hash}}")
        
        # Create hash from kwargs
        key_components = "_".join(f"{k}:{v}" for k, v in sorted(kwargs.items()))
        key_hash = hashlib.md5(key_components.encode('utf-8')).hexdigest()
        
        # Format pattern
        return pattern.format(hash=key_hash, **kwargs)
    
    async def get_or_set(
        self,
        key: str,
        factory_func,
        ttl: Optional[int] = None,
        *args,
        **kwargs
    ) -> Any:
        """
        Get from cache or execute function and cache result
        
        Args:
            key: Cache key
            factory_func: Function to execute if cache miss
            ttl: Time to live in seconds
            *args: Arguments for factory function
            **kwargs: Keyword arguments for factory function
            
        Returns:
            Cached or computed value
        """
        # Try to get from cache first
        cached_value = await self.get(key)
        if cached_value is not None:
            return cached_value
        
        # Execute factory function
        if asyncio.iscoroutinefunction(factory_func):
            computed_value = await factory_func(*args, **kwargs)
        else:
            computed_value = factory_func(*args, **kwargs)
        
        # Cache the result
        await self.set(key, computed_value, ttl)
        
        return computed_value
    
    async def batch_get(self, keys: List[str]) -> Dict[str, Any]:
        """
        Get multiple keys in batch
        
        Args:
            keys: List of cache keys
            
        Returns:
            Dictionary of key-value pairs
        """
        if not self.is_available() or not keys:
            return {}
        
        try:
            # Use Redis pipeline for batch operations
            pipe = self.redis_client.pipeline()
            for key in keys:
                pipe.get(key)
            
            results = await pipe.execute()
            
            # Process results
            batch_result = {}
            for key, data in zip(keys, results):
                if data is not None:
                    try:
                        # Decompress if needed
                        if self.cache_config.ENABLE_COMPRESSION:
                            try:
                                data = gzip.decompress(data)
                            except gzip.BadGzipFile:
                                pass
                        
                        # Deserialize
                        batch_result[key] = json.loads(data.decode('utf-8'))
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        logger.warning(f"Failed to deserialize cached data for key: {key}")
            
            return batch_result
            
        except Exception as e:
            logger.error(f"Batch get operation failed: {e}")
            if isinstance(e, (redis.ConnectionError, redis.TimeoutError)):
                self._connected = False
            return {}
    
    async def batch_set(self, items: Dict[str, Any], ttl: Optional[int] = None) -> int:
        """
        Set multiple keys in batch
        
        Args:
            items: Dictionary of key-value pairs to cache
            ttl: Time to live in seconds
            
        Returns:
            Number of items successfully cached
        """
        if not self.is_available() or not items:
            return 0
        
        try:
            pipe = self.redis_client.pipeline()
            
            for key, value in items.items():
                # Serialize
                serialized_data = json.dumps(value, ensure_ascii=False).encode('utf-8')
                
                # Compress if enabled
                if self.cache_config.ENABLE_COMPRESSION:
                    serialized_data = gzip.compress(
                        serialized_data,
                        compresslevel=self.cache_config.COMPRESSION_LEVEL
                    )
                
                # Add to pipeline
                if ttl:
                    pipe.setex(key, ttl, serialized_data)
                else:
                    pipe.set(key, serialized_data)
            
            results = await pipe.execute()
            return sum(1 for result in results if result)
            
        except Exception as e:
            logger.error(f"Batch set operation failed: {e}")
            if isinstance(e, (redis.ConnectionError, redis.TimeoutError)):
                self._connected = False
            return 0
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics
        
        Returns:
            Dictionary with cache statistics
        """
        if not self.is_available():
            return {"available": False}
        
        try:
            info = await self.redis_client.info()
            
            return {
                "available": True,
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory_human", "0B"),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "instantaneous_ops_per_sec": info.get("instantaneous_ops_per_sec", 0),
                "redis_version": info.get("redis_version", "unknown")
            }
            
        except Exception as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {"available": False, "error": str(e)}


# Global cache manager instance
cache_manager = CacheManager()


async def get_cache_manager() -> CacheManager:
    """Get cache manager instance"""
    if not cache_manager._connected and cache_manager.keyword_config.ENABLE_CACHING:
        await cache_manager.initialize()
    return cache_manager


async def initialize_cache() -> bool:
    """Initialize cache system"""
    return await cache_manager.initialize()


async def cleanup_cache():
    """Cleanup cache resources"""
    await cache_manager.close()