# Core Framework - Essential for basic operation
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database - Using SQLite for development
sqlalchemy[asyncio]==2.0.23

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Communication
httpx==0.25.2
beautifulsoup4==4.12.2

# Basic utilities
structlog==23.2.0

# Testing Framework
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0

# Caching and Redis
redis[hiredis]==5.0.1
aioredis==2.0.1

# Rate limiting and security
slowapi==0.1.9
limits==3.6.0

# Performance monitoring
prometheus-client==0.19.0
psutil==5.9.6

# Data validation
validators==0.22.0

# Fuzzy matching
fuzzywuzzy==0.18.0
python-Levenshtein==0.23.0