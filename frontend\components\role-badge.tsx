import { Badge } from "@/components/ui/badge"
import type { UserRole } from "@/contexts/auth-context"

interface RoleBadgeProps {
  role: UserRole
  className?: string
}

export function RoleBadge({ role, className }: RoleBadgeProps) {
  const roleConfig: Record<UserRole, { label: string; variant: "default" | "outline" | "secondary" }> = {
    super_admin: {
      label: "超级管理员",
      variant: "default",
    },
    sales: {
      label: "销售人员",
      variant: "outline",
    },
    operations: {
      label: "运维人员",
      variant: "outline",
    },
    customer: {
      label: "客户",
      variant: "secondary",
    },
    guest: {
      label: "访客",
      variant: "secondary",
    },
  }

  const config = roleConfig[role] || { label: role, variant: "outline" }

  return (
    <Badge variant={config.variant} className={className}>
      {config.label}
    </Badge>
  )
}
