"""
文献资源数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, JSON
from sqlalchemy.sql import func
from app.core.database import Base


class Literature(Base):
    """文献表"""
    __tablename__ = "literature"

    id = Column(Integer, primary_key=True, index=True)
    
    # 基本信息
    title = Column(String(500), nullable=False, index=True)
    authors = Column(Text)  # JSON格式存储作者列表
    journal = Column(String(200))
    publication_year = Column(Integer, index=True)
    volume = Column(String(50))
    issue = Column(String(50))
    pages = Column(String(50))
    
    # 标识符
    doi = Column(String(200), unique=True, index=True)
    pubmed_id = Column(String(50), unique=True, index=True)
    pmcid = Column(String(50))
    
    # 内容
    abstract = Column(Text)
    keywords = Column(Text)  # JSON格式存储关键词列表
    full_text = Column(Text)  # 如果有全文的话
    
    # 分类和标签
    category = Column(String(100), index=True)  # 主分类：技术方法、应用案例、综述等
    subcategory = Column(String(100))  # 子分类
    technology_tags = Column(Text)  # JSON格式：scRNA-seq, scATAC-seq等
    application_tags = Column(Text)  # JSON格式：免疫学、肿瘤学等
    
    # 质量指标
    impact_factor = Column(Float)
    citation_count = Column(Integer, default=0)
    quality_score = Column(Float, default=0.0)  # 内部质量评分
    
    # 相关性评分（针对单细胞测序）
    relevance_score = Column(Float, default=0.0)
    technical_relevance = Column(Float, default=0.0)  # 技术相关性
    application_relevance = Column(Float, default=0.0)  # 应用相关性
    
    # 内容摘要和提取
    key_findings = Column(Text)  # 关键发现摘要
    methodology_summary = Column(Text)  # 方法学摘要
    business_value = Column(Text)  # 商业价值说明
    technical_details = Column(Text)  # JSON格式存储技术细节
    
    # 使用统计
    view_count = Column(Integer, default=0)
    reference_count = Column(Integer, default=0)  # 被引用次数
    last_referenced = Column(DateTime(timezone=True))
    
    # 状态和元数据
    status = Column(String(50), default="active")  # active, archived, pending_review
    source = Column(String(100))  # 来源：pubmed, manual_input, web_scraping等
    verification_status = Column(String(50), default="unverified")  # verified, unverified, disputed
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    indexed_at = Column(DateTime(timezone=True))  # 索引时间
    
    # 向量化数据（用于语义搜索）
    embedding_vector = Column(Text)  # JSON格式存储向量
    embedding_model = Column(String(100))  # 使用的embedding模型
    
    def __repr__(self):
        return f"<Literature(id={self.id}, title='{self.title[:50]}...', year={self.publication_year})>"


class LiteratureRecommendation(Base):
    """文献推荐记录表"""
    __tablename__ = "literature_recommendations"

    id = Column(Integer, primary_key=True, index=True)
    
    # 关联信息
    literature_id = Column(Integer, index=True)  # 外键关联到Literature
    user_id = Column(Integer, index=True)  # 推荐给的用户
    conversation_id = Column(Integer, index=True)  # 相关对话
    
    # 推荐上下文
    query_context = Column(Text)  # 用户查询上下文
    recommendation_reason = Column(Text)  # 推荐理由
    relevance_score = Column(Float)  # 相关性评分
    
    # 推荐类型
    recommendation_type = Column(String(50))  # authority, methodology, application, comparison
    support_points = Column(Text)  # JSON格式存储支持要点
    
    # 用户反馈
    user_feedback = Column(String(50))  # helpful, not_helpful, irrelevant
    feedback_comment = Column(Text)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    feedback_at = Column(DateTime(timezone=True))
    
    def __repr__(self):
        return f"<LiteratureRecommendation(id={self.id}, literature_id={self.literature_id}, score={self.relevance_score})>"


class LiteratureCollection(Base):
    """文献集合表（用于主题分组）"""
    __tablename__ = "literature_collections"

    id = Column(Integer, primary_key=True, index=True)
    
    # 基本信息
    name = Column(String(200), nullable=False)
    description = Column(Text)
    category = Column(String(100))  # 技术平台、应用领域、方法学等
    
    # 集合内容
    literature_ids = Column(Text)  # JSON格式存储文献ID列表
    literature_count = Column(Integer, default=0)
    
    # 质量指标
    average_impact_factor = Column(Float)
    total_citations = Column(Integer, default=0)
    collection_score = Column(Float, default=0.0)
    
    # 使用统计
    access_count = Column(Integer, default=0)
    last_accessed = Column(DateTime(timezone=True))
    
    # 维护信息
    curator_id = Column(Integer)  # 策展人ID
    is_public = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<LiteratureCollection(id={self.id}, name='{self.name}', count={self.literature_count})>"
