"""
PersonalizedSolutionGenerator - 个性化方案生成器
整合所有AI服务组件，生成完整的个性化单细胞研究解决方案
"""
import asyncio
import json
import logging
import uuid
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.ai_service import AIService
from app.services.intelligent_research_advisor import get_intelligent_research_advisor
from app.services.smart_literature_linker import get_smart_literature_linker
from app.services.dynamic_keyword_generator import get_dynamic_keyword_generator
from app.models.dynamic_recommendation import PersonalizedSolutionFramework

logger = logging.getLogger(__name__)


@dataclass
class PersonalizedRecommendation:
    """个性化推荐项"""
    recommendation_id: str
    category: str  # methodology/resource/collaboration/timeline
    title: str
    description: str
    priority: int  # 1-5, 1为最高优先级
    rationale: str
    estimated_impact: float  # 0-1
    implementation_complexity: str  # low/medium/high
    estimated_cost: Optional[str] = None
    timeline_estimate: Optional[str] = None


@dataclass
class LearningPathStage:
    """学习路径阶段"""
    stage_number: int
    stage_name: str
    learning_objectives: List[str]
    key_concepts: List[str]
    recommended_resources: List[str]
    practical_exercises: List[str]
    duration: str
    success_criteria: List[str]


@dataclass
class LearningPath:
    """学习路径"""
    path_id: str
    path_name: str
    target_audience: str
    total_duration: str
    difficulty_level: str
    prerequisites: List[str]
    stages: List[LearningPathStage]
    final_outcomes: List[str]
    certification_opportunities: List[str]


@dataclass
class ResourceConfiguration:
    """资源配置方案"""
    budget_analysis: Dict[str, Any]
    equipment_requirements: List[Dict[str, Any]]
    software_tools: List[Dict[str, Any]]
    data_resources: List[Dict[str, Any]]
    collaboration_framework: Dict[str, Any]
    timeline_milestones: List[Dict[str, Any]]
    risk_mitigation_strategies: List[Dict[str, Any]]


@dataclass
class RiskAssessment:
    """风险评估"""
    technical_risks: List[Dict[str, Any]]
    resource_risks: List[Dict[str, Any]]
    timeline_risks: List[Dict[str, Any]]
    collaboration_risks: List[Dict[str, Any]]
    overall_risk_level: str  # low/medium/high
    mitigation_priority: List[str]


@dataclass
class SuccessMetrics:
    """成功指标"""
    quantitative_metrics: List[Dict[str, Any]]
    qualitative_metrics: List[Dict[str, Any]]
    milestone_checkpoints: List[Dict[str, Any]]
    performance_indicators: List[str]
    evaluation_timeline: str


@dataclass
class ComprehensiveSolution:
    """综合个性化解决方案"""
    solution_id: str
    session_id: str
    user_id: int
    generation_timestamp: str
    
    # 核心分析结果
    user_profile_summary: Dict[str, Any]
    intent_analysis: Dict[str, Any]
    research_directions: List[Dict[str, Any]]
    
    # 资源整合结果  
    literature_resources: Dict[str, Any]
    keyword_strategies: Dict[str, Any]
    
    # 个性化方案组件
    personalized_recommendations: List[PersonalizedRecommendation]
    learning_paths: List[LearningPath]
    resource_configuration: ResourceConfiguration
    risk_assessment: RiskAssessment
    success_metrics: SuccessMetrics
    
    # 执行指导
    immediate_next_steps: List[str]
    long_term_roadmap: List[Dict[str, Any]]
    decision_support: Dict[str, Any]
    
    # 元数据
    confidence_score: float
    personalization_level: str  # basic/standard/advanced/expert
    solution_complexity: str    # simple/moderate/complex/comprehensive
    estimated_success_rate: float
    generation_metadata: Dict[str, Any]


class PersonalizedSolutionGenerator:
    """个性化方案生成器 - AI驱动的综合研究解决方案生成"""
    
    def __init__(self):
        # 核心AI服务组件
        self.ai_service = AIService()
        self.intelligent_research_advisor = None  # 延迟初始化
        self.smart_literature_linker = None
        self.dynamic_keyword_generator = None
        
        # 方案生成模板
        self.solution_integration_template = """
        你是一位顶级的单细胞生物学研究顾问和项目管理专家。基于用户的完整研究分析，生成一个全面的个性化研究解决方案。

        用户研究意图分析:
        {intent_analysis}

        推荐研究方向:
        {research_directions}

        文献资源策略:
        {literature_strategy}

        关键词优化方案:
        {keyword_strategy}

        用户背景和约束:
        {user_context}

        请生成一个综合的个性化解决方案，输出JSON格式：

        {{
            "solution_framework": {{
                "personalized_recommendations": [
                    {{
                        "category": "methodology/resource/collaboration/timeline",
                        "title": "推荐标题",
                        "description": "详细描述为什么这个推荐对用户有价值",
                        "priority": 1,
                        "rationale": "推荐理由和个性化调整说明",
                        "estimated_impact": 0.85,
                        "implementation_complexity": "medium",
                        "estimated_cost": "成本估算",
                        "timeline_estimate": "时间估算"
                    }}
                ],
                "learning_path_design": {{
                    "primary_path": {{
                        "path_name": "针对用户的主要学习路径",
                        "target_audience": "用户特征描述",
                        "total_duration": "总体时间安排",
                        "difficulty_level": "beginner/intermediate/advanced",
                        "prerequisites": ["前置要求"],
                        "stages": [
                            {{
                                "stage_number": 1,
                                "stage_name": "阶段名称",
                                "learning_objectives": ["学习目标"],
                                "key_concepts": ["核心概念"],
                                "recommended_resources": ["推荐资源"],
                                "practical_exercises": ["实践练习"],
                                "duration": "阶段时长",
                                "success_criteria": ["成功标准"]
                            }}
                        ],
                        "final_outcomes": ["最终成果"],
                        "certification_opportunities": ["认证机会"]
                    }},
                    "alternative_paths": [
                        {{
                            "path_name": "备选路径名称",
                            "适用场景": "什么情况下选择这个路径",
                            "key_differences": "与主路径的区别"
                        }}
                    ]
                }},
                "resource_optimization": {{
                    "budget_analysis": {{
                        "total_estimate": "总体预算估算",
                        "cost_breakdown": {{
                            "equipment": "设备成本",
                            "software": "软件成本", 
                            "data_access": "数据获取成本",
                            "training": "培训成本",
                            "consultation": "咨询成本"
                        }},
                        "cost_optimization_tips": ["成本优化建议"],
                        "funding_opportunities": ["资助机会"]
                    }},
                    "equipment_requirements": [
                        {{
                            "item": "设备名称",
                            "necessity": "essential/recommended/optional",
                            "alternatives": ["替代方案"],
                            "cost_range": "价格范围"
                        }}
                    ],
                    "software_stack": [
                        {{
                            "tool": "软件工具名称",
                            "purpose": "使用目的",
                            "learning_curve": "学习难度",
                            "cost": "费用情况",
                            "alternatives": ["替代工具"]
                        }}
                    ],
                    "collaboration_framework": {{
                        "internal_team_needs": "内部团队需求",
                        "external_partnerships": ["外部合作需求"],
                        "expertise_gaps": ["专业知识缺口"],
                        "collaboration_strategies": ["合作策略"]
                    }}
                }},
                "risk_management": {{
                    "technical_risks": [
                        {{
                            "risk": "技术风险描述",
                            "probability": "high/medium/low",
                            "impact": "high/medium/low", 
                            "mitigation_strategy": "缓解策略",
                            "contingency_plan": "应急计划"
                        }}
                    ],
                    "resource_risks": [
                        {{
                            "risk": "资源风险描述",
                            "mitigation_approach": "缓解方法"
                        }}
                    ],
                    "timeline_risks": [
                        {{
                            "risk": "时间风险描述",
                            "buffer_strategies": "缓冲策略"
                        }}
                    ],
                    "overall_risk_assessment": "综合风险评估"
                }},
                "success_framework": {{
                    "quantitative_metrics": [
                        {{
                            "metric": "定量指标",
                            "target_value": "目标值",
                            "measurement_method": "测量方法"
                        }}
                    ],
                    "qualitative_indicators": [
                        {{
                            "indicator": "定性指标",
                            "assessment_criteria": "评估标准"
                        }}
                    ],
                    "milestone_checkpoints": [
                        {{
                            "milestone": "里程碑",
                            "timeline": "时间点",
                            "success_criteria": "成功标准",
                            "review_process": "评估流程"
                        }}
                    ]
                }},
                "execution_guidance": {{
                    "immediate_actions": [
                        "立即可执行的具体行动步骤"
                    ],
                    "first_month_plan": [
                        "第一个月的详细计划"
                    ],
                    "long_term_roadmap": [
                        {{
                            "phase": "阶段名称",
                            "duration": "持续时间",
                            "key_activities": ["关键活动"],
                            "deliverables": ["交付成果"]
                        }}
                    ],
                    "decision_points": [
                        {{
                            "decision": "需要做出的决策",
                            "timing": "决策时机",
                            "factors_to_consider": ["考虑因素"],
                            "recommended_approach": "推荐方法"
                        }}
                    ]
                }},
                "personalization_notes": {{
                    "adaptation_rationale": "个性化调整的理由",
                    "user_strength_utilization": "如何利用用户优势",
                    "weakness_mitigation": "如何弥补用户弱点",
                    "preference_accommodation": "如何适应用户偏好"
                }}
            }}
        }}

        生成要求：
        1. 所有建议必须针对用户的具体情况和需求
        2. 提供实用的、可执行的具体建议
        3. 考虑用户的经验水平、资源约束和时间安排
        4. 平衡理想方案与现实可行性
        5. 提供明确的成功指标和评估方法
        6. 包含风险评估和应对策略
        7. 确保方案的可持续性和可扩展性
        """

    async def initialize(self):
        """初始化所有服务组件"""
        try:
            # 初始化核心服务
            self.intelligent_research_advisor = get_intelligent_research_advisor()
            self.smart_literature_linker = get_smart_literature_linker()
            self.dynamic_keyword_generator = get_dynamic_keyword_generator()
            
            # 确保动态关键词生成器初始化
            await self.dynamic_keyword_generator.initialize()
            
            logger.info("PersonalizedSolutionGenerator initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize PersonalizedSolutionGenerator: {e}")
            raise

    async def generate_comprehensive_solution(
        self,
        user_input: str,
        requirements: Dict[str, Any],
        user_id: int,
        user_context: Optional[Dict[str, Any]] = None
    ) -> ComprehensiveSolution:
        """
        生成综合个性化解决方案
        
        Args:
            user_input: 用户原始输入
            requirements: 结构化需求
            user_id: 用户ID
            user_context: 用户上下文信息
            
        Returns:
            ComprehensiveSolution: 完整的个性化解决方案
        """
        try:
            if not self.intelligent_research_advisor:
                await self.initialize()
            
            start_time = datetime.now()
            solution_id = self._generate_solution_id()
            
            logger.info(f"开始生成综合个性化方案 - 用户ID: {user_id}, 方案ID: {solution_id}")
            
            # === 第一阶段：核心分析 ===
            
            # 1. 执行用户意图分析
            logger.info("执行用户意图分析...")
            intent_result = await self.intelligent_research_advisor.understand_user_intent(
                user_input=user_input,
                requirements=requirements,
                user_id=user_id,
                user_context=user_context
            )
            
            if "error" in intent_result:
                logger.warning(f"意图分析出现错误: {intent_result['error']}")
                # 使用降级分析继续
                intent_analysis = intent_result.get("fallback_analysis", {})
                session_id = "fallback_session"
            else:
                intent_analysis = intent_result["intent_analysis"]
                session_id = intent_result["session_id"]
            
            # 2. 生成个性化研究方向
            logger.info("生成个性化研究方向...")
            directions_result = await self.intelligent_research_advisor.generate_personalized_directions(
                session_id=session_id,
                user_id=user_id,
                intent_analysis=intent_analysis
            )
            
            if "error" in directions_result:
                logger.warning(f"研究方向生成出现错误: {directions_result['error']}")
                research_directions = directions_result.get("fallback_directions", [])
            else:
                research_directions = directions_result["research_directions"]
            
            # === 第二阶段：资源整合 ===
            
            # 3. 并发生成文献链接和关键词策略
            logger.info("并发生成文献资源和关键词策略...")
            
            literature_task = self._generate_literature_strategy(
                research_directions, intent_analysis, user_context
            )
            
            keywords_task = self._generate_keyword_strategy(
                research_directions, intent_analysis, user_context
            )
            
            # 等待并发任务完成
            literature_strategy, keyword_strategy = await asyncio.gather(
                literature_task, keywords_task, return_exceptions=True
            )
            
            # 处理异常情况
            if isinstance(literature_strategy, Exception):
                logger.error(f"文献策略生成失败: {literature_strategy}")
                literature_strategy = {"error": str(literature_strategy), "links": {}}
            
            if isinstance(keyword_strategy, Exception):
                logger.error(f"关键词策略生成失败: {keyword_strategy}")
                keyword_strategy = {"error": str(keyword_strategy), "keywords": []}
            
            # === 第三阶段：方案整合生成 ===
            
            logger.info("整合生成个性化解决方案...")
            solution_framework = await self._generate_integrated_solution(
                intent_analysis=intent_analysis,
                research_directions=research_directions,
                literature_strategy=literature_strategy,
                keyword_strategy=keyword_strategy,
                user_context=user_context or {}
            )
            
            # === 第四阶段：构建完整方案 ===
            
            logger.info("构建完整解决方案...")
            comprehensive_solution = await self._build_comprehensive_solution(
                solution_id=solution_id,
                session_id=session_id,
                user_id=user_id,
                intent_analysis=intent_analysis,
                research_directions=research_directions,
                literature_strategy=literature_strategy,
                keyword_strategy=keyword_strategy,
                solution_framework=solution_framework,
                user_context=user_context
            )
            
            # === 第五阶段：保存和优化 ===
            
            # 保存方案到数据库
            await self._save_comprehensive_solution(comprehensive_solution)
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            comprehensive_solution.generation_metadata["processing_time_ms"] = processing_time
            
            logger.info(f"综合个性化方案生成完成 - 方案ID: {solution_id}, 用时: {processing_time:.2f}ms")
            
            return comprehensive_solution
            
        except Exception as e:
            logger.error(f"综合方案生成失败: {str(e)}")
            return await self._generate_fallback_solution(user_input, user_id, str(e))

    async def _generate_literature_strategy(
        self,
        research_directions: List[Dict[str, Any]],
        intent_analysis: Dict[str, Any],
        user_context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """生成文献资源策略"""
        try:
            if not research_directions:
                return {"error": "无研究方向", "links": {}}
            
            # 为所有研究方向生成文献链接
            literature_links = await self.smart_literature_linker.generate_multi_direction_links(
                research_directions=research_directions,
                intent_analysis=intent_analysis,
                user_profile=user_context
            )
            
            return {
                "multi_direction_links": literature_links,
                "total_directions": len(research_directions),
                "literature_generation_success": True
            }
            
        except Exception as e:
            logger.error(f"文献策略生成失败: {str(e)}")
            return {"error": str(e), "links": {}}

    async def _generate_keyword_strategy(
        self,
        research_directions: List[Dict[str, Any]],
        intent_analysis: Dict[str, Any],
        user_context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """生成关键词策略"""
        try:
            if not research_directions:
                return {"error": "无研究方向", "keywords": []}
            
            # 为主要研究方向生成动态关键词
            primary_direction = research_directions[0] if research_directions else {}
            
            keyword_result = await self.dynamic_keyword_generator.generate_dynamic_keywords(
                research_direction=primary_direction,
                intent_analysis=intent_analysis,
                user_context=user_context
            )
            
            # 为多个方向优化关键词
            platform_optimizations = await self.dynamic_keyword_generator.optimize_keywords_for_platforms(
                keyword_result=keyword_result,
                target_platforms=["pubmed", "google_scholar", "semantic_scholar"]
            )
            
            return {
                "dynamic_keywords": keyword_result,
                "platform_optimizations": platform_optimizations,
                "keyword_generation_success": True
            }
            
        except Exception as e:
            logger.error(f"关键词策略生成失败: {str(e)}")
            return {"error": str(e), "keywords": []}

    async def _generate_integrated_solution(
        self,
        intent_analysis: Dict[str, Any],
        research_directions: List[Dict[str, Any]],
        literature_strategy: Dict[str, Any],
        keyword_strategy: Dict[str, Any],
        user_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成整合解决方案"""
        try:
            # 构建AI提示
            integration_prompt = self.solution_integration_template.format(
                intent_analysis=json.dumps(intent_analysis, ensure_ascii=False, indent=2),
                research_directions=json.dumps(research_directions, ensure_ascii=False, indent=2),
                literature_strategy=json.dumps({
                    "total_links": literature_strategy.get("multi_direction_links", {}).get("total_links", 0),
                    "coverage": "多方向文献资源覆盖"
                }, ensure_ascii=False, indent=2),
                keyword_strategy=json.dumps({
                    "total_keywords": getattr(keyword_strategy.get("dynamic_keywords"), "total_keywords", 0),
                    "strategy": getattr(keyword_strategy.get("dynamic_keywords"), "generation_strategy", "未知")
                }, ensure_ascii=False, indent=2),
                user_context=json.dumps(user_context, ensure_ascii=False, indent=2)
            )
            
            # 执行AI整合生成
            ai_response = await self.ai_service.generate_response(
                message=integration_prompt,
                context={"conversation_type": "solution_integration"}
            )
            
            # 解析AI响应
            solution_framework = await self._parse_ai_response(ai_response.get("content", ""))
            
            return solution_framework.get("solution_framework", {})
            
        except Exception as e:
            logger.error(f"整合方案生成失败: {str(e)}")
            return await self._generate_fallback_framework()

    async def _build_comprehensive_solution(
        self,
        solution_id: str,
        session_id: str,
        user_id: int,
        intent_analysis: Dict[str, Any],
        research_directions: List[Dict[str, Any]],
        literature_strategy: Dict[str, Any],
        keyword_strategy: Dict[str, Any],
        solution_framework: Dict[str, Any],
        user_context: Optional[Dict[str, Any]]
    ) -> ComprehensiveSolution:
        """构建完整的综合解决方案"""
        try:
            # 1. 构建个性化推荐
            recommendations = self._build_personalized_recommendations(
                solution_framework.get("personalized_recommendations", [])
            )
            
            # 2. 构建学习路径
            learning_paths = self._build_learning_paths(
                solution_framework.get("learning_path_design", {})
            )
            
            # 3. 构建资源配置
            resource_config = self._build_resource_configuration(
                solution_framework.get("resource_optimization", {})
            )
            
            # 4. 构建风险评估
            risk_assessment = self._build_risk_assessment(
                solution_framework.get("risk_management", {})
            )
            
            # 5. 构建成功指标
            success_metrics = self._build_success_metrics(
                solution_framework.get("success_framework", {})
            )
            
            # 6. 提取执行指导
            execution_guidance = solution_framework.get("execution_guidance", {})
            
            # 7. 计算方案指标
            confidence_score = self._calculate_solution_confidence(
                intent_analysis, research_directions, solution_framework
            )
            
            personalization_level = self._determine_personalization_level(
                user_context, solution_framework
            )
            
            solution_complexity = self._assess_solution_complexity(
                research_directions, solution_framework
            )
            
            estimated_success_rate = self._estimate_success_rate(
                confidence_score, risk_assessment, user_context
            )
            
            # 8. 构建完整方案
            comprehensive_solution = ComprehensiveSolution(
                solution_id=solution_id,
                session_id=session_id,
                user_id=user_id,
                generation_timestamp=datetime.now().isoformat(),
                
                # 核心分析结果
                user_profile_summary=self._build_user_profile_summary(user_context, intent_analysis),
                intent_analysis=intent_analysis,
                research_directions=research_directions,
                
                # 资源整合结果
                literature_resources=literature_strategy,
                keyword_strategies=keyword_strategy,
                
                # 个性化方案组件
                personalized_recommendations=recommendations,
                learning_paths=learning_paths,
                resource_configuration=resource_config,
                risk_assessment=risk_assessment,
                success_metrics=success_metrics,
                
                # 执行指导
                immediate_next_steps=execution_guidance.get("immediate_actions", []),
                long_term_roadmap=execution_guidance.get("long_term_roadmap", []),
                decision_support=execution_guidance.get("decision_points", []),
                
                # 元数据
                confidence_score=confidence_score,
                personalization_level=personalization_level,
                solution_complexity=solution_complexity,
                estimated_success_rate=estimated_success_rate,
                generation_metadata={
                    "generation_method": "ai_integrated",
                    "components_used": ["intent_analysis", "research_directions", "literature_links", "dynamic_keywords"],
                    "ai_confidence": solution_framework.get("confidence_score", 0.8),
                    "personalization_notes": solution_framework.get("personalization_notes", {})
                }
            )
            
            return comprehensive_solution
            
        except Exception as e:
            logger.error(f"构建综合方案失败: {str(e)}")
            raise

    def _build_personalized_recommendations(
        self,
        raw_recommendations: List[Dict[str, Any]]
    ) -> List[PersonalizedRecommendation]:
        """构建个性化推荐列表"""
        recommendations = []
        
        for i, rec_data in enumerate(raw_recommendations):
            try:
                recommendation = PersonalizedRecommendation(
                    recommendation_id=f"rec_{i+1}_{datetime.now().strftime('%Y%m%d')}",
                    category=rec_data.get("category", "general"),
                    title=rec_data.get("title", ""),
                    description=rec_data.get("description", ""),
                    priority=int(rec_data.get("priority", 3)),
                    rationale=rec_data.get("rationale", ""),
                    estimated_impact=float(rec_data.get("estimated_impact", 0.5)),
                    implementation_complexity=rec_data.get("implementation_complexity", "medium"),
                    estimated_cost=rec_data.get("estimated_cost"),
                    timeline_estimate=rec_data.get("timeline_estimate")
                )
                recommendations.append(recommendation)
                
            except Exception as e:
                logger.warning(f"构建推荐项失败: {str(e)}")
                continue
        
        # 按优先级排序
        recommendations.sort(key=lambda x: x.priority)
        
        return recommendations

    def _build_learning_paths(
        self,
        learning_path_design: Dict[str, Any]
    ) -> List[LearningPath]:
        """构建学习路径"""
        learning_paths = []
        
        try:
            # 主要学习路径
            primary_path_data = learning_path_design.get("primary_path", {})
            if primary_path_data:
                stages = []
                for stage_data in primary_path_data.get("stages", []):
                    stage = LearningPathStage(
                        stage_number=int(stage_data.get("stage_number", 1)),
                        stage_name=stage_data.get("stage_name", ""),
                        learning_objectives=stage_data.get("learning_objectives", []),
                        key_concepts=stage_data.get("key_concepts", []),
                        recommended_resources=stage_data.get("recommended_resources", []),
                        practical_exercises=stage_data.get("practical_exercises", []),
                        duration=stage_data.get("duration", ""),
                        success_criteria=stage_data.get("success_criteria", [])
                    )
                    stages.append(stage)
                
                primary_path = LearningPath(
                    path_id=f"primary_{datetime.now().strftime('%Y%m%d')}",
                    path_name=primary_path_data.get("path_name", "主要学习路径"),
                    target_audience=primary_path_data.get("target_audience", ""),
                    total_duration=primary_path_data.get("total_duration", ""),
                    difficulty_level=primary_path_data.get("difficulty_level", "intermediate"),
                    prerequisites=primary_path_data.get("prerequisites", []),
                    stages=stages,
                    final_outcomes=primary_path_data.get("final_outcomes", []),
                    certification_opportunities=primary_path_data.get("certification_opportunities", [])
                )
                learning_paths.append(primary_path)
            
            # 备选学习路径
            alternative_paths = learning_path_design.get("alternative_paths", [])
            for i, alt_path_data in enumerate(alternative_paths):
                alt_path = LearningPath(
                    path_id=f"alternative_{i+1}_{datetime.now().strftime('%Y%m%d')}",
                    path_name=alt_path_data.get("path_name", f"备选路径 {i+1}"),
                    target_audience=alt_path_data.get("适用场景", ""),
                    total_duration="根据情况调整",
                    difficulty_level="varied",
                    prerequisites=[],
                    stages=[],  # 备选路径可能只是概念性的
                    final_outcomes=[alt_path_data.get("key_differences", "")],
                    certification_opportunities=[]
                )
                learning_paths.append(alt_path)
                
        except Exception as e:
            logger.error(f"构建学习路径失败: {str(e)}")
        
        return learning_paths

    def _build_resource_configuration(
        self,
        resource_optimization: Dict[str, Any]
    ) -> ResourceConfiguration:
        """构建资源配置"""
        try:
            return ResourceConfiguration(
                budget_analysis=resource_optimization.get("budget_analysis", {}),
                equipment_requirements=resource_optimization.get("equipment_requirements", []),
                software_tools=resource_optimization.get("software_stack", []),
                data_resources=[],  # 可以从其他地方补充
                collaboration_framework=resource_optimization.get("collaboration_framework", {}),
                timeline_milestones=[],  # 可以从执行指导中提取
                risk_mitigation_strategies=[]  # 可以从风险管理中提取
            )
            
        except Exception as e:
            logger.error(f"构建资源配置失败: {str(e)}")
            return ResourceConfiguration(
                budget_analysis={},
                equipment_requirements=[],
                software_tools=[],
                data_resources=[],
                collaboration_framework={},
                timeline_milestones=[],
                risk_mitigation_strategies=[]
            )

    def _build_risk_assessment(
        self,
        risk_management: Dict[str, Any]
    ) -> RiskAssessment:
        """构建风险评估"""
        try:
            # 评估整体风险水平
            technical_risks = risk_management.get("technical_risks", [])
            resource_risks = risk_management.get("resource_risks", [])
            timeline_risks = risk_management.get("timeline_risks", [])
            
            # 简单的风险水平评估
            total_high_risks = sum(1 for risk in technical_risks + resource_risks + timeline_risks 
                                 if isinstance(risk, dict) and risk.get("probability") == "high")
            
            if total_high_risks >= 3:
                overall_risk_level = "high"
            elif total_high_risks >= 1:
                overall_risk_level = "medium"
            else:
                overall_risk_level = "low"
            
            return RiskAssessment(
                technical_risks=technical_risks,
                resource_risks=resource_risks,
                timeline_risks=timeline_risks,
                collaboration_risks=[],  # 可以补充
                overall_risk_level=overall_risk_level,
                mitigation_priority=[risk.get("risk", "") for risk in technical_risks[:3]]
            )
            
        except Exception as e:
            logger.error(f"构建风险评估失败: {str(e)}")
            return RiskAssessment(
                technical_risks=[],
                resource_risks=[],
                timeline_risks=[],
                collaboration_risks=[],
                overall_risk_level="medium",
                mitigation_priority=[]
            )

    def _build_success_metrics(
        self,
        success_framework: Dict[str, Any]
    ) -> SuccessMetrics:
        """构建成功指标"""
        try:
            return SuccessMetrics(
                quantitative_metrics=success_framework.get("quantitative_metrics", []),
                qualitative_metrics=success_framework.get("qualitative_indicators", []),
                milestone_checkpoints=success_framework.get("milestone_checkpoints", []),
                performance_indicators=[
                    metric.get("metric", "") for metric in success_framework.get("quantitative_metrics", [])
                ],
                evaluation_timeline="根据里程碑进行定期评估"
            )
            
        except Exception as e:
            logger.error(f"构建成功指标失败: {str(e)}")
            return SuccessMetrics(
                quantitative_metrics=[],
                qualitative_metrics=[],
                milestone_checkpoints=[],
                performance_indicators=[],
                evaluation_timeline="待定"
            )

    def _build_user_profile_summary(
        self,
        user_context: Optional[Dict[str, Any]],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """构建用户档案摘要"""
        try:
            context_assessment = intent_analysis.get("user_context_assessment", {})
            
            return {
                "experience_level": context_assessment.get("inferred_experience_level", "unknown"),
                "technical_background": context_assessment.get("technical_background", ""),
                "resource_constraints": context_assessment.get("resource_constraints", {}),
                "collaboration_needs": context_assessment.get("collaboration_needs", []),
                "user_preferences": user_context or {},
                "profile_confidence": intent_analysis.get("confidence_score", 0.5)
            }
            
        except Exception as e:
            logger.error(f"构建用户档案摘要失败: {str(e)}")
            return {"experience_level": "unknown", "profile_confidence": 0.0}

    def _calculate_solution_confidence(
        self,
        intent_analysis: Dict[str, Any],
        research_directions: List[Dict[str, Any]],
        solution_framework: Dict[str, Any]
    ) -> float:
        """计算方案置信度"""
        try:
            # 基础置信度
            base_confidence = 0.6
            
            # 意图分析置信度影响
            intent_confidence = intent_analysis.get("confidence_score", 0.5)
            base_confidence += intent_confidence * 0.2
            
            # 研究方向数量和质量影响
            if research_directions:
                direction_quality = sum(d.get("user_suitability_score", 0.5) for d in research_directions) / len(research_directions)
                base_confidence += direction_quality * 0.15
            
            # 方案框架完整性影响
            framework_completeness = len([k for k in ["personalized_recommendations", "learning_path_design", "resource_optimization"] 
                                        if k in solution_framework and solution_framework[k]]) / 3
            base_confidence += framework_completeness * 0.05
            
            return min(base_confidence, 1.0)
            
        except Exception as e:
            logger.error(f"置信度计算失败: {str(e)}")
            return 0.5

    def _determine_personalization_level(
        self,
        user_context: Optional[Dict[str, Any]],
        solution_framework: Dict[str, Any]
    ) -> str:
        """确定个性化水平"""
        try:
            personalization_score = 0
            
            # 用户上下文信息丰富度
            if user_context:
                personalization_score += len(user_context) * 0.1
            
            # 个性化推荐数量
            recommendations = solution_framework.get("personalized_recommendations", [])
            personalization_score += len(recommendations) * 0.05
            
            # 学习路径定制程度
            learning_path = solution_framework.get("learning_path_design", {})
            if learning_path.get("primary_path", {}).get("stages"):
                personalization_score += 0.3
            
            if personalization_score >= 1.0:
                return "expert"
            elif personalization_score >= 0.7:
                return "advanced"
            elif personalization_score >= 0.4:
                return "standard"
            else:
                return "basic"
                
        except Exception as e:
            logger.error(f"个性化水平确定失败: {str(e)}")
            return "standard"

    def _assess_solution_complexity(
        self,
        research_directions: List[Dict[str, Any]],
        solution_framework: Dict[str, Any]
    ) -> str:
        """评估方案复杂度"""
        try:
            complexity_score = 0
            
            # 研究方向数量和复杂度
            complexity_score += len(research_directions) * 0.2
            
            for direction in research_directions:
                if direction.get("technical_requirements", {}).get("complexity") == "high":
                    complexity_score += 0.3
            
            # 资源需求复杂度
            resource_optimization = solution_framework.get("resource_optimization", {})
            equipment_count = len(resource_optimization.get("equipment_requirements", []))
            software_count = len(resource_optimization.get("software_stack", []))
            complexity_score += (equipment_count + software_count) * 0.05
            
            # 风险管理复杂度
            risk_management = solution_framework.get("risk_management", {})
            total_risks = (len(risk_management.get("technical_risks", [])) + 
                          len(risk_management.get("resource_risks", [])) +
                          len(risk_management.get("timeline_risks", [])))
            complexity_score += total_risks * 0.1
            
            if complexity_score >= 2.0:
                return "comprehensive"
            elif complexity_score >= 1.5:
                return "complex"
            elif complexity_score >= 1.0:
                return "moderate"
            else:
                return "simple"
                
        except Exception as e:
            logger.error(f"复杂度评估失败: {str(e)}")
            return "moderate"

    def _estimate_success_rate(
        self,
        confidence_score: float,
        risk_assessment: RiskAssessment,
        user_context: Optional[Dict[str, Any]]
    ) -> float:
        """估算成功率"""
        try:
            # 基于置信度的基础成功率
            base_success_rate = confidence_score * 0.8
            
            # 风险调整
            risk_penalty = {
                "high": 0.3,
                "medium": 0.15,
                "low": 0.05
            }
            
            risk_adjustment = risk_penalty.get(risk_assessment.overall_risk_level, 0.15)
            base_success_rate -= risk_adjustment
            
            # 用户经验调整
            if user_context:
                experience_level = user_context.get("experience_level", "intermediate")
                experience_bonus = {
                    "expert": 0.1,
                    "intermediate": 0.0,
                    "novice": -0.1
                }
                base_success_rate += experience_bonus.get(experience_level, 0.0)
            
            return max(min(base_success_rate, 0.95), 0.1)  # 限制在 10%-95% 之间
            
        except Exception as e:
            logger.error(f"成功率估算失败: {str(e)}")
            return 0.6

    async def _save_comprehensive_solution(
        self,
        comprehensive_solution: ComprehensiveSolution
    ):
        """保存综合方案到数据库"""
        try:
            db = next(get_db())
            
            # 创建方案记录
            solution_record = PersonalizedSolutionFramework(
                solution_id=comprehensive_solution.solution_id,
                user_id=comprehensive_solution.user_id,
                session_id=comprehensive_solution.session_id,
                solution_type="comprehensive",
                personalization_level=comprehensive_solution.personalization_level,
                solution_complexity=comprehensive_solution.solution_complexity,
                confidence_score=comprehensive_solution.confidence_score,
                estimated_success_rate=comprehensive_solution.estimated_success_rate,
                user_profile_summary=comprehensive_solution.user_profile_summary,
                research_directions_summary=json.dumps([
                    {"title": d.get("title", ""), "suitability_score": d.get("user_suitability_score", 0.5)}
                    for d in comprehensive_solution.research_directions
                ], ensure_ascii=False),
                personalized_recommendations=json.dumps([
                    asdict(rec) for rec in comprehensive_solution.personalized_recommendations
                ], ensure_ascii=False),
                resource_configuration=json.dumps(asdict(comprehensive_solution.resource_configuration), ensure_ascii=False),
                risk_assessment_summary=json.dumps(asdict(comprehensive_solution.risk_assessment), ensure_ascii=False),
                success_metrics=json.dumps(asdict(comprehensive_solution.success_metrics), ensure_ascii=False),
                implementation_roadmap=json.dumps({
                    "immediate_steps": comprehensive_solution.immediate_next_steps,
                    "long_term": comprehensive_solution.long_term_roadmap
                }, ensure_ascii=False),
                generation_metadata=comprehensive_solution.generation_metadata
            )
            
            db.add(solution_record)
            db.commit()
            db.refresh(solution_record)
            
            logger.info(f"综合方案已保存到数据库 - ID: {solution_record.id}")
            
        except Exception as e:
            logger.error(f"保存综合方案失败: {str(e)}")
            # 不影响主流程

    async def _generate_fallback_solution(
        self,
        user_input: str,
        user_id: int,
        error_msg: str
    ) -> ComprehensiveSolution:
        """生成降级解决方案"""
        try:
            fallback_solution = ComprehensiveSolution(
                solution_id=self._generate_solution_id(),
                session_id="fallback",
                user_id=user_id,
                generation_timestamp=datetime.now().isoformat(),
                
                user_profile_summary={"experience_level": "unknown"},
                intent_analysis={"error": "分析失败", "fallback_mode": True},
                research_directions=[{
                    "title": "基础单细胞RNA测序分析",
                    "description": "标准的单细胞转录组分析流程"
                }],
                
                literature_resources={"error": "文献资源获取失败"},
                keyword_strategies={"error": "关键词策略生成失败"},
                
                personalized_recommendations=[
                    PersonalizedRecommendation(
                        recommendation_id="fallback_1",
                        category="methodology",
                        title="从基础教程开始",
                        description="建议从单细胞生物学基础概念开始学习",
                        priority=1,
                        rationale="降级建议 - 系统无法生成个性化方案",
                        estimated_impact=0.6,
                        implementation_complexity="low"
                    )
                ],
                
                learning_paths=[],
                resource_configuration=ResourceConfiguration(
                    budget_analysis={}, equipment_requirements=[], software_tools=[],
                    data_resources=[], collaboration_framework={}, timeline_milestones=[],
                    risk_mitigation_strategies=[]
                ),
                risk_assessment=RiskAssessment(
                    technical_risks=[], resource_risks=[], timeline_risks=[],
                    collaboration_risks=[], overall_risk_level="unknown", mitigation_priority=[]
                ),
                success_metrics=SuccessMetrics(
                    quantitative_metrics=[], qualitative_metrics=[], milestone_checkpoints=[],
                    performance_indicators=[], evaluation_timeline=""
                ),
                
                immediate_next_steps=["联系技术支持", "手动制定研究计划"],
                long_term_roadmap=[],
                decision_support=[],
                
                confidence_score=0.2,
                personalization_level="basic",
                solution_complexity="simple",
                estimated_success_rate=0.3,
                generation_metadata={
                    "generation_method": "fallback",
                    "error": error_msg,
                    "fallback_reason": "系统组件故障"
                }
            )
            
            return fallback_solution
            
        except Exception as e:
            logger.error(f"生成降级方案失败: {str(e)}")
            raise

    async def _generate_fallback_framework(self) -> Dict[str, Any]:
        """生成降级方案框架"""
        return {
            "personalized_recommendations": [
                {
                    "category": "methodology",
                    "title": "基础方法学习",
                    "description": "从单细胞基础概念开始",
                    "priority": 1,
                    "rationale": "降级建议",
                    "estimated_impact": 0.5,
                    "implementation_complexity": "low"
                }
            ],
            "learning_path_design": {
                "primary_path": {
                    "path_name": "基础学习路径",
                    "stages": [
                        {
                            "stage_number": 1,
                            "stage_name": "基础概念",
                            "learning_objectives": ["理解单细胞生物学基础"],
                            "key_concepts": ["单细胞", "转录组"],
                            "recommended_resources": ["基础教程"],
                            "practical_exercises": ["理论学习"],
                            "duration": "1-2周",
                            "success_criteria": ["完成基础概念学习"]
                        }
                    ]
                }
            },
            "resource_optimization": {
                "budget_analysis": {"total_estimate": "待确定"},
                "equipment_requirements": [],
                "software_stack": []
            },
            "risk_management": {
                "technical_risks": [],
                "overall_risk_assessment": "未知"
            },
            "success_framework": {
                "quantitative_metrics": [],
                "qualitative_indicators": []
            },
            "execution_guidance": {
                "immediate_actions": ["寻求专业帮助"],
                "long_term_roadmap": []
            }
        }

    async def _parse_ai_response(self, content: str) -> Dict[str, Any]:
        """解析AI响应内容"""
        try:
            # 尝试直接解析JSON
            if content.strip().startswith('{'):
                return json.loads(content)
            
            # 提取JSON块
            import re
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            
            # 查找大括号内的内容
            json_match = re.search(r'(\{.*\})', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            
            # 如果都失败，返回空结构
            return {}
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return {}

    def _generate_solution_id(self) -> str:
        """生成方案ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_suffix = str(uuid.uuid4())[:8]
        return f"solution_{timestamp}_{unique_suffix}"


# 全局服务实例
personalized_solution_generator = PersonalizedSolutionGenerator()


def get_personalized_solution_generator() -> PersonalizedSolutionGenerator:
    """获取个性化方案生成器实例"""
    return personalized_solution_generator