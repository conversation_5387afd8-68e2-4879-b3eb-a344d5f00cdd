"""
关键词搜索服务
用于从网页内容中提取关键词并通过API搜索相关信息
"""
import re
import json
import asyncio
import aiohttp
from typing import List, Dict, Any, Optional
from urllib.parse import quote_plus
import logging

logger = logging.getLogger(__name__)

class KeywordSearchService:
    """关键词搜索服务"""

    def __init__(self):
        self.academic_keywords = {
            'positions': [
                'professor', 'assistant professor', 'associate professor', 'principal investigator',
                'postdoc', 'postdoctoral', 'research scientist', 'research fellow', 'phd student',
                'graduate student', 'lab director', 'department head', 'chair'
            ],
            'institutions': [
                'university', 'institute', 'college', 'hospital', 'medical center',
                'research center', 'laboratory', 'school of medicine', 'department'
            ],
            'research_areas': [
                'single cell', 'genomics', 'transcriptomics', 'proteomics', 'bioinformatics',
                'computational biology', 'systems biology', 'cancer research', 'immunology',
                'neuroscience', 'developmental biology', 'stem cell', 'cell biology'
            ],
            'technologies': [
                '10x genomics', 'scrna-seq', 'scatac-seq', 'cite-seq', 'smart-seq',
                'drop-seq', 'indrops', 'cel-seq', 'mars-seq', 'plate-seq'
            ]
        }

        # 搜索API配置（可以配置多个搜索源）
        self.search_apis = {
            'google_scholar': {
                'enabled': False,  # 需要API密钥
                'base_url': 'https://serpapi.com/search.json',
                'params': {'engine': 'google_scholar'}
            },
            'pubmed': {
                'enabled': True,
                'base_url': 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi',
                'params': {'db': 'pubmed', 'retmode': 'json'}
            },
            'crossref': {
                'enabled': True,
                'base_url': 'https://api.crossref.org/works',
                'params': {'rows': 10}
            }
        }

    def extract_keywords(self, text: str, source_type: str = 'web') -> Dict[str, List[str]]:
        """从文本中提取关键词"""
        text_lower = text.lower()

        extracted = {
            'persons': [],
            'institutions': [],
            'research_areas': [],
            'technologies': [],
            'publications': [],
            'grants': [],
            'collaborations': []
        }

        # 提取人名（简单的启发式方法）
        person_patterns = [
            r'\b([A-Z][a-z]+ [A-Z][a-z]+)\b',  # 首字母大写的姓名
            r'\bDr\.?\s+([A-Z][a-z]+ [A-Z][a-z]+)\b',  # Dr. 开头的姓名
            r'\bProf\.?\s+([A-Z][a-z]+ [A-Z][a-z]+)\b'  # Prof. 开头的姓名
        ]

        for pattern in person_patterns:
            matches = re.findall(pattern, text)
            extracted['persons'].extend(matches)

        # 提取机构名称
        for keyword in self.academic_keywords['institutions']:
            pattern = rf'\b([A-Z][^.]*{keyword}[^.]*)\b'
            matches = re.findall(pattern, text, re.IGNORECASE)
            extracted['institutions'].extend(matches)

        # 提取研究领域
        for area in self.academic_keywords['research_areas']:
            if area.lower() in text_lower:
                extracted['research_areas'].append(area)

        # 提取技术关键词
        for tech in self.academic_keywords['technologies']:
            if tech.lower() in text_lower:
                extracted['technologies'].append(tech)

        # 提取发表信息
        pub_patterns = [
            r'published in ([^,\n.]+)',
            r'appeared in ([^,\n.]+)',
            r'journal of ([^,\n.]+)',
            r'nature ([^,\n.]+)',
            r'science ([^,\n.]+)',
            r'cell ([^,\n.]+)'
        ]

        for pattern in pub_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            extracted['publications'].extend(matches)

        # 提取资助信息
        grant_patterns = [
            r'funded by ([^,\n.]+)',
            r'supported by ([^,\n.]+)',
            r'grant from ([^,\n.]+)',
            r'NIH ([^,\n.]+)',
            r'NSF ([^,\n.]+)'
        ]

        for pattern in grant_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            extracted['grants'].extend(matches)

        # 去重并限制数量
        for key in extracted:
            extracted[key] = list(set(extracted[key]))[:10]

        return extracted

    async def search_academic_info(self, keywords: Dict[str, List[str]]) -> Dict[str, Any]:
        """使用关键词搜索学术信息"""
        search_results = {
            'publications': [],
            'collaborations': [],
            'citations': [],
            'research_impact': {},
            'network_analysis': {}
        }

        try:
            # 构建搜索查询
            search_queries = self._build_search_queries(keywords)

            # 并行搜索多个数据源
            tasks = []
            for query in search_queries[:3]:  # 限制查询数量
                if self.search_apis['pubmed']['enabled']:
                    tasks.append(self._search_pubmed(query))
                if self.search_apis['crossref']['enabled']:
                    tasks.append(self._search_crossref(query))

            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # 处理搜索结果
                for result in results:
                    if isinstance(result, dict) and not isinstance(result, Exception):
                        search_results = self._merge_search_results(search_results, result)

            # 分析搜索结果
            search_results['research_impact'] = self._analyze_research_impact(search_results)
            search_results['network_analysis'] = self._analyze_collaboration_network(search_results)

        except Exception as e:
            logger.error(f"学术信息搜索失败: {e}")
            search_results['error'] = str(e)

        return search_results

    def _build_search_queries(self, keywords: Dict[str, List[str]]) -> List[str]:
        """构建搜索查询"""
        queries = []

        # 基于人名和机构的查询
        for person in keywords.get('persons', [])[:3]:
            for institution in keywords.get('institutions', [])[:2]:
                query = f'"{person}" "{institution}"'
                queries.append(query)

        # 基于研究领域和技术的查询
        for area in keywords.get('research_areas', [])[:3]:
            for tech in keywords.get('technologies', [])[:2]:
                query = f'"{area}" "{tech}"'
                queries.append(query)

        # 基于人名和研究领域的查询
        for person in keywords.get('persons', [])[:2]:
            for area in keywords.get('research_areas', [])[:2]:
                query = f'"{person}" "{area}"'
                queries.append(query)

        return queries[:5]  # 限制查询数量

    async def _search_pubmed(self, query: str) -> Dict[str, Any]:
        """搜索PubMed数据库"""
        try:
            url = self.search_apis['pubmed']['base_url']
            params = {
                **self.search_apis['pubmed']['params'],
                'term': query,
                'retmax': 10
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        count = data.get('esearchresult', {}).get('count', 0)
                        # 确保count是整数类型
                        if isinstance(count, str):
                            count = int(count) if count.isdigit() else 0

                        return {
                            'source': 'pubmed',
                            'query': query,
                            'results': data.get('esearchresult', {}),
                            'count': count
                        }
        except Exception as e:
            logger.error(f"PubMed搜索失败: {e}")

        return {'source': 'pubmed', 'error': 'search_failed'}

    async def _search_crossref(self, query: str) -> Dict[str, Any]:
        """搜索Crossref数据库"""
        try:
            url = self.search_apis['crossref']['base_url']
            params = {
                **self.search_apis['crossref']['params'],
                'query': query
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'source': 'crossref',
                            'query': query,
                            'results': data.get('message', {}),
                            'count': data.get('message', {}).get('total-results', 0)
                        }
        except Exception as e:
            logger.error(f"Crossref搜索失败: {e}")

        return {'source': 'crossref', 'error': 'search_failed'}

    def _merge_search_results(self, main_results: Dict[str, Any],
                            new_result: Dict[str, Any]) -> Dict[str, Any]:
        """合并搜索结果"""
        if 'error' in new_result:
            return main_results

        source = new_result.get('source', 'unknown')
        count = new_result.get('count', 0)

        # 添加发表信息
        if count > 0:
            main_results['publications'].append({
                'source': source,
                'query': new_result.get('query', ''),
                'count': count,
                'relevance': min(count / 100, 1.0)  # 归一化相关性分数
            })

        return main_results

    def _analyze_research_impact(self, search_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析研究影响力"""
        impact = {
            'publication_count': 0,
            'research_visibility': 'low',
            'field_relevance': 'low',
            'collaboration_index': 0.0
        }

        # 计算总发表数量
        total_pubs = sum(pub.get('count', 0) for pub in search_results.get('publications', []))
        impact['publication_count'] = total_pubs

        # 评估研究可见度
        if total_pubs > 50:
            impact['research_visibility'] = 'high'
        elif total_pubs > 10:
            impact['research_visibility'] = 'medium'

        # 评估领域相关性
        single_cell_pubs = sum(
            pub.get('count', 0) for pub in search_results.get('publications', [])
            if 'single cell' in pub.get('query', '').lower()
        )

        if single_cell_pubs > 10:
            impact['field_relevance'] = 'high'
        elif single_cell_pubs > 3:
            impact['field_relevance'] = 'medium'

        return impact

    def _analyze_collaboration_network(self, search_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析合作网络"""
        network = {
            'collaboration_score': 0.0,
            'network_size': 'small',
            'international_collaboration': False,
            'industry_collaboration': False
        }

        # 基于搜索结果推断合作网络
        pub_count = len(search_results.get('publications', []))
        if pub_count > 5:
            network['collaboration_score'] = min(pub_count / 20, 1.0)

        if pub_count > 10:
            network['network_size'] = 'large'
        elif pub_count > 5:
            network['network_size'] = 'medium'

        return network

    def enhance_profile_with_search(self, profile_data: Dict[str, Any],
                                  search_results: Dict[str, Any]) -> Dict[str, Any]:
        """使用搜索结果增强客户画像"""
        enhanced_profile = profile_data.copy()

        # 增强研究画像
        if 'research_profile' not in enhanced_profile:
            enhanced_profile['research_profile'] = {}

        research_impact = search_results.get('research_impact', {})
        enhanced_profile['research_profile'].update({
            'publication_count': research_impact.get('publication_count', 0),
            'research_visibility': research_impact.get('research_visibility', 'unknown'),
            'field_relevance': research_impact.get('field_relevance', 'unknown')
        })

        # 增强技术画像
        if 'technical_profile' not in enhanced_profile:
            enhanced_profile['technical_profile'] = {}

        enhanced_profile['technical_profile'].update({
            'collaboration_index': search_results.get('network_analysis', {}).get('collaboration_score', 0.0),
            'research_network_size': search_results.get('network_analysis', {}).get('network_size', 'unknown')
        })

        # 增强商业画像
        if 'business_profile' not in enhanced_profile:
            enhanced_profile['business_profile'] = {}

        # 基于研究影响力推断商业价值
        pub_count = research_impact.get('publication_count', 0)
        if pub_count > 20:
            business_value = 'high'
        elif pub_count > 5:
            business_value = 'medium'
        else:
            business_value = 'low'

        enhanced_profile['business_profile'].update({
            'research_based_value': business_value,
            'academic_influence': research_impact.get('research_visibility', 'unknown')
        })

        return enhanced_profile
