"""
Metrics and monitoring utilities for keyword generation service
"""
import time
import asyncio
import logging
import psutil
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
from functools import wraps

from prometheus_client import Counter, Histogram, Gauge, Summary, generate_latest
from app.config.keyword_config import get_metrics_config, get_keyword_config
from app.utils.cache_manager import get_cache_manager


logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Performance metric data structure"""
    operation: str
    duration: float
    timestamp: datetime
    success: bool
    error_type: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class QualityMetric:
    """Quality metric data structure"""
    query: str
    keywords_count: int
    confidence_score: float
    domains_covered: int
    processing_time: float
    timestamp: datetime
    user_feedback: Optional[float] = None


class MetricsCollector:
    """Centralized metrics collection and aggregation"""
    
    def __init__(self):
        self.config = get_metrics_config()
        self.keyword_config = get_keyword_config()
        
        # Prometheus metrics
        self._init_prometheus_metrics()
        
        # In-memory metric storage
        self.performance_metrics = deque(maxlen=10000)
        self.quality_metrics = deque(maxlen=5000)
        self.error_counts = defaultdict(int)
        self.cache_stats = {}
        
        # Background task for metric aggregation
        self._aggregation_task = None
        self._running = False
    
    def _init_prometheus_metrics(self):
        """Initialize Prometheus metrics"""
        # Request metrics
        self.request_total = Counter(
            'keyword_requests_total',
            'Total number of keyword generation requests',
            ['endpoint', 'status']
        )
        
        self.request_duration = Histogram(
            'keyword_request_duration_seconds',
            'Request duration in seconds',
            ['endpoint', 'method']
        )
        
        # Processing metrics
        self.keyword_generation_duration = Histogram(
            'keyword_generation_duration_seconds',
            'Keyword generation processing time',
            ['stage']
        )
        
        self.keywords_generated = Summary(
            'keywords_generated_count',
            'Number of keywords generated per request'
        )
        
        self.confidence_score = Summary(
            'keyword_confidence_score',
            'Confidence score of generated keywords'
        )
        
        # Quality metrics
        self.domain_coverage = Histogram(
            'keyword_domain_coverage',
            'Number of domains covered by keywords',
            buckets=[1, 2, 3, 4, 5, 6, 10]
        )
        
        # Cache metrics
        self.cache_hits = Counter(
            'keyword_cache_hits_total',
            'Total cache hits',
            ['cache_type']
        )
        
        self.cache_misses = Counter(
            'keyword_cache_misses_total', 
            'Total cache misses',
            ['cache_type']
        )
        
        # Error metrics
        self.errors_total = Counter(
            'keyword_errors_total',
            'Total number of errors',
            ['error_type', 'operation']
        )
        
        # System metrics
        self.active_requests = Gauge(
            'keyword_active_requests',
            'Number of active requests'
        )
        
        self.memory_usage = Gauge(
            'keyword_memory_usage_bytes',
            'Memory usage in bytes'
        )
        
        self.cpu_usage = Gauge(
            'keyword_cpu_usage_percent',
            'CPU usage percentage'
        )
    
    async def start(self):
        """Start metrics collection"""
        if self._running:
            return
        
        self._running = True
        self._aggregation_task = asyncio.create_task(self._aggregate_metrics_loop())
        logger.info("Metrics collector started")
    
    async def stop(self):
        """Stop metrics collection"""
        self._running = False
        if self._aggregation_task:
            self._aggregation_task.cancel()
            try:
                await self._aggregation_task
            except asyncio.CancelledError:
                pass
        logger.info("Metrics collector stopped")
    
    def record_request(self, endpoint: str, status: str, duration: float):
        """Record API request metrics"""
        if not self.config.TRACK_RESPONSE_TIMES:
            return
        
        self.request_total.labels(endpoint=endpoint, status=status).inc()
        self.request_duration.labels(endpoint=endpoint, method='POST').observe(duration)
    
    def record_performance(
        self,
        operation: str,
        duration: float,
        success: bool = True,
        error_type: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Record performance metrics"""
        metric = PerformanceMetric(
            operation=operation,
            duration=duration,
            timestamp=datetime.now(),
            success=success,
            error_type=error_type,
            metadata=metadata or {}
        )
        
        self.performance_metrics.append(metric)
        
        # Update Prometheus metrics
        self.keyword_generation_duration.labels(stage=operation).observe(duration)
        
        if not success and error_type:
            self.errors_total.labels(error_type=error_type, operation=operation).inc()
    
    def record_quality(
        self,
        query: str,
        keywords_count: int,
        confidence_score: float,
        domains_covered: int,
        processing_time: float,
        user_feedback: Optional[float] = None
    ):
        """Record quality metrics"""
        if not self.config.TRACK_KEYWORD_QUALITY:
            return
        
        metric = QualityMetric(
            query=query,
            keywords_count=keywords_count,
            confidence_score=confidence_score,
            domains_covered=domains_covered,
            processing_time=processing_time,
            timestamp=datetime.now(),
            user_feedback=user_feedback
        )
        
        self.quality_metrics.append(metric)
        
        # Update Prometheus metrics
        self.keywords_generated.observe(keywords_count)
        self.confidence_score.observe(confidence_score)
        self.domain_coverage.observe(domains_covered)
    
    def record_cache_hit(self, cache_type: str):
        """Record cache hit"""
        if self.config.TRACK_CACHE_HIT_RATES:
            self.cache_hits.labels(cache_type=cache_type).inc()
    
    def record_cache_miss(self, cache_type: str):
        """Record cache miss"""
        if self.config.TRACK_CACHE_HIT_RATES:
            self.cache_misses.labels(cache_type=cache_type).inc()
    
    def record_error(self, error_type: str, operation: str):
        """Record error occurrence"""
        if self.config.TRACK_ERROR_RATES:
            self.error_counts[f"{error_type}:{operation}"] += 1
            self.errors_total.labels(error_type=error_type, operation=operation).inc()
    
    def set_active_requests(self, count: int):
        """Set current active request count"""
        self.active_requests.set(count)
    
    async def _aggregate_metrics_loop(self):
        """Background task for metrics aggregation"""
        while self._running:
            try:
                await self._aggregate_metrics()
                await asyncio.sleep(self.config.METRICS_FLUSH_INTERVAL)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in metrics aggregation: {e}")
                await asyncio.sleep(5)  # Wait before retrying
    
    async def _aggregate_metrics(self):
        """Aggregate and process metrics"""
        # Update system metrics
        process = psutil.Process()
        self.memory_usage.set(process.memory_info().rss)
        self.cpu_usage.set(process.cpu_percent())
        
        # Update cache statistics
        try:
            cache_manager = await get_cache_manager()
            if cache_manager.is_available():
                self.cache_stats = await cache_manager.get_stats()
        except Exception as e:
            logger.warning(f"Failed to get cache stats: {e}")
        
        # Clean old metrics
        cutoff_time = datetime.now() - timedelta(days=self.config.METRICS_RETENTION_DAYS)
        
        # Clean performance metrics
        while (self.performance_metrics and 
               self.performance_metrics[0].timestamp < cutoff_time):
            self.performance_metrics.popleft()
        
        # Clean quality metrics
        while (self.quality_metrics and 
               self.quality_metrics[0].timestamp < cutoff_time):
            self.quality_metrics.popleft()
    
    def get_metrics_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get metrics summary for the last N hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # Filter recent metrics
        recent_performance = [
            m for m in self.performance_metrics
            if m.timestamp >= cutoff_time
        ]
        
        recent_quality = [
            m for m in self.quality_metrics
            if m.timestamp >= cutoff_time
        ]
        
        # Calculate performance statistics
        performance_stats = self._calculate_performance_stats(recent_performance)
        quality_stats = self._calculate_quality_stats(recent_quality)
        
        return {
            "time_range_hours": hours,
            "performance": performance_stats,
            "quality": quality_stats,
            "cache": self.cache_stats,
            "system": {
                "memory_usage_mb": psutil.Process().memory_info().rss / 1024 / 1024,
                "cpu_usage_percent": psutil.Process().cpu_percent()
            },
            "errors": dict(self.error_counts)
        }
    
    def _calculate_performance_stats(self, metrics: List[PerformanceMetric]) -> Dict[str, Any]:
        """Calculate performance statistics"""
        if not metrics:
            return {}
        
        # Group by operation
        by_operation = defaultdict(list)
        for metric in metrics:
            by_operation[metric.operation].append(metric)
        
        stats = {}
        for operation, op_metrics in by_operation.items():
            durations = [m.duration for m in op_metrics]
            success_count = sum(1 for m in op_metrics if m.success)
            
            stats[operation] = {
                "total_requests": len(op_metrics),
                "success_rate": success_count / len(op_metrics),
                "avg_duration": sum(durations) / len(durations),
                "min_duration": min(durations),
                "max_duration": max(durations),
                "p95_duration": self._percentile(durations, 95),
                "p99_duration": self._percentile(durations, 99)
            }
        
        return stats
    
    def _calculate_quality_stats(self, metrics: List[QualityMetric]) -> Dict[str, Any]:
        """Calculate quality statistics"""
        if not metrics:
            return {}
        
        keyword_counts = [m.keywords_count for m in metrics]
        confidence_scores = [m.confidence_score for m in metrics]
        domain_counts = [m.domains_covered for m in metrics]
        processing_times = [m.processing_time for m in metrics]
        
        return {
            "total_queries": len(metrics),
            "avg_keywords_per_query": sum(keyword_counts) / len(keyword_counts),
            "avg_confidence_score": sum(confidence_scores) / len(confidence_scores),
            "avg_domains_covered": sum(domain_counts) / len(domain_counts),
            "avg_processing_time": sum(processing_times) / len(processing_times),
            "confidence_distribution": {
                "high_confidence": sum(1 for s in confidence_scores if s >= 0.8),
                "medium_confidence": sum(1 for s in confidence_scores if 0.5 <= s < 0.8),
                "low_confidence": sum(1 for s in confidence_scores if s < 0.5)
            }
        }
    
    @staticmethod
    def _percentile(data: List[float], percentile: int) -> float:
        """Calculate percentile of data"""
        if not data:
            return 0.0
        
        sorted_data = sorted(data)
        k = (len(sorted_data) - 1) * percentile / 100
        f = int(k)
        c = k - f
        
        if f == len(sorted_data) - 1:
            return sorted_data[f]
        
        return sorted_data[f] * (1 - c) + sorted_data[f + 1] * c
    
    def export_prometheus_metrics(self) -> str:
        """Export metrics in Prometheus format"""
        return generate_latest().decode('utf-8')


class PerformanceMonitor:
    """Context manager for performance monitoring"""
    
    def __init__(self, operation: str, collector: MetricsCollector, metadata: Optional[Dict] = None):
        self.operation = operation
        self.collector = collector
        self.metadata = metadata or {}
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        success = exc_type is None
        error_type = exc_type.__name__ if exc_type else None
        
        self.collector.record_performance(
            operation=self.operation,
            duration=duration,
            success=success,
            error_type=error_type,
            metadata=self.metadata
        )
        
        # Log slow operations
        if duration > self.collector.config.LOG_PERFORMANCE_THRESHOLD:
            logger.warning(
                f"Slow operation detected: {self.operation} took {duration:.2f}s"
            )


@asynccontextmanager
async def monitor_performance(operation: str, metadata: Optional[Dict] = None):
    """Async context manager for performance monitoring"""
    collector = get_metrics_collector()
    start_time = time.time()
    
    try:
        yield
        duration = time.time() - start_time
        collector.record_performance(operation, duration, True, metadata=metadata)
    except Exception as e:
        duration = time.time() - start_time
        collector.record_performance(
            operation, duration, False, 
            error_type=type(e).__name__, metadata=metadata
        )
        raise


def monitor_function(operation: Optional[str] = None):
    """Decorator for function performance monitoring"""
    def decorator(func: Callable) -> Callable:
        op_name = operation or f"{func.__module__}.{func.__qualname__}"
        
        if asyncio.iscoroutinefunction(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                async with monitor_performance(op_name):
                    return await func(*args, **kwargs)
            return async_wrapper
        else:
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                collector = get_metrics_collector()
                with PerformanceMonitor(op_name, collector):
                    return func(*args, **kwargs)
            return sync_wrapper
    
    return decorator


# Global metrics collector
_metrics_collector = None


def get_metrics_collector() -> MetricsCollector:
    """Get global metrics collector instance"""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector


async def initialize_metrics():
    """Initialize metrics system"""
    collector = get_metrics_collector()
    await collector.start()


async def shutdown_metrics():
    """Shutdown metrics system"""
    collector = get_metrics_collector()
    await collector.stop()