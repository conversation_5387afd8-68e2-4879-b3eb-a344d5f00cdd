/**
 * 学术搜索链接生成工具
 */

export interface SearchEngine {
  name: string
  baseUrl: string
  emoji: string
  description: string
  color: string
}

export const SEARCH_ENGINES: Record<string, SearchEngine> = {
  pubmed: {
    name: "PubMed",
    baseUrl: "https://pubmed.ncbi.nlm.nih.gov/?term=",
    emoji: "🔬",
    description: "医学文献数据库",
    color: "blue"
  },
  googleScholar: {
    name: "Google Scholar", 
    baseUrl: "https://scholar.google.com/scholar?q=",
    emoji: "🎓",
    description: "学术搜索引擎",
    color: "green"
  },
  semanticScholar: {
    name: "Semantic Scholar",
    baseUrl: "https://www.semanticscholar.org/search?q=",
    emoji: "🧠",
    description: "AI驱动学术搜索",
    color: "purple"
  },
  bioRxiv: {
    name: "bioRxiv",
    baseUrl: "https://www.biorxiv.org/search/",
    emoji: "📄",
    description: "生物学预印本",
    color: "orange"
  },
  webOfScience: {
    name: "Web of Science",
    baseUrl: "https://www.webofscience.com/wos/woscc/basic-search?search=",
    emoji: "📊",
    description: "科学引文索引",
    color: "red"
  }
}

/**
 * 生成搜索链接
 */
export function generateSearchLink(keyword: string, engine: string): string {
  const searchEngine = SEARCH_ENGINES[engine]
  if (!searchEngine) return ""
  
  // URL编码关键词
  const encodedKeyword = encodeURIComponent(keyword.trim())
  return `${searchEngine.baseUrl}${encodedKeyword}`
}

/**
 * 生成多个搜索引擎的链接组合
 */
export function generateSearchLinks(keyword: string, engines: string[] = ['pubmed', 'googleScholar', 'semanticScholar']): Array<{
  engine: string
  name: string
  url: string
  emoji: string
  description: string
  color: string
}> {
  return engines.map(engine => {
    const searchEngine = SEARCH_ENGINES[engine]
    return {
      engine,
      name: searchEngine.name,
      url: generateSearchLink(keyword, engine),
      emoji: searchEngine.emoji,
      description: searchEngine.description,
      color: searchEngine.color
    }
  })
}

/**
 * 优化关键词用于搜索
 */
export function optimizeKeywordForSearch(keyword: string): string {
  // 移除特殊字符，优化搜索效果
  return keyword
    .replace(/[^\w\s-]/g, ' ') // 移除特殊字符
    .replace(/\s+/g, ' ')      // 合并多个空格
    .trim()
}

/**
 * 生成建议的搜索关键词组合
 */
export function generateSuggestedKeywords(baseKeyword: string, context: {
  species?: string
  technique?: string 
  application?: string
}): string[] {
  const keywords = []
  
  // 基础关键词
  keywords.push(baseKeyword)
  
  // 加入物种信息
  if (context.species) {
    keywords.push(`${baseKeyword} ${context.species}`)
  }
  
  // 加入技术信息
  if (context.technique) {
    keywords.push(`${baseKeyword} ${context.technique}`)
    if (context.species) {
      keywords.push(`${baseKeyword} ${context.technique} ${context.species}`)
    }
  }
  
  // 加入应用场景
  if (context.application) {
    keywords.push(`${baseKeyword} ${context.application}`)
  }
  
  // 方法学关键词
  keywords.push(`${baseKeyword} protocol`)
  keywords.push(`${baseKeyword} methods`)
  keywords.push(`${baseKeyword} best practices`)
  
  // 最新研究
  const currentYear = new Date().getFullYear()
  keywords.push(`${baseKeyword} ${currentYear}`)
  keywords.push(`${baseKeyword} recent advances`)
  
  return [...new Set(keywords)] // 去重
}

/**
 * 生成Markdown格式的搜索链接
 */
export function generateSearchLinksMarkdown(keyword: string, engines: string[] = ['pubmed', 'googleScholar', 'semanticScholar']): string {
  const links = generateSearchLinks(keyword, engines)
  
  return links.map(link => 
    `[${link.emoji} ${link.name}](${link.url} "${link.description}")`
  ).join(' • ')
}

/**
 * 为智能推荐生成完整的搜索建议
 */
export function generateIntelligentSearchSuggestions(requirements: {
  speciesType?: string
  experimentType?: string
  researchGoal?: string
  sampleType?: string
}): {
  primaryKeywords: string[]
  secondaryKeywords: string[]
  searchLinks: Record<string, Array<{
    engine: string
    name: string
    url: string
    emoji: string
    description: string
    color: string
  }>>
} {
  // 提取核心概念
  const species = requirements.speciesType?.toLowerCase().includes('大鼠') ? 'rat' : 
                  requirements.speciesType?.toLowerCase().includes('小鼠') ? 'mouse' :
                  requirements.speciesType?.toLowerCase().includes('人') ? 'human' : ''
  
  const technique = requirements.experimentType?.includes('单细胞RNA') ? 'scRNA-seq' :
                   requirements.experimentType?.includes('单细胞ATAC') ? 'scATAC-seq' :
                   'single cell'
  
  const application = requirements.researchGoal?.includes('发育轨迹') ? 'trajectory analysis' :
                     requirements.researchGoal?.includes('免疫') ? 'immune analysis' :
                     requirements.researchGoal?.includes('细胞类型') ? 'cell type identification' : ''
  
  const tissue = requirements.sampleType?.includes('心脏') ? 'cardiac' :
                requirements.sampleType?.includes('脑') ? 'brain' :
                requirements.sampleType?.includes('肝') ? 'liver' : ''
  
  // 生成主要关键词
  const primaryKeywords = [
    `${species} ${technique}`,
    `${technique} ${tissue}`,
    `${species} ${tissue} ${technique}`,
    `${application} ${technique}`
  ].filter(k => k.trim().length > 0)
  
  // 生成次要关键词
  const secondaryKeywords = [
    `${technique} protocol`,
    `${technique} data analysis`,
    `${species} ${tissue} development`,
    `single cell genomics ${species}`,
    `${technique} best practices`,
    `${technique} computational methods`
  ].filter(k => k.trim().length > 0)
  
  // 为每个关键词生成搜索链接
  const searchLinks: Record<string, any> = {}
  
  // 主要关键词的完整搜索链接
  primaryKeywords.slice(0, 3).forEach(keyword => {
    searchLinks[keyword] = generateSearchLinks(keyword, ['pubmed', 'googleScholar', 'semanticScholar'])
  })
  
  // 次要关键词的精选搜索链接  
  secondaryKeywords.slice(0, 2).forEach(keyword => {
    searchLinks[keyword] = generateSearchLinks(keyword, ['pubmed', 'googleScholar'])
  })
  
  return {
    primaryKeywords,
    secondaryKeywords, 
    searchLinks
  }
}