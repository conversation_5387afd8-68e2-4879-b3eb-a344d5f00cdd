"use client"

import { useAuth } from "@/contexts/auth-context"
import { KnowledgeManagement } from "@/components/knowledge-management"
import { AccessDenied } from "@/components/access-denied"

export default function KnowledgePage() {
  const { hasPermission } = useAuth()

  return (
    <div className="min-h-[calc(100vh-64px)]">
      {hasPermission("view_knowledge") ? <KnowledgeManagement /> : <AccessDenied />}
    </div>
  )
}
