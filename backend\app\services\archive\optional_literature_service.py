"""
可选文献搜集服务
只有在用户明确启用时才激活的文献搜集功能
"""
from typing import Dict, List, Optional, Any
import asyncio
import logging
from datetime import datetime

from app.core.external_apis import get_api_manager, check_literature_prerequisites
from app.core.config import settings
from app.services.ai_service import AIService

logger = logging.getLogger(__name__)


class OptionalLiteratureService:
    """可选文献搜集服务"""
    
    def __init__(self):
        self.api_manager = get_api_manager()
        self.ai_service = AIService()
        self._clients = {}  # 延迟加载的API客户端
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return self.api_manager.is_literature_search_enabled()
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return check_literature_prerequisites()
    
    async def collect_literature_if_enabled(
        self,
        requirements: Dict[str, Any],
        user_preferences: Dict[str, Any] = None,
        enable_literature_search: bool = False
    ) -> Dict[str, Any]:
        """
        根据用户选择决定是否搜集文献
        
        Args:
            requirements: 用户需求信息
            user_preferences: 用户偏好设置
            enable_literature_search: 是否启用文献搜索
        
        Returns:
            文献搜集结果或空结果
        """
        # 检查是否启用文献搜索
        if not enable_literature_search:
            return self._get_disabled_response("用户未启用文献搜索")
        
        if not self.is_available():
            return self._get_disabled_response("文献搜索功能不可用")
        
        try:
            # 执行文献搜集
            return await self._perform_literature_collection(requirements, user_preferences)
        
        except Exception as e:
            logger.error(f"文献搜集失败: {e}")
            return self._get_error_response(str(e))
    
    async def _perform_literature_collection(
        self,
        requirements: Dict[str, Any],
        user_preferences: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """执行实际的文献搜集"""
        
        # 1. 分析需求生成搜索策略
        search_strategy = await self._analyze_requirements(requirements)
        
        # 2. 执行多源搜索（如果有可用的API）
        search_results = await self._search_multiple_sources(search_strategy)
        
        # 3. AI相关性评估（如果启用AI服务）
        evaluated_results = await self._evaluate_relevance(search_results, requirements)
        
        # 4. 筛选和排序
        final_results = self._filter_and_rank_results(evaluated_results, user_preferences)
        
        return {
            "status": "success",
            "enabled": True,
            "collection_timestamp": datetime.utcnow().isoformat(),
            "search_strategy": search_strategy,
            "total_found": len(search_results),
            "evaluated_count": len(evaluated_results),
            "supporting_papers": final_results.get("supporting", []),
            "potential_papers": final_results.get("potential", []),
            "search_summary": self._generate_search_summary(search_strategy, search_results)
        }
    
    async def _analyze_requirements(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """分析需求生成搜索策略"""
        # 简化的需求分析逻辑
        research_question = requirements.get("research_question", "")
        tissue_type = requirements.get("tissue_type", "")
        research_goal = requirements.get("research_goal", "")
        
        # 生成基础搜索关键词
        keywords = []
        if tissue_type:
            keywords.append(tissue_type)
        if research_goal:
            keywords.append(research_goal.replace("_", " "))
        
        # 添加单细胞相关术语
        keywords.extend(["single cell", "scRNA-seq"])
        
        return {
            "primary_keywords": keywords[:3],
            "secondary_keywords": keywords[3:],
            "search_queries": [" ".join(keywords[:2])],
            "max_results_per_source": settings.LITERATURE_COLLECTION_MAX_PAPERS // 3
        }
    
    async def _search_multiple_sources(self, strategy: Dict[str, Any]) -> List[Dict[str, Any]]:
        """搜索多个数据源"""
        all_results = []
        available_apis = self.api_manager.get_available_apis()

        # 如果有可用的外部API，使用它们
        if available_apis:
            # 为每个可用的API创建搜索任务
            search_tasks = []
            for api_name in available_apis:
                if api_name == "pubmed":
                    task = self._search_pubmed_mock(strategy)
                elif api_name == "semantic_scholar":
                    task = self._search_semantic_scholar_mock(strategy)
                elif api_name == "biorxiv":
                    task = self._search_biorxiv_mock(strategy)
                else:
                    continue

                search_tasks.append(task)

            # 并行执行搜索
            if search_tasks:
                results = await asyncio.gather(*search_tasks, return_exceptions=True)
                for result in results:
                    if isinstance(result, list):
                        all_results.extend(result)
                    else:
                        logger.warning(f"搜索任务失败: {result}")
        else:
            # 如果没有外部API，使用本地文献库
            all_results = await self._search_local_literature(strategy)

        return all_results

    async def _search_local_literature(self, strategy: Dict[str, Any]) -> List[Dict[str, Any]]:
        """搜索本地文献库"""
        try:
            from app.services.literature_service import literature_service

            # 构建搜索查询
            search_terms = strategy.get("search_terms", [])
            query = " ".join(search_terms[:3])  # 使用前3个搜索词

            if not query:
                query = "single cell"  # 默认查询

            # 调用本地文献服务
            results = await literature_service.search_literature(
                query=query,
                top_k=10
            )

            return results

        except Exception as e:
            logger.error(f"本地文献搜索失败: {e}")
            return []

    async def _search_pubmed_mock(self, strategy: Dict[str, Any]) -> List[Dict[str, Any]]:
        """PubMed搜索模拟（实际实现时替换为真实API调用）"""
        # 模拟搜索延迟
        await asyncio.sleep(0.1)
        
        return [
            {
                "source": "pubmed",
                "title": "Single-cell RNA sequencing reveals cellular heterogeneity",
                "authors": ["Smith J", "Johnson A"],
                "journal": "Nature",
                "year": 2023,
                "abstract": "This study demonstrates...",
                "pmid": "12345678",
                "relevance_score": 0.85
            }
        ]
    
    async def _search_semantic_scholar_mock(self, strategy: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Semantic Scholar搜索模拟"""
        await asyncio.sleep(0.1)
        
        return [
            {
                "source": "semantic_scholar",
                "title": "Advances in single-cell analysis methods",
                "authors": ["Brown K", "Davis L"],
                "journal": "Cell",
                "year": 2023,
                "abstract": "Recent developments in...",
                "doi": "10.1016/j.cell.2023.01.001",
                "citation_count": 150,
                "relevance_score": 0.78
            }
        ]
    
    async def _search_biorxiv_mock(self, strategy: Dict[str, Any]) -> List[Dict[str, Any]]:
        """bioRxiv搜索模拟"""
        await asyncio.sleep(0.1)
        
        return [
            {
                "source": "biorxiv",
                "title": "Novel computational approaches for single-cell data",
                "authors": ["Wilson M", "Taylor R"],
                "journal": "bioRxiv (preprint)",
                "year": 2024,
                "abstract": "We present new methods...",
                "doi": "10.1101/2024.01.001",
                "is_preprint": True,
                "relevance_score": 0.72
            }
        ]
    
    async def _evaluate_relevance(
        self,
        papers: List[Dict[str, Any]],
        requirements: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """AI相关性评估"""
        if not self.ai_service.use_real_ai:
            # 如果没有AI服务，使用基础评分
            return papers
        
        # 这里可以集成真实的AI评估逻辑
        # 暂时返回原始结果
        return papers
    
    def _filter_and_rank_results(
        self,
        papers: List[Dict[str, Any]],
        user_preferences: Dict[str, Any] = None
    ) -> Dict[str, List[Dict[str, Any]]]:
        """筛选和排序结果"""
        threshold = settings.LITERATURE_RELEVANCE_THRESHOLD
        
        supporting = []
        potential = []
        
        for paper in papers:
            score = paper.get("relevance_score", 0.0)
            if score >= threshold:
                supporting.append(paper)
            elif score >= threshold - 0.2:  # 容忍度范围
                potential.append(paper)
        
        # 按相关性评分排序
        supporting.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
        potential.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
        
        return {
            "supporting": supporting,
            "potential": potential
        }
    
    def _generate_search_summary(
        self,
        strategy: Dict[str, Any],
        results: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """生成搜索摘要"""
        sources = list(set(paper.get("source", "unknown") for paper in results))
        
        return {
            "keywords_used": strategy.get("primary_keywords", []),
            "sources_searched": sources,
            "total_results": len(results),
            "search_duration": "模拟搜索"
        }
    
    def _get_disabled_response(self, reason: str) -> Dict[str, Any]:
        """获取禁用状态的响应"""
        return {
            "status": "disabled",
            "enabled": False,
            "reason": reason,
            "supporting_papers": [],
            "potential_papers": [],
            "recommendations": check_literature_prerequisites().get("recommendations", [])
        }
    
    def _get_error_response(self, error_message: str) -> Dict[str, Any]:
        """获取错误响应"""
        return {
            "status": "error",
            "enabled": True,
            "error": error_message,
            "supporting_papers": [],
            "potential_papers": []
        }


# 全局服务实例
optional_literature_service = OptionalLiteratureService()


def get_optional_literature_service() -> OptionalLiteratureService:
    """获取可选文献服务实例"""
    return optional_literature_service
