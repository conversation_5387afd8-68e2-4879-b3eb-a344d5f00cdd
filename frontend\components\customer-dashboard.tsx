"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CustomerProfileAnalysis } from "@/components/customer-profile-analysis"
import { CustomerProfileDashboard } from "@/components/customer-profile-dashboard"
import {
  Star,
  Brain,
  UserCheck,
  Users,
  Filter,
  Search,
  Plus
} from "lucide-react"

export function CustomerDashboard() {
  const [activeTab, setActiveTab] = useState("analysis")
  const [searchTerm, setSearchTerm] = useState("")

  const customers = [
    {
      id: 1,
      name: "北京大学医学院",
      logo: "🏥",
      industry: "学术研究",
      scale: "大型",
      techLevel: 4,
      status: "活跃",
      value: 85,
      risk: "低",
      lastContact: "2天前",
    },
    {
      id: 2,
      name: "华大基因",
      logo: "🧬",
      industry: "生物技术",
      scale: "大型",
      techLevel: 5,
      status: "商机",
      value: 92,
      risk: "低",
      lastContact: "1周前",
    },
  ]

  const interactions = [
    {
      id: 1,
      type: "咨询",
      title: "单细胞转录组测序方案咨询",
      date: "2024-01-15",
      status: "已完成",
    },
    {
      id: 2,
      type: "报价",
      title: "10x Genomics平台报价单",
      date: "2024-01-10",
      status: "待回复",
    },
  ]

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题和导航 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">客户管理中心</h2>
          <p className="text-slate-600 mt-1">智能客户画像分析与管理</p>
        </div>
        <div className="flex items-center space-x-4">
          <Input
            placeholder="搜索客户..."
            className="w-64"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            筛选
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            新增客户
          </Button>
        </div>
      </div>

      {/* 功能标签页 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="analysis" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            画像分析
          </TabsTrigger>
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <UserCheck className="h-4 w-4" />
            客户画像
          </TabsTrigger>
          <TabsTrigger value="management" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            客户管理
          </TabsTrigger>
        </TabsList>

        {/* 画像分析页面 */}
        <TabsContent value="analysis">
          <CustomerProfileAnalysis />
        </TabsContent>

        {/* 客户画像仪表板 */}
        <TabsContent value="dashboard">
          <CustomerProfileDashboard />
        </TabsContent>

        {/* 客户管理页面 */}
        <TabsContent value="management">
          <div className="space-y-6">

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Customer List */}
        <div className="lg:col-span-2 space-y-4">
          {customers.map((customer) => (
            <Card key={customer.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="text-3xl">{customer.logo}</div>
                    <div>
                      <h3 className="font-semibold text-lg">{customer.name}</h3>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="secondary">{customer.industry}</Badge>
                        <Badge variant="outline">{customer.scale}</Badge>
                      </div>
                      <div className="flex items-center space-x-1 mt-2">
                        <span className="text-sm text-slate-600">技术成熟度:</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${
                                i < customer.techLevel ? "text-yellow-400 fill-current" : "text-slate-300"
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge
                      className={
                        customer.status === "活跃"
                          ? "bg-green-100 text-green-800"
                          : customer.status === "商机"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-slate-100 text-slate-800"
                      }
                    >
                      {customer.status}
                    </Badge>
                    <div className="mt-2 text-sm text-slate-600">最后联系: {customer.lastContact}</div>
                  </div>
                </div>

                <div className="mt-4 grid grid-cols-2 gap-4">
                  <div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-slate-600">客户价值</span>
                      <span className="font-medium">{customer.value}/100</span>
                    </div>
                    <Progress value={customer.value} className="mt-1" />
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-slate-600">风险等级:</span>
                      <Badge variant={customer.risk === "低" ? "default" : "destructive"}>{customer.risk}</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Interaction Timeline */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>互动历史</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {interactions.map((interaction) => (
                  <div key={interaction.id} className="flex items-start space-x-3">
                    <div
                      className={`w-2 h-2 rounded-full mt-2 ${
                        interaction.status === "已完成" ? "bg-green-500" : "bg-yellow-500"
                      }`}
                    />
                    <div className="flex-1">
                      <div className="text-sm font-medium">{interaction.title}</div>
                      <div className="text-xs text-slate-500 mt-1">
                        {interaction.type} • {interaction.date}
                      </div>
                      <Badge variant={interaction.status === "已完成" ? "default" : "secondary"} className="mt-1">
                        {interaction.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Opportunity Analysis */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>商机分析</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="text-sm text-slate-600">预估订单价值</div>
                <div className="text-2xl font-bold text-green-600">¥125,000</div>
              </div>
              <div>
                <div className="text-sm text-slate-600">成交概率</div>
                <div className="flex items-center space-x-2">
                  <Progress value={75} className="flex-1" />
                  <span className="text-sm font-medium">75%</span>
                </div>
              </div>
              <div>
                <div className="text-sm text-slate-600 mb-2">下一步行动</div>
                <Button className="w-full" size="sm">
                  发送技术方案
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
