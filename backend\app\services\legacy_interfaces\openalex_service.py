"""
OpenAlex API集成服务
提供免费的学术文献摘要数据库访问
"""
import aiohttp
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class OpenAlexService:
    """OpenAlex API服务 - 免费的学术文献摘要数据库"""
    
    def __init__(self):
        self.base_url = "https://api.openalex.org"
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            headers = {
                "User-Agent": "CellForge-AI/1.0 (mailto:<EMAIL>)",
                "Accept": "application/json"
            }
            self.session = aiohttp.ClientSession(timeout=timeout, headers=headers)
        return self.session
    
    async def search_papers(
        self, 
        query: str, 
        max_results: int = 20,
        publication_year_from: int = None
    ) -> List[Dict[str, Any]]:
        """搜索学术论文"""
        try:
            session = await self._get_session()
            
            # 构建搜索URL - 使用最优策略：简化查询
            url = f"{self.base_url}/works"
            
            # 根据诊断测试结果，使用简化查询策略获得最佳效果
            params = {
                'search': query,  # 基础搜索，不添加复杂过滤器
                'per-page': min(max_results, 200),  # 增加每页结果数以获得更多结果
                'sort': 'relevance_score:desc'  # 按相关性排序，而非引用数
            }
            
            # 移除年份过滤器，因为测试显示这会大幅减少结果数量
            
            logger.info(f"OpenAlex搜索: {query}")
            logger.info(f"OpenAlex参数: {params}")
            
            async with session.get(url, params=params) as response:
                logger.info(f"OpenAlex响应状态: {response.status}")
                
                if response.status != 200:
                    response_text = await response.text()
                    logger.error(f"OpenAlex搜索失败: {response.status}, 响应: {response_text}")
                    return []
                
                data = await response.json()
                
                # 检查响应结构
                meta = data.get('meta', {})
                results = data.get('results', [])
                total_count = meta.get('count', 0)
                
                logger.info(f"OpenAlex元数据: 总计 {total_count} 篇文献")
                logger.info(f"OpenAlex返回: {len(results)} 篇文献")
                
                if not results:
                    logger.warning(f"OpenAlex未找到匹配的文献，查询: {query}")
                    # 如果还是没有结果，尝试更基础的搜索
                    if ' ' in query:
                        logger.info("尝试拆分关键词搜索...")
                        keywords = query.split()[:2]  # 取前两个关键词
                        return await self._fallback_search(' '.join(keywords), max_results)
                    return []
                
                # 解析文献
                parsed_papers = []
                for paper in results:
                    parsed = self._parse_openalex_paper(paper)
                    if parsed:  # 只添加成功解析的论文
                        parsed_papers.append(parsed)
                
                logger.info(f"OpenAlex成功解析: {len(parsed_papers)} 篇文献")
                return parsed_papers
                
        except Exception as e:
            logger.error(f"OpenAlex搜索异常: {e}", exc_info=True)
            return []
    
    async def _fallback_search(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """关键词拆分的后备搜索"""
        try:
            session = await self._get_session()
            url = f"{self.base_url}/works"
            
            # 极简参数，确保最大兼容性
            params = {
                'search': query,
                'per-page': min(max_results, 100)  # 稍微降低以确保稳定性
            }
            
            logger.info(f"OpenAlex后备搜索: {query}")
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    results = data.get('results', [])
                    logger.info(f"OpenAlex后备搜索找到: {len(results)} 篇文献")
                    
                    parsed_papers = []
                    for paper in results:
                        parsed = self._parse_openalex_paper(paper)
                        if parsed:
                            parsed_papers.append(parsed)
                    
                    return parsed_papers
                else:
                    logger.error(f"OpenAlex后备搜索失败: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"OpenAlex后备搜索异常: {e}")
            return []
    
    def _parse_openalex_paper(self, paper: Dict) -> Dict[str, Any]:
        """解析OpenAlex论文数据"""
        try:
            # 基础信息提取
            title = paper.get('title', '')
            if not title:
                return None
            
            # 提取作者信息
            authors = []
            authorships = paper.get('authorships', [])
            for authorship in authorships[:5]:  # 最多5个作者
                author = authorship.get('author', {})
                if author.get('display_name'):
                    authors.append(author['display_name'])
            
            # 提取期刊信息
            primary_location = paper.get('primary_location') or {}
            source = primary_location.get('source') or {}
            journal = source.get('display_name', '')
            
            # 提取年份
            year = paper.get('publication_year', 2024)
            
            # 提取引用数
            citation_count = paper.get('cited_by_count', 0)
            
            # 提取摘要（如果有）
            abstract = ""
            abstract_inverted = paper.get('abstract_inverted_index')
            if abstract_inverted:
                abstract = self._reconstruct_abstract(abstract_inverted)
            
            # 如果没有摘要，尝试从其他字段获取
            if not abstract:
                # 从概念或其他字段获取描述
                concepts = paper.get('concepts', [])[:3]
                if concepts:
                    concept_names = [c.get('display_name', '') for c in concepts]
                    abstract = f"研究涉及: {', '.join(concept_names)}"
            
            # 提取概念标签
            concepts = paper.get('concepts', [])[:5]
            concept_tags = [concept.get('display_name', '') for concept in concepts if concept.get('score', 0) > 0.3]
            
            # 开放获取信息
            open_access = paper.get('open_access', {})
            is_oa = open_access.get('is_oa', False) if open_access else False
            
            return {
                "title": title,
                "authors": authors,
                "journal": journal,
                "publication_year": year,
                "abstract": abstract[:500] + "..." if len(abstract) > 500 else abstract,
                "citation_count": citation_count,
                "openalex_id": paper.get('id', ''),
                "concepts": concept_tags,
                "open_access": is_oa,
                "source": "OpenAlex",
                "relevance_score": self._calculate_relevance_score(paper),
                "key_findings": self._extract_key_findings_from_abstract(abstract),
                "methodology_summary": self._extract_methodology_from_abstract(abstract)
            }
            
        except Exception as e:
            logger.error(f"解析OpenAlex论文失败: {e}")
            return None
    
    def _reconstruct_abstract(self, inverted_index: Optional[Dict]) -> str:
        """从倒排索引重构摘要"""
        if not inverted_index:
            return ""
        
        try:
            # 创建位置-单词映射
            word_positions = {}
            for word, positions in inverted_index.items():
                for pos in positions:
                    word_positions[pos] = word
            
            # 按位置排序重构摘要
            sorted_positions = sorted(word_positions.keys())
            abstract_words = [word_positions[pos] for pos in sorted_positions]
            
            return " ".join(abstract_words)
            
        except Exception as e:
            logger.debug(f"摘要重构失败: {e}")
            return ""
    
    def _calculate_relevance_score(self, paper: Dict) -> float:
        """计算相关性评分"""
        score = 0.5  # 基础分数
        
        # 引用数加分
        citations = paper.get('cited_by_count', 0)
        if citations > 100:
            score += 0.2
        elif citations > 50:
            score += 0.1
        
        # 开放获取加分
        if paper.get('open_access', {}).get('is_oa', False):
            score += 0.1
        
        # 概念相关性加分
        concepts = paper.get('concepts', [])
        bio_concepts = ['Biology', 'Medicine', 'Genetics', 'Cell biology', 'Molecular biology']
        for concept in concepts:
            if concept.get('display_name') in bio_concepts and concept.get('score', 0) > 0.5:
                score += 0.1
                break
        
        # 时间新近性加分
        year = paper.get('publication_year', 2020)
        if year >= 2023:
            score += 0.1
        elif year >= 2021:
            score += 0.05
        
        return min(score, 1.0)
    
    def _extract_key_findings_from_abstract(self, abstract: str) -> str:
        """从摘要中提取关键发现"""
        if not abstract:
            return ""
        
        # 寻找结果相关的句子
        sentences = abstract.split('. ')
        for sentence in sentences:
            if any(keyword in sentence.lower() for keyword in 
                   ['found', 'identified', 'revealed', 'demonstrated', 'showed', 'discovered']):
                return sentence.strip()[:200] + "..."
        
        return abstract[:200] + "..." if len(abstract) > 200 else abstract
    
    def _extract_methodology_from_abstract(self, abstract: str) -> str:
        """从摘要中提取方法学信息"""
        if not abstract:
            return ""
        
        # 寻找方法相关的句子
        sentences = abstract.split('. ')
        for sentence in sentences:
            if any(keyword in sentence.lower() for keyword in 
                   ['using', 'performed', 'analyzed', 'sequenced', 'applied', 'employed', 'method']):
                return sentence.strip()[:200] + "..."
        
        return ""
    
    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()


# 全局服务实例
openalex_service = OpenAlexService()


async def get_openalex_service() -> OpenAlexService:
    """获取OpenAlex服务实例"""
    return openalex_service