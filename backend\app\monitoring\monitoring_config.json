{"monitoring": {"enabled": true, "collection_interval": 60, "retention_days": 30, "metrics": {"api_performance": {"enabled": true, "track_response_time": true, "track_request_count": true, "track_error_rate": true, "percentiles": [50, 90, 95, 99]}, "business_metrics": {"enabled": true, "track_literature_success_rate": true, "track_ai_service_quality": true, "track_user_satisfaction": true, "track_cache_hit_rate": true}, "system_metrics": {"enabled": true, "track_memory_usage": true, "track_cpu_usage": true, "track_database_connections": true, "track_external_api_calls": true}}, "health_checks": {"enabled": true, "check_interval": 30, "endpoints": [{"name": "database", "type": "database_connection", "timeout": 5}, {"name": "openai_api", "type": "external_api", "url": "https://api.openai.com/v1/models", "timeout": 10}, {"name": "openalex_api", "type": "external_api", "url": "https://api.openalex.org/works", "timeout": 10}]}, "alerting": {"enabled": true, "notification_channels": ["console", "log"], "rules": {"api_response_time": {"threshold": 5000, "condition": "greater_than", "severity": "warning"}, "error_rate": {"threshold": 0.05, "condition": "greater_than", "severity": "critical"}, "memory_usage": {"threshold": 0.85, "condition": "greater_than", "severity": "warning"}, "literature_success_rate": {"threshold": 0.8, "condition": "less_than", "severity": "warning"}}}}}