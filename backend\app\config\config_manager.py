"""
配置管理器 - 统一管理系统配置文件
支持JSON格式配置文件的加载、验证、缓存和热重载功能
"""
import json
import os
import time
from typing import Dict, Any, Optional, Callable, List
from pathlib import Path
import logging
from threading import Lock
import asyncio
from datetime import datetime

logger = logging.getLogger(__name__)


class ConfigValidationError(Exception):
    """配置验证错误"""
    pass


class ConfigFileNotFoundError(Exception):
    """配置文件未找到错误"""
    pass


class ConfigManager:
    """
    配置管理器
    
    功能特性：
    - 动态加载JSON配置文件
    - 配置文件热重载
    - 配置验证和默认值
    - 配置缓存机制
    - 错误处理和回退机制
    """
    
    def __init__(self, config_dir: str = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录路径
        """
        # 设置配置文件目录
        if config_dir:
            self.config_dir = Path(config_dir)
        else:
            # 默认使用当前文件同级的config目录
            current_file = Path(__file__)
            self.config_dir = current_file.parent
        
        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置缓存
        self._config_cache: Dict[str, Dict[str, Any]] = {}
        self._file_timestamps: Dict[str, float] = {}
        self._cache_lock = Lock()
        
        # 配置验证器
        self._validators: Dict[str, Callable] = {}
        
        # 默认配置
        self._default_configs: Dict[str, Dict[str, Any]] = {}
        
        # 热重载设置
        self._auto_reload = True
        self._reload_interval = 1.0  # 秒
        
        # 初始化日志
        self._setup_logging()
        
        logger.info(f"ConfigManager initialized with config directory: {self.config_dir}")
    
    def _setup_logging(self):
        """设置日志配置"""
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
    
    def register_validator(self, config_name: str, validator: Callable[[Dict[str, Any]], bool]):
        """
        注册配置验证器
        
        Args:
            config_name: 配置文件名（不含扩展名）
            validator: 验证函数，返回True表示验证通过
        """
        self._validators[config_name] = validator
        logger.info(f"Registered validator for config: {config_name}")
    
    def set_default_config(self, config_name: str, default_config: Dict[str, Any]):
        """
        设置默认配置
        
        Args:
            config_name: 配置文件名（不含扩展名）
            default_config: 默认配置字典
        """
        self._default_configs[config_name] = default_config
        logger.info(f"Set default config for: {config_name}")
    
    def get_config_file_path(self, config_name: str) -> Path:
        """获取配置文件路径"""
        return self.config_dir / f"{config_name}.json"
    
    def _load_config_file(self, config_name: str) -> Dict[str, Any]:
        """
        从文件加载配置
        
        Args:
            config_name: 配置文件名（不含扩展名）
            
        Returns:
            配置字典
            
        Raises:
            ConfigFileNotFoundError: 配置文件不存在
            ConfigValidationError: 配置验证失败
        """
        config_path = self.get_config_file_path(config_name)
        
        if not config_path.exists():
            # 如果有默认配置，使用默认配置
            if config_name in self._default_configs:
                logger.warning(f"Config file {config_path} not found, using default config")
                return self._default_configs[config_name].copy()
            else:
                raise ConfigFileNotFoundError(f"Config file not found: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 验证配置
            if config_name in self._validators:
                if not self._validators[config_name](config_data):
                    raise ConfigValidationError(f"Config validation failed for: {config_name}")
            
            # 更新文件时间戳
            self._file_timestamps[config_name] = config_path.stat().st_mtime
            
            logger.info(f"Successfully loaded config: {config_name}")
            return config_data
            
        except json.JSONDecodeError as e:
            raise ConfigValidationError(f"Invalid JSON in config file {config_path}: {e}")
        except Exception as e:
            logger.error(f"Error loading config {config_name}: {e}")
            # 尝试使用默认配置作为回退
            if config_name in self._default_configs:
                logger.warning(f"Using default config as fallback for: {config_name}")
                return self._default_configs[config_name].copy()
            raise
    
    def _should_reload_config(self, config_name: str) -> bool:
        """检查是否需要重载配置文件"""
        if not self._auto_reload:
            return False
        
        config_path = self.get_config_file_path(config_name)
        if not config_path.exists():
            return False
        
        current_mtime = config_path.stat().st_mtime
        cached_mtime = self._file_timestamps.get(config_name, 0)
        
        return current_mtime > cached_mtime
    
    def get_config(self, config_name: str, force_reload: bool = False) -> Dict[str, Any]:
        """
        获取配置
        
        Args:
            config_name: 配置文件名（不含扩展名）
            force_reload: 是否强制重新加载
            
        Returns:
            配置字典
        """
        with self._cache_lock:
            # 检查是否需要重载
            if (force_reload or 
                config_name not in self._config_cache or 
                self._should_reload_config(config_name)):
                
                try:
                    self._config_cache[config_name] = self._load_config_file(config_name)
                    logger.info(f"Config reloaded: {config_name}")
                except Exception as e:
                    logger.error(f"Failed to reload config {config_name}: {e}")
                    # 如果重载失败且缓存中有旧配置，继续使用旧配置
                    if config_name not in self._config_cache:
                        raise
            
            return self._config_cache[config_name].copy()
    
    async def get_config_async(self, config_name: str, force_reload: bool = False) -> Dict[str, Any]:
        """异步获取配置"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.get_config, config_name, force_reload
        )
    
    def get_config_section(self, config_name: str, section_path: str, default: Any = None) -> Any:
        """
        获取配置的特定部分
        
        Args:
            config_name: 配置文件名
            section_path: 配置路径，用点分隔，如 'api_endpoints.literature_search.enabled'
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            config = self.get_config(config_name)
            keys = section_path.split('.')
            
            value = config
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            
            return value
        except Exception as e:
            logger.error(f"Error getting config section {section_path} from {config_name}: {e}")
            return default
    
    async def get_config_section_async(self, config_name: str, section_path: str, default: Any = None) -> Any:
        """异步获取配置的特定部分"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.get_config_section, config_name, section_path, default
        )
    
    def update_config(self, config_name: str, updates: Dict[str, Any], save_to_file: bool = True):
        """
        更新配置
        
        Args:
            config_name: 配置文件名
            updates: 更新的配置项
            save_to_file: 是否保存到文件
        """
        with self._cache_lock:
            # 获取当前配置
            current_config = self.get_config(config_name)
            
            # 深度合并更新
            updated_config = self._deep_merge(current_config, updates)
            
            # 验证更新后的配置
            if config_name in self._validators:
                if not self._validators[config_name](updated_config):
                    raise ConfigValidationError(f"Updated config validation failed for: {config_name}")
            
            # 更新缓存
            self._config_cache[config_name] = updated_config
            
            # 保存到文件
            if save_to_file:
                self._save_config_to_file(config_name, updated_config)
            
            logger.info(f"Config updated: {config_name}")
    
    def _deep_merge(self, base: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并字典"""
        result = base.copy()
        
        for key, value in updates.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _save_config_to_file(self, config_name: str, config_data: Dict[str, Any]):
        """保存配置到文件"""
        config_path = self.get_config_file_path(config_name)
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            # 更新时间戳
            self._file_timestamps[config_name] = config_path.stat().st_mtime
            logger.info(f"Config saved to file: {config_path}")
            
        except Exception as e:
            logger.error(f"Error saving config to file {config_path}: {e}")
            raise
    
    def reload_config(self, config_name: str):
        """手动重载指定配置"""
        self.get_config(config_name, force_reload=True)
    
    def reload_all_configs(self):
        """重载所有已缓存的配置"""
        with self._cache_lock:
            for config_name in list(self._config_cache.keys()):
                try:
                    self.reload_config(config_name)
                except Exception as e:
                    logger.error(f"Error reloading config {config_name}: {e}")
    
    def clear_cache(self):
        """清空配置缓存"""
        with self._cache_lock:
            self._config_cache.clear()
            self._file_timestamps.clear()
            logger.info("Config cache cleared")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        with self._cache_lock:
            return {
                "cached_configs": list(self._config_cache.keys()),
                "cache_size": len(self._config_cache),
                "file_timestamps": self._file_timestamps.copy(),
                "auto_reload": self._auto_reload,
                "reload_interval": self._reload_interval,
                "config_directory": str(self.config_dir)
            }
    
    def validate_all_configs(self) -> Dict[str, bool]:
        """验证所有配置文件"""
        results = {}
        
        for config_file in self.config_dir.glob("*.json"):
            config_name = config_file.stem
            try:
                self.get_config(config_name)
                results[config_name] = True
            except Exception as e:
                logger.error(f"Validation failed for {config_name}: {e}")
                results[config_name] = False
        
        return results
    
    def list_available_configs(self) -> List[str]:
        """列出所有可用的配置文件"""
        config_files = []
        
        # 文件系统中的配置文件
        for config_file in self.config_dir.glob("*.json"):
            config_files.append(config_file.stem)
        
        # 默认配置
        for config_name in self._default_configs.keys():
            if config_name not in config_files:
                config_files.append(config_name)
        
        return sorted(config_files)
    
    def enable_auto_reload(self, enabled: bool = True, interval: float = 1.0):
        """启用/禁用自动重载"""
        self._auto_reload = enabled
        self._reload_interval = interval
        logger.info(f"Auto reload {'enabled' if enabled else 'disabled'}, interval: {interval}s")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        # 清理资源
        pass


# 全局配置管理器实例
_config_manager_instance: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager_instance
    
    if _config_manager_instance is None:
        # 自动检测配置目录
        current_dir = Path(__file__).parent
        config_dir = current_dir
        
        _config_manager_instance = ConfigManager(config_dir)
        
        # 注册默认验证器
        _register_default_validators(_config_manager_instance)
    
    return _config_manager_instance


def _register_default_validators(config_manager: ConfigManager):
    """注册默认的配置验证器"""
    
    def validate_literature_categories(config: Dict[str, Any]) -> bool:
        """验证文献分类配置"""
        required_keys = ['categories', 'technology_tags', 'application_tags']
        return all(key in config for key in required_keys)
    
    def validate_terminology_mapping(config: Dict[str, Any]) -> bool:
        """验证术语映射配置"""
        return 'terminology_mapping' in config and isinstance(config['terminology_mapping'], dict)
    
    def validate_domain_keywords(config: Dict[str, Any]) -> bool:
        """验证领域关键词配置"""
        return 'domain_keywords' in config and isinstance(config['domain_keywords'], dict)
    
    def validate_platform_config(config: Dict[str, Any]) -> bool:
        """验证平台配置"""
        required_sections = ['platform_settings', 'api_endpoints', 'external_services']
        return all(section in config for section in required_sections)
    
    # 注册验证器
    config_manager.register_validator('literature_categories', validate_literature_categories)
    config_manager.register_validator('terminology_mapping', validate_terminology_mapping)
    config_manager.register_validator('domain_keywords', validate_domain_keywords)
    config_manager.register_validator('platform_config', validate_platform_config)


# 便捷函数
def get_config(config_name: str, force_reload: bool = False) -> Dict[str, Any]:
    """获取配置的便捷函数"""
    return get_config_manager().get_config(config_name, force_reload)


async def get_config_async(config_name: str, force_reload: bool = False) -> Dict[str, Any]:
    """异步获取配置的便捷函数"""
    return await get_config_manager().get_config_async(config_name, force_reload)


def get_config_section(config_name: str, section_path: str, default: Any = None) -> Any:
    """获取配置片段的便捷函数"""
    return get_config_manager().get_config_section(config_name, section_path, default)


async def get_config_section_async(config_name: str, section_path: str, default: Any = None) -> Any:
    """异步获取配置片段的便捷函数"""
    return await get_config_manager().get_config_section_async(config_name, section_path, default)


if __name__ == "__main__":
    # 测试代码
    config_manager = get_config_manager()
    
    print("Available configs:", config_manager.list_available_configs())
    print("Cache info:", config_manager.get_cache_info())
    print("Validation results:", config_manager.validate_all_configs())