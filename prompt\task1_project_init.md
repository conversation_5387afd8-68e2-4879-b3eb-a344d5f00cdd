# 任务1: 项目初始化和基础架构搭建

## 给Cursor/Augment的提示词

你是一个全栈开发专家，需要为"单细胞测序方案生成系统"创建完整的项目架构。

### 项目需求
创建一个AI驱动的单细胞测序实验方案推荐系统，包含：
- 对话式用户界面
- 智能需求分析
- 方案自动生成
- 成本估算和报告输出

### 技术栈要求
- **前端**: React 18 + TypeScript + Tailwind CSS + Vite
- **后端**: Node.js + Express + TypeScript
- **数据库**: MongoDB + Mongoose
- **AI集成**: OpenAI GPT-4 API
- **部署**: Docker容器化

### 具体任务

#### 1. 创建项目结构
```
single-cell-advisor/
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── services/
│   │   ├── types/
│   │   └── utils/
│   ├── public/
│   └── package.json
├── backend/
│   ├── src/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── services/
│   │   ├── middleware/
│   │   └── utils/
│   └── package.json
├── shared/
│   └── types/
├── docker-compose.yml
└── README.md
```

#### 2. 前端基础配置
- 使用Vite创建React+TypeScript项目
- 配置Tailwind CSS
- 设置ESLint和Prettier
- 配置路由系统（React Router）
- 设置状态管理（Zustand）

#### 3. 后端基础配置
- 创建Express+TypeScript服务器
- 配置CORS和基础中间件
- 设置MongoDB连接
- 创建基础路由结构
- 配置环境变量管理

#### 4. 共享类型定义
创建TypeScript接口定义：
```typescript
// 用户需求接口
interface UserRequirement {
  tissueType: string;
  sampleCount: number;
  sequencingDepth: string;
  researchGoal: string;
  budget?: number;
  timeline?: string;
}

// 产品信息接口
interface Product {
  id: string;
  name: string;
  category: string;
  specifications: Record<string, any>;
  price: number;
  applicableScenarios: string[];
}

// 实验方案接口
interface ExperimentalProtocol {
  id: string;
  name: string;
  steps: ProtocolStep[];
  estimatedCost: number;
  estimatedTime: string;
  requiredProducts: Product[];
}
```

#### 5. Docker配置
- 创建前端、后端和数据库的Docker配置
- 设置docker-compose.yml用于本地开发
- 配置开发环境的热重载

#### 6. 基础API端点
创建以下REST API端点：
- `POST /api/chat/message` - 处理对话消息
- `GET /api/products` - 获取产品列表
- `POST /api/protocols/generate` - 生成实验方案
- `GET /api/protocols/:id` - 获取特定方案

### 开发要求
1. 使用TypeScript严格模式
2. 遵循RESTful API设计原则
3. 实现错误处理和日志记录
4. 添加基础的单元测试配置
5. 确保代码可读性和可维护性

### 交付物
- 完整的项目脚手架
- 可运行的前后端基础框架
- Docker开发环境
- 详细的README.md文档
- 基础的API文档

请按照以上要求创建完整的项目结构和基础代码，确保项目可以正常启动并运行。
