# CellForge AI Backend

## 项目概述

CellForge AI Backend 是一个专为单细胞测序方案咨询设计的智能后端系统。该系统为前端 React/Next.js 应用提供强大的 API 服务，包括智能对话、文献资源管理、客户画像分析、网页画像分析等核心功能。

## 🚀 实施状态

### ✅ 已实现功能 (完成度: 85%)
- **用户认证系统** (100%): JWT认证、用户注册/登录、权限管理、密码安全
- **智能对话系统** (90%): AI驱动的专业咨询、安全提示词管理、模拟/真实AI切换
- **客户画像系统** (85%): 多维画像构建、行为分析、需求历史跟踪
- **网页画像分析** (80%): URL分析、关键词提取、快速画像生成
- **文献资源系统** (75%): 文献搜索、智能推荐、分类管理（基于模拟数据）
- **数据库架构** (90%): 完整的数据模型和关系设计（SQLite开发环境）

### 🔄 开发中功能 (完成度: 30%)
- **知识库管理** (40%): 基础搜索框架已完成，向量化存储待完善
- **WebSocket实时对话** (60%): 基础框架已实现，流式响应待优化
- **Redis缓存系统** (20%): 配置已完成，实际集成待实现

### ❌ 待实现功能 (完成度: 0%)
- **方案生成引擎**: 智能配置和成本估算
- **数据分析平台**: 业务指标和用户洞察
- **向量数据库集成**: ChromaDB语义搜索
- **数据库迁移系统**: Alembic迁移管理

## 核心功能

### 🤖 智能对话系统
- **专业 AI 咨询**: 基于 DeepSeek/GPT-4 的单细胞测序专业咨询
- **安全提示词管理**: 防止提示词泄露的安全机制
- **实时对话**: WebSocket 支持的流式对话体验
- **上下文理解**: 结合用户画像和历史对话的智能回复
- **知识库增强**: 集成文献资源的RAG技术

### 📚 文献资源系统
- **智能搜索**: 基于关键词和语义的文献检索
- **质量评估**: 影响因子、引用数、相关性综合评分
- **智能推荐**: 基于用户需求和画像的个性化推荐
- **多维分类**: 技术方法、应用领域、研究类型分类
- **实时统计**: 文献库覆盖度和使用统计

### 👥 客户画像系统
- **多维分析**: 研究背景、技术需求、预算范围、行为偏好
- **智能更新**: 基于对话和需求自动更新画像
- **预测分析**: 转化概率、生命周期价值、流失风险
- **个性化推荐**: 基于画像的定制化服务建议
- **行为追踪**: 对话记录、偏好分析、决策模式

### 🌐 网页画像分析
- **URL智能分析**: 自动抓取和分析目标网页内容
- **多源数据整合**: 支持网页、文本等多种数据源
- **快速画像生成**: 基于网页内容快速构建客户画像
- **风险评估**: 客户可信度和合作风险分析
- **行动建议**: 基于分析结果的具体行动建议

## 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   Backend       │
│   (Next.js)     │◄───┤   (FastAPI)     │◄───┤   Services      │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▲                       │
                                │                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vector DB     │    │   Cache Layer   │    │   Main Database │
│   (ChromaDB)    │    │   (Redis)       │    │   (SQLite/PG)   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心技术栈
- **框架**: FastAPI (Python) - 高性能异步 Web 框架
- **数据库**: SQLite (开发) + PostgreSQL (生产) + Redis (缓存) + ChromaDB (向量库)
- **AI/ML**: DeepSeek API, LangChain, Sentence Transformers
- **认证**: JWT + bcrypt
- **部署**: Docker + Docker Compose
- **监控**: 结构化日志 + Sentry

## 项目结构

```
cellforge-backend/
├── app/
│   ├── api/                    # API 路由
│   │   ├── endpoints/         # 具体端点实现
│   │   │   ├── auth.py       # 认证相关 ✅
│   │   │   ├── conversation.py  # 智能对话 ✅
│   │   │   ├── customer_profile.py  # 客户画像 ✅
│   │   │   ├── literature.py     # 文献资源 ✅
│   │   │   └── web_profile_analysis.py  # 网页画像分析 ✅
│   │   └── router.py          # 路由汇总 ✅
│   ├── core/                  # 核心配置
│   │   ├── config.py         # 应用配置 ✅
│   │   ├── database.py       # 数据库连接 ✅
│   │   ├── auth.py           # 认证逻辑 ✅
│   │   └── security.py       # 安全配置 ✅
│   ├── models/                # 数据模型
│   │   ├── user.py           # 用户模型 ✅
│   │   ├── conversation.py   # 对话模型 ✅
│   │   ├── customer_profile.py  # 客户画像模型 ✅
│   │   └── literature.py     # 文献模型 ✅
│   ├── schemas/               # Pydantic 模式
│   │   ├── user.py           # 用户模式 ✅
│   │   ├── conversation.py   # 对话模式 ✅
│   │   ├── customer_profile.py  # 客户画像模式 ✅
│   │   └── common.py         # 通用模式 ✅
│   ├── services/              # 业务逻辑
│   │   ├── ai_service.py     # AI 服务 ✅
│   │   ├── knowledge_service.py  # 知识库服务 ✅
│   │   ├── customer_profile_service.py   # 客户画像服务 ✅
│   │   ├── literature_service.py  # 文献服务 ✅
│   │   ├── web_profile_analyzer.py  # 网页画像分析 ✅
│   │   ├── prompt_manager.py     # 提示词管理 ✅
│   │   └── keyword_search_service.py  # 关键词搜索 ✅
│   ├── middleware/            # 中间件
│   │   └── logging.py        # 日志中间件 ✅
│   └── main.py               # 应用入口 ✅
├── docs/                      # 文档
│   └── personal-memory.md    # 项目记忆 ✅
├── docker-compose.yml         # Docker 编排 ✅
├── requirements.txt           # Python 依赖 ✅
├── cellforge.db              # SQLite 数据库文件 ✅
├── test_*.py                 # 测试脚本 ✅
├── create_*.py               # 数据库初始化脚本 ✅
└── README.md                  # 项目文档 ✅
```

## 快速开始

### 环境要求
- Python 3.11+
- PostgreSQL 14+ (生产环境)
- Redis 6+ (可选)
- Docker (可选)

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd cellforge-backend
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **环境配置**
```bash
# 创建环境变量文件（可选）
# 默认使用SQLite，无需额外配置
export OPENAI_API_KEY=your_deepseek_api_key  # 可选，用于真实AI服务
```

4. **数据库初始化**
```bash
# SQLite会自动创建，或手动初始化
python init_simple_db.py
```

5. **启动服务**
```bash
# 使用测试脚本启动
python test_startup.py

# 或直接启动
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Docker 部署

```bash
docker-compose up -d
```

## API 文档

启动服务后，访问以下地址查看 API 文档：
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 主要 API 端点

### 认证相关
- `POST /api/v1/auth/login` - 用户登录 ✅
- `POST /api/v1/auth/register` - 用户注册 ✅
- `GET /api/v1/auth/me` - 获取当前用户信息 ✅
- `POST /api/v1/auth/change-password` - 修改密码 ✅

### 智能对话
- `POST /api/v1/conversation/chat` - AI 对话 ✅
- `WS /api/v1/conversation/ws/{client_id}` - 实时对话 ✅
- `GET /api/v1/conversation/history` - 对话历史 ✅

### 客户画像
- `GET /api/v1/customer/profile/{user_id}` - 获取客户画像 ✅
- `GET /api/v1/customer/profile/{user_id}/details` - 获取详细画像 ✅
- `PUT /api/v1/customer/profile/{user_id}` - 更新客户画像 ✅
- `POST /api/v1/customer/analyze` - 分析客户行为 ✅
- `POST /api/v1/customer/requirements/{user_id}` - 基于需求更新画像 ✅

### 文献资源
- `POST /api/v1/literature/search` - 搜索文献 ✅
- `POST /api/v1/literature/recommendations` - 获取文献推荐 ✅
- `GET /api/v1/literature/categories` - 获取文献分类 ✅
- `GET /api/v1/literature/trending` - 获取热门文献 ✅

### 网页画像分析
- `POST /api/v1/profile-analysis/analyze-url` - 分析URL生成画像 ✅
- `POST /api/v1/profile-analysis/analyze-text` - 分析文本生成画像 ✅
- `POST /api/v1/profile-analysis/batch-analyze` - 批量分析 ✅

### 🚧 开发中的端点
- `POST /api/v1/knowledge/upload` - 上传知识文档
- `GET /api/v1/knowledge/search` - 搜索知识库
- `POST /api/v1/solution/generate` - 生成测序方案
- `GET /api/v1/analytics/dashboard` - 仪表板数据

## API 响应示例

### 成功响应示例
```json
// POST /api/v1/conversation/chat
{
  "message": "单细胞RNA测序是一种...",
  "confidence": 0.95,
  "sources": ["知识库", "文献资源"],
  "suggestions": ["了解更多技术细节", "查看相关案例"],
  "timestamp": "2024-01-01T12:00:00Z",
  "conversation_id": 123
}
```

### 错误响应示例
```json
// 认证失败
{
  "detail": "邮箱或密码错误",
  "status_code": 401
}

// 权限不足
{
  "detail": "没有权限访问该资源",
  "status_code": 403
}
```

## 与前端集成

### 认证集成
后端使用 JWT 认证，与前端 NextAuth 无缝集成：

```typescript
// 前端 API 调用示例
const response = await fetch('/api/v1/conversation/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    message: "我想了解单细胞 RNA 测序的基本流程",
    history: []
  })
})
```

### WebSocket 连接
```typescript
// 实时对话连接
const ws = new WebSocket(`ws://localhost:8000/api/v1/conversation/ws/${userId}`)
ws.onmessage = (event) => {
  const data = JSON.parse(event.data)
  if (data.type === 'chunk') {
    // 处理流式响应
  }
}
```

## 性能优化

### 缓存策略
- **Redis 缓存**: 用户会话、频繁查询结果
- **向量缓存**: 知识库检索结果缓存
- **应用缓存**: AI 回复缓存机制

### 异步处理
- **异步 AI 调用**: 非阻塞的 AI 服务调用
- **WebSocket 流式响应**: 实时数据传输
- **异步数据库操作**: SQLAlchemy异步支持

### 数据库优化
- **连接池**: 异步数据库连接池
- **索引优化**: 核心查询字段索引
- **分页查询**: 大数据集分页处理

## 安全考虑

### 认证授权
- JWT 令牌认证
- 角色基础访问控制 (RBAC)
- API 密钥管理

### 数据安全
- 密码加密存储
- 敏感数据脱敏
- HTTPS 强制加密

### API 安全
- 请求频率限制
- CORS 策略配置
- SQL 注入防护

## 监控与日志

### 应用监控
- 性能指标监控
- 错误率追踪
- 服务健康检查

### 业务监控
- API 调用统计
- 用户行为分析
- AI 服务质量监控

## 部署指南

### 生产环境部署
1. 使用 Docker 容器化部署
2. 负载均衡配置
3. 数据库主从复制
4. Redis 集群部署
5. 监控和日志收集

### 环境变量配置
```bash
# .env 生产环境配置
DATABASE_URL=postgresql+asyncpg://user:pass@db:5432/cellforge_prod
REDIS_URL=redis://redis:6379/0
OPENAI_API_KEY=your_deepseek_api_key
OPENAI_API_BASE=https://api.deepseek.com/v1
OPENAI_MODEL=deepseek-chat
SECRET_KEY=your_secret_key
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启 Pull Request

## 许可证

此项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者: CellForge AI Team
- 邮箱: <EMAIL>
- 文档: https://docs.cellforge-ai.com
- 支持: https://support.cellforge-ai.com