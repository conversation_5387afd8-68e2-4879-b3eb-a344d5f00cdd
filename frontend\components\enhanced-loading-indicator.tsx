"use client"

import React, { useState, useEffect } from 'react'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import { Sparkles, RefreshCw, AlertCircle, Clock } from 'lucide-react'
import { LoadingStage } from '@/lib/enhanced-error-handler'

interface EnhancedLoadingIndicatorProps {
  isLoading: boolean
  stage?: LoadingStage
  progress?: number
  estimatedTime?: number
  onCancel?: () => void
  onRetry?: () => void
  showRetryAfter?: number // 秒数，超过这个时间显示重试按钮
  allowCancel?: boolean // 是否允许取消操作
  className?: string
}

export function EnhancedLoadingIndicator({
  isLoading,
  stage = LoadingStage.ANALYZING,
  progress = 0,
  estimatedTime,
  onCancel,
  onRetry,
  showRetryAfter = 45, // 增加到45秒，给AI更多处理时间
  allowCancel = true,
  className = ''
}: EnhancedLoadingIndicatorProps) {
  const [elapsedTime, setElapsedTime] = useState(0)
  const [showRetry, setShowRetry] = useState(false)

  useEffect(() => {
    if (!isLoading) {
      setElapsedTime(0)
      setShowRetry(false)
      return
    }

    const interval = setInterval(() => {
      setElapsedTime(prev => {
        const newTime = prev + 1
        if (newTime >= showRetryAfter && onRetry) {
          setShowRetry(true)
        }
        return newTime
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [isLoading, showRetryAfter, onRetry])

  if (!isLoading) {
    return null
  }

  const formatTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}秒`
    }
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}分${remainingSeconds}秒`
  }

  const getStageIcon = (stage: LoadingStage) => {
    switch (stage) {
      case LoadingStage.ANALYZING:
        return <Sparkles className="h-4 w-4 text-blue-600" />
      case LoadingStage.SEARCHING_LITERATURE:
        return <RefreshCw className="h-4 w-4 text-green-600 animate-spin" />
      case LoadingStage.GENERATING_RESPONSE:
        return <Sparkles className="h-4 w-4 text-purple-600" />
      case LoadingStage.FORMATTING:
        return <RefreshCw className="h-4 w-4 text-orange-600 animate-spin" />
      default:
        return <Sparkles className="h-4 w-4 text-blue-600" />
    }
  }

  const getStageColor = (stage: LoadingStage) => {
    switch (stage) {
      case LoadingStage.ANALYZING:
        return 'from-blue-50 to-indigo-50 border-blue-200'
      case LoadingStage.SEARCHING_LITERATURE:
        return 'from-green-50 to-emerald-50 border-green-200'
      case LoadingStage.GENERATING_RESPONSE:
        return 'from-purple-50 to-violet-50 border-purple-200'
      case LoadingStage.FORMATTING:
        return 'from-orange-50 to-amber-50 border-orange-200'
      default:
        return 'from-slate-50 to-gray-50 border-slate-200'
    }
  }

  return (
    <div className={`bg-gradient-to-r ${getStageColor(stage)} border rounded-2xl p-4 shadow-sm ${className}`}>
      {/* 主要加载信息 */}
      <div className="flex items-center gap-3 mb-3">
        <div className="flex-shrink-0">
          {getStageIcon(stage)}
        </div>
        <div className="flex-1">
          <div className="text-sm font-medium text-slate-800">
            {stage}
          </div>
          <div className="text-xs text-slate-600 mt-1">
            已用时: {formatTime(elapsedTime)}
            {estimatedTime && (
              <span className="ml-2">
                预计还需: {formatTime(Math.max(0, estimatedTime - elapsedTime))}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* 进度条 */}
      {progress > 0 && (
        <div className="mb-3">
          <div className="flex items-center justify-between text-xs text-slate-600 mb-1">
            <span>处理进度</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {/* 动画指示器 */}
      <div className="flex items-center justify-center mb-3">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-xs text-slate-500">
          <Clock className="h-3 w-3" />
          <span>请耐心等待，AI正在为您生成专业建议</span>
        </div>

        <div className="flex items-center gap-2">
          {/* 取消按钮 - 只在允许且时间较长时显示 */}
          {allowCancel && onCancel && elapsedTime > 10 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              className="text-xs text-slate-500 hover:text-slate-700"
            >
              取消
            </Button>
          )}

          {/* 重试按钮 - 只在真正超时且有重试函数时显示 */}
          {showRetry && onRetry && elapsedTime > showRetryAfter && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="text-xs border-orange-200 text-orange-700 hover:bg-orange-50"
            >
              <AlertCircle className="h-3 w-3 mr-1" />
              重试
            </Button>
          )}
        </div>
      </div>

      {/* 超时警告 - 更温和的提示 */}
      {elapsedTime > showRetryAfter && (
        <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2 text-xs text-blue-700">
            <Clock className="h-3 w-3" />
            <span>
              AI正在深度分析您的需求，请稍等片刻。复杂的专业建议需要更多处理时间。
            </span>
          </div>
        </div>
      )}

      {/* 真正的错误警告 - 只在极长时间后显示 */}
      {elapsedTime > showRetryAfter + 30 && onRetry && (
        <div className="mt-2 p-2 bg-amber-50 border border-amber-200 rounded-lg">
          <div className="flex items-center gap-2 text-xs text-amber-700">
            <AlertCircle className="h-3 w-3" />
            <span>
              响应时间异常，可能是网络问题。您可以尝试重新发送请求。
            </span>
          </div>
        </div>
      )}
    </div>
  )
}

// 简化版本的加载指示器
export function SimpleLoadingIndicator({
  message = "正在处理...",
  className = ""
}: {
  message?: string
  className?: string
}) {
  return (
    <div className={`flex items-center gap-3 p-3 bg-slate-50 rounded-lg ${className}`}>
      <div className="flex space-x-1">
        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce"></div>
        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
      </div>
      <span className="text-sm text-slate-600">{message}</span>
    </div>
  )
}

// 带阶段的加载Hook
export function useStageLoading() {
  const [isLoading, setIsLoading] = useState(false)
  const [currentStage, setCurrentStage] = useState<LoadingStage>(LoadingStage.ANALYZING)
  const [progress, setProgress] = useState(0)

  const startLoading = (initialStage: LoadingStage = LoadingStage.ANALYZING) => {
    setIsLoading(true)
    setCurrentStage(initialStage)
    setProgress(0)
  }

  const updateStage = (stage: LoadingStage, progressValue?: number) => {
    setCurrentStage(stage)
    if (progressValue !== undefined) {
      setProgress(progressValue)
    }
  }

  const finishLoading = () => {
    setProgress(100)
    setTimeout(() => {
      setIsLoading(false)
      setProgress(0)
    }, 500) // 短暂显示完成状态
  }

  const cancelLoading = () => {
    setIsLoading(false)
    setProgress(0)
  }

  return {
    isLoading,
    currentStage,
    progress,
    startLoading,
    updateStage,
    finishLoading,
    cancelLoading
  }
}
