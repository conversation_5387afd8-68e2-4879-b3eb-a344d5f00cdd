"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Send, Paperclip, Mic, AlertCircle, MessageSquare, ClipboardList, ChevronLeft, ChevronRight, Sparkles, Brain, Zap, History, RotateCcw } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/contexts/auth-context"
import { conversationApi, literatureApi, solutionApi, intelligentRecommendationApi, smartLiteratureApi } from "@/lib/api"
import { generateIntelligentSearchSuggestions } from "@/lib/search-links"
import { intelligentSolutionGenerator } from "@/lib/intelligent-solution-generator"
import { errorHandler, LoadingStage } from "@/lib/enhanced-error-handler"
import { EnhancedLoadingIndicator, useStageLoading } from "./enhanced-loading-indicator"
import { toast } from "sonner"
import { ProgressiveRequirementCollector } from "./progressive-requirement-collector"
import { FormattedMessage } from "./formatted-message"
import { ConversationHistory } from "./conversation-history"

interface Message {
  id: string
  type: "user" | "ai"
  content: string
  timestamp: Date
  status: "sending" | "sent" | "read" | "error"
  confidence?: number
  sources?: string[]
  suggestions?: string[]
}

interface RequirementData {
  // 第1步：基础分类信息
  speciesType: string          // 物种类型 - 最重要的基础信息
  experimentType: string       // 实验类型 - 决定技术路线
  researchGoal: string        // 研究目标

  // 第2步：样本详细信息
  sampleType: string          // 样本类型（根据物种动态调整）
  sampleCount: string         // 样本数目
  sampleCondition: string     // 样本状态（新鲜/冷冻/固定等）
  sampleProcessing: string    // 样本处理方式
  cellCount: string           // 预期细胞数量
  cellViability: string       // 预期细胞活力

  // 第3步：项目规划
  budget: string              // 预算范围
  timeline: string            // 项目周期
  urgencyLevel: string        // 紧急程度

  // 第4步：技术细节和高级选项
  sequencingDepth: string     // 测序深度
  analysisType: string        // 分析类型
  dataAnalysisNeeds: string   // 数据分析需求
  specialRequirements: string // 特殊要求
  needsCellSorting: string    // 是否需要细胞分选

  // 自动推断信息
  recommendedPlatform: string
  estimatedCost: string
  riskFactors: string[]

  // 完成度
  completeness: number
  collectedFields: string[]
}

export function ConversationInterface() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState("")
  const [error, setError] = useState<string>("")
  const [conversationId, setConversationId] = useState<number | null>(null)
  const [requirements, setRequirements] = useState<RequirementData | null>(null)
  const [rightPanelCollapsed, setRightPanelCollapsed] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [showHistory, setShowHistory] = useState(false)
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)
  const [resetTrigger, setResetTrigger] = useState(0) // 用于触发需求收集助手重置
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { user } = useAuth()

  // 使用增强的加载状态管理
  const {
    isLoading,
    currentStage,
    progress,
    startLoading,
    updateStage,
    finishLoading,
    cancelLoading
  } = useStageLoading()

  // 响应式检测
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024)
      if (window.innerWidth < 1024) {
        setRightPanelCollapsed(true)
      }
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 初始化欢迎消息
  useEffect(() => {
    const welcomeMessage: Message = {
      id: "welcome",
      type: "ai",
      content: `您好${user?.name ? `，${user.name}` : ''}！👋

我是 **CellForge AI** 智能顾问，专注于单细胞测序技术咨询。

🔬 **我的专长**：
• 单细胞RNA测序 (scRNA-seq)
• 单细胞ATAC测序 (scATAC-seq)
• 多组学测序 (Multiome)
• 空间转录组学
• 实验设计与数据分析

💡 **开始方式**：
您可以直接提问，我会智能收集您的需求信息，或者使用右侧的需求收集助手快速填写项目信息。

请告诉我您的研究目标，我来为您制定最佳的技术方案！`,
      timestamp: new Date(),
      status: "read",
      confidence: 1.0,
      suggestions: [
        "我需要进行单细胞RNA测序",
        "请推荐适合的技术平台",
        "帮我分析项目成本",
        "查看成功案例"
      ]
    }
    setMessages([welcomeMessage])
  }, [user])

  // 自动保存对话到历史
  const autoSaveConversation = (messages: Message[]) => {
    if (messages.length >= 2) { // 至少有一轮对话
      try {
        const storageKey = `cellforge_conversations_${user?.id || 'anonymous'}`
        const existingSessions = JSON.parse(localStorage.getItem(storageKey) || '[]')

        // 生成对话标题
        const generateTitle = (msgs: Message[]): string => {
          const firstUserMessage = msgs.find(m => m.type === "user")
          if (firstUserMessage) {
            const content = firstUserMessage.content.trim()
            return content.length > 30 ? content.substring(0, 30) + "..." : content
          }
          return "新对话"
        }

        const sessionId = currentSessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        const now = new Date()

        const session = {
          id: sessionId,
          title: generateTitle(messages),
          messages,
          createdAt: currentSessionId ? existingSessions.find((s: any) => s.id === sessionId)?.createdAt || now : now,
          updatedAt: now,
          isStarred: false,
          isArchived: false,
          tags: [],
          messageCount: messages.length,
          lastMessage: messages[messages.length - 1]?.content.substring(0, 100) || ""
        }

        // 更新或添加会话
        const updatedSessions = currentSessionId
          ? existingSessions.map((s: any) => s.id === sessionId ? session : s)
          : [session, ...existingSessions].slice(0, 50) // 最多保存50个对话

        localStorage.setItem(storageKey, JSON.stringify(updatedSessions))
        setCurrentSessionId(sessionId)
      } catch (error) {
        console.error('自动保存对话失败:', error)
      }
    }
  }

  // 处理表单提交 - 使用增强的错误处理和分阶段加载
  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const userMessage = inputValue.trim()
    setInputValue("")
    setError("")

    // 创建用户消息
    const newUserMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: userMessage,
      timestamp: new Date(),
      status: "sending",
    }

    setMessages((prev) => [...prev, newUserMessage])

    // 构建对话历史和上下文（在分阶段操作外部准备）
    const conversationHistory = messages.map(msg => ({
      role: msg.type === "user" ? "user" : "assistant",
      content: msg.content
    }))

    const enhancedContext = {
      user_profile: {
        id: user?.id,
        organization: user?.organization || "",
        role: user?.role || "",
        expertise: user?.expertise_areas || "",
        research_interests: user?.research_interests || ""
      },
      requirements: requirements,
      conversation_history: conversationHistory,
      enable_literature_search: false
    }

    try {
      // 使用分阶段加载处理
      let aiApiResponse: any

      await errorHandler.handleStagedOperation([
        {
          stage: LoadingStage.ANALYZING,
          operation: async () => {
            // 模拟分析阶段
            await new Promise(resolve => setTimeout(resolve, 800))
            return { step: "analyzed" }
          }
        },
        {
          stage: LoadingStage.SEARCHING_LITERATURE,
          operation: async () => {
            // 模拟文献搜索
            await new Promise(resolve => setTimeout(resolve, 1200))
            return { step: "literature_searched" }
          }
        },
        {
          stage: LoadingStage.GENERATING_RESPONSE,
          operation: async () => {
            // 实际调用AI API
            aiApiResponse = await conversationApi.sendMessage({
              message: userMessage,
              conversation_id: conversationId || undefined,
              conversation_type: "enhanced",
              history: conversationHistory,
              context: enhancedContext
            })
            return aiApiResponse
          }
        },
        {
          stage: LoadingStage.FORMATTING,
          operation: async () => {
            // 模拟格式化处理
            await new Promise(resolve => setTimeout(resolve, 500))
            return { step: "formatted" }
          }
        }
      ],
      (stage, progress) => {
        if (!isLoading) {
          startLoading(stage)
        } else {
          updateStage(stage, progress)
        }
      },
      {
        maxRetries: 1, // 减少重试次数，避免频繁重试
        retryDelay: 3000, // 增加重试延迟到3秒
        userFriendlyMessage: "AI服务暂时不可用，请稍后重试",
        showToast: false, // 不显示重试toast，减少打扰
        onRetry: (attempt, error) => {
          // 只在网络错误时才显示重试信息
          if (error.message?.includes('fetch') || error.message?.includes('网络')) {
            toast.info(`检测到网络问题，正在重试...`)
          }
        }
      })

      // aiApiResponse已经在GENERATING_RESPONSE阶段中设置

      // 更新用户消息状态为已发送
      setMessages((prev) =>
        prev.map(msg =>
          msg.id === newUserMessage.id
            ? { ...msg, status: "sent" as const }
            : msg
        )
      )

      // 创建AI回复消息
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: "ai",
        content: aiApiResponse.message,
        timestamp: new Date(),
        status: "read",
        confidence: aiApiResponse.confidence,
        sources: aiApiResponse.sources,
        suggestions: aiApiResponse.suggestions
      }

      setMessages((prev) => {
        const newMessages = [...prev, aiResponse]
        // 自动保存对话
        setTimeout(() => autoSaveConversation(newMessages), 1000)
        return newMessages
      })

      finishLoading()

    } catch (err: any) {
      console.error("发送消息失败:", err)

      // 更新用户消息状态为错误
      setMessages((prev) =>
        prev.map(msg =>
          msg.id === newUserMessage.id
            ? { ...msg, status: "error" as const }
            : msg
        )
      )

      // 错误已经由errorHandler处理，这里只需要更新UI状态
      setError("发送消息失败，请稍后重试")
      cancelLoading()
    }
  }

  // 处理需求变更
  const handleRequirementsChange = (newRequirements: RequirementData) => {
    setRequirements(newRequirements)
  }

  // 处理需求提交 - 使用新的方案生成API
  const handleRequirementsSubmit = async (requirements: RequirementData) => {
    // 生成需求总结消息
    const requirementSummary = generateRequirementSummary(requirements)

    // 添加用户消息到对话中
    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: requirementSummary,
      timestamp: new Date(),
      status: "sending",
    }

    setMessages((prev) => [...prev, userMessage])

    try {
      // 使用分阶段加载处理需求提交，但现在专注于方案生成
      let solutionResponse: any
      let literatureResults: any = null

      await errorHandler.handleStagedOperation([
        {
          stage: LoadingStage.ANALYZING,
          operation: async () => {
            // 验证需求完整性
            const validation = await solutionApi.validateRequirements(requirements)
            return validation
          }
        },
        {
          stage: LoadingStage.SEARCHING_LITERATURE,
          operation: async () => {
            // 文献搜索功能已移除，直接返回null
            return null
          }
        },
        {
          stage: LoadingStage.GENERATING_RESPONSE,
          operation: async () => {
            // 优先调用综合方案框架API（按设计文档要求）
            try {
              console.log('🚀 开始调用综合方案框架API...')
              console.log('请求数据:', {
                requirements: requirements,
                user_message: requirementSummary,
                framework_template: 'standard',
                enable_literature_search: false
              })

              const frameworkResponse = await solutionApi.generateComprehensiveFramework({
                requirements: requirements,
                user_message: requirementSummary,
                framework_template: 'standard',
                enable_literature_search: false
              })

              console.log('✅ 综合方案框架API响应:', frameworkResponse)

              if (frameworkResponse.success) {
                // 将综合方案框架结果转换为前端需要的格式
                solutionResponse = {
                  solution_id: frameworkResponse.framework_id,
                  generated_at: frameworkResponse.generation_time,
                  client_requirements: requirements,
                  comprehensive_framework: frameworkResponse.data, // 完整框架数据
                  recommended_solution: {
                    platform: "单细胞测序解决方案",
                    reasoning: "基于设计文档的完整方案框架，包含方案概览、研究意图分析、关键要素、文献推荐、平台对比和实施规划",
                    specifications: frameworkResponse.data.solution_overview
                  },
                  cost_analysis: frameworkResponse.data.solution_overview?.cost_analysis || {},
                  risk_assessment: frameworkResponse.data.risk_assessment || {},
                  timeline: frameworkResponse.data.implementation_plan?.timeline || {},
                  deliverables: [
                    "方案概览卡片",
                    "研究意图精准搜索链接",
                    "关键要素分析",
                    "智能文献推荐",
                    "平台对比分析",
                    "项目实施规划"
                  ],
                  next_steps: [
                    "查看完整的方案框架分析",
                    "使用精准搜索链接获取文献",
                    "根据平台对比选择技术方案"
                  ],
                  contact_info: {
                    email: "<EMAIL>",
                    phone: "************"
                  },
                  framework_features: {
                    comprehensive_framework: true,
                    research_intent_analysis: true,
                    precision_search_links: true,
                    literature_recommendations: true,
                    platform_comparison: true
                  }
                }
              } else {
                throw new Error('综合方案框架API调用失败')
              }
            } catch (frameworkError) {
              console.error('❌ 综合方案框架API失败:', frameworkError)
              console.error('错误详情:', {
                message: frameworkError.message,
                stack: frameworkError.stack,
                name: frameworkError.name
              })

              // 使用简化的降级方案
              solutionResponse = {
                solution_id: `fallback_${Date.now()}`,
                generated_at: new Date().toISOString(),
                client_requirements: requirements,
                recommended_solution: {
                  platform: "CellForge AI 基础方案",
                  reasoning: "由于技术原因，当前提供基础方案。我们的专家将为您进行人工分析，确保方案的准确性和个性化。",
                  specifications: {}
                },
                cost_analysis: {},
                risk_assessment: {},
                timeline: {},
                deliverables: [
                  "人工专家分析报告",
                  "个性化技术方案设计",
                  "详细成本效益分析"
                ],
                next_steps: [
                  "联系专家进行人工分析",
                  "获取个性化方案设计",
                  "确定最优技术路线"
                ],
                contact_info: {
                  email: "<EMAIL>",
                  phone: "************"
                },
                fallback_notice: {
                  message: "为确保方案质量，建议联系专家进行人工分析",
                  expert_contact: true,
                  reason: "技术服务暂时不可用"
                }
              }
            }
            return solutionResponse
          }
        },
        {
          stage: LoadingStage.FORMATTING,
          operation: async () => {
            // 格式化方案内容，如果有文献结果则包含进去
            await new Promise(resolve => setTimeout(resolve, 400))
            return { step: "solution_formatted" }
          }
        }
      ],
      (stage, progress) => {
        if (!isLoading) {
          startLoading(stage)
        } else {
          updateStage(stage, progress)
        }
      },
      {
        maxRetries: 1,
        retryDelay: 3000,
        userFriendlyMessage: "方案生成失败，请稍后重试",
        showToast: false,
        onRetry: (attempt, error) => {
          if (error.message?.includes('fetch') || error.message?.includes('网络')) {
            toast.info(`网络问题，正在重试生成方案...`)
          }
        }
      })

      // 更新用户消息状态
      setMessages((prev) =>
        prev.map(msg =>
          msg.id === userMessage.id
            ? { ...msg, status: "sent" as const }
            : msg
        )
      )

      // 生成方案展示的AI回复，包含文献搜索结果（如果有）
      const solutionContent = formatSolutionResponse(solutionResponse, literatureResults)
      
      // 更新sources以反映实际的数据来源
      let sources = ["方案生成系统", "技术知识库", "成本分析引擎"]
      
      // 根据不同的方案类型使用不同的数据来源
      if (solutionResponse.ai_analysis_features?.intent_analyzed) {
        sources = ["AI意图分析引擎", "用户行为分析", "个性化算法", "专业知识库"]
        if (solutionResponse.ai_analysis_features.analysis_sources) {
          const validSources = solutionResponse.ai_analysis_features.analysis_sources.map((source: any) => 
            typeof source === 'string' ? source : source?.name || JSON.stringify(source)
          )
          sources = sources.concat(validSources)
        }
      } else if (solutionResponse.solution_id?.includes('intelligent') || solutionResponse.intelligent_features) {
        sources = ["AI智能推荐引擎", "专业知识库", "热点文献发现", "技术规格数据库", "风险评估算法"]
      } else if (solutionResponse.fallback_notice) {
        sources = ["基础知识库", "专家经验库", "标准方案模板"]
      }
      
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: "ai",
        content: solutionContent,
        timestamp: new Date(),
        status: "read",
        confidence: solutionResponse.ai_analysis_features?.confidence_score || 
                   (literatureResults ? 0.98 : 
                    solutionResponse.fallback_notice ? 0.70 : 0.95),
        sources: sources,
        suggestions: [
          "查看详细的成本分析",
          "了解项目时间规划",
          "获取技术平台对比",
          "联系专家进行咨询"
        ]
      }

      setMessages((prev) => {
        const newMessages = [...prev, aiResponse]
        // 自动保存对话
        setTimeout(() => autoSaveConversation(newMessages), 1000)
        return newMessages
      })

      finishLoading()
      toast.success("已基于您的需求生成专业方案！")

    } catch (err: any) {
      console.error("生成建议失败:", err)

      // 更新用户消息状态为错误
      setMessages((prev) =>
        prev.map(msg =>
          msg.id === userMessage.id
            ? { ...msg, status: "error" as const }
            : msg
        )
      )

      cancelLoading()
      // 错误已经由errorHandler处理
    }
  }

  // 生成需求总结 - 更新以匹配新的字段结构
  const generateRequirementSummary = (reqs: RequirementData): string => {
    const sections = []

    sections.push("📋 **项目需求总结**\n")

    // 基础分类信息
    if (reqs.speciesType) sections.push(`🧬 **物种类型**: ${reqs.speciesType}`)
    if (reqs.experimentType) sections.push(`🔬 **实验类型**: ${reqs.experimentType}`)
    if (reqs.researchGoal) sections.push(`🎯 **研究目标**: ${reqs.researchGoal}`)

    // 样本信息
    if (reqs.sampleType || reqs.sampleCount || reqs.sampleCondition || reqs.cellCount) {
      sections.push("\n🧪 **样本信息**")
      if (reqs.sampleType) sections.push(`• 样本类型: ${reqs.sampleType}`)
      if (reqs.sampleCount) sections.push(`• 样本数目: ${reqs.sampleCount}`)
      if (reqs.sampleCondition) sections.push(`• 样本状态: ${reqs.sampleCondition}`)
      if (reqs.sampleProcessing) sections.push(`• 处理方式: ${reqs.sampleProcessing}`)
      if (reqs.cellCount) sections.push(`• 细胞数量: ${reqs.cellCount}`)
      if (reqs.cellViability) sections.push(`• 细胞活力: ${reqs.cellViability}`)
    }

    // 项目规划
    if (reqs.budget || reqs.timeline || reqs.urgencyLevel) {
      sections.push("\n📅 **项目规划**")
      if (reqs.budget) sections.push(`• 预算范围: ${reqs.budget}`)
      if (reqs.timeline) sections.push(`• 项目周期: ${reqs.timeline}`)
      if (reqs.urgencyLevel) sections.push(`• 紧急程度: ${reqs.urgencyLevel}`)
    }

    // 技术细节
    if (reqs.sequencingDepth || reqs.analysisType || reqs.dataAnalysisNeeds || reqs.needsCellSorting || reqs.specialRequirements) {
      sections.push("\n⚙️ **技术细节**")
      if (reqs.sequencingDepth) sections.push(`• 测序深度: ${reqs.sequencingDepth}`)
      if (reqs.analysisType) sections.push(`• 分析类型: ${reqs.analysisType}`)
      if (reqs.dataAnalysisNeeds) sections.push(`• 数据分析需求: ${reqs.dataAnalysisNeeds}`)
      if (reqs.needsCellSorting) sections.push(`• 细胞分选: ${reqs.needsCellSorting}`)
      if (reqs.specialRequirements) sections.push(`• 特殊要求: ${reqs.specialRequirements}`)
    }

    sections.push("\n请为我生成详细的技术方案建议。")

    return sections.join("\n")
  }

  // 格式化方案响应，包含文献搜索结果和个性化信息
  const formatSolutionResponse = (solutionResponse: any, literatureResults?: any): string => {
    if (!solutionResponse) return "抱歉，方案生成失败。"

    const {
      solution_id,
      client_requirements,
      recommended_solution,
      cost_analysis,
      risk_assessment,
      timeline,
      deliverables,
      next_steps,
      contact_info,
      intelligent_features,
      intent_analysis,
      research_domain,
      personalization_level,
      ai_analysis_features,
      fallback_notice
    } = solutionResponse

    const sections = []

    // 检测是否为个性化推荐或AI分析结果
    const isPersonalized = personalization_level === "high" || intent_analysis
    const isIntelligentRecommendation = solution_id?.includes('intelligent') || intelligent_features
    const isAiAnalyzed = ai_analysis_features?.intent_analyzed || solution_id?.includes('ai_analyzed')
    const isFallback = fallback_notice || solution_id?.includes('fallback')

    // 根据个性化程度和研究领域定制标题
    if (isFallback) {
      sections.push("⚠️ **CellForge AI 基础方案**")
      sections.push("⏰ 系统正在升级，专家人工分析中...")
      sections.push(`方案编号: ${solution_id}`)
      sections.push("")
      
      // 添加回退通知
      if (fallback_notice) {
        sections.push("📢 **重要提示**")
        sections.push(`• ${fallback_notice.message}`)
        if (fallback_notice.expert_contact) {
          sections.push("• 专家团队将在2小时内与您联系")
        }
        sections.push(`• 原因: ${fallback_notice.reason}`)
        sections.push("")
      }
    } else if (isAiAnalyzed && ai_analysis_features) {
      sections.push("🧠 **CellForge AI 智能意图分析方案**")
      sections.push("✨ 基于深度用户意图分析的个性化推荐")
      sections.push(`方案编号: ${solution_id}`)
      sections.push("")
      
      // 添加AI分析摘要
      sections.push("🔍 **AI意图分析结果**")
      sections.push(`• 分析置信度：${Math.round((ai_analysis_features.confidence_score || 0.85) * 100)}%`)
      if (ai_analysis_features.analysis_sources?.length > 0) {
        const validAnalysisSources = ai_analysis_features.analysis_sources.map((source: any) => 
          typeof source === 'string' ? source : source?.name || JSON.stringify(source)
        )
        sections.push(`• 分析来源：${validAnalysisSources.join(", ")}`)
      }
      sections.push("• 个性化程度：高度定制")
      sections.push("")
    } else if (isPersonalized && research_domain) {
      sections.push(`🎯 **CellForge AI ${research_domain}专业方案**`)
      sections.push(`✨ 基于意图分析的个性化推荐`)
      sections.push(`方案编号: ${solution_id}`)
      sections.push("")

      // 添加意图分析摘要
      if (intent_analysis) {
        sections.push("🧠 **用户意图分析**")
        sections.push(`• 研究领域：${intent_analysis.research_domain || '通用研究'}`)
        sections.push(`• 预算类别：${intent_analysis.budget_category || '标准型'}`)
        sections.push(`• 紧急程度：${intent_analysis.urgency_level || '常规'}`)
        sections.push(`• 技术偏好：${intent_analysis.technical_preference || '10x Genomics'}`)
        sections.push(`• 置信度：${Math.round((intent_analysis.confidence_score || 0.85) * 100)}%`)
        sections.push("")
      }
    } else if (isIntelligentRecommendation) {
      sections.push("🎯 **CellForge AI 智能推荐方案**")
      sections.push(`方案编号: ${solution_id}`)
      sections.push("")
    } else {
      sections.push("🎯 **CellForge AI 专业方案**")
      sections.push(`方案编号: ${solution_id}`)
      sections.push("")
    }





    // 风险评估 - 支持新格式和旧格式
    if (risk_assessment) {
      sections.push("⚠️ **风险评估**")
      
      if (risk_assessment.success_probability) {
        sections.push(`**成功概率**: ${risk_assessment.success_probability}`)
      }
      
      if (risk_assessment.overall_risk_level) {
        sections.push(`**整体风险等级**: ${risk_assessment.overall_risk_level}`)
      }
      
      // 新格式技术风险
      if (risk_assessment.technical_risks?.length > 0) {
        sections.push("**主要风险**:")
        risk_assessment.technical_risks.forEach((risk: any) => {
          if (typeof risk === 'object') {
            sections.push(`• ${risk.risk} (${risk.probability})`)
            sections.push(`  影响: ${risk.impact}`)
            sections.push(`  缓解: ${risk.mitigation}`)
          } else {
            sections.push(`• ${risk}`)
          }
        })
      }
      // 旧格式风险
      else if (risk_assessment.identified_risks?.length > 0) {
        sections.push("**主要风险**:")
        risk_assessment.identified_risks.forEach((risk: string) => {
          sections.push(`• ${risk}`)
        })
      }
      sections.push("")
    }



    // 添加智能推荐特性（如果是智能推荐）
    if (intelligent_features) {
      // 智能文献推荐 - 修复数据结构访问
      if (intelligent_features.hot_papers?.length > 0) {
        sections.push("🧬 **智能文献推荐报告**")
        sections.push("")
        sections.push("🔥 **热点文献发现**:")
        intelligent_features.hot_papers.forEach((paper: any, index: number) => {
          const pubmedLink = paper.doi 
            ? `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(paper.doi)}[DOI]`
            : `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(paper.title)}`
          const scholarLink = `https://scholar.google.com/scholar?q=${encodeURIComponent(paper.title)}`
          
          sections.push(`**[${index + 1}] ${paper.title}**`)
          sections.push(`📖 ${paper.journal} ${paper.impact_factor ? `(IF: ${paper.impact_factor})` : ''}`)
          sections.push(`💡 ${paper.reason}`)
          sections.push(`🔗 [PubMed](${pubmedLink}) | [Google Scholar](${scholarLink})`)
          sections.push("")
        })
      }
      
      // 智能关键词扩展 - 修复数据结构访问并添加搜索链接
      if (intelligent_features.expanded_keywords) {
        const keywords = intelligent_features.expanded_keywords
        
        if (keywords.semantic_expansion?.length > 0 || keywords.trending_terms?.length > 0) {
          sections.push("🔍 **智能关键词扩展与搜索链接**:")
          sections.push("")
          
          if (keywords.semantic_expansion?.length > 0) {
            sections.push("**语义扩展关键词**:")
            keywords.semantic_expansion.slice(0, 5).forEach((keyword: string) => {
              const pubmedLink = `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(keyword)}`
              const scholarLink = `https://scholar.google.com/scholar?q=${encodeURIComponent(keyword)}`
              sections.push(`• ${keyword} - [PubMed](${pubmedLink}) | [Scholar](${scholarLink})`)
            })
            sections.push("")
          }
          
          if (keywords.trending_terms?.length > 0) {
            sections.push("**热点趋势关键词**:")
            keywords.trending_terms.slice(0, 5).forEach((keyword: string) => {
              const pubmedLink = `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(keyword)}`
              const scholarLink = `https://scholar.google.com/scholar?q=${encodeURIComponent(keyword)}`
              sections.push(`• ${keyword} - [PubMed](${pubmedLink}) | [Scholar](${scholarLink})`)
            })
            sections.push("")
          }
          
          if (keywords.molecular_targets?.length > 0) {
            sections.push("**分子靶点关键词**:")
            keywords.molecular_targets.slice(0, 3).forEach((keyword: string) => {
              const pubmedLink = `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(keyword)}`
              sections.push(`• ${keyword} - [PubMed](${pubmedLink})`)
            })
            sections.push("")
          }
        }
      }
      
      // 外部文献搜索结果
      if (intelligent_features.literature_recommendations?.combined_results?.length > 0) {
        sections.push("📚 **相关文献搜索结果**:")
        sections.push("")
        intelligent_features.literature_recommendations.combined_results.slice(0, 3).forEach((paper: any, index: number) => {
          const pubmedLink = paper.pubmed_id 
            ? `https://pubmed.ncbi.nlm.nih.gov/${paper.pubmed_id}/`
            : paper.doi 
            ? `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(paper.doi)}[DOI]`
            : `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(paper.title)}`
          
          sections.push(`**[${index + 1}] ${paper.title}**`)
          sections.push(`📖 ${paper.journal} ${paper.publication_year ? `(${paper.publication_year})` : ''}`)
          if (paper.authors?.length > 0) {
            sections.push(`👥 ${paper.authors.slice(0, 3).join(', ')}${paper.authors.length > 3 ? ' et al.' : ''}`)
          }
          if (paper.abstract) {
            sections.push(`📝 ${paper.abstract.substring(0, 200)}...`)
          }
          sections.push(`🔗 [查看原文](${pubmedLink}) | 来源: ${paper.source || 'Unknown'}`)
          sections.push("")
        })
      }
      
      // AI智能洞察
      if (intelligent_features.smart_insights?.length > 0) {
        sections.push("💡 **AI智能洞察**")
        intelligent_features.smart_insights.forEach((insight: string) => {
          sections.push(`• ${insight}`)
        })
        sections.push("")
      }
      
      // 专家合作机会推荐
      if (intelligent_features.collaboration_opportunities?.length > 0) {
        sections.push("🤝 **专家合作机会推荐**")
        intelligent_features.collaboration_opportunities.forEach((collab: any) => {
          sections.push(`• **${collab.institution}** - ${collab.expert}`)
          sections.push(`  ${collab.collaboration_type}`)
        })
        sections.push("")
      }
      
      // 智能文献检索建议
      if (intelligent_features.search_suggestions) {
        sections.push("🔍 **智能文献检索建议**")
        sections.push("")
        
        sections.push("📌 **推荐检索关键词**:")
        intelligent_features.search_suggestions.primaryKeywords.forEach((keyword: string, index: number) => {
          sections.push(`${index + 1}. **${keyword}**`)
          
          // 为每个主要关键词添加搜索链接
          const links = intelligent_features.search_suggestions.searchLinks[keyword]
          if (links && links.length > 0) {
            const linkTexts = links.map((link: any) => 
              `[${link.emoji} ${link.name}](${link.url})`
            ).join(' • ')
            sections.push(`   ${linkTexts}`)
          }
          sections.push("")
        })
        
        if (intelligent_features.search_suggestions.secondaryKeywords.length > 0) {
          sections.push("🔖 **扩展检索关键词**:")
          intelligent_features.search_suggestions.secondaryKeywords.slice(0, 4).forEach((keyword: string) => {
            sections.push(`• ${keyword}`)
          })
          sections.push("")
        }
        
        sections.push("💡 **检索建议**:")
        sections.push("• 建议先用主要关键词在PubMed中检索最新文献")
        sections.push("• 使用Google Scholar查找引用次数高的经典文献")  
        sections.push("• Semantic Scholar可发现相关的综述文章")
        sections.push("• 组合关键词可获得更精准的搜索结果")
        sections.push("")
      }
    }

    // 添加文献搜索结果（如果启用了文献搜索）
    if (literatureResults && literatureResults.literature_results?.length > 0) {
      sections.push("")
      sections.push("📚 **相关文献支撑**")
      sections.push("")
      
      // 显示搜索到的文献（最多显示3篇）
      const papersToShow = literatureResults.literature_results.slice(0, 3)
      papersToShow.forEach((paper: any, index: number) => {
        sections.push(`**[${index + 1}] ${paper.title || '文献标题'}**`)
        
        if (paper.authors && paper.authors.length > 0) {
          const authorList = paper.authors.slice(0, 3).join(", ")
          const moreAuthors = paper.authors.length > 3 ? " 等" : ""
          sections.push(`*${authorList}${moreAuthors}*`)
        }
        
        if (paper.journal) {
          sections.push(`📖 ${paper.journal}${paper.publication_year ? ` (${paper.publication_year})` : ''}`)
        }
        
        if (paper.key_findings) {
          sections.push(`💡 **核心发现**: ${paper.key_findings}`)
        }
        
        if (paper.methodology_summary) {
          sections.push(`🔬 **方法**: ${paper.methodology_summary}`)
        }
        
        sections.push("")
      })
      
      // 显示搜索统计信息
      if (literatureResults.total_papers > 3) {
        sections.push(`📊 **搜索统计**: 共找到 ${literatureResults.total_papers} 篇相关文献，已显示最相关的 ${papersToShow.length} 篇`)
        sections.push("")
      }
      
      // 显示数据来源
      if (literatureResults.sources && literatureResults.sources.length > 0) {
        sections.push(`🔍 **数据来源**: ${literatureResults.sources.join(", ")}`)
        sections.push("")
      }
    }

    // 处理综合方案框架（按用户要求的新结构）
    const { comprehensive_framework, framework_features } = solutionResponse
    if (comprehensive_framework && framework_features?.comprehensive_framework) {
      sections.push("")
      sections.push("🎯 **单细胞测序解决方案**")
      sections.push("")

      // 添加简化方案的JSON数据供前端组件解析
      sections.push("```json")
      sections.push(JSON.stringify({
        type: "streamlined_solution",
        data: comprehensive_framework
      }, null, 2))
      sections.push("```")
      sections.push("")
    }

    // 处理综合解决方案（5个核心功能）- 作为降级方案
    const { comprehensive_solution, enhanced_features } = solutionResponse
    if (comprehensive_solution && enhanced_features?.five_core_functions) {
      sections.push("")
      sections.push("🎯 **CellForge AI 5大核心功能综合分析**")
      sections.push("")

      // 添加综合解决方案的JSON数据供前端组件解析
      sections.push("```json")
      sections.push(JSON.stringify({
        type: "comprehensive_solution",
        data: comprehensive_solution
      }, null, 2))
      sections.push("```")

      sections.push("")
      sections.push("💡 **功能说明**:")
      sections.push("• **个性化方案**: 基于您的需求定制的专业技术方案")
      sections.push("• **文献推荐**: 领域相关的高质量学术文献")
      sections.push("• **搜索关键词**: 优化的学术检索关键词组合")
      sections.push("• **痛点分析**: 该领域常见技术挑战及解决方案")
      sections.push("• **风险评估**: 项目实施风险评估及缓解策略")
      sections.push("")
    }

    sections.push("---")
    if (isFallback) {
      sections.push("🧬 **CellForge AI** - 您的单细胞测序专业伙伴")
      sections.push("")
      sections.push("**服务状态**: 临时方案 • **专家支持**: 人工分析中 • **预计响应**: 2小时内")
    } else if (isAiAnalyzed) {
      sections.push("🧬 **CellForge AI** - 您的智能单细胞测序专业伙伴")
      sections.push("")
      const confidence = ai_analysis_features?.confidence_score ? Math.round(ai_analysis_features.confidence_score * 100) : 95
      sections.push(`**AI分析置信度**: ${confidence}% • **来源**: AI意图分析引擎, 专业知识库, 个性化算法`)
    } else if (isIntelligentRecommendation) {
      sections.push("🧬 **CellForge AI** - 您的智能单细胞测序专业伙伴")
      sections.push("")
      sections.push("**置信度**: 95% • **来源**: AI智能推荐引擎, 专业知识库, 热点文献发现")
    } else {
      sections.push("🧬 **CellForge AI** - 您的单细胞测序专业伙伴")
    }

    return sections.join("\n")
  }

  // 基于需求生成AI建议 - 使用增强的加载状态
  const generateRequirementBasedSuggestion = async (reqs: RequirementData) => {
    const suggestionMessage = `基于您填写的需求信息：
- 研究目标：${reqs.researchGoal}
- 样本类型：${reqs.sampleType}
- 细胞数量：${reqs.cellCount}
- 预算范围：${reqs.budget}
- 项目周期：${reqs.timeline}

我来为您生成专业的技术方案建议。`

    try {
      // 使用分阶段加载
      let apiResponse: any

      await errorHandler.handleStagedOperation([
        {
          stage: LoadingStage.ANALYZING,
          operation: async () => {
            await new Promise(resolve => setTimeout(resolve, 500))
            return { step: "analyzed" }
          }
        },
        {
          stage: LoadingStage.GENERATING_RESPONSE,
          operation: async () => {
            apiResponse = await conversationApi.sendMessage({
              message: suggestionMessage,
              conversation_type: "requirement_based",
              context: {
                user_profile: {
                  id: user?.id,
                  organization: user?.organization || "",
                  role: user?.role || ""
                },
                requirements: reqs
              }
            })
            return apiResponse
          }
        }
      ],
      (stage, progress) => {
        if (!isLoading) {
          startLoading(stage)
        } else {
          updateStage(stage, progress)
        }
      },
      {
        maxRetries: 1,
        retryDelay: 3000,
        userFriendlyMessage: "生成建议失败，请稍后重试",
        showToast: false
      })

      // apiResponse已经在GENERATING_RESPONSE阶段中设置

      const aiSuggestion: Message = {
        id: Date.now().toString(),
        type: "ai",
        content: apiResponse.message,
        timestamp: new Date(),
        status: "read",
        confidence: apiResponse.confidence,
        sources: apiResponse.sources,
        suggestions: apiResponse.suggestions
      }

      setMessages((prev) => [...prev, aiSuggestion])
      finishLoading()
      toast.success("已基于您的需求生成专业建议！")

    } catch (err) {
      console.error("生成建议失败:", err)
      cancelLoading()
      // 错误已经由errorHandler处理
    }
  }

  // 获取缺失的关键需求字段 - 更新以匹配新的必需字段
  const getMissingRequirements = () => {
    const requiredFields = [
      { key: "speciesType", label: "物种类型", question: "您研究的是什么物种？" },
      { key: "experimentType", label: "实验类型", question: "您需要什么类型的单细胞实验？" },
      { key: "researchGoal", label: "研究目标", question: "您的研究目标是什么？" },
      { key: "sampleType", label: "样本类型", question: "您使用什么类型的样本？" },
      { key: "budget", label: "预算范围", question: "您的项目预算大概是多少？" }
    ]

    if (!requirements) return requiredFields

    return requiredFields.filter(field => !requirements[field.key as keyof RequirementData])
  }

  // 获取智能建议（合并AI建议和快速回复）
  const getSmartSuggestions = () => {
    // 优先显示AI回复中的建议
    const lastMessage = messages[messages.length - 1]
    if (lastMessage?.type === "ai" && lastMessage.suggestions && lastMessage.suggestions.length > 0) {
      return {
        type: "ai_suggestions",
        title: "AI智能建议",
        icon: "✨",
        items: lastMessage.suggestions.slice(0, 4).map(suggestion => ({
          text: suggestion,
          icon: "💡"
        }))
      }
    }

    // 检查是否有缺失的关键需求信息
    const missingReqs = getMissingRequirements()
    if (missingReqs.length > 0 && (!requirements || requirements.completeness < 60)) {
      return {
        type: "requirement_framework",
        title: "完善需求信息",
        icon: "📋",
        subtitle: `还需要填写 ${missingReqs.length} 项关键信息，建议先完善后再提交`,
        items: missingReqs.slice(0, 3).map(req => ({
          text: req.question,
          icon: "❓",
          action: "fill_requirement",
          field: req.key
        }))
      }
    }

    // 否则显示基于需求完成度的快速开始建议
    if (!requirements || requirements.completeness < 20) {
      return {
        type: "quick_start",
        title: "快速开始",
        icon: "💬",
        items: [
          { text: "我需要单细胞RNA测序方案", icon: "🧬" },
          { text: "请推荐技术平台", icon: "⚡" },
          { text: "分析项目成本", icon: "💰" },
          { text: "查看成功案例", icon: "📊" }
        ]
      }
    } else if (requirements.completeness < 80) {
      return {
        type: "continue_conversation",
        title: "继续对话",
        icon: "🔄",
        items: [
          { text: "继续完善需求信息", icon: "📝" },
          { text: "基于当前信息给建议", icon: "💡" },
          { text: "了解技术细节", icon: "🔬" },
          { text: "预估项目周期", icon: "⏱️" }
        ]
      }
    } else {
      return {
        type: "advanced_actions",
        title: "高级功能",
        icon: "🚀",
        items: [
          { text: "生成完整技术方案", icon: "📋" },
          { text: "优化成本配置", icon: "💰" },
          { text: "风险评估分析", icon: "⚠️" },
          { text: "联系技术专家", icon: "👨‍🔬" }
        ]
      }
    }
  }

  // 处理建议点击
  const handleSuggestionClick = (item: any) => {
    if (item.action === "fill_requirement") {
      // 如果是需求填写建议，显示右侧面板并聚焦到对应字段
      setRightPanelCollapsed(false)
      // 可以添加滚动到对应字段的逻辑
      toast.info(`请在右侧面板填写：${item.text}`)
    } else {
      // 普通建议直接设置到输入框
      setInputValue(item.text)
    }
  }

  // 加载历史对话
  const handleLoadConversation = (session: any) => {
    setMessages(session.messages)
    setCurrentSessionId(session.id)
    setShowHistory(false)
    toast.success(`已加载对话: ${session.title}`)
  }

  // 保存当前对话
  const handleSaveCurrentConversation = () => {
    // 这个函数会在ConversationHistory组件中处理
    setShowHistory(false)
  }

  // 开始新对话
  const handleNewConversation = () => {
    setMessages([])
    setCurrentSessionId(null)
    setRequirements(null)
    setError("")
    setShowHistory(false)
    setRightPanelCollapsed(false) // 显示需求收集助手
    setResetTrigger(prev => prev + 1) // 触发需求收集助手重置
    toast.success("已开始新对话，请在右侧填写项目需求")
  }

  return (
    <div className="flex h-full bg-slate-50">
      {/* 左侧主对话区域 */}
      <div className={`flex flex-col h-full transition-all duration-300 ${
        rightPanelCollapsed ? 'flex-1' : 'flex-1'
      }`}>
        {/* 对话头部 */}
        <div className="bg-white border-b border-slate-200 px-4 py-3 flex items-center justify-between flex-shrink-0">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                <Brain className="h-4 w-4 text-white" />
              </div>
              <div>
                <h2 className="font-semibold text-slate-900">CellForge AI</h2>
                <p className="text-xs text-slate-500">单细胞测序智能顾问</p>
              </div>
            </div>
            {requirements && requirements.completeness > 0 && (
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                需求完成度 {requirements.completeness}%
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            {/* 历史对话按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowHistory(!showHistory)}
              className="text-slate-600 hover:text-slate-900"
            >
              <History className="h-4 w-4 mr-1" />
              历史
            </Button>

            {/* 新对话按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNewConversation}
              className="text-slate-600 hover:text-slate-900"
            >
              <RotateCcw className="h-4 w-4 mr-1" />
              新对话
            </Button>

            {!isMobile && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setRightPanelCollapsed(!rightPanelCollapsed)}
                className="text-slate-600 hover:text-slate-900"
              >
                {rightPanelCollapsed ? (
                  <>
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    显示助手
                  </>
                ) : (
                  <>
                    隐藏助手
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </>
                )}
              </Button>
            )}
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="p-4 flex-shrink-0">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </div>
        )}

        {/* 消息区域容器 */}
        <div className="flex-1 min-h-0 relative">
          {/* 消息区域 */}
          <div className="absolute inset-0 overflow-y-auto p-4 space-y-6 bg-white">
            {messages.map((message) => (
              <div key={message.id} className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}>
                {message.type === "ai" && (
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1">
                    <Sparkles className="h-4 w-4 text-white" />
                  </div>
                )}

                <div className={`${
                  message.type === "user"
                    ? "max-w-lg ml-12"
                    : "max-w-4xl mr-8"  // Give AI messages more space
                }`}>
                  <div
                    className={`${
                      message.type === "user"
                        ? "px-4 py-3 rounded-2xl shadow-sm"
                        : "px-6 py-4 rounded-2xl shadow-sm"  // More padding for AI messages
                    } ${
                      message.type === "user"
                        ? message.status === "error"
                          ? "bg-red-500 text-white"
                          : "bg-gradient-to-r from-blue-600 to-indigo-600 text-white"
                        : "bg-white border border-slate-200 text-slate-900"  // White background for AI
                    }`}
                  >
                    {message.type === "ai" ? (
                      <FormattedMessage content={message.content} className="text-sm" />
                    ) : (
                      <div className="text-sm leading-relaxed whitespace-pre-wrap">{message.content}</div>
                    )}

                    {/* AI消息的额外信息 */}
                    {message.type === "ai" && (
                      <div className="mt-3 pt-2 border-t border-slate-200 space-y-1">
                        {message.confidence !== undefined && (
                          <div className="flex items-center gap-2">
                            <Zap className="h-3 w-3 text-blue-600" />
                            <span className="text-xs text-slate-600">
                              置信度: {Math.round(message.confidence * 100)}%
                            </span>
                          </div>
                        )}
                        {message.sources && message.sources.length > 0 && (
                          <div className="flex items-center gap-2">
                            <MessageSquare className="h-3 w-3 text-green-600" />
                            <span className="text-xs text-slate-600">
                              来源: {message.sources.map(source => typeof source === 'string' ? source : source?.name || 'Unknown').slice(0, 2).join(", ")}
                              {message.sources.length > 2 && "..."}
                            </span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  <div className={`text-xs mt-2 flex items-center ${
                    message.type === "user" ? "justify-end text-slate-500" : "justify-start text-slate-500"
                  }`}>
                    <span>{message.timestamp.toLocaleTimeString()}</span>
                    {message.type === "user" && (
                      <span className="ml-2">
                        {message.status === "sending" && "⏳"}
                        {message.status === "sent" && "✓"}
                        {message.status === "read" && "✓✓"}
                        {message.status === "error" && "❌"}
                      </span>
                    )}
                  </div>
                </div>

                {message.type === "user" && (
                  <div className="w-8 h-8 bg-gradient-to-r from-slate-400 to-slate-500 rounded-full flex items-center justify-center ml-3 flex-shrink-0 mt-1">
                    <span className="text-white text-sm font-medium">
                      {user?.name?.charAt(0) || "U"}
                    </span>
                  </div>
                )}
              </div>
            ))}

            {isLoading && (
              <div className="flex justify-start">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1">
                  <Sparkles className="h-4 w-4 text-white" />
                </div>
                <div className="flex-1 max-w-md">
                  <EnhancedLoadingIndicator
                    isLoading={isLoading}
                    stage={currentStage}
                    progress={progress}
                    estimatedTime={30}
                    onCancel={cancelLoading}
                    onRetry={() => {
                      cancelLoading()
                      handleSendMessage()
                    }}
                    showRetryAfter={60} // 60秒后才显示重试，给AI充分时间
                    allowCancel={true}
                  />
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* 智能建议区域（合并AI建议和快速回复） */}
        {(() => {
          const suggestions = getSmartSuggestions()
          return (
            <div className={`border-t border-slate-200 p-4 flex-shrink-0 ${
              suggestions.type === "ai_suggestions"
                ? "bg-gradient-to-r from-blue-50 to-indigo-50"
                : suggestions.type === "requirement_framework"
                ? "bg-gradient-to-r from-amber-50 to-orange-50"
                : "bg-white"
            }`}>
              <div className="mb-4">
                <div className="flex items-center gap-2 mb-3">
                  {suggestions.type === "ai_suggestions" ? (
                    <Sparkles className="h-4 w-4 text-blue-600" />
                  ) : (
                    <span className="text-sm">{suggestions.icon}</span>
                  )}
                  <span className={`text-sm font-medium ${
                    suggestions.type === "ai_suggestions"
                      ? "text-blue-800"
                      : suggestions.type === "requirement_framework"
                      ? "text-amber-800"
                      : "text-slate-600"
                  }`}>
                    {suggestions.title}
                  </span>
                </div>

                {/* 显示副标题（用于需求框架提示） */}
                {suggestions.subtitle && (
                  <div className="text-xs text-amber-700 mb-3 p-2 bg-amber-100 rounded-lg border border-amber-200">
                    {suggestions.subtitle}
                  </div>
                )}

                <div className="flex flex-wrap gap-2">
                  {suggestions.items.map((item, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => handleSuggestionClick(item)}
                      className={`text-xs ${
                        suggestions.type === "ai_suggestions"
                          ? "bg-white hover:bg-blue-50 border-blue-200 text-blue-700 hover:text-blue-800"
                          : suggestions.type === "requirement_framework"
                          ? "bg-white hover:bg-amber-50 border-amber-200 text-amber-700 hover:text-amber-800"
                          : "bg-slate-50 hover:bg-slate-100 border-slate-200"
                      }`}
                    >
                      <span className="mr-1">{item.icon}</span>
                      {item.text}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          )
        })()}

        {/* 输入区域 */}
        <div className="border-t border-slate-200 p-4 bg-white flex-shrink-0">
          <div className="flex items-center space-x-3 bg-slate-50 rounded-xl p-3">
            <Button variant="ghost" size="icon" className="text-slate-500 hover:text-slate-700">
              <Paperclip className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" className="text-slate-500 hover:text-slate-700">
              <Mic className="h-4 w-4" />
            </Button>
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="输入您的问题，我来为您提供专业建议..."
              onKeyDown={(e) => e.key === "Enter" && !e.shiftKey && handleSendMessage()}
              className="flex-1 border-0 bg-white shadow-sm focus:ring-2 focus:ring-blue-500"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* 右侧面板 - 需求收集或历史对话 */}
      {(!rightPanelCollapsed || showHistory) && (
        <div className={`border-l border-slate-200 bg-white transition-all duration-300 flex flex-col h-full ${
          isMobile ? 'absolute right-0 top-0 bottom-0 w-80 shadow-xl z-10' : 'w-80 flex-shrink-0'
        }`}>
          {/* 右侧面板头部 */}
          <div className="border-b border-slate-200 p-4 flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {showHistory ? (
                  <>
                    <History className="h-5 w-5 text-indigo-600" />
                    <h3 className="font-semibold text-slate-900">对话历史</h3>
                  </>
                ) : (
                  <>
                    <ClipboardList className="h-5 w-5 text-indigo-600" />
                    <h3 className="font-semibold text-slate-900">需求收集助手</h3>
                  </>
                )}
              </div>

              <div className="flex items-center gap-1">
                {/* 切换按钮 */}
                {!showHistory && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowHistory(true)}
                    className="text-slate-500 hover:text-slate-700 h-6 w-6 p-0"
                  >
                    <History className="h-3 w-3" />
                  </Button>
                )}
                {showHistory && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowHistory(false)}
                    className="text-slate-500 hover:text-slate-700 h-6 w-6 p-0"
                  >
                    <ClipboardList className="h-3 w-3" />
                  </Button>
                )}

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setRightPanelCollapsed(true)
                    setShowHistory(false)
                  }}
                  className="text-slate-500 h-6 w-6 p-0"
                  title="隐藏面板"
                >
                  <ChevronRight className="h-3 w-3" />
                </Button>
              </div>
            </div>
            <p className="text-xs text-slate-600 mt-1">
              {showHistory
                ? "查看和管理您的对话历史"
                : "快速填写项目需求，获得更精准的AI建议"
              }
            </p>
          </div>

          {/* 面板内容 */}
          <div className="flex-1 min-h-0 relative">
            {showHistory ? (
              <div className="absolute inset-0 overflow-y-auto">
                <ConversationHistory
                  onLoadConversation={handleLoadConversation}
                  currentMessages={messages}
                  onSaveCurrentConversation={handleSaveCurrentConversation}
                />
              </div>
            ) : (
              <div className="absolute inset-0 overflow-y-auto p-4 space-y-4">
                {/* 需求收集器 */}
                <ProgressiveRequirementCollector
                  onRequirementsChange={handleRequirementsChange}
                  onSubmitRequirements={handleRequirementsSubmit}
                  isCompact={true}
                  resetTrigger={resetTrigger}
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* 移动端遮罩 */}
      {isMobile && !rightPanelCollapsed && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-5"
          onClick={() => setRightPanelCollapsed(true)}
        />
      )}

      {/* 移动端显示助手按钮 */}
      {isMobile && rightPanelCollapsed && (
        <Button
          className="fixed bottom-4 right-4 rounded-full w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg z-10"
          onClick={() => setRightPanelCollapsed(false)}
        >
          <ClipboardList className="h-5 w-5" />
        </Button>
      )}
    </div>
  )
}