"""
增强的解决方案生成器
集成研究意图分析、文献搜索和AI解析功能
提供按研究方案分类的展示和风险注意点说明
"""
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from app.services.research_intent_service import get_research_intent_service
from app.services.unified_literature_api_service import get_unified_literature_api_service
from app.services.ai_service import AIService

logger = logging.getLogger(__name__)


class EnhancedSolutionGenerator:
    """增强的解决方案生成器"""
    
    def __init__(self):
        self.ai_service = AIService()
    
    async def generate_comprehensive_solution(
        self,
        requirements: Dict[str, Any],
        user_message: str = ""
    ) -> Dict[str, Any]:
        """
        生成综合解决方案
        包括研究意图分析、文献搜索、AI解析和风险评估
        """
        try:
            logger.info("🚀 开始生成综合解决方案")
            
            # 1. 研究意图分析和关键词生成
            research_intent_service = get_research_intent_service()
            intent_analysis = await research_intent_service.analyze_research_intent_and_generate_keywords(
                requirements, user_message
            )
            
            # 2. 基于意图进行文献搜索
            literature_analysis = await research_intent_service.generate_literature_search_with_ai_analysis(
                requirements, user_message
            )
            
            # 3. 生成按研究方案分类的展示
            categorized_solutions = await self._generate_categorized_solutions(
                intent_analysis, literature_analysis
            )
            
            # 4. AI风险分析和注意点
            risk_analysis = await self._generate_ai_risk_analysis(
                requirements, intent_analysis, literature_analysis
            )
            
            # 5. 生成技术方案推荐
            technical_recommendations = await self._generate_technical_recommendations(
                requirements, intent_analysis
            )
            
            # 6. 整合所有结果
            comprehensive_solution = {
                "solution_overview": {
                    "primary_research_focus": intent_analysis.get("comprehensive_intent_analysis", {}).get("primary_research_domain", ""),
                    "research_possibilities": intent_analysis.get("comprehensive_intent_analysis", {}).get("research_possibilities", []),
                    "confidence_score": intent_analysis.get("comprehensive_intent_analysis", {}).get("overall_confidence", 0.85),
                    "complexity_assessment": intent_analysis.get("comprehensive_intent_analysis", {}).get("complexity_assessment", "medium")
                },
                "categorized_solutions": categorized_solutions,
                "literature_insights": {
                    "search_results": literature_analysis.get("literature_search_results", {}),
                    "clickable_links": literature_analysis.get("clickable_search_links", {}),
                    "total_papers": sum(
                        len(category.get("papers", []))
                        for category in literature_analysis.get("literature_search_results", {}).get("research_intent_based_results", {}).values()
                    )
                },
                "ai_risk_analysis": risk_analysis,
                "technical_recommendations": technical_recommendations,
                "generation_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "generation_method": "enhanced_comprehensive_analysis",
                    "processing_modules": ["intent_analysis", "literature_search", "risk_assessment", "technical_recommendation"]
                }
            }
            
            logger.info("✅ 综合解决方案生成完成")
            return comprehensive_solution
            
        except Exception as e:
            logger.error(f"综合解决方案生成失败: {e}")
            return self._empty_comprehensive_solution()
    
    async def _generate_categorized_solutions(
        self,
        intent_analysis: Dict[str, Any],
        literature_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成按研究方案分类的展示"""
        try:
            research_possibilities = intent_analysis.get("comprehensive_intent_analysis", {}).get("research_possibilities", [])
            literature_results = literature_analysis.get("literature_search_results", {}).get("research_intent_based_results", {})
            
            categorized_solutions = {}
            
            for i, possibility in enumerate(research_possibilities):
                possibility_name = possibility.get("possibility_name", f"研究方案{i+1}")
                probability = possibility.get("probability", 0.5)
                research_focus = possibility.get("research_focus", "")
                
                # 为每个研究可能性匹配相关文献
                relevant_literature = []
                for category, literature_data in literature_results.items():
                    papers = literature_data.get("papers", [])
                    # 取前几篇最相关的文献
                    relevant_literature.extend(papers[:2])
                
                # 生成具体的实施方案
                implementation_plan = self._generate_implementation_plan(possibility)
                
                # 估算成本和时间
                cost_time_estimate = self._estimate_cost_and_time(possibility)
                
                categorized_solutions[possibility_name] = {
                    "research_focus": research_focus,
                    "probability": probability,
                    "key_questions": possibility.get("key_questions", []),
                    "expected_outcomes": possibility.get("expected_outcomes", []),
                    "technical_challenges": possibility.get("technical_challenges", []),
                    "implementation_plan": implementation_plan,
                    "relevant_literature": relevant_literature[:3],  # 最多3篇
                    "cost_time_estimate": cost_time_estimate,
                    "success_probability": min(probability * 0.9, 0.95)  # 考虑实施难度
                }
            
            return {
                "solutions": categorized_solutions,
                "total_solutions": len(categorized_solutions),
                "recommended_solution": self._select_recommended_solution(categorized_solutions)
            }
            
        except Exception as e:
            logger.error(f"生成分类解决方案失败: {e}")
            return {"solutions": {}, "total_solutions": 0}
    
    def _generate_implementation_plan(self, possibility: Dict[str, Any]) -> Dict[str, Any]:
        """为研究可能性生成具体实施方案"""
        possibility_name = possibility.get("possibility_name", "")
        
        # 基于研究类型生成不同的实施方案
        if "发育轨迹" in possibility_name or "trajectory" in possibility_name.lower():
            return {
                "phase_1": {
                    "name": "样本收集与预处理",
                    "duration": "1-2周",
                    "tasks": ["多时间点样本收集", "细胞分离和质控", "样本标准化处理"]
                },
                "phase_2": {
                    "name": "单细胞测序",
                    "duration": "2-3周",
                    "tasks": ["10x Genomics平台测序", "RNA Velocity分析准备", "质量控制检测"]
                },
                "phase_3": {
                    "name": "轨迹分析",
                    "duration": "2-4周",
                    "tasks": ["Monocle3伪时间分析", "CellRank命运预测", "分支点识别验证"]
                }
            }
        elif "肿瘤" in possibility_name or "tumor" in possibility_name.lower():
            return {
                "phase_1": {
                    "name": "肿瘤样本处理",
                    "duration": "1-2周",
                    "tasks": ["肿瘤组织解离", "免疫细胞分离", "活细胞质控"]
                },
                "phase_2": {
                    "name": "单细胞测序",
                    "duration": "2-3周",
                    "tasks": ["10x Genomics + Spatial平台", "多组学数据获取", "数据质量评估"]
                },
                "phase_3": {
                    "name": "微环境分析",
                    "duration": "3-4周",
                    "tasks": ["肿瘤细胞分类", "免疫浸润分析", "细胞通讯网络构建"]
                }
            }
        elif "免疫" in possibility_name or "immune" in possibility_name.lower():
            return {
                "phase_1": {
                    "name": "免疫细胞分离",
                    "duration": "1周",
                    "tasks": ["PBMC分离", "细胞活力检测", "免疫细胞计数"]
                },
                "phase_2": {
                    "name": "单细胞测序",
                    "duration": "2-3周",
                    "tasks": ["10x Genomics + VDJ分析", "TCR/BCR序列获取", "转录组数据生成"]
                },
                "phase_3": {
                    "name": "免疫分析",
                    "duration": "2-3周",
                    "tasks": ["免疫细胞分型", "功能状态评估", "克隆性分析"]
                }
            }
        else:
            return {
                "phase_1": {
                    "name": "样本准备",
                    "duration": "1-2周",
                    "tasks": ["样本收集处理", "细胞分离质控", "实验设计优化"]
                },
                "phase_2": {
                    "name": "单细胞测序",
                    "duration": "2-3周",
                    "tasks": ["平台选择执行", "文库构建测序", "数据质量控制"]
                },
                "phase_3": {
                    "name": "数据分析",
                    "duration": "2-4周",
                    "tasks": ["细胞类型注释", "差异表达分析", "功能富集分析"]
                }
            }
    
    def _estimate_cost_and_time(self, possibility: Dict[str, Any]) -> Dict[str, Any]:
        """估算成本和时间"""
        possibility_name = possibility.get("possibility_name", "")
        probability = possibility.get("probability", 0.5)
        
        # 基础成本和时间
        base_cost = 80000  # 8万元基础
        base_time = 8      # 8周基础
        
        # 根据研究复杂度调整
        if "发育轨迹" in possibility_name:
            cost_multiplier = 1.3
            time_multiplier = 1.2
        elif "肿瘤" in possibility_name:
            cost_multiplier = 1.5
            time_multiplier = 1.3
        elif "免疫" in possibility_name:
            cost_multiplier = 1.2
            time_multiplier = 1.1
        else:
            cost_multiplier = 1.0
            time_multiplier = 1.0
        
        # 根据成功概率调整（高风险项目需要更多资源）
        if probability < 0.6:
            cost_multiplier *= 1.2
            time_multiplier *= 1.15
        
        estimated_cost = int(base_cost * cost_multiplier)
        estimated_time = int(base_time * time_multiplier)
        
        return {
            "estimated_cost": {
                "total": estimated_cost,
                "currency": "CNY",
                "breakdown": {
                    "sample_preparation": int(estimated_cost * 0.2),
                    "sequencing": int(estimated_cost * 0.4),
                    "analysis": int(estimated_cost * 0.3),
                    "reporting": int(estimated_cost * 0.1)
                }
            },
            "estimated_time": {
                "total_weeks": estimated_time,
                "total_days": estimated_time * 7,
                "phases": {
                    "preparation": f"1-{max(1, estimated_time//4)}周",
                    "execution": f"{estimated_time//3}-{estimated_time//2}周",
                    "analysis": f"{estimated_time//4}-{estimated_time//2}周"
                }
            },
            "risk_factors": {
                "technical_risk": "中等" if probability > 0.7 else "较高",
                "time_risk": "可控" if probability > 0.8 else "需要关注",
                "cost_risk": "预算范围内" if probability > 0.7 else "可能超支"
            }
        }
    
    def _select_recommended_solution(self, categorized_solutions: Dict[str, Any]) -> Optional[str]:
        """选择推荐的解决方案"""
        if not categorized_solutions:
            return None
        
        # 综合考虑概率、复杂度和成功率
        best_solution = None
        best_score = 0
        
        for solution_name, solution_data in categorized_solutions.items():
            probability = solution_data.get("probability", 0.5)
            success_probability = solution_data.get("success_probability", 0.5)
            
            # 综合评分：概率 * 成功率
            score = probability * success_probability
            
            if score > best_score:
                best_score = score
                best_solution = solution_name
        
        return best_solution
    
    async def _generate_ai_risk_analysis(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any],
        literature_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成AI风险分析和注意点"""
        try:
            if not self.ai_service:
                return self._fallback_risk_analysis(requirements)
            
            # 构建风险分析提示词
            research_domain = intent_analysis.get("comprehensive_intent_analysis", {}).get("primary_research_domain", "")
            research_possibilities = intent_analysis.get("comprehensive_intent_analysis", {}).get("research_possibilities", [])
            sample_type = requirements.get('sampleType', '')
            
            risk_analysis_prompt = f"""
作为单细胞测序专家，请基于以下研究信息分析潜在风险和注意点：

研究领域：{research_domain}
样本类型：{sample_type}
研究目标：{requirements.get('researchGoal', '')}

研究可能性：
{chr(10).join([f"- {p.get('possibility_name', '')}: {p.get('research_focus', '')}" for p in research_possibilities])}

请分析并提供JSON格式的风险评估：
{{
    "technical_risks": [
        {{
            "risk_category": "技术风险类别",
            "risk_description": "风险描述",
            "likelihood": "高/中/低",
            "impact": "严重/中等/轻微",
            "mitigation_strategies": ["缓解策略1", "缓解策略2"]
        }}
    ],
    "sample_specific_risks": [
        {{
            "risk_type": "样本特异性风险",
            "description": "详细描述",
            "prevention_measures": ["预防措施1", "预防措施2"]
        }}
    ],
    "data_quality_concerns": [
        {{
            "concern": "数据质量关注点",
            "quality_indicators": ["质量指标1", "质量指标2"],
            "acceptable_thresholds": "可接受阈值"
        }}
    ],
    "success_factors": [
        "成功关键因素1",
        "成功关键因素2"
    ],
    "overall_risk_level": "高/中/低",
    "confidence_in_assessment": 0.85
}}

请确保分析专业、全面且实用。
"""
            
            response = await self.ai_service.generate_response(
                message=risk_analysis_prompt,
                context={"task": "risk_analysis"},
                conversation_type="analysis"
            )
            
            # 尝试解析AI返回的JSON
            try:
                import json
                import re
                
                json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
                if json_match:
                    risk_analysis = json.loads(json_match.group())
                    logger.info("AI风险分析生成成功")
                    return risk_analysis
                else:
                    logger.warning("AI返回格式不是有效JSON，使用备用分析")
                    return self._fallback_risk_analysis(requirements)
            
            except json.JSONDecodeError:
                logger.warning("AI风险分析JSON解析失败，使用备用分析")
                return self._fallback_risk_analysis(requirements)
        
        except Exception as e:
            logger.error(f"AI风险分析失败: {e}")
            return self._fallback_risk_analysis(requirements)
    
    def _fallback_risk_analysis(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """备用风险分析"""
        sample_type = requirements.get('sampleType', '').lower()
        research_goal = requirements.get('researchGoal', '').lower()
        
        technical_risks = [
            {
                "risk_category": "样本质量风险",
                "risk_description": "细胞活力低或RNA降解可能影响数据质量",
                "likelihood": "中",
                "impact": "严重",
                "mitigation_strategies": ["严格控制样本处理时间", "使用RNA稳定试剂", "实时监测细胞活力"]
            },
            {
                "risk_category": "技术平台风险",
                "risk_description": "双联率过高或捕获效率低",
                "likelihood": "中",
                "impact": "中等",
                "mitigation_strategies": ["优化细胞浓度", "使用质控标准", "选择合适的平台参数"]
            }
        ]
        
        sample_specific_risks = []
        if "肿瘤" in sample_type or "tumor" in sample_type:
            sample_specific_risks.append({
                "risk_type": "肿瘤样本异质性",
                "description": "肿瘤组织中细胞类型复杂，可能存在坏死区域",
                "prevention_measures": ["选择活细胞区域", "快速处理避免缺氧", "多部位取样"]
            })
        elif "血液" in sample_type or "pbmc" in sample_type:
            sample_specific_risks.append({
                "risk_type": "免疫细胞激活",
                "description": "处理过程中免疫细胞可能被激活，影响基因表达",
                "prevention_measures": ["低温处理", "使用激活抑制剂", "快速分离"]
            })
        
        return {
            "technical_risks": technical_risks,
            "sample_specific_risks": sample_specific_risks,
            "data_quality_concerns": [
                {
                    "concern": "测序深度不足",
                    "quality_indicators": ["每细胞检测基因数", "UMI计数", "测序饱和度"],
                    "acceptable_thresholds": "每细胞>1000基因，UMI>2000"
                }
            ],
            "success_factors": [
                "严格的质量控制标准",
                "合适的技术平台选择",
                "专业的数据分析团队",
                "充足的样本量和重复"
            ],
            "overall_risk_level": "中",
            "confidence_in_assessment": 0.80
        }
    
    async def _generate_technical_recommendations(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成技术方案推荐"""
        try:
            research_domain = intent_analysis.get("comprehensive_intent_analysis", {}).get("primary_research_domain", "")
            research_possibilities = intent_analysis.get("comprehensive_intent_analysis", {}).get("research_possibilities", [])
            
            # 基于研究领域推荐技术平台
            if "development" in research_domain or any("轨迹" in p.get("possibility_name", "") for p in research_possibilities):
                recommended_platform = {
                    "primary_platform": "10x Genomics Chromium",
                    "complementary_platform": "RNA Velocity分析",
                    "rationale": "发育轨迹研究需要高时间分辨率和伪时间分析能力"
                }
            elif "cancer" in research_domain or any("肿瘤" in p.get("possibility_name", "") for p in research_possibilities):
                recommended_platform = {
                    "primary_platform": "10x Genomics Chromium + Spatial Transcriptomics",
                    "complementary_platform": "多组学整合",
                    "rationale": "肿瘤研究需要结合空间信息和细胞异质性分析"
                }
            elif "immunology" in research_domain or any("免疫" in p.get("possibility_name", "") for p in research_possibilities):
                recommended_platform = {
                    "primary_platform": "10x Genomics Chromium + VDJ分析",
                    "complementary_platform": "TCR/BCR序列分析",
                    "rationale": "免疫研究需要结合转录组和免疫组库分析"
                }
            else:
                recommended_platform = {
                    "primary_platform": "10x Genomics Chromium",
                    "complementary_platform": "标准单细胞转录组",
                    "rationale": "通用性强，数据质量可靠的标准平台"
                }
            
            return {
                "recommended_platform": recommended_platform,
                "sample_preparation": {
                    "key_steps": ["样本快速处理", "细胞活力检测", "细胞计数和质控"],
                    "critical_points": ["维持细胞活力", "避免RNA降解", "控制处理时间"],
                    "quality_metrics": ["细胞活力>80%", "细胞浓度500-1000/μL", "细胞形态完整"]
                },
                "data_analysis_pipeline": {
                    "primary_tools": ["Cell Ranger", "Seurat", "Scanpy"],
                    "analysis_steps": ["质量控制", "降维聚类", "细胞类型注释", "差异表达分析"],
                    "advanced_analysis": self._get_advanced_analysis_recommendations(research_domain)
                },
                "quality_assurance": {
                    "pre_sequencing": ["样本质控", "文库质量检测", "测序深度估算"],
                    "post_sequencing": ["数据质量评估", "批次效应检测", "结果验证"],
                    "success_criteria": ["细胞通过率>70%", "基因检测数>1000/细胞", "双联率<10%"]
                }
            }
            
        except Exception as e:
            logger.error(f"生成技术推荐失败: {e}")
            return {"recommended_platform": {"primary_platform": "10x Genomics Chromium"}}
    
    def _get_advanced_analysis_recommendations(self, research_domain: str) -> List[str]:
        """获取高级分析推荐"""
        if "development" in research_domain:
            return ["Monocle3轨迹分析", "RNA Velocity", "CellRank命运预测", "转录因子活性推断"]
        elif "cancer" in research_domain:
            return ["CNV分析", "细胞通讯分析", "免疫浸润评估", "空间转录组整合"]
        elif "immunology" in research_domain:
            return ["免疫细胞分型", "TCR/BCR多样性分析", "克隆扩增检测", "功能状态评估"]
        else:
            return ["细胞类型注释", "差异表达分析", "功能富集分析", "细胞间通讯"]
    
    def _empty_comprehensive_solution(self) -> Dict[str, Any]:
        """返回空的综合解决方案"""
        return {
            "solution_overview": {},
            "categorized_solutions": {"solutions": {}, "total_solutions": 0},
            "literature_insights": {"search_results": {}, "clickable_links": {}, "total_papers": 0},
            "ai_risk_analysis": {"overall_risk_level": "未知", "confidence_in_assessment": 0.0},
            "technical_recommendations": {},
            "generation_metadata": {
                "generated_at": datetime.now().isoformat(),
                "generation_method": "fallback",
                "processing_modules": []
            }
        }


# 全局服务实例
enhanced_solution_generator = EnhancedSolutionGenerator()


def get_enhanced_solution_generator() -> EnhancedSolutionGenerator:
    """获取增强解决方案生成器实例"""
    return enhanced_solution_generator


def get_solution_generator() -> EnhancedSolutionGenerator:
    """获取解决方案生成器实例 - 兼容性接口"""
    return enhanced_solution_generator