"""
统一API响应格式定义
提供标准化的API响应结构和工具函数
"""
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional, Generic, TypeVar
from datetime import datetime
from enum import Enum
import traceback

T = TypeVar('T')

class ResponseStatus(str, Enum):
    """响应状态枚举"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    PARTIAL = "partial"

class APIResponse(BaseModel, Generic[T]):
    """统一API响应格式"""
    success: bool = Field(..., description="请求是否成功")
    status: ResponseStatus = Field(..., description="响应状态")
    message: str = Field(..., description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")
    error: Optional[Dict[str, Any]] = Field(None, description="错误信息")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="响应时间戳")
    processing_time_ms: Optional[float] = Field(None, description="处理时间(毫秒)")
    request_id: Optional[str] = Field(None, description="请求ID")

class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应格式"""
    items: List[T] = Field(..., description="数据项")
    total: int = Field(..., description="总数据量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")

class ErrorDetail(BaseModel):
    """错误详情"""
    code: str = Field(..., description="错误代码")
    message: str = Field(..., description="错误消息")
    field: Optional[str] = Field(None, description="错误字段")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")

class ValidationErrorResponse(BaseModel):
    """验证错误响应"""
    success: bool = False
    status: ResponseStatus = ResponseStatus.ERROR
    message: str = "请求参数验证失败"
    errors: List[ErrorDetail] = Field(..., description="验证错误列表")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())

# ===== 响应构建工具函数 =====

def success_response(
    data: Any = None,
    message: str = "操作成功",
    metadata: Optional[Dict[str, Any]] = None,
    processing_time_ms: Optional[float] = None,
    request_id: Optional[str] = None
) -> APIResponse:
    """构建成功响应"""
    return APIResponse(
        success=True,
        status=ResponseStatus.SUCCESS,
        message=message,
        data=data,
        metadata=metadata,
        processing_time_ms=processing_time_ms,
        request_id=request_id
    )

def error_response(
    message: str = "操作失败",
    error_code: Optional[str] = None,
    error_details: Optional[Dict[str, Any]] = None,
    processing_time_ms: Optional[float] = None,
    request_id: Optional[str] = None,
    include_traceback: bool = False
) -> APIResponse:
    """构建错误响应"""
    error_info = {
        "code": error_code or "GENERAL_ERROR",
        "message": message
    }
    
    if error_details:
        error_info["details"] = error_details
    
    if include_traceback:
        error_info["traceback"] = traceback.format_exc()
    
    return APIResponse(
        success=False,
        status=ResponseStatus.ERROR,
        message=message,
        error=error_info,
        processing_time_ms=processing_time_ms,
        request_id=request_id
    )

def warning_response(
    data: Any = None,
    message: str = "操作完成但有警告",
    warning_details: Optional[Dict[str, Any]] = None,
    processing_time_ms: Optional[float] = None,
    request_id: Optional[str] = None
) -> APIResponse:
    """构建警告响应"""
    metadata = {"warnings": warning_details} if warning_details else None
    
    return APIResponse(
        success=True,
        status=ResponseStatus.WARNING,
        message=message,
        data=data,
        metadata=metadata,
        processing_time_ms=processing_time_ms,
        request_id=request_id
    )

def partial_response(
    data: Any = None,
    message: str = "操作部分成功",
    partial_details: Optional[Dict[str, Any]] = None,
    processing_time_ms: Optional[float] = None,
    request_id: Optional[str] = None
) -> APIResponse:
    """构建部分成功响应"""
    metadata = {"partial_results": partial_details} if partial_details else None
    
    return APIResponse(
        success=True,
        status=ResponseStatus.PARTIAL,
        message=message,
        data=data,
        metadata=metadata,
        processing_time_ms=processing_time_ms,
        request_id=request_id
    )

def paginated_response(
    items: List[Any],
    total: int,
    page: int,
    size: int,
    message: str = "数据获取成功",
    processing_time_ms: Optional[float] = None,
    request_id: Optional[str] = None
) -> APIResponse[PaginatedResponse]:
    """构建分页响应"""
    pages = (total + size - 1) // size if size > 0 else 0
    has_next = page < pages
    has_prev = page > 1
    
    paginated_data = PaginatedResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=pages,
        has_next=has_next,
        has_prev=has_prev
    )
    
    return APIResponse(
        success=True,
        status=ResponseStatus.SUCCESS,
        message=message,
        data=paginated_data,
        processing_time_ms=processing_time_ms,
        request_id=request_id
    )

# ===== 特定业务响应模型 =====

class ServiceHealthResponse(BaseModel):
    """服务健康状态响应"""
    service_name: str = Field(..., description="服务名称")
    status: str = Field(..., description="健康状态")
    uptime: Optional[float] = Field(None, description="运行时间(秒)")
    memory_usage: Optional[Dict[str, Any]] = Field(None, description="内存使用情况")
    dependencies: Optional[Dict[str, str]] = Field(None, description="依赖服务状态")
    last_check: str = Field(default_factory=lambda: datetime.now().isoformat(), description="最后检查时间")

class BatchOperationResponse(BaseModel):
    """批量操作响应"""
    total_items: int = Field(..., description="总项目数")
    successful_items: int = Field(..., description="成功项目数")
    failed_items: int = Field(..., description="失败项目数")
    success_rate: float = Field(..., description="成功率")
    results: List[Dict[str, Any]] = Field(..., description="详细结果")
    errors: List[ErrorDetail] = Field(default_factory=list, description="错误列表")

class AsyncTaskResponse(BaseModel):
    """异步任务响应"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    progress: Optional[float] = Field(None, description="进度百分比")
    estimated_completion: Optional[str] = Field(None, description="预计完成时间")
    result_url: Optional[str] = Field(None, description="结果获取URL")
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat(), description="创建时间")

# ===== 错误处理装饰器 =====

def handle_api_errors(include_traceback: bool = False):
    """API错误处理装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = datetime.now()
            try:
                result = await func(*args, **kwargs)
                processing_time = (datetime.now() - start_time).total_seconds() * 1000
                
                if isinstance(result, APIResponse):
                    if result.processing_time_ms is None:
                        result.processing_time_ms = processing_time
                    return result
                else:
                    return success_response(
                        data=result,
                        processing_time_ms=processing_time
                    )
                    
            except Exception as e:
                processing_time = (datetime.now() - start_time).total_seconds() * 1000
                return error_response(
                    message=str(e),
                    error_code=type(e).__name__,
                    processing_time_ms=processing_time,
                    include_traceback=include_traceback
                )
        return wrapper
    return decorator

# ===== 响应验证和转换工具 =====

def validate_response_data(data: Any, expected_fields: List[str]) -> Dict[str, Any]:
    """验证响应数据包含必要字段"""
    if not isinstance(data, dict):
        raise ValueError("响应数据必须是字典格式")
    
    missing_fields = [field for field in expected_fields if field not in data]
    if missing_fields:
        raise ValueError(f"响应数据缺少必要字段: {missing_fields}")
    
    return data

def sanitize_response_data(data: Any, remove_sensitive: bool = True) -> Any:
    """清理响应数据，移除敏感信息"""
    if not remove_sensitive:
        return data
    
    if isinstance(data, dict):
        sensitive_keys = ['password', 'token', 'secret', 'key', 'api_key', 'auth_token']
        return {
            k: sanitize_response_data(v, remove_sensitive) 
            for k, v in data.items() 
            if k.lower() not in sensitive_keys
        }
    elif isinstance(data, list):
        return [sanitize_response_data(item, remove_sensitive) for item in data]
    else:
        return data

# ===== 响应格式转换工具 =====

def convert_to_legacy_format(response: APIResponse) -> Dict[str, Any]:
    """转换为旧版API格式（向后兼容）"""
    legacy_response = {
        "status": "success" if response.success else "error",
        "message": response.message,
        "timestamp": response.timestamp
    }
    
    if response.data is not None:
        legacy_response["data"] = response.data
    
    if response.error is not None:
        legacy_response["error"] = response.error
    
    if response.processing_time_ms is not None:
        legacy_response["processing_time"] = response.processing_time_ms
    
    return legacy_response

def convert_from_service_result(
    service_result: Dict[str, Any],
    success_message: str = "操作成功",
    error_message: str = "操作失败"
) -> APIResponse:
    """从服务层结果转换为标准API响应"""
    if service_result.get("error"):
        return error_response(
            message=error_message,
            error_details=service_result.get("error_details"),
            processing_time_ms=service_result.get("processing_time_ms")
        )
    elif service_result.get("warning"):
        return warning_response(
            data=service_result.get("data"),
            message=service_result.get("warning", "操作完成但有警告"),
            warning_details=service_result.get("warning_details"),
            processing_time_ms=service_result.get("processing_time_ms")
        )
    else:
        return success_response(
            data=service_result.get("data"),
            message=success_message,
            metadata=service_result.get("metadata"),
            processing_time_ms=service_result.get("processing_time_ms")
        )

# ===== 常用响应常量 =====

class CommonResponses:
    """常用响应消息常量"""
    
    # 成功消息
    SUCCESS = "操作成功"
    CREATED = "创建成功"
    UPDATED = "更新成功"
    DELETED = "删除成功"
    
    # 错误消息
    NOT_FOUND = "资源未找到"
    UNAUTHORIZED = "未授权访问"
    FORBIDDEN = "禁止访问"
    VALIDATION_ERROR = "参数验证失败"
    INTERNAL_ERROR = "内部服务器错误"
    SERVICE_UNAVAILABLE = "服务暂时不可用"
    
    # 警告消息
    PARTIAL_SUCCESS = "操作部分成功"
    DEPRECATED_API = "此API已弃用，请使用新版本"
    RATE_LIMITED = "请求频率过高，请稍后重试"