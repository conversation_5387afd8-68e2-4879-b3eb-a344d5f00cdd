/**
 * 增强的错误处理机制
 */

import { toast } from 'sonner'

export interface RetryOptions {
  maxRetries?: number
  retryDelay?: number
  exponentialBackoff?: boolean
  retryCondition?: (error: any) => boolean
  onRetry?: (attempt: number, error: any) => void
}

export interface ErrorHandlerOptions<T> extends RetryOptions {
  fallbackResponse?: T
  userFriendlyMessage?: string
  showToast?: boolean
  logError?: boolean
}

export enum LoadingStage {
  ANALYZING = "正在分析您的需求...",
  SEARCHING_LITERATURE = "搜索相关文献资源...",
  GENERATING_RESPONSE = "生成专业建议...",
  FORMATTING = "格式化回复内容...",
  FINALIZING = "完成处理..."
}

export class EnhancedErrorHandler {
  private static instance: EnhancedErrorHandler

  static getInstance(): EnhancedErrorHandler {
    if (!EnhancedErrorHandler.instance) {
      EnhancedErrorHandler.instance = new EnhancedErrorHandler()
    }
    return EnhancedErrorHandler.instance
  }

  /**
   * 带重试机制的API调用处理
   */
  async handleApiCall<T>(
    apiCall: () => Promise<T>,
    options: ErrorHandlerOptions<T> = {}
  ): Promise<T> {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      exponentialBackoff = true,
      retryCondition = this.defaultRetryCondition,
      onRetry,
      fallbackResponse,
      userFriendlyMessage,
      showToast = true,
      logError = true
    } = options

    let lastError: any

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall()
      } catch (error) {
        lastError = error

        if (logError) {
          console.error(`API调用失败 (尝试 ${attempt}/${maxRetries}):`, error)
        }

        // 检查是否应该重试
        if (attempt < maxRetries && retryCondition(error)) {
          if (onRetry) {
            onRetry(attempt, error)
          }

          // 计算延迟时间
          const delay = exponentialBackoff
            ? retryDelay * Math.pow(2, attempt - 1)
            : retryDelay

          await this.delay(delay)
          continue
        }

        // 最后一次尝试失败或不满足重试条件
        break
      }
    }

    // 处理最终失败
    const friendlyMessage = this.getFriendlyErrorMessage(lastError, userFriendlyMessage)

    if (showToast) {
      toast.error(friendlyMessage)
    }

    if (fallbackResponse !== undefined) {
      return fallbackResponse
    }

    throw lastError
  }

  /**
   * 带超时的API调用
   */
  async handleApiCallWithTimeout<T>(
    apiCall: () => Promise<T>,
    timeoutMs: number = 30000,
    options: ErrorHandlerOptions<T> = {}
  ): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), timeoutMs)
    })

    const apiCallWithTimeout = () => Promise.race([apiCall(), timeoutPromise])

    return this.handleApiCall(apiCallWithTimeout, options)
  }

  /**
   * 分阶段加载处理
   */
  async handleStagedOperation<T>(
    stages: Array<{
      stage: LoadingStage
      operation: () => Promise<any>
    }>,
    onStageChange: (stage: LoadingStage, progress: number) => void,
    options: ErrorHandlerOptions<T> = {}
  ): Promise<T> {
    let result: any
    const totalStages = stages.length

    for (let i = 0; i < stages.length; i++) {
      const { stage, operation } = stages[i]
      const progress = Math.round(((i + 1) / totalStages) * 100)

      onStageChange(stage, progress)

      try {
        result = await this.handleApiCall(operation, {
          ...options,
          showToast: false, // 不在每个阶段显示错误toast
          userFriendlyMessage: `${stage}时出现错误`
        })
      } catch (error) {
        // 如果某个阶段失败，显示具体的错误信息
        const friendlyMessage = `${stage}失败: ${this.getFriendlyErrorMessage(error)}`
        toast.error(friendlyMessage)
        throw error
      }
    }

    return result
  }

  /**
   * 默认重试条件 - 更保守的重试策略
   */
  private defaultRetryCondition(error: any): boolean {
    // 只有明确的网络连接错误才重试
    if (error.message?.includes('Failed to fetch') ||
        error.message?.includes('NetworkError') ||
        error.message?.includes('ERR_NETWORK') ||
        error.message?.includes('ERR_INTERNET_DISCONNECTED')) {
      return true
    }

    // 只有服务器内部错误(500)和网关错误(502, 503, 504)才重试
    if (error.status === 500 ||
        error.status === 502 ||
        error.status === 503 ||
        error.status === 504) {
      return true
    }

    // 限流错误可以重试，但延迟更长
    if (error.status === 429) {
      return true
    }

    // 超时错误可以重试
    if (error.message?.includes('timeout') || error.message?.includes('超时')) {
      return true
    }

    // 其他错误（包括4xx客户端错误）不重试
    return false
  }

  /**
   * 获取用户友好的错误信息
   */
  private getFriendlyErrorMessage(error: any, customMessage?: string): string {
    if (customMessage) {
      return customMessage
    }

    const errorMessage = error?.message || error?.toString() || '未知错误'

    // 网络相关错误
    if (errorMessage.includes('网络') ||
        errorMessage.includes('fetch') ||
        errorMessage.includes('Failed to fetch')) {
      return '网络连接不稳定，请检查网络后重试'
    }

    // 超时错误
    if (errorMessage.includes('超时') || errorMessage.includes('timeout')) {
      return '服务响应超时，请稍后重试'
    }

    // 认证错误
    if (error?.status === 401 || errorMessage.includes('401')) {
      return '登录已过期，请重新登录'
    }

    // 权限错误
    if (error?.status === 403 || errorMessage.includes('403')) {
      return '没有权限执行此操作'
    }

    // 资源不存在
    if (error?.status === 404 || errorMessage.includes('404')) {
      return '请求的资源不存在'
    }

    // 请求格式错误
    if (error?.status === 422 || errorMessage.includes('422')) {
      return '请求数据格式错误，请检查输入信息'
    }

    // 服务器错误
    if (error?.status >= 500) {
      return '服务器暂时不可用，请稍后重试'
    }

    // 限流错误
    if (error?.status === 429) {
      return '请求过于频繁，请稍后重试'
    }

    // AI服务相关错误
    if (errorMessage.includes('AI') || errorMessage.includes('模型')) {
      return 'AI服务暂时不可用，请稍后重试'
    }

    // 文献服务相关错误
    if (errorMessage.includes('文献') || errorMessage.includes('literature')) {
      return '文献服务暂时不可用，请稍后重试'
    }

    // 默认错误信息
    return '服务暂时不可用，请稍后重试'
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 错误分类
   */
  categorizeError(error: any): 'network' | 'server' | 'client' | 'auth' | 'unknown' {
    const errorMessage = error?.message || error?.toString() || ''
    const status = error?.status

    if (errorMessage.includes('网络') ||
        errorMessage.includes('fetch') ||
        errorMessage.includes('timeout')) {
      return 'network'
    }

    if (status === 401 || status === 403) {
      return 'auth'
    }

    if (status >= 500) {
      return 'server'
    }

    if (status >= 400 && status < 500) {
      return 'client'
    }

    return 'unknown'
  }

  /**
   * 生成错误报告
   */
  generateErrorReport(error: any, context?: Record<string, any>): ErrorReport {
    return {
      timestamp: new Date().toISOString(),
      category: this.categorizeError(error),
      message: error?.message || 'Unknown error',
      status: error?.status,
      stack: error?.stack,
      context: context || {},
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'Server',
      url: typeof window !== 'undefined' ? window.location.href : 'Unknown'
    }
  }
}

export interface ErrorReport {
  timestamp: string
  category: 'network' | 'server' | 'client' | 'auth' | 'unknown'
  message: string
  status?: number
  stack?: string
  context: Record<string, any>
  userAgent: string
  url: string
}

// 导出单例实例
export const errorHandler = EnhancedErrorHandler.getInstance()
