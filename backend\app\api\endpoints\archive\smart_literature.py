"""
智能文献搜索API端点
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from app.models.user import User
from app.core.auth import get_current_active_user
from app.services.smart_prompt_generator import get_smart_prompt_generator
from app.services.literature_service import literature_service
# from app.services.optional_literature_service import get_optional_literature_service

router = APIRouter()


class SmartSearchRequest(BaseModel):
    """智能搜索请求"""
    requirements: Dict[str, Any]
    user_query: Optional[str] = None
    max_queries: int = 6
    enable_literature_search: bool = True


class SearchQueryResponse(BaseModel):
    """搜索查询响应"""
    primary_query: str
    suggested_queries: List[str]
    related_topics: List[str]
    search_strategy: Dict[str, Any]
    optimization_notes: List[str]
    generated_at: str


class PerplexitySearchRequest(BaseModel):
    """Perplexity风格搜索请求"""
    query: str
    requirements: Optional[Dict[str, Any]] = None
    include_analysis: bool = True
    max_papers: int = 10


class PerplexitySearchResponse(BaseModel):
    """Perplexity风格搜索响应"""
    query: str
    optimized_query: str
    papers: List[Dict[str, Any]]
    ai_summary: str
    related_topics: List[str]
    sources: List[str]
    search_time_ms: int


@router.post("/generate-smart-queries", response_model=SearchQueryResponse)
async def generate_smart_queries(
    request: SmartSearchRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    基于需求信息生成智能搜索查询
    """
    try:
        prompt_generator = get_smart_prompt_generator()
        
        result = await prompt_generator.generate_literature_search_queries(
            requirements=request.requirements,
            user_query=request.user_query,
            max_queries=request.max_queries
        )
        
        return SearchQueryResponse(**result)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成智能查询失败: {str(e)}")


@router.post("/perplexity-search", response_model=PerplexitySearchResponse)
async def perplexity_search(
    request: PerplexitySearchRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    执行Perplexity风格的智能文献搜索
    """
    import time
    start_time = time.time()
    
    try:
        prompt_generator = get_smart_prompt_generator()
        
        # 1. 优化搜索查询
        if request.requirements:
            query_result = await prompt_generator.generate_literature_search_queries(
                requirements=request.requirements,
                user_query=request.query,
                max_queries=1
            )
            optimized_query = query_result["primary_query"]
        else:
            optimized_query = f"single cell {request.query}"
        
        # 2. 执行文献搜索
        search_results = await literature_service.search_literature(
            query=optimized_query,
            top_k=request.max_papers
        )
        
        # 3. AI分析和摘要生成
        ai_summary = ""
        if request.include_analysis and search_results:
            ai_summary = await _generate_ai_summary(
                search_results, 
                optimized_query, 
                request.requirements
            )
        
        # 4. 生成相关主题
        related_topics = []
        if request.requirements:
            analysis = prompt_generator._analyze_requirements(request.requirements)
            related_topics = prompt_generator._generate_related_topics(analysis)[:4]
        else:
            related_topics = ["quality control", "data analysis", "best practices", "protocols"]
        
        search_time_ms = int((time.time() - start_time) * 1000)
        
        return PerplexitySearchResponse(
            query=request.query,
            optimized_query=optimized_query,
            papers=search_results,
            ai_summary=ai_summary,
            related_topics=related_topics,
            sources=["PubMed", "Semantic Scholar", "bioRxiv"],
            search_time_ms=search_time_ms
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Perplexity搜索失败: {str(e)}")


@router.post("/enhanced-literature-search")
async def enhanced_literature_search(
    request: Dict[str, Any],
    current_user: User = Depends(get_current_active_user)
):
    """
    增强的文献搜索，整合智能提示词生成和可选文献服务
    """
    try:
        requirements = request.get("requirements", {})
        user_query = request.get("query", "")
        enable_literature_search = request.get("enable_literature_search", True)
        
        # 1. 生成智能查询
        prompt_generator = get_smart_prompt_generator()
        query_result = await prompt_generator.generate_literature_search_queries(
            requirements=requirements,
            user_query=user_query,
            max_queries=3
        )
        
        # 2. 执行搜索
        search_results = []
        for query in query_result["suggested_queries"][:2]:  # 使用前2个查询
            results = await literature_service.search_literature(
                query=query,
                top_k=5
            )
            search_results.extend(results)
        
        # 3. 可选的增强文献搜集（已禁用）
        optional_results = {}
        
        # 4. 合并结果
        combined_results = {
            "smart_queries": query_result,
            "literature_results": search_results,
            "optional_literature": optional_results,
            "total_papers": len(search_results),
            "sources": ["内置文献库", "智能查询优化"],
            "recommendations": _generate_search_recommendations(requirements, search_results)
        }
        
        # 5. 如果有可选文献结果，添加到源列表
        if optional_results.get("supporting_papers"):
            combined_results["sources"].extend(["PubMed", "Semantic Scholar", "bioRxiv"])
            combined_results["total_papers"] += len(optional_results["supporting_papers"])
        
        return combined_results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"增强文献搜索失败: {str(e)}")


async def _generate_ai_summary(
    papers: List[Dict[str, Any]], 
    query: str, 
    requirements: Optional[Dict[str, Any]] = None
) -> str:
    """生成AI摘要"""
    if not papers:
        return "未找到相关文献。"
    
    # 构建摘要
    summary_parts = [
        f"基于查询 \"{query}\"，找到了 {len(papers)} 篇相关文献。",
        "\n\n**主要发现：**"
    ]
    
    # 分析技术趋势
    tech_counts = {}
    for paper in papers:
        for tag in paper.get("technology_tags", []):
            tech_counts[tag] = tech_counts.get(tag, 0) + 1
    
    if tech_counts:
        most_common_tech = max(tech_counts, key=tech_counts.get)
        summary_parts.append(f"\n• **主流技术**：{most_common_tech} 在相关研究中应用最为广泛")
    
    # 分析期刊分布
    journals = [paper.get("journal", "") for paper in papers]
    high_impact_journals = [j for j in journals if any(keyword in j for keyword in ["Nature", "Cell", "Science"])]
    
    if high_impact_journals:
        summary_parts.append(f"\n• **权威期刊**：包含 {len(high_impact_journals)} 篇高影响因子期刊文章")
    
    # 分析发表年份
    years = [paper.get("publication_year", 0) for paper in papers if paper.get("publication_year", 0) > 0]
    if years:
        recent_papers = len([y for y in years if y >= 2022])
        summary_parts.append(f"\n• **时效性**：{recent_papers} 篇为近两年发表的最新研究")
    
    # 个性化建议
    if requirements:
        budget = requirements.get("budget", "")
        if "5万" in budget:
            summary_parts.append(f"\n\n**针对您的预算建议**：建议优先参考方法学文献，选择性价比较高的技术方案。")
        elif requirements.get("researchGoal"):
            goal = requirements["researchGoal"]
            summary_parts.append(f"\n\n**针对您的研究目标**：这些文献为{goal}提供了重要的方法学指导。")
    
    return "".join(summary_parts)


def _generate_search_recommendations(
    requirements: Dict[str, Any], 
    papers: List[Dict[str, Any]]
) -> List[str]:
    """生成搜索建议"""
    recommendations = []
    
    if len(papers) < 5:
        recommendations.append("结果较少，建议尝试更宽泛的搜索词汇")
    
    if requirements.get("experimentType"):
        exp_type = requirements["experimentType"]
        if "RNA" in exp_type:
            recommendations.append("建议搜索scRNA-seq数据分析最佳实践")
        elif "ATAC" in exp_type:
            recommendations.append("建议搜索scATAC-seq质量控制方法")
    
    # 分析结果质量
    high_quality_papers = [p for p in papers if p.get("impact_factor", 0) > 10]
    if len(high_quality_papers) > len(papers) * 0.7:
        recommendations.append("搜索结果质量较高，建议深入阅读关键文献")
    
    if not recommendations:
        recommendations.append("搜索结果丰富，建议按相关性和影响因子筛选")
    
    return recommendations