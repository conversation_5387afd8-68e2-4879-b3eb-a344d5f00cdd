"""
Test suite for cache manager
"""
import pytest
import asyncio
import json
import gzip
from unittest.mock import AsyncMock, patch, MagicMock

from app.utils.cache_manager import Cache<PERSON>anager
from app.exceptions.keyword_exceptions import CacheError


class TestCacheManager:
    """Test cases for CacheManager"""
    
    @pytest.fixture
    async def cache_manager(self):
        """Create cache manager instance for testing"""
        manager = CacheManager()
        # Mock Redis for testing
        manager.redis_client = AsyncMock()
        manager._connected = True
        return manager
    
    async def test_initialization_success(self):
        """Test successful cache initialization"""
        with patch('redis.asyncio.ConnectionPool') as mock_pool, \
             patch('redis.asyncio.Redis') as mock_redis:
            
            mock_redis_instance = AsyncMock()
            mock_redis.return_value = mock_redis_instance
            mock_redis_instance.ping.return_value = True
            
            manager = CacheManager()
            result = await manager.initialize()
            
            assert result == True
            assert manager._connected == True
    
    async def test_initialization_failure(self):
        """Test cache initialization failure"""
        with patch('redis.asyncio.ConnectionPool') as mock_pool, \
             patch('redis.asyncio.Redis') as mock_redis:
            
            mock_redis_instance = AsyncMock()
            mock_redis.return_value = mock_redis_instance
            mock_redis_instance.ping.side_effect = Exception("Connection failed")
            
            manager = CacheManager()
            result = await manager.initialize()
            
            assert result == False
            assert manager._connected == False
    
    async def test_is_available(self, cache_manager):
        """Test cache availability check"""
        # Cache is available
        cache_manager._connected = True
        cache_manager.keyword_config.ENABLE_CACHING = True
        assert cache_manager.is_available() == True
        
        # Cache is not connected
        cache_manager._connected = False
        assert cache_manager.is_available() == False
        
        # Caching is disabled
        cache_manager._connected = True
        cache_manager.keyword_config.ENABLE_CACHING = False
        assert cache_manager.is_available() == False
    
    async def test_basic_get_set(self, cache_manager):
        """Test basic get and set operations"""
        test_key = "test_key"
        test_value = {"data": "test_data", "number": 42}
        
        # Mock Redis responses
        cache_manager.redis_client.get.return_value = None
        cache_manager.redis_client.setex.return_value = True
        
        # Test cache miss
        result = await cache_manager.get(test_key)
        assert result is None
        
        # Test cache set
        success = await cache_manager.set(test_key, test_value, ttl=3600)
        assert success == True
        
        # Mock successful get
        serialized_data = json.dumps(test_value, ensure_ascii=False).encode('utf-8')
        if cache_manager.cache_config.ENABLE_COMPRESSION:
            serialized_data = gzip.compress(serialized_data)
        
        cache_manager.redis_client.get.return_value = serialized_data
        
        # Test cache hit
        result = await cache_manager.get(test_key)
        assert result == test_value
    
    async def test_compression(self, cache_manager):
        """Test data compression functionality"""
        test_key = "compress_test"
        test_value = {"large_data": "x" * 1000}
        
        cache_manager.cache_config.ENABLE_COMPRESSION = True
        cache_manager.redis_client.setex.return_value = True
        
        # Set data with compression
        await cache_manager.set(test_key, test_value, compress=True)
        
        # Verify setex was called with compressed data
        call_args = cache_manager.redis_client.setex.call_args
        compressed_data = call_args[0][2]
        
        # Decompress and verify
        decompressed = gzip.decompress(compressed_data)
        original_data = json.loads(decompressed.decode('utf-8'))
        assert original_data == test_value
    
    async def test_delete(self, cache_manager):
        """Test cache deletion"""
        test_key = "delete_test"
        
        # Mock successful deletion
        cache_manager.redis_client.delete.return_value = 1
        
        result = await cache_manager.delete(test_key)
        assert result == True
        
        # Mock key not found
        cache_manager.redis_client.delete.return_value = 0
        
        result = await cache_manager.delete(test_key)
        assert result == False
    
    async def test_exists(self, cache_manager):
        """Test key existence check"""
        test_key = "exists_test"
        
        # Key exists
        cache_manager.redis_client.exists.return_value = 1
        assert await cache_manager.exists(test_key) == True
        
        # Key doesn't exist
        cache_manager.redis_client.exists.return_value = 0
        assert await cache_manager.exists(test_key) == False
    
    async def test_cache_key_generation(self, cache_manager):
        """Test cache key generation"""
        # Test with different parameters
        key1 = cache_manager.generate_cache_key("terminology", term="单细胞")
        key2 = cache_manager.generate_cache_key("terminology", term="T细胞")
        key3 = cache_manager.generate_cache_key("query_result", query_hash="abc123")
        
        # Keys should be different for different parameters
        assert key1 != key2
        assert key1 != key3
        assert key2 != key3
        
        # Same parameters should generate same key
        key1_duplicate = cache_manager.generate_cache_key("terminology", term="单细胞")
        assert key1 == key1_duplicate
    
    async def test_get_or_set(self, cache_manager):
        """Test get_or_set functionality"""
        test_key = "get_or_set_test"
        test_value = {"computed": "value"}
        
        # Mock cache miss, then successful set
        cache_manager.redis_client.get.return_value = None
        cache_manager.redis_client.setex.return_value = True
        
        def factory_function():
            return test_value
        
        result = await cache_manager.get_or_set(test_key, factory_function, ttl=3600)
        assert result == test_value
        
        # Verify set was called
        cache_manager.redis_client.setex.assert_called_once()
    
    async def test_get_or_set_async_factory(self, cache_manager):
        """Test get_or_set with async factory function"""
        test_key = "async_factory_test"
        test_value = {"async": "result"}
        
        cache_manager.redis_client.get.return_value = None
        cache_manager.redis_client.setex.return_value = True
        
        async def async_factory():
            return test_value
        
        result = await cache_manager.get_or_set(test_key, async_factory, ttl=3600)
        assert result == test_value
    
    async def test_batch_get(self, cache_manager):
        """Test batch get operations"""
        test_keys = ["key1", "key2", "key3"]
        test_values = [
            {"data": "value1"},
            {"data": "value2"},
            None  # key3 not found
        ]
        
        # Mock pipeline behavior
        mock_pipeline = AsyncMock()
        cache_manager.redis_client.pipeline.return_value = mock_pipeline
        
        # Mock serialized values
        serialized_values = []
        for value in test_values:
            if value is not None:
                serialized = json.dumps(value, ensure_ascii=False).encode('utf-8')
                if cache_manager.cache_config.ENABLE_COMPRESSION:
                    serialized = gzip.compress(serialized)
                serialized_values.append(serialized)
            else:
                serialized_values.append(None)
        
        mock_pipeline.execute.return_value = serialized_values
        
        result = await cache_manager.batch_get(test_keys)
        
        expected = {
            "key1": test_values[0],
            "key2": test_values[1]
            # key3 should not be in result since it's None
        }
        
        assert result == expected
    
    async def test_batch_set(self, cache_manager):
        """Test batch set operations"""
        test_items = {
            "key1": {"data": "value1"},
            "key2": {"data": "value2"},
            "key3": {"data": "value3"}
        }
        
        # Mock pipeline behavior
        mock_pipeline = AsyncMock()
        cache_manager.redis_client.pipeline.return_value = mock_pipeline
        mock_pipeline.execute.return_value = [True, True, True]
        
        result = await cache_manager.batch_set(test_items, ttl=3600)
        
        assert result == 3  # All items successfully set
        assert mock_pipeline.setex.call_count == 3
    
    async def test_invalidate_pattern(self, cache_manager):
        """Test pattern-based cache invalidation"""
        pattern = "test_pattern:*"
        matching_keys = [b"test_pattern:key1", b"test_pattern:key2"]
        
        # Mock scan_iter to return matching keys
        async def mock_scan_iter(match=None):
            for key in matching_keys:
                yield key
        
        cache_manager.redis_client.scan_iter = mock_scan_iter
        cache_manager.redis_client.delete.return_value = len(matching_keys)
        
        result = await cache_manager.invalidate_pattern(pattern)
        
        assert result == len(matching_keys)
        cache_manager.redis_client.delete.assert_called_once_with(*matching_keys)
    
    async def test_get_stats(self, cache_manager):
        """Test cache statistics retrieval"""
        mock_info = {
            "connected_clients": 5,
            "used_memory_human": "1.5M",
            "keyspace_hits": 1000,
            "keyspace_misses": 200,
            "total_commands_processed": 5000,
            "instantaneous_ops_per_sec": 10,
            "redis_version": "6.2.0"
        }
        
        cache_manager.redis_client.info.return_value = mock_info
        
        stats = await cache_manager.get_stats()
        
        assert stats["available"] == True
        assert stats["connected_clients"] == 5
        assert stats["used_memory"] == "1.5M"
        assert stats["keyspace_hits"] == 1000
        assert stats["redis_version"] == "6.2.0"
    
    async def test_error_handling(self, cache_manager):
        """Test error handling in cache operations"""
        test_key = "error_test"
        
        # Test get operation error
        cache_manager.redis_client.get.side_effect = Exception("Redis error")
        
        with pytest.raises(CacheError):
            await cache_manager.get(test_key)
        
        # Test set operation error
        cache_manager.redis_client.setex.side_effect = Exception("Redis error")
        
        with pytest.raises(CacheError):
            await cache_manager.set(test_key, {"data": "test"})
    
    async def test_cache_unavailable_fallback(self):
        """Test fallback behavior when cache is unavailable"""
        manager = CacheManager()
        manager._connected = False
        
        # All operations should return gracefully when cache is unavailable
        assert await manager.get("test_key") is None
        assert await manager.set("test_key", "value") == False
        assert await manager.delete("test_key") == False
        assert await manager.exists("test_key") == False
        assert await manager.invalidate_pattern("pattern") == 0
        
        batch_result = await manager.batch_get(["key1", "key2"])
        assert batch_result == {}
        
        batch_set_result = await manager.batch_set({"key": "value"})
        assert batch_set_result == 0
    
    async def test_ttl_handling(self, cache_manager):
        """Test TTL (Time To Live) handling"""
        test_key = "ttl_test"
        test_value = {"ttl": "test"}
        ttl = 7200
        
        cache_manager.redis_client.setex.return_value = True
        
        # Test with TTL
        await cache_manager.set(test_key, test_value, ttl=ttl)
        cache_manager.redis_client.setex.assert_called_with(
            test_key, ttl, json.dumps(test_value, ensure_ascii=False).encode('utf-8')
        )
        
        # Test without TTL
        cache_manager.redis_client.set.return_value = True
        await cache_manager.set(test_key, test_value)
        cache_manager.redis_client.set.assert_called_once()
    
    async def test_close(self, cache_manager):
        """Test cache manager cleanup"""
        await cache_manager.close()
        
        cache_manager.redis_client.aclose.assert_called_once()
        assert cache_manager._connected == False


class TestCacheIntegration:
    """Integration tests for cache functionality"""
    
    async def test_realistic_workflow(self):
        """Test realistic cache usage workflow"""
        manager = CacheManager()
        
        # Mock Redis for integration test
        manager.redis_client = AsyncMock()
        manager._connected = True
        
        # Simulate caching terminology mappings
        terminology_data = {
            "单细胞": "single cell",
            "T细胞": "T cell",
            "基因表达": "gene expression"
        }
        
        # Cache each term
        for chinese, english in terminology_data.items():
            cache_key = manager.generate_cache_key("terminology", term=chinese)
            await manager.set(cache_key, english, ttl=86400)
        
        # Verify all terms were cached
        assert manager.redis_client.setex.call_count == len(terminology_data)
    
    async def test_cache_performance(self):
        """Test cache performance characteristics"""
        import time
        
        manager = CacheManager()
        manager.redis_client = AsyncMock()
        manager._connected = True
        
        # Test key generation performance
        start_time = time.time()
        for i in range(1000):
            manager.generate_cache_key("test", param=f"value_{i}")
        
        key_gen_duration = time.time() - start_time
        assert key_gen_duration < 1.0  # Should be fast
        
        # Test serialization performance
        large_data = {"data": "x" * 10000}
        
        start_time = time.time()
        serialized = json.dumps(large_data, ensure_ascii=False).encode('utf-8')
        if manager.cache_config.ENABLE_COMPRESSION:
            compressed = gzip.compress(serialized)
        
        serialization_duration = time.time() - start_time
        assert serialization_duration < 0.1  # Should be reasonably fast
    
    async def test_memory_efficiency(self):
        """Test memory efficiency of cache operations"""
        manager = CacheManager()
        manager.redis_client = AsyncMock()
        manager._connected = True
        
        # Test compression ratio
        large_text = "单细胞RNA测序分析" * 1000
        test_data = {"large_field": large_text, "metadata": {"count": 1000}}
        
        # Serialize without compression
        uncompressed = json.dumps(test_data, ensure_ascii=False).encode('utf-8')
        
        # Serialize with compression
        compressed = gzip.compress(uncompressed, compresslevel=6)
        
        # Compression should significantly reduce size for repetitive data
        compression_ratio = len(compressed) / len(uncompressed)
        assert compression_ratio < 0.5  # At least 50% compression
    
    async def test_concurrent_access(self):
        """Test concurrent cache access"""
        manager = CacheManager()
        manager.redis_client = AsyncMock()
        manager._connected = True
        
        # Mock successful operations
        manager.redis_client.get.return_value = None
        manager.redis_client.setex.return_value = True
        
        # Simulate concurrent set operations
        async def cache_operation(key_suffix):
            key = f"concurrent_test_{key_suffix}"
            data = {"id": key_suffix, "data": f"test_data_{key_suffix}"}
            return await manager.set(key, data, ttl=3600)
        
        # Run multiple operations concurrently
        tasks = [cache_operation(i) for i in range(10)]
        results = await asyncio.gather(*tasks)
        
        # All operations should succeed
        assert all(results)
        assert manager.redis_client.setex.call_count == 10