"""
统一文献API服务
整合所有文献检索API（PubMed、Google Scholar、OpenAlex、Semantic Scholar等）
支持多个开源API并发查询和结果汇总
"""
import asyncio
import aiohttp
import json
import logging
import xml.etree.ElementTree as ET
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from urllib.parse import quote
from concurrent.futures import ThreadPoolExecutor
import time

from app.core.external_apis import get_api_manager
from app.core.config import settings

logger = logging.getLogger(__name__)


class UnifiedLiteratureAPIService:
    """统一文献API服务"""
    
    def __init__(self):
        self.api_manager = get_api_manager()
        self.session: Optional[aiohttp.ClientSession] = None
        
        # API配置
        self.api_configs = {
            "pubmed": {
                "name": "PubMed",
                "base_url": "https://eutils.ncbi.nlm.nih.gov/entrez/eutils",
                "timeout": 15,
                "max_results": 20,
                "priority": 1,  # 优先级：1最高
                "enabled": True
            },
            "openalex": {
                "name": "OpenAlex",
                "base_url": "https://api.openalex.org",
                "timeout": 10,
                "max_results": 20,
                "priority": 2,
                "enabled": True
            },
            "semantic_scholar": {
                "name": "Semantic Scholar",
                "base_url": "https://api.semanticscholar.org/graph/v1",
                "timeout": 12,
                "max_results": 15,
                "priority": 3,
                "enabled": True
            },
            "google_scholar": {
                "name": "Google Scholar",
                "base_url": "https://serpapi.com/search",
                "timeout": 8,
                "max_results": 10,
                "priority": 4,
                "enabled": True  # 由于限制较多，优先级较低
            }
        }
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={
                    'User-Agent': 'CellForge-AI/1.0 (Research Literature Service)',
                    'Accept': 'application/json'
                }
            )
        return self.session
    
    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def comprehensive_search(
        self, 
        query: str, 
        max_results_per_source: int = 10,
        enabled_sources: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        综合搜索多个数据源
        
        Args:
            query: 搜索查询
            max_results_per_source: 每个数据源最大结果数
            enabled_sources: 启用的数据源列表，为None时使用所有可用源
        
        Returns:
            包含所有搜索结果的字典
        """
        logger.info(f"开始综合文献搜索: {query}")
        start_time = time.time()
        
        # 确定要使用的数据源
        if enabled_sources is None:
            enabled_sources = [name for name, config in self.api_configs.items() if config["enabled"]]
        
        # 验证API可用性
        available_sources = []
        for source in enabled_sources:
            if source in self.api_configs and self._is_api_available(source):
                available_sources.append(source)
        
        if not available_sources:
            logger.warning("没有可用的文献数据源")
            return self._empty_search_result(query)
        
        logger.info(f"可用数据源: {available_sources}")
        
        # 并发搜索所有可用的数据源
        search_tasks = []
        for source in available_sources:
            task = self._create_search_task(source, query, max_results_per_source)
            search_tasks.append((source, task))
        
        # 执行并发搜索，使用超时控制
        results = {}
        successful_sources = []
        failed_sources = []
        
        try:
            # 设置总体超时时间
            task_results = await asyncio.wait_for(
                asyncio.gather(*[task[1] for task in search_tasks], return_exceptions=True),
                timeout=45  # 45秒总超时
            )
            
            for i, (source_name, _) in enumerate(search_tasks):
                result = task_results[i]
                if isinstance(result, Exception):
                    logger.warning(f"{source_name}搜索失败: {result}")
                    failed_sources.append(source_name)
                    results[source_name] = []
                else:
                    results[source_name] = result or []
                    if result:
                        successful_sources.append(source_name)
                        logger.info(f"{source_name}找到{len(result)}篇文献")
        
        except asyncio.TimeoutError:
            logger.error("文献搜索总体超时")
            for source_name, _ in search_tasks:
                failed_sources.append(source_name)
                results[source_name] = []
        
        # 合并和去重结果
        merged_results = self._merge_and_deduplicate_results(results, query)
        
        search_time = time.time() - start_time
        logger.info(f"综合搜索完成，耗时{search_time:.2f}秒，成功源:{successful_sources}")
        
        return {
            "query": query,
            "papers": merged_results,
            "sources_used": successful_sources,
            "sources_failed": failed_sources,
            "total_found": len(merged_results),
            "search_time": search_time,
            "search_timestamp": datetime.utcnow().isoformat()
        }
    
    def _is_api_available(self, source: str) -> bool:
        """检查API是否可用"""
        try:
            if source == "pubmed":
                return True  # PubMed是免费API，总是可用
            elif source == "openalex":
                return True  # OpenAlex是免费API，总是可用
            elif source == "semantic_scholar":
                return True  # Semantic Scholar有免费层
            elif source == "google_scholar":
                # 检查是否配置了SerpAPI密钥
                config = self.api_manager.get_api_config("google_scholar")
                return config and config.is_available()
            return False
        except Exception:
            return False
    
    def _create_search_task(self, source: str, query: str, max_results: int):
        """为指定数据源创建搜索任务"""
        if source == "pubmed":
            return self._search_pubmed(query, max_results)
        elif source == "openalex":
            return self._search_openalex(query, max_results)
        elif source == "semantic_scholar":
            return self._search_semantic_scholar(query, max_results)
        elif source == "google_scholar":
            return self._search_google_scholar(query, max_results)
        else:
            return self._empty_task()
    
    async def _empty_task(self) -> List[Dict]:
        """空任务，返回空列表"""
        return []
    
    async def _search_pubmed(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """搜索PubMed数据库"""
        try:
            session = await self._get_session()
            base_url = self.api_configs["pubmed"]["base_url"]
            timeout = self.api_configs["pubmed"]["timeout"]
            
            # 第一步：搜索获取PMID列表
            search_url = f"{base_url}/esearch.fcgi"
            search_params = {
                "db": "pubmed",
                "term": query,
                "retmax": str(max_results),
                "retmode": "json",
                "sort": "relevance"
            }
            
            async with asyncio.timeout(timeout):
                async with session.get(search_url, params=search_params) as response:
                    if response.status != 200:
                        logger.error(f"PubMed搜索失败: {response.status}")
                        return []
                    
                    search_data = await response.json()
                    id_list = search_data.get("esearchresult", {}).get("idlist", [])
                    
                    if not id_list:
                        logger.info(f"PubMed未找到相关文献: {query}")
                        return []
            
            # 第二步：获取详细信息
            fetch_url = f"{base_url}/efetch.fcgi"
            fetch_params = {
                "db": "pubmed",
                "id": ",".join(id_list),
                "retmode": "xml",
                "rettype": "abstract"
            }
            
            async with asyncio.timeout(timeout):
                async with session.get(fetch_url, params=fetch_params) as response:
                    if response.status != 200:
                        logger.error(f"PubMed获取详情失败: {response.status}")
                        return []
                    
                    xml_data = await response.text()
                    papers = self._parse_pubmed_xml(xml_data)
                    logger.info(f"PubMed解析出 {len(papers)} 篇文献")
                    return papers
        
        except Exception as e:
            logger.error(f"PubMed搜索异常: {e}")
            return []
    
    def _parse_pubmed_xml(self, xml_data: str) -> List[Dict[str, Any]]:
        """解析PubMed XML数据"""
        papers = []
        try:
            root = ET.fromstring(xml_data)
            
            for article in root.findall(".//PubmedArticle"):
                paper = self._extract_pubmed_article_info(article)
                if paper:
                    papers.append(paper)
        
        except Exception as e:
            logger.error(f"解析PubMed XML失败: {e}")
        
        return papers
    
    def _extract_pubmed_article_info(self, article) -> Optional[Dict[str, Any]]:
        """从PubMed文章XML中提取信息"""
        try:
            medline_citation = article.find(".//MedlineCitation")
            if medline_citation is None:
                return None
            
            pmid = medline_citation.find("PMID")
            pmid_text = pmid.text if pmid is not None else ""
            
            article_elem = medline_citation.find("Article")
            if article_elem is None:
                return None
            
            # 提取基本信息
            title_elem = article_elem.find("ArticleTitle")
            title = title_elem.text if title_elem is not None else ""
            
            # 提取摘要
            abstract_elem = article_elem.find(".//AbstractText")
            abstract = abstract_elem.text if abstract_elem is not None else ""
            
            # 提取作者
            authors = []
            author_list = article_elem.find("AuthorList")
            if author_list is not None:
                for author in author_list.findall("Author")[:5]:  # 最多取5个作者
                    last_name = author.find("LastName")
                    first_name = author.find("ForeName") or author.find("Initials")
                    if last_name is not None:
                        name = last_name.text
                        if first_name is not None:
                            name = f"{last_name.text}, {first_name.text}"
                        authors.append(name)
            
            # 提取期刊信息
            journal_elem = article_elem.find("Journal")
            journal = ""
            if journal_elem is not None:
                journal_title = journal_elem.find(".//Title")
                if journal_title is not None:
                    journal = journal_title.text
            
            # 提取发表年份
            pub_date = article_elem.find(".//PubDate")
            year = datetime.now().year  # 默认当前年份
            if pub_date is not None:
                year_elem = pub_date.find("Year")
                if year_elem is not None:
                    try:
                        year = int(year_elem.text)
                    except ValueError:
                        pass
            
            # 提取DOI
            doi = ""
            article_id_list = article_elem.find(".//ArticleIdList")
            if article_id_list is not None:
                for article_id in article_id_list.findall("ArticleId"):
                    if article_id.get("IdType") == "doi":
                        doi = article_id.text
                        break
            
            return {
                "title": title,
                "authors": authors,
                "journal": journal,
                "publication_year": year,
                "doi": doi,
                "pubmed_id": pmid_text,
                "abstract": abstract[:500] + "..." if len(abstract) > 500 else abstract,
                "source": "PubMed",
                "relevance_score": 0.85,
                "url": f"https://pubmed.ncbi.nlm.nih.gov/{pmid_text}/" if pmid_text else "",
                "open_access": False,  # PubMed不直接提供开放获取信息
                "citation_count": 0  # PubMed不提供引用数据
            }
        
        except Exception as e:
            logger.error(f"提取PubMed文章信息失败: {e}")
            return None
    
    async def _search_openalex(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """搜索OpenAlex数据库"""
        try:
            session = await self._get_session()
            base_url = self.api_configs["openalex"]["base_url"]
            timeout = self.api_configs["openalex"]["timeout"]
            
            # OpenAlex搜索参数
            url = f"{base_url}/works"
            params = {
                "search": query,
                "per_page": max_results,
                "sort": "relevance_score:desc",
                "filter": "publication_year:>2015,type:journal-article"  # 只要2015年后的期刊文章
            }
            
            async with asyncio.timeout(timeout):
                async with session.get(url, params=params) as response:
                    if response.status != 200:
                        logger.error(f"OpenAlex搜索失败: {response.status}")
                        return []
                    
                    data = await response.json()
                    works = data.get("results", [])
                    
                    papers = []
                    for work in works:
                        paper = self._parse_openalex_work(work)
                        if paper:
                            papers.append(paper)
                    
                    logger.info(f"OpenAlex解析出 {len(papers)} 篇文献")
                    return papers
        
        except Exception as e:
            logger.error(f"OpenAlex搜索异常: {e}")
            return []
    
    def _parse_openalex_work(self, work: Dict) -> Optional[Dict[str, Any]]:
        """解析OpenAlex作品数据"""
        try:
            # 提取基本信息
            title = work.get("title", "")
            if not title:
                return None
            
            # 提取作者
            authors = []
            authorships = work.get("authorships", [])
            for authorship in authorships[:5]:  # 最多取5个作者
                author = authorship.get("author", {})
                display_name = author.get("display_name", "")
                if display_name:
                    authors.append(display_name)
            
            # 提取期刊信息
            host_venue = work.get("host_venue", {})
            journal = host_venue.get("display_name", "")
            
            # 提取年份
            publication_year = work.get("publication_year", datetime.now().year)
            
            # 提取DOI
            doi = work.get("doi", "")
            if doi and doi.startswith("https://doi.org/"):
                doi = doi.replace("https://doi.org/", "")
            
            # 提取摘要（OpenAlex可能没有完整摘要）
            abstract_inverted = work.get("abstract_inverted_index", {})
            abstract = ""
            if abstract_inverted:
                # 重构摘要（简化版本）
                word_list = []
                for word, positions in abstract_inverted.items():
                    for pos in positions:
                        word_list.append((pos, word))
                word_list.sort(key=lambda x: x[0])
                abstract = " ".join([word for pos, word in word_list[:100]])  # 限制长度
                if len(word_list) > 100:
                    abstract += "..."
            
            return {
                "title": title,
                "authors": authors,
                "journal": journal,
                "publication_year": publication_year,
                "doi": doi,
                "abstract": abstract,
                "source": "OpenAlex",
                "relevance_score": 0.80,
                "url": work.get("primary_location", {}).get("landing_page_url", ""),
                "open_access": work.get("open_access", {}).get("is_oa", False),
                "citation_count": work.get("cited_by_count", 0),
                "openalex_id": work.get("id", "")
            }
        
        except Exception as e:
            logger.error(f"解析OpenAlex作品失败: {e}")
            return None
    
    async def _search_semantic_scholar(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """搜索Semantic Scholar"""
        try:
            session = await self._get_session()
            base_url = self.api_configs["semantic_scholar"]["base_url"]
            timeout = self.api_configs["semantic_scholar"]["timeout"]
            
            url = f"{base_url}/paper/search"
            params = {
                "query": query,
                "limit": max_results,
                "fields": "title,authors,journal,year,abstract,citationCount,influentialCitationCount,externalIds,openAccessPdf"
            }
            
            # Semantic Scholar API可能需要API密钥
            headers = {}
            config = self.api_manager.get_api_config("semantic_scholar")
            if config and hasattr(config, 'api_key') and config.api_key:
                headers['x-api-key'] = config.api_key
            
            async with asyncio.timeout(timeout):
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 429:
                        logger.warning("Semantic Scholar API限流，跳过")
                        return []
                    elif response.status != 200:
                        logger.error(f"Semantic Scholar搜索失败: {response.status}")
                        return []
                    
                    data = await response.json()
                    papers_data = data.get("data", [])
                    
                    papers = []
                    for paper_data in papers_data:
                        paper = self._parse_semantic_scholar_paper(paper_data)
                        if paper:
                            papers.append(paper)
                    
                    logger.info(f"Semantic Scholar解析出 {len(papers)} 篇文献")
                    return papers
        
        except Exception as e:
            logger.warning(f"Semantic Scholar搜索异常: {e}")
            return []
    
    def _parse_semantic_scholar_paper(self, paper_data: Dict) -> Optional[Dict[str, Any]]:
        """解析Semantic Scholar论文数据"""
        try:
            title = paper_data.get("title", "")
            if not title:
                return None
            
            # 提取作者
            authors = []
            authors_data = paper_data.get("authors", [])
            for author_data in authors_data[:5]:  # 最多取5个作者
                name = author_data.get("name", "")
                if name:
                    authors.append(name)
            
            # 提取期刊
            journal_data = paper_data.get("journal", {}) or {}
            journal = journal_data.get("name", "")
            
            # 提取其他信息
            year = paper_data.get("year", datetime.now().year)
            abstract = paper_data.get("abstract", "")
            if abstract and len(abstract) > 500:
                abstract = abstract[:500] + "..."
            
            # 提取DOI
            external_ids = paper_data.get("externalIds", {}) or {}
            doi = external_ids.get("DOI", "")
            pubmed_id = external_ids.get("PubMed", "")
            
            # 开放获取信息
            open_access_pdf = paper_data.get("openAccessPdf", {})
            is_open_access = bool(open_access_pdf and open_access_pdf.get("url"))
            
            return {
                "title": title,
                "authors": authors,
                "journal": journal,
                "publication_year": year,
                "doi": doi,
                "pubmed_id": pubmed_id,
                "abstract": abstract,
                "source": "Semantic Scholar",
                "relevance_score": 0.82,
                "citation_count": paper_data.get("citationCount", 0),
                "influential_citation_count": paper_data.get("influentialCitationCount", 0),
                "open_access": is_open_access,
                "url": open_access_pdf.get("url", "") if is_open_access else ""
            }
        
        except Exception as e:
            logger.error(f"解析Semantic Scholar论文失败: {e}")
            return None
    
    async def _search_google_scholar(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """搜索Google Scholar (通过SerpAPI)"""
        try:
            config = self.api_manager.get_api_config("google_scholar")
            if not config or not config.is_available():
                logger.warning("Google Scholar API不可用，跳过")
                return []
            
            session = await self._get_session()
            timeout = self.api_configs["google_scholar"]["timeout"]
            
            url = config.base_url
            params = {
                "engine": "google_scholar",
                "q": query,
                "num": max_results,
                "api_key": config.api_key
            }
            
            async with asyncio.timeout(timeout):
                async with session.get(url, params=params) as response:
                    if response.status != 200:
                        logger.error(f"Google Scholar搜索失败: {response.status}")
                        return []
                    
                    data = await response.json()
                    results = data.get("organic_results", [])
                    
                    papers = []
                    for result in results:
                        paper = self._parse_google_scholar_result(result)
                        if paper:
                            papers.append(paper)
                    
                    logger.info(f"Google Scholar解析出 {len(papers)} 篇文献")
                    return papers
        
        except Exception as e:
            logger.warning(f"Google Scholar搜索异常: {e}")
            return []
    
    def _parse_google_scholar_result(self, result: Dict) -> Optional[Dict[str, Any]]:
        """解析Google Scholar搜索结果"""
        try:
            title = result.get("title", "")
            if not title:
                return None
            
            # 提取作者信息
            authors = []
            publication_info = result.get("publication_info", {})
            authors_text = publication_info.get("authors", "")
            
            if authors_text:
                if isinstance(authors_text, str):
                    authors = [author.strip() for author in authors_text.split(",")][:5]
                elif isinstance(authors_text, list):
                    authors = [str(author).strip() for author in authors_text][:5]
            
            # 提取年份
            year = datetime.now().year
            summary = publication_info.get("summary", "")
            if isinstance(summary, str) and summary:
                import re
                year_match = re.search(r'\b(19|20)\d{2}\b', summary)
                if year_match:
                    year = int(year_match.group())
            
            # 提取期刊
            journal = self._extract_journal_from_pub_info(summary)
            
            # 处理摘要
            snippet = result.get("snippet", "")
            if isinstance(snippet, list):
                snippet = " ".join(str(s) for s in snippet)
            elif not isinstance(snippet, str):
                snippet = str(snippet) if snippet else ""
            
            abstract = snippet[:500] + "..." if len(snippet) > 500 else snippet
            
            # 提取引用数
            inline_links = result.get("inline_links", {})
            cited_by_info = inline_links.get("cited_by", {})
            citation_count = cited_by_info.get("total", 0) if cited_by_info else 0
            
            return {
                "title": title,
                "authors": authors,
                "journal": journal,
                "publication_year": year,
                "abstract": abstract,
                "source": "Google Scholar",
                "relevance_score": 0.78,
                "citation_count": citation_count,
                "url": result.get("link", ""),
                "open_access": False  # Google Scholar不直接提供开放获取信息
            }
        
        except Exception as e:
            logger.error(f"解析Google Scholar结果失败: {e}")
            return None
    
    def _extract_journal_from_pub_info(self, pub_info: str) -> str:
        """从发表信息中提取期刊名称"""
        if not pub_info or not isinstance(pub_info, str):
            return ""
        
        # 简单的期刊名称提取逻辑
        parts = pub_info.split(" - ")
        if len(parts) > 1:
            journal_part = parts[1].split(",")[0].strip()
            return journal_part
        
        return pub_info.split(",")[0].strip() if "," in pub_info else ""
    
    def _merge_and_deduplicate_results(self, results: Dict[str, List[Dict]], query: str) -> List[Dict[str, Any]]:
        """合并和去重搜索结果"""
        all_papers = []
        seen_titles = set()
        seen_dois = set()
        
        # 按优先级排序数据源
        sorted_sources = sorted(
            results.keys(),
            key=lambda x: self.api_configs.get(x, {}).get("priority", 999)
        )
        
        for source in sorted_sources:
            papers = results.get(source, [])
            
            for paper in papers:
                if not paper or not isinstance(paper, dict):
                    continue
                
                # 标题去重
                title = paper.get("title", "").strip().lower()
                if not title:
                    continue
                
                # DOI去重（如果有DOI）
                doi = paper.get("doi", "").strip()
                if doi and doi in seen_dois:
                    continue
                
                # 标题相似性去重
                if self._is_similar_title(title, seen_titles):
                    continue
                
                # 计算综合相关性评分
                paper["relevance_score"] = self._calculate_relevance_score(paper, query)
                paper["combined_score"] = self._calculate_combined_score(paper)
                
                # 添加到结果中
                all_papers.append(paper)
                seen_titles.add(title)
                if doi:
                    seen_dois.add(doi)
        
        # 按综合评分排序
        all_papers.sort(key=lambda x: x.get("combined_score", 0), reverse=True)
        
        logger.info(f"去重后共获得 {len(all_papers)} 篇唯一文献")
        return all_papers
    
    def _is_similar_title(self, title: str, seen_titles: set) -> bool:
        """检查标题是否与已有标题相似"""
        if not title:
            return True
        
        # 简单的相似性检查：完全匹配
        return title in seen_titles
    
    def _calculate_relevance_score(self, paper: Dict, query: str) -> float:
        """计算文献相关性评分"""
        score = paper.get("relevance_score", 0.5)
        
        # 根据查询词匹配调整评分
        query_terms = query.lower().split()
        title = paper.get("title", "").lower()
        abstract = paper.get("abstract", "").lower()
        
        # 标题匹配权重更高
        title_matches = sum(1 for term in query_terms if term in title)
        abstract_matches = sum(1 for term in query_terms if term in abstract)
        
        if query_terms:
            match_score = (title_matches * 0.3 + abstract_matches * 0.1) / len(query_terms)
            score = min(1.0, score + match_score)
        
        return score
    
    def _calculate_combined_score(self, paper: Dict) -> float:
        """计算综合评分"""
        relevance_score = paper.get("relevance_score", 0.5)
        citation_count = paper.get("citation_count", 0)
        publication_year = paper.get("publication_year", 2020)
        
        # 引用数归一化（对数缩放）
        import math
        citation_score = math.log(citation_count + 1) / 10.0  # 最大1.0
        citation_score = min(citation_score, 1.0)
        
        # 年份评分（近年来的文献有加分）
        current_year = datetime.now().year
        year_score = max(0, min(0.2, (publication_year - 2015) / 10.0))
        
        # 开放获取加分
        oa_score = 0.1 if paper.get("open_access", False) else 0
        
        # 综合评分
        combined_score = (
            relevance_score * 0.5 +
            citation_score * 0.3 +
            year_score * 0.1 +
            oa_score * 0.1
        )
        
        return min(combined_score, 1.0)
    
    def _empty_search_result(self, query: str) -> Dict[str, Any]:
        """返回空的搜索结果"""
        return {
            "query": query,
            "papers": [],
            "sources_used": [],
            "sources_failed": [],
            "total_found": 0,
            "search_time": 0.0,
            "search_timestamp": datetime.utcnow().isoformat()
        }
    
    async def search_by_research_intent(
        self,
        research_intent: Dict[str, Any],
        max_results: int = 15
    ) -> Dict[str, Any]:
        """
        基于研究意图进行文献搜索
        
        Args:
            research_intent: 包含研究意图分析结果的字典
            max_results: 最大结果数
        
        Returns:
            按研究方案分类的文献搜索结果
        """
        try:
            # 从研究意图中提取搜索查询
            comprehensive_search_queries = research_intent.get("dynamic_terminology", {}).get("comprehensive_search_queries", [])
            
            if not comprehensive_search_queries:
                logger.warning("研究意图中没有搜索查询")
                return self._empty_categorized_result()
            
            # 对每个查询进行搜索
            categorized_results = {}
            
            for query_info in comprehensive_search_queries[:3]:  # 最多处理3个查询
                query = query_info.get("english_query", "")
                query_type = query_info.get("query_type", "general")
                target_research = query_info.get("target_research", "")
                
                if not query:
                    continue
                
                logger.info(f"基于研究意图搜索: {query} (类型: {query_type})")
                
                # 执行搜索
                search_result = await self.comprehensive_search(
                    query=query,
                    max_results_per_source=max_results // 3
                )
                
                # 为结果添加AI解析
                analyzed_papers = []
                for paper in search_result.get("papers", []):
                    analyzed_paper = paper.copy()
                    analyzed_paper.update(self._analyze_paper_relevance(paper, research_intent))
                    analyzed_papers.append(analyzed_paper)
                
                categorized_results[query_type] = {
                    "query": query,
                    "target_research": target_research,
                    "papers": analyzed_papers,
                    "total_found": len(analyzed_papers)
                }
            
            return {
                "research_intent_based_results": categorized_results,
                "total_categories": len(categorized_results),
                "search_timestamp": datetime.utcnow().isoformat()
            }
        
        except Exception as e:
            logger.error(f"基于研究意图的文献搜索失败: {e}")
            return self._empty_categorized_result()
    
    def _analyze_paper_relevance(self, paper: Dict, research_intent: Dict) -> Dict[str, Any]:
        """分析文献与研究意图的相关性"""
        try:
            # 提取研究意图的关键信息
            research_possibilities = research_intent.get("comprehensive_intent_analysis", {}).get("research_possibilities", [])
            primary_domain = research_intent.get("comprehensive_intent_analysis", {}).get("primary_research_domain", "")
            
            # 分析文献内容
            title = paper.get("title", "").lower()
            abstract = paper.get("abstract", "").lower()
            content = f"{title} {abstract}"
            
            # 计算与研究可能性的匹配度
            relevance_analysis = {
                "risk_factors": [],
                "attention_points": [],
                "research_value": "",
                "methodology_relevance": ""
            }
            
            # 基于领域添加风险因素和注意点
            if "cancer" in primary_domain or "tumor" in content:
                relevance_analysis["risk_factors"].extend([
                    "肿瘤样本异质性可能影响分析结果",
                    "免疫细胞浸润程度需要特别关注"
                ])
                relevance_analysis["attention_points"].extend([
                    "注意区分肿瘤细胞和正常细胞",
                    "考虑肿瘤微环境的复杂性"
                ])
                relevance_analysis["research_value"] = "为肿瘤单细胞研究提供重要方法学参考"
            
            elif "development" in primary_domain or "trajectory" in content:
                relevance_analysis["risk_factors"].extend([
                    "发育时间点的选择至关重要",
                    "细胞状态转换的捕获难度较高"
                ])
                relevance_analysis["attention_points"].extend([
                    "确保发育阶段的准确性",
                    "注意伪时间分析的准确性"
                ])
                relevance_analysis["research_value"] = "为发育轨迹分析提供重要理论基础"
            
            elif "immun" in primary_domain or "immune" in content:
                relevance_analysis["risk_factors"].extend([
                    "免疫细胞激活状态易受实验条件影响",
                    "细胞类型注释的准确性要求很高"
                ])
                relevance_analysis["attention_points"].extend([
                    "控制样本处理过程中的激活",
                    "使用标准化的免疫细胞标记"
                ])
                relevance_analysis["research_value"] = "为免疫细胞功能研究提供技术指导"
            
            # 分析方法学相关性
            if any(term in content for term in ["10x", "chromium", "droplet"]):
                relevance_analysis["methodology_relevance"] = "使用了与推荐技术平台相同的方法"
            elif any(term in content for term in ["smart-seq", "full-length"]):
                relevance_analysis["methodology_relevance"] = "提供了全长转录组测序的参考"
            else:
                relevance_analysis["methodology_relevance"] = "提供了相关的单细胞分析方法"
            
            # 生成超链接形式的搜索链接
            search_links = self._generate_literature_search_links(paper)
            relevance_analysis["search_links"] = search_links
            
            return relevance_analysis
        
        except Exception as e:
            logger.error(f"分析文献相关性失败: {e}")
            return {
                "risk_factors": ["数据质量需要仔细评估"],
                "attention_points": ["注意实验条件的标准化"],
                "research_value": "提供相关研究参考",
                "methodology_relevance": "相关方法学参考",
                "search_links": {}
            }
    
    def _generate_literature_search_links(self, paper: Dict) -> Dict[str, str]:
        """基于文献信息生成搜索链接"""
        links = {}
        
        try:
            title = paper.get("title", "")
            authors = paper.get("authors", [])
            doi = paper.get("doi", "")
            
            # 构建搜索关键词
            if title:
                # 提取标题中的关键词
                import re
                # 移除常见的停用词并提取重要词汇
                important_words = re.findall(r'\b[A-Za-z]{4,}\b', title)
                search_keywords = " ".join(important_words[:5])  # 最多5个关键词
                
                # 生成各平台的搜索链接
                links["pubmed"] = f"https://pubmed.ncbi.nlm.nih.gov/?term={quote(search_keywords)}"
                links["google_scholar"] = f"https://scholar.google.com/scholar?q={quote(search_keywords)}"
                links["semantic_scholar"] = f"https://www.semanticscholar.org/search?q={quote(search_keywords)}"
            
            # 如果有DOI，添加DOI链接
            if doi:
                links["doi_link"] = f"https://doi.org/{doi}"
            
            # 如果有原始URL，保留
            original_url = paper.get("url", "")
            if original_url:
                links["original_source"] = original_url
        
        except Exception as e:
            logger.error(f"生成搜索链接失败: {e}")
        
        return links
    
    def _empty_categorized_result(self) -> Dict[str, Any]:
        """返回空的分类搜索结果"""
        return {
            "research_intent_based_results": {},
            "total_categories": 0,
            "search_timestamp": datetime.utcnow().isoformat()
        }


# 全局服务实例
unified_literature_api_service = UnifiedLiteratureAPIService()


def get_unified_literature_api_service() -> UnifiedLiteratureAPIService:
    """获取统一文献API服务实例"""
    return unified_literature_api_service