"use client"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { But<PERSON> } from "@/components/ui/button"
import { LogOut, User, MessageSquare, Users, FileText, BookOpen, BarChart3 } from "lucide-react"

// 简化导入，避免动态导入问题
import { ConversationInterface } from "@/components/conversation-interface"
import { CustomerDashboard } from "@/components/customer-dashboard"
import { SolutionPresentation } from "@/components/solution-presentation"
import { KnowledgeManagement } from "@/components/knowledge-management"
import { AnalyticsDashboard } from "@/components/analytics-dashboard"

export default function CellForgeAI() {
  const [mounted, setMounted] = useState(false)

  // 确保组件在客户端挂载后再渲染
  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600">正在加载...</p>
        </div>
      </div>
    )
  }

  return <CellForgeAIContent />
}

function CellForgeAIContent() {
  const { user, isLoading, logout } = useAuth()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("conversation")

  // 检查认证状态
  useEffect(() => {
    console.log("主页面认证检查:", { isLoading, user: !!user })

    if (!isLoading && !user) {
      console.log("用户未登录，重定向到登录页")
      router.push("/login")
    } else if (user) {
      console.log("用户已登录:", user.name, user.role)
    }
  }, [user, isLoading, router])

  // 处理登出
  const handleLogout = () => {
    logout()
    router.push("/login")
  }

  // 如果正在加载或未登录，显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600">正在加载...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // 将重定向到登录页面
  }
  // 导航菜单配置
  const navigationItems = [
    { id: "conversation", label: "智能对话", icon: MessageSquare, component: ConversationInterface },
    { id: "customer", label: "客户画像", icon: Users, component: CustomerDashboard },
    { id: "solution", label: "方案展示", icon: FileText, component: SolutionPresentation },
    { id: "knowledge", label: "知识库", icon: BookOpen, component: KnowledgeManagement },
    { id: "analytics", label: "数据分析", icon: BarChart3, component: AnalyticsDashboard }
  ]

  const ActiveComponent = navigationItems.find(item => item.id === activeTab)?.component || ConversationInterface

  return (
    <div className="h-screen bg-slate-50 flex">
      {/* 左侧导航栏 */}
      <div className="w-64 bg-white border-r border-slate-200 flex flex-col">
        {/* 头部 */}
        <div className="p-6 border-b border-slate-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-lg">CF</span>
            </div>
            <div>
              <h1 className="text-lg font-semibold text-slate-900">CellForge AI</h1>
              <p className="text-xs text-slate-500">单细胞测序方案咨询系统</p>
            </div>
          </div>
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 p-4">
          <div className="space-y-2">
            {navigationItems.map((item) => {
              const Icon = item.icon
              return (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                    activeTab === item.id
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-slate-600 hover:bg-slate-50 hover:text-slate-900'
                  }`}
                >
                  <Icon className={`h-5 w-5 ${activeTab === item.id ? 'text-blue-600' : 'text-slate-500'}`} />
                  <span className="font-medium">{item.label}</span>
                </button>
              )
            })}
          </div>
        </nav>

        {/* 用户信息和登出 */}
        <div className="p-4 border-t border-slate-200">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-8 h-8 bg-slate-200 rounded-full flex items-center justify-center">
              <User className="h-4 w-4 text-slate-600" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-slate-900 truncate">{user.name}</p>
              <p className="text-xs text-slate-500">
                {user.role === 'super_admin' ? '超级管理员' :
                 user.role === 'sales' ? '销售人员' :
                 user.role === 'customer' ? '客户' :
                 user.role === 'operations' ? '运营人员' : '用户'}
              </p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleLogout}
            className="w-full flex items-center justify-center space-x-2"
          >
            <LogOut className="h-4 w-4" />
            <span>登出</span>
          </Button>
        </div>
      </div>

      {/* 右侧主内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 主内容区域 - 移除所有页面的顶部标题栏 */}
        <main className="flex-1">
          <ActiveComponent />
        </main>
      </div>
    </div>
  )
}
