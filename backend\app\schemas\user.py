"""
用户相关Pydantic模式
"""
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List
from datetime import datetime

from app.models.user import UserRole, UserStatus

class UserBase(BaseModel):
    """用户基础模式"""
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=50)
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    organization: Optional[str] = Field(None, max_length=255)
    department: Optional[str] = Field(None, max_length=255)
    title: Optional[str] = Field(None, max_length=255)
    preferred_language: str = Field("zh", max_length=10)
    email_notifications: bool = True
    research_interests: Optional[str] = None
    expertise_areas: Optional[str] = None

class UserCreate(UserBase):
    """用户创建模式"""
    password: str = Field(..., min_length=8, max_length=128)
    role: UserRole = UserRole.CUSTOMER

    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('密码长度至少8位')
        return v

class UserUpdate(BaseModel):
    """用户更新模式"""
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    organization: Optional[str] = Field(None, max_length=255)
    department: Optional[str] = Field(None, max_length=255)
    title: Optional[str] = Field(None, max_length=255)
    preferred_language: Optional[str] = Field(None, max_length=10)
    email_notifications: Optional[bool] = None
    research_interests: Optional[str] = None
    expertise_areas: Optional[str] = None

class UserResponse(UserBase):
    """用户响应模式"""
    id: int
    role: UserRole
    status: UserStatus
    is_verified: bool
    created_at: datetime
    updated_at: Optional[datetime]
    last_login: Optional[datetime]

    class Config:
        from_attributes = True

class UserLogin(BaseModel):
    """用户登录模式"""
    email: EmailStr
    password: str

class UserRegister(UserCreate):
    """用户注册模式"""
    confirm_password: str

    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'password' in values and v != values['password']:
            raise ValueError('密码确认不匹配')
        return v

class PasswordChange(BaseModel):
    """密码修改模式"""
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=128)
    confirm_password: str

    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('新密码确认不匹配')
        return v

class PasswordReset(BaseModel):
    """密码重置模式"""
    email: EmailStr

class PasswordResetConfirm(BaseModel):
    """密码重置确认模式"""
    token: str
    new_password: str = Field(..., min_length=8, max_length=128)
    confirm_password: str

    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('密码确认不匹配')
        return v

class Token(BaseModel):
    """令牌模式"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int

class TokenData(BaseModel):
    """令牌数据模式"""
    user_id: Optional[int] = None
