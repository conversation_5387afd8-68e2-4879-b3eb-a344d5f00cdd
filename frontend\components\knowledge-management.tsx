"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { FolderOpen, FileText, Upload, Search, Edit, Trash2, Eye, Clock, User, Tag } from "lucide-react"
// Import the PermissionGate component
import { PermissionGate } from "@/components/permission-gate"

export function KnowledgeManagement() {
  const [selectedFolder, setSelectedFolder] = useState("protocols")

  const folders = [
    { id: "protocols", name: "实验协议", count: 24 },
    { id: "products", name: "产品资料", count: 156 },
    { id: "cases", name: "成功案例", count: 89 },
    { id: "faq", name: "常见问题", count: 67 },
    { id: "training", name: "培训材料", count: 43 },
  ]

  const documents = [
    {
      id: 1,
      title: "10x Genomics单细胞RNA测序标准操作流程",
      type: "协议",
      author: "张博士",
      lastModified: "2024-01-15",
      status: "已发布",
      tags: ["单细胞", "RNA测序", "10x"],
      views: 234,
    },
    {
      id: 2,
      title: "样本质量控制检查清单",
      type: "检查清单",
      author: "李研究员",
      lastModified: "2024-01-12",
      status: "审核中",
      tags: ["质控", "样本"],
      views: 156,
    },
    {
      id: 3,
      title: "单细胞数据分析流程指南",
      type: "指南",
      author: "王工程师",
      lastModified: "2024-01-10",
      status: "已发布",
      tags: ["数据分析", "生物信息学"],
      views: 189,
    },
  ]

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-slate-900">知识库管理</h2>
        {/* Wrap the upload button with PermissionGate */}
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
            <Input placeholder="搜索文档..." className="pl-10 w-64" />
          </div>
          <PermissionGate permission="manage_knowledge">
            <Button>
              <Upload className="h-4 w-4 mr-2" />
              上传文档
            </Button>
          </PermissionGate>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Folder Navigation */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">文档分类</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {folders.map((folder) => (
                  <div
                    key={folder.id}
                    className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors ${
                      selectedFolder === folder.id ? "bg-blue-50 border border-blue-200" : "hover:bg-slate-50"
                    }`}
                    onClick={() => setSelectedFolder(folder.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <FolderOpen className="h-4 w-4 text-slate-500" />
                      <span className="text-sm font-medium">{folder.name}</span>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {folder.count}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-lg">统计信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-600">总文档数</span>
                <span className="font-semibold">379</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-600">本月新增</span>
                <span className="font-semibold text-green-600">+23</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-600">待审核</span>
                <span className="font-semibold text-yellow-600">8</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-600">总浏览量</span>
                <span className="font-semibold">12,456</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          <Tabs defaultValue="list" className="w-full">
            <TabsList>
              <TabsTrigger value="list">文档列表</TabsTrigger>
              <TabsTrigger value="editor">编辑器</TabsTrigger>
              <TabsTrigger value="analytics">分析报告</TabsTrigger>
            </TabsList>

            <TabsContent value="list" className="mt-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>文档列表</CardTitle>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        批量操作
                      </Button>
                      <Button variant="outline" size="sm">
                        筛选
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {documents.map((doc) => (
                      <div
                        key={doc.id}
                        className="border border-slate-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3">
                              <FileText className="h-5 w-5 text-slate-500" />
                              <h3 className="font-medium text-slate-900">{doc.title}</h3>
                              <Badge variant={doc.status === "已发布" ? "default" : "secondary"}>{doc.status}</Badge>
                            </div>

                            <div className="mt-2 flex items-center space-x-4 text-sm text-slate-600">
                              <div className="flex items-center space-x-1">
                                <User className="h-4 w-4" />
                                <span>{doc.author}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-4 w-4" />
                                <span>{doc.lastModified}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Eye className="h-4 w-4" />
                                <span>{doc.views} 次浏览</span>
                              </div>
                            </div>

                            <div className="mt-2 flex items-center space-x-2">
                              {doc.tags.map((tag, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  <Tag className="h-3 w-3 mr-1" />
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          {/* Wrap the edit and delete buttons with PermissionGate */}
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <PermissionGate permission="manage_knowledge">
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </PermissionGate>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="editor" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>文档编辑器</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Input placeholder="文档标题" />
                    <div className="border border-slate-200 rounded-lg p-4 min-h-[400px]">
                      <p className="text-slate-500">富文本编辑器区域...</p>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm">
                          保存草稿
                        </Button>
                        <Button variant="outline" size="sm">
                          预览
                        </Button>
                      </div>
                      <Button>发布文档</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>热门文档</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {documents.slice(0, 3).map((doc, index) => (
                        <div key={doc.id} className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                              {index + 1}
                            </div>
                            <span className="text-sm font-medium">{doc.title}</span>
                          </div>
                          <span className="text-sm text-slate-600">{doc.views}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>使用趋势</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-slate-500">图表区域 - 显示文档访问趋势</div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
