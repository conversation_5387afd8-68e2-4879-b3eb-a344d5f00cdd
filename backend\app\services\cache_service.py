"""
缓存服务模块
为核心AI服务提供智能缓存机制，提升性能和响应速度
"""
import asyncio
import json
import hashlib
import time
from typing import Dict, Any, Optional, Union, Callable
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


class CacheService:
    """智能缓存服务 - 为AI服务提供多层缓存机制"""
    
    def __init__(self):
        # 内存缓存存储
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_stats = {
            "hits": 0,
            "misses": 0,
            "total_requests": 0,
            "memory_usage": 0
        }
        
        # 缓存配置
        self.default_ttl = 3600  # 1小时默认TTL
        self.max_memory_items = 1000  # 最大内存缓存项数
        self.cleanup_interval = 300  # 5分钟清理间隔
        
        # 启动后台清理任务
        asyncio.create_task(self._background_cleanup())
    
    def _generate_cache_key(self, service: str, method: str, **kwargs) -> str:
        """生成缓存键"""
        # 创建参数的稳定哈希
        params_str = json.dumps(kwargs, sort_keys=True, default=str)
        key_data = f"{service}:{method}:{params_str}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    async def get(self, service: str, method: str, **kwargs) -> Optional[Any]:
        """获取缓存数据"""
        cache_key = self._generate_cache_key(service, method, **kwargs)
        self._cache_stats["total_requests"] += 1
        
        if cache_key in self._memory_cache:
            cache_entry = self._memory_cache[cache_key]
            
            # 检查是否过期
            if datetime.now() < cache_entry["expires_at"]:
                self._cache_stats["hits"] += 1
                logger.debug(f"缓存命中: {service}.{method}")
                return cache_entry["data"]
            else:
                # 删除过期缓存
                del self._memory_cache[cache_key]
        
        self._cache_stats["misses"] += 1
        return None
    
    async def set(self, service: str, method: str, data: Any, ttl: Optional[int] = None, **kwargs):
        """设置缓存数据"""
        cache_key = self._generate_cache_key(service, method, **kwargs)
        expires_at = datetime.now() + timedelta(seconds=ttl or self.default_ttl)
        
        # 检查内存限制
        if len(self._memory_cache) >= self.max_memory_items:
            await self._evict_oldest()
        
        self._memory_cache[cache_key] = {
            "data": data,
            "created_at": datetime.now(),
            "expires_at": expires_at,
            "service": service,
            "method": method,
            "access_count": 0
        }
        
        logger.debug(f"缓存设置: {service}.{method}")
    
    async def cached_call(self, service: str, method: str, func: Callable, ttl: Optional[int] = None, **kwargs):
        """缓存装饰器方法"""
        # 尝试从缓存获取
        cached_result = await self.get(service, method, **kwargs)
        if cached_result is not None:
            return cached_result
        
        # 执行原始函数
        try:
            result = await func(**kwargs) if asyncio.iscoroutinefunction(func) else func(**kwargs)
            
            # 缓存结果
            await self.set(service, method, result, ttl, **kwargs)
            return result
            
        except Exception as e:
            logger.error(f"缓存调用失败 {service}.{method}: {str(e)}")
            raise
    
    async def invalidate(self, service: str, method: Optional[str] = None, **kwargs):
        """清除缓存"""
        if method:
            # 清除特定方法的缓存
            cache_key = self._generate_cache_key(service, method, **kwargs)
            if cache_key in self._memory_cache:
                del self._memory_cache[cache_key]
                logger.debug(f"缓存清除: {service}.{method}")
        else:
            # 清除整个服务的缓存
            keys_to_remove = [
                key for key, entry in self._memory_cache.items()
                if entry["service"] == service
            ]
            for key in keys_to_remove:
                del self._memory_cache[key]
            logger.debug(f"缓存清除: {service} (全部)")
    
    async def _evict_oldest(self):
        """驱逐最旧的缓存项"""
        if not self._memory_cache:
            return
        
        # 找到最旧的缓存项
        oldest_key = min(
            self._memory_cache.keys(),
            key=lambda k: self._memory_cache[k]["created_at"]
        )
        del self._memory_cache[oldest_key]
        logger.debug(f"驱逐最旧缓存: {oldest_key}")
    
    async def _background_cleanup(self):
        """后台清理过期缓存"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                
                current_time = datetime.now()
                expired_keys = [
                    key for key, entry in self._memory_cache.items()
                    if current_time >= entry["expires_at"]
                ]
                
                for key in expired_keys:
                    del self._memory_cache[key]
                
                if expired_keys:
                    logger.info(f"清理了 {len(expired_keys)} 个过期缓存项")
                
                # 更新内存使用统计
                self._cache_stats["memory_usage"] = len(self._memory_cache)
                
            except Exception as e:
                logger.error(f"后台清理异常: {str(e)}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        hit_rate = (
            self._cache_stats["hits"] / self._cache_stats["total_requests"]
            if self._cache_stats["total_requests"] > 0 else 0
        )
        
        return {
            **self._cache_stats,
            "hit_rate": round(hit_rate * 100, 2),
            "cache_size": len(self._memory_cache)
        }
    
    async def clear_all(self):
        """清空所有缓存"""
        self._memory_cache.clear()
        self._cache_stats = {
            "hits": 0,
            "misses": 0,
            "total_requests": 0,
            "memory_usage": 0
        }
        logger.info("所有缓存已清空")


# 全局缓存服务实例
cache_service = CacheService()


def cached(ttl: int = 3600, service: str = "default"):
    """缓存装饰器"""
    def decorator(func: Callable):
        async def wrapper(*args, **kwargs):
            method_name = func.__name__
            return await cache_service.cached_call(
                service=service,
                method=method_name,
                func=func,
                ttl=ttl,
                args=args,
                **kwargs
            )
        return wrapper
    return decorator