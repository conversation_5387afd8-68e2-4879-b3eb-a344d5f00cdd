"""
智能研究顾问服务
完全AI驱动的研究意图理解和个性化方案生成核心服务
"""
import asyncio
import json
import uuid
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.ai_service import AIService
from app.services.cache_service import cache_service, cached
from app.services.performance_monitor import monitor_performance
from app.models.research_intent import (
    UserResearchProfile, ResearchIntentSession, ResearchDirectionAnalysis,
    DynamicKeywordGeneration, AIModelPerformanceLog
)
from app.models.dynamic_recommendation import (
    PersonalizedSolutionFramework, UserLearningProfile
)

logger = logging.getLogger(__name__)


class IntelligentResearchAdvisor:
    """智能研究顾问 - AI驱动的个性化研究支持系统"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.model_version = "gpt-4-turbo-2024"
        
        # AI提示词模板
        self.intent_analysis_template = """
        你是一位资深的单细胞生物学研究专家和科研顾问，拥有15年以上的研究经验。请深度分析用户的研究意图，提供专业、个性化的指导。

        用户原始输入: {user_input}
        详细需求信息: {requirements}
        用户背景上下文: {user_context}

        请进行深度的多维度意图分析，重点关注：
        1. 用户的真实研究目标和科学问题
        2. 技术背景和经验水平的准确评估
        3. 实验设计的可行性和潜在挑战
        4. 个性化的研究路径建议

        输出结构化JSON，包含：

        1. comprehensive_intent_analysis: {{
            "primary_research_domain": "核心研究领域（如immunology, oncology, neuroscience等）",
            "specific_biological_questions": ["用户想要回答的具体生物学问题"],
            "research_objectives": {{
                "primary_goal": "主要研究目标的详细描述",
                "secondary_goals": ["次要目标1", "次要目标2"],
                "expected_discoveries": ["期望发现的生物学现象或机制"]
            }},
            "research_depth_level": "exploratory/focused/comprehensive",
            "technical_complexity": "basic/intermediate/advanced",
            "experimental_design_insights": {{
                "sample_strategy_assessment": "对样本选择策略的专业评估",
                "technical_approach_suitability": "技术方法选择的合理性分析",
                "potential_experimental_challenges": ["实验设计中的潜在问题"],
                "optimization_suggestions": ["实验优化建议"]
            }},
            "research_novelty_assessment": {{
                "innovation_level": "研究的创新程度评估",
                "field_contribution_potential": "对领域贡献的潜力分析",
                "competitive_landscape": "相关研究现状简析"
            }}
        }}

        2. user_context_assessment: {{
            "experience_level_analysis": {{
                "inferred_level": "novice/intermediate/expert",
                "evidence_basis": "基于什么信息做出的判断",
                "knowledge_gaps": ["识别出的知识盲点"],
                "strengths": ["用户展现的优势领域"]
            }},
            "technical_proficiency": {{
                "computational_skills": "计算分析能力评估",
                "experimental_experience": "实验技能水平",
                "data_analysis_familiarity": "数据分析熟练度",
                "recommended_skill_development": ["建议提升的技能"]
            }},
            "resource_constraints_analysis": {{
                "budget_optimization_needs": "预算优化建议",
                "timeline_feasibility": "时间安排的可行性分析",
                "equipment_requirements": "设备需求评估",
                "collaboration_recommendations": ["建议的合作方向"]
            }},
            "personalized_guidance_strategy": "针对该用户的个性化指导策略"
        }}

        3. research_direction_seeds: [
            {{
                "direction_name": "具体的研究方向名称",
                "scientific_rationale": "该方向的科学依据和重要性",
                "core_biological_questions": ["该方向要解决的核心生物学问题"],
                "methodological_approach": {{
                    "primary_techniques": ["主要技术方法"],
                    "analytical_strategies": ["分析策略"],
                    "validation_methods": ["结果验证方法"]
                }},
                "expected_outcomes": {{
                    "immediate_results": ["短期可获得的结果"],
                    "long_term_insights": ["长期科学洞察"],
                    "potential_applications": ["潜在应用价值"]
                }},
                "user_suitability_analysis": {{
                    "suitability_score": 0.9,
                    "alignment_reasons": ["与用户需求匹配的原因"],
                    "skill_requirements": ["需要的技能要求"],
                    "learning_curve": "学习难度评估",
                    "success_probability": "成功概率评估"
                }},
                "implementation_roadmap": {{
                    "phase_1": "第一阶段工作内容",
                    "phase_2": "第二阶段工作内容",
                    "key_milestones": ["关键里程碑"],
                    "risk_mitigation": ["风险缓解策略"]
                }}
            }}
        ]

        **分析要求：**
        - 基于用户的具体情况进行深度个性化分析
        - 避免通用模板，提供针对性的专业建议
        - 考虑用户的技术背景、资源限制和研究目标
        - 提供可操作的具体指导和建议
        """
        
        self.direction_generation_template = """
        你是一位资深的单细胞生物学研究专家和科研导师，拥有丰富的项目指导经验。基于深度的意图分析，为用户设计高度个性化、可执行的研究方向。

        **任务目标：** 为用户生成3-5个精心设计的研究方向，每个方向都应该：
        1. 完全契合用户的研究背景和技术水平
        2. 提供清晰的执行路径和里程碑
        3. 考虑实际的资源限制和时间安排
        4. 包含风险评估和应对策略

        **输入信息：**
        研究意图深度分析: {intent_analysis}
        用户背景特征: {user_profile}

        **输出要求：** 为每个研究方向生成详细的执行方案，JSON格式：

        {{
            "research_directions": [
                {{
                    "direction_id": "唯一标识",
                    "title": "【为用户定制】方向标题",
                    "description": "详细描述为什么这个方向适合用户",
                    "research_focus": "具体研究重点",
                    "advantages": [
                        "适合用户的原因1：基于其经验水平",
                        "优势2：考虑其资源情况",
                        "优势3：符合其研究目标"
                    ],
                    "challenges": [
                        "具体挑战1及应对建议",
                        "挑战2及解决方案"
                    ],
                    "risk_factors": [
                        "风险因素1：详细说明和预防措施",
                        "风险2：具体影响和缓解策略"
                    ],
                    "attention_points": [
                        "关键注意事项1：为什么重要",
                        "注意点2：如何确保成功"
                    ],
                    "estimated_cost": {{
                        "range": "8-12万元",
                        "breakdown": "成本组成说明",
                        "cost_factors": "影响成本的因素"
                    }},
                    "timeline": {{
                        "duration": "3-4个月",
                        "milestones": ["里程碑1", "里程碑2"],
                        "critical_path": "关键路径说明"
                    }},
                    "technical_requirements": {{
                        "platforms": ["推荐技术平台"],
                        "skills_needed": ["需要的技能"],
                        "equipment": "设备需求"
                    }},
                    "success_factors": [
                        "成功关键因素1",
                        "成功因素2"
                    ],
                    "user_suitability_score": 0.85,
                    "personalization_notes": "个性化调整说明"
                }}
            ]
        }}

        确保每个方向都针对用户的具体情况进行了深度定制。
        """

    async def understand_user_intent(
        self,
        user_input: str,
        requirements: Dict[str, Any],
        user_id: int,
        user_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        深度理解用户研究意图 - 带缓存优化
        
        Args:
            user_input: 用户原始输入
            requirements: 结构化需求信息
            user_id: 用户ID
            user_context: 用户上下文信息
            
        Returns:
            完整的意图分析结果
        """
        # 使用缓存包装器
        return await cache_service.cached_call(
            service="intelligent_research",
            method="understand_user_intent",
            func=self._understand_user_intent_impl,
            ttl=1800,  # 30分钟缓存
            user_input=user_input,
            requirements=requirements,
            user_id=user_id,
            user_context=user_context
        )
    
    async def _understand_user_intent_impl(
        self,
        user_input: str,
        requirements: Dict[str, Any],
        user_id: int,
        user_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        深度理解用户研究意图
        
        Args:
            user_input: 用户原始输入
            requirements: 结构化需求信息
            user_id: 用户ID
            user_context: 用户上下文信息
            
        Returns:
            完整的意图分析结果
        """
        try:
            start_time = datetime.now()
            session_id = str(uuid.uuid4())
            
            logger.info(f"开始分析用户研究意图 - 用户ID: {user_id}, 会话ID: {session_id}")
            
            # 获取或创建用户研究档案
            db = next(get_db())
            user_profile = await self._get_or_create_user_profile(db, user_id)
            
            # 构建AI分析提示
            context_info = {
                "user_experience": user_profile.research_experience_level if user_profile else "unknown",
                "research_domains": user_profile.primary_research_domains if user_profile else [],
                "previous_interactions": user_context or {}
            }
            
            analysis_prompt = self.intent_analysis_template.format(
                user_input=user_input,
                requirements=json.dumps(requirements, ensure_ascii=False, indent=2),
                user_context=json.dumps(context_info, ensure_ascii=False, indent=2)
            )
            
            # 执行AI分析
            logger.info("执行AI意图分析...")
            ai_response = await self.ai_service.generate_response(
                message=analysis_prompt,
                context={"conversation_type": "research_intent_analysis"}
            )
            
            # 解析AI响应
            intent_analysis = await self._parse_ai_response(ai_response.get("content", ""))
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # 保存分析会话
            session = await self._save_intent_session(
                db, user_id, session_id, user_input, requirements,
                intent_analysis, processing_time
            )
            
            # 更新用户档案
            await self._update_user_profile(db, user_profile, intent_analysis)
            
            # 记录AI模型性能
            await self._log_model_performance(
                db, "intent_analysis", processing_time, intent_analysis
            )
            
            logger.info(f"意图分析完成 - 处理时间: {processing_time:.2f}ms")
            
            return {
                "session_id": session_id,
                "intent_analysis": intent_analysis,
                "processing_time_ms": processing_time,
                "confidence_score": intent_analysis.get("confidence_score", 0.8),
                "analysis_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"用户意图分析失败: {str(e)}")
            return {
                "error": f"意图分析失败: {str(e)}",
                "fallback_analysis": await self._generate_fallback_analysis(user_input, requirements)
            }

    async def generate_personalized_directions(
        self,
        session_id: str,
        user_id: int,
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        生成个性化的研究方向
        
        Args:
            session_id: 意图分析会话ID
            user_id: 用户ID
            intent_analysis: 意图分析结果
            
        Returns:
            个性化研究方向列表
        """
        try:
            start_time = datetime.now()
            
            logger.info(f"生成个性化研究方向 - 用户ID: {user_id}, 会话: {session_id}")
            
            # 获取用户档案
            db = next(get_db())
            user_profile = await self._get_user_profile(db, user_id)
            user_learning_profile = await self._get_user_learning_profile(db, user_id)
            
            # 构建个性化上下文
            personalization_context = {
                "experience_level": user_profile.research_experience_level if user_profile else "intermediate",
                "technical_proficiency": user_profile.technical_proficiency if user_profile else {},
                "resource_constraints": {
                    "budget_sensitivity": user_profile.budget_sensitivity if user_profile else "medium",
                    "available_platforms": user_profile.available_platforms if user_profile else [],
                    "lab_resources": user_profile.lab_resources if user_profile else {}
                },
                "learning_preferences": {
                    "information_depth": user_learning_profile.information_depth_preference if user_learning_profile else "detailed",
                    "decision_style": user_learning_profile.decision_making_pattern if user_learning_profile else "deliberate"
                }
            }
            
            # 生成研究方向
            direction_prompt = self.direction_generation_template.format(
                intent_analysis=json.dumps(intent_analysis, ensure_ascii=False, indent=2),
                user_profile=json.dumps(personalization_context, ensure_ascii=False, indent=2)
            )
            
            logger.info("生成个性化研究方向...")
            ai_response = await self.ai_service.generate_response(
                message=direction_prompt,
                context={"conversation_type": "direction_generation"}
            )
            
            # 解析研究方向
            directions_data = await self._parse_ai_response(ai_response.get("content", ""))
            research_directions = directions_data.get("research_directions", [])
            
            # 保存研究方向
            direction_records = []
            for direction in research_directions:
                direction_record = await self._save_research_direction(
                    db, session_id, direction, personalization_context
                )
                direction_records.append(direction_record)
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            logger.info(f"研究方向生成完成 - 生成{len(research_directions)}个方向")
            
            return {
                "session_id": session_id,
                "research_directions": research_directions,
                "total_directions": len(research_directions),
                "personalization_applied": personalization_context,
                "processing_time_ms": processing_time,
                "generation_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"研究方向生成失败: {str(e)}")
            return {
                "error": f"方向生成失败: {str(e)}",
                "fallback_directions": await self._generate_fallback_directions(intent_analysis)
            }

    async def _get_or_create_user_profile(
        self,
        db: Session,
        user_id: int
    ) -> Optional[UserResearchProfile]:
        """获取或创建用户研究档案"""
        try:
            # 查找现有档案
            profile = db.query(UserResearchProfile).filter(
                UserResearchProfile.user_id == user_id
            ).first()
            
            if not profile:
                # 创建新档案
                profile = UserResearchProfile(
                    user_id=user_id,
                    research_experience_level="unknown",
                    primary_research_domains=[],
                    technical_proficiency={},
                    budget_sensitivity="medium",
                    interaction_patterns={},
                    confidence_score=0.5
                )
                db.add(profile)
                db.commit()
                db.refresh(profile)
                logger.info(f"创建新用户研究档案 - 用户ID: {user_id}")
            
            return profile
            
        except Exception as e:
            logger.error(f"获取/创建用户档案失败: {str(e)}")
            return None

    async def _parse_ai_response(self, content: str) -> Dict[str, Any]:
        """解析AI响应内容"""
        try:
            # 尝试直接解析JSON
            if content.strip().startswith('{'):
                return json.loads(content)
            
            # 提取JSON块
            import re
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            
            # 查找大括号内的内容
            json_match = re.search(r'(\{.*\})', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            
            # 如果都失败，返回包装的文本内容
            return {
                "raw_content": content,
                "parsed": False,
                "confidence_score": 0.5
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return {
                "raw_content": content,
                "parse_error": str(e),
                "confidence_score": 0.3
            }

    async def _save_intent_session(
        self,
        db: Session,
        user_id: int,
        session_id: str,
        user_input: str,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any],
        processing_time: float
    ) -> ResearchIntentSession:
        """保存意图分析会话"""
        try:
            session = ResearchIntentSession(
                user_id=user_id,
                session_id=session_id,
                original_query=user_input,
                structured_requirements=requirements,
                intent_analysis_result=intent_analysis,
                analysis_confidence=intent_analysis.get("confidence_score", 0.8),
                processing_time_ms=int(processing_time),
                ai_model_version=self.model_version
            )
            
            db.add(session)
            db.commit()
            db.refresh(session)
            
            return session
            
        except Exception as e:
            logger.error(f"保存意图分析会话失败: {str(e)}")
            db.rollback()
            raise

    async def _update_user_profile(
        self,
        db: Session,
        profile: UserResearchProfile,
        intent_analysis: Dict[str, Any]
    ):
        """更新用户档案"""
        try:
            if not profile:
                return
            
            # 从意图分析中提取信息更新档案
            context_assessment = intent_analysis.get("user_context_assessment", {})
            
            # 更新经验水平
            if context_assessment.get("inferred_experience_level"):
                profile.research_experience_level = context_assessment["inferred_experience_level"]
            
            # 更新研究领域
            comprehensive_analysis = intent_analysis.get("comprehensive_intent_analysis", {})
            if comprehensive_analysis.get("primary_research_domain"):
                current_domains = profile.primary_research_domains or []
                new_domain = comprehensive_analysis["primary_research_domain"]
                if new_domain not in current_domains:
                    current_domains.append(new_domain)
                    profile.primary_research_domains = current_domains
            
            # 更新资源约束信息
            resource_constraints = context_assessment.get("resource_constraints", {})
            if resource_constraints:
                profile.budget_sensitivity = resource_constraints.get("budget_sensitivity", profile.budget_sensitivity)
            
            # 更新置信度和版本
            profile.confidence_score = min(profile.confidence_score + 0.1, 1.0)
            profile.profile_version += 1
            profile.last_analysis_date = datetime.now()
            
            db.commit()
            logger.info(f"用户档案更新完成 - 用户ID: {profile.user_id}")
            
        except Exception as e:
            logger.error(f"更新用户档案失败: {str(e)}")
            db.rollback()

    async def _save_research_direction(
        self,
        db: Session,
        session_id: str,
        direction: Dict[str, Any],
        personalization_context: Dict[str, Any]
    ) -> ResearchDirectionAnalysis:
        """保存研究方向分析"""
        try:
            # 获取会话记录
            session = db.query(ResearchIntentSession).filter(
                ResearchIntentSession.session_id == session_id
            ).first()
            
            if not session:
                raise ValueError(f"找不到会话记录: {session_id}")
            
            direction_record = ResearchDirectionAnalysis(
                session_id=session.id,
                direction_id=direction.get("direction_id", str(uuid.uuid4())),
                direction_title=direction.get("title", ""),
                direction_description=direction.get("description", ""),
                research_focus=direction.get("research_focus", ""),
                advantages=direction.get("advantages", []),
                challenges=direction.get("challenges", []),
                risk_factors=direction.get("risk_factors", []),
                attention_points=direction.get("attention_points", []),
                estimated_cost_range=direction.get("estimated_cost", {}),
                timeline_estimation=direction.get("timeline", {}),
                recommended_platforms=direction.get("technical_requirements", {}).get("platforms", []),
                workflow_steps=direction.get("technical_requirements", {}).get("workflow", []),
                user_suitability_score=direction.get("user_suitability_score", 0.8),
                customization_notes=personalization_context
            )
            
            db.add(direction_record)
            db.commit()
            db.refresh(direction_record)
            
            return direction_record
            
        except Exception as e:
            logger.error(f"保存研究方向失败: {str(e)}")
            db.rollback()
            raise

    async def _log_model_performance(
        self,
        db: Session,
        operation_type: str,
        processing_time: float,
        result_data: Dict[str, Any]
    ):
        """记录AI模型性能"""
        try:
            performance_log = AIModelPerformanceLog(
                model_version=self.model_version,
                model_type="research_advisor",
                operation_type=operation_type,
                processing_time_ms=int(processing_time),
                generation_quality_score=result_data.get("confidence_score", 0.8),
                user_acceptance_rate=0.0,  # 将在用户反馈后更新
                error_rate=0.0 if "error" not in result_data else 1.0
            )
            
            db.add(performance_log)
            db.commit()
            
        except Exception as e:
            logger.error(f"记录模型性能失败: {str(e)}")
            db.rollback()

    async def _generate_fallback_analysis(
        self,
        user_input: str,
        requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成降级分析结果"""
        return {
            "comprehensive_intent_analysis": {
                "primary_research_domain": "single_cell_analysis",
                "research_objectives": ["单细胞数据分析", "细胞类型识别"],
                "research_depth_level": "focused",
                "technical_complexity": "intermediate"
            },
            "user_context_assessment": {
                "inferred_experience_level": "intermediate",
                "technical_background": "生物信息学基础",
                "resource_constraints": {
                    "budget_sensitivity": "medium",
                    "timeline_pressure": "moderate"
                }
            },
            "confidence_score": 0.5,
            "fallback_mode": True
        }

    async def _generate_fallback_directions(
        self,
        intent_analysis: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成降级研究方向"""
        return [
            {
                "direction_id": "fallback_1",
                "title": "基础单细胞RNA测序分析",
                "description": "标准的单细胞转录组测序数据分析流程",
                "advantages": ["技术成熟", "成本可控", "风险较低"],
                "challenges": ["需要生信分析能力"],
                "estimated_cost": {"range": "8-12万元"}
            }
        ]

    async def _get_user_profile(self, db: Session, user_id: int) -> Optional[UserResearchProfile]:
        """获取用户研究档案"""
        try:
            return db.query(UserResearchProfile).filter(
                UserResearchProfile.user_id == user_id
            ).first()
        except Exception as e:
            logger.error(f"获取用户档案失败: {str(e)}")
            return None

    async def _get_user_learning_profile(self, db: Session, user_id: int) -> Optional[UserLearningProfile]:
        """获取用户学习档案"""
        try:
            return db.query(UserLearningProfile).filter(
                UserLearningProfile.user_id == user_id
            ).first()
        except Exception as e:
            logger.error(f"获取用户学习档案失败: {str(e)}")
            return None


# 全局服务实例
intelligent_research_advisor = IntelligentResearchAdvisor()


def get_intelligent_research_advisor() -> IntelligentResearchAdvisor:
    """获取智能研究顾问服务实例"""
    return intelligent_research_advisor