"use client"

import React, { useState, useEffect } from 'react'
import { Search, BookOpen, ExternalLink, Star, TrendingUp, Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { literatureApi } from '@/lib/api'
import { toast } from 'sonner'

interface Literature {
  id?: number
  title: string
  authors: string[]
  journal: string
  publication_year: number
  doi?: string
  pubmed_id?: string
  abstract: string
  category: string
  technology_tags: string[]
  application_tags: string[]
  impact_factor?: number
  citation_count: number
  relevance_score: number
  key_findings: string
  methodology_summary: string
  business_value: string
}

interface LiteraturePanelProps {
  className?: string
  onLiteratureSelect?: (literature: Literature) => void
}

export function LiteraturePanel({ className = '', onLiteratureSelect }: LiteraturePanelProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [literature, setLiterature] = useState<Literature[]>([])
  const [trendingLiterature, setTrendingLiterature] = useState<Literature[]>([])
  const [loading, setLoading] = useState(false)
  const [categories, setCategories] = useState<any>({})
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [stats, setStats] = useState<any>({})

  // 加载初始数据
  useEffect(() => {
    loadTrendingLiterature()
    loadCategories()
    loadStats()
  }, [])

  const loadTrendingLiterature = async () => {
    try {
      const response = await literatureApi.getTrendingLiterature(5)
      setTrendingLiterature(response.trending_literature)
    } catch (error) {
      console.error('加载热门文献失败:', error)
    }
  }

  const loadCategories = async () => {
    try {
      const response = await literatureApi.getCategories()
      setCategories(response)
    } catch (error) {
      console.error('加载分类失败:', error)
    }
  }

  const loadStats = async () => {
    try {
      const response = await literatureApi.getStats()
      setStats(response)
    } catch (error) {
      console.error('加载统计信息失败:', error)
    }
  }

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      toast.error('请输入搜索关键词')
      return
    }

    setLoading(true)
    try {
      const response = await literatureApi.searchLiterature({
        query: searchQuery,
        category: selectedCategory || undefined,
        top_k: 10
      })
      setLiterature(response)
      toast.success(`找到 ${response.length} 篇相关文献`)
    } catch (error) {
      console.error('搜索失败:', error)
      toast.error('搜索失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const formatAuthors = (authors: string[]) => {
    if (authors.length <= 3) {
      return authors.join(', ')
    }
    return `${authors.slice(0, 3).join(', ')}等`
  }

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'methodology': 'bg-blue-100 text-blue-800',
      'application': 'bg-green-100 text-green-800',
      'technology': 'bg-purple-100 text-purple-800',
      'review': 'bg-orange-100 text-orange-800',
      'protocol': 'bg-gray-100 text-gray-800'
    }
    return colors[category] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 搜索区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            文献资源库
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              placeholder="搜索文献标题、作者、关键词..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1"
            />
            <Button onClick={handleSearch} disabled={loading}>
              <Search className="h-4 w-4" />
            </Button>
          </div>

          {/* 分类筛选 */}
          {categories.categories && (
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === '' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory('')}
              >
                全部
              </Button>
              {categories.categories.map((cat: any) => (
                <Button
                  key={cat.value}
                  variant={selectedCategory === cat.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory(cat.value)}
                >
                  {cat.label}
                </Button>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 统计信息 */}
      {stats.total_literature && (
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">{stats.total_literature}</div>
                <div className="text-sm text-gray-600">总文献数</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">{stats.average_impact_factor}</div>
                <div className="text-sm text-gray-600">平均影响因子</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">{stats.total_citations}</div>
                <div className="text-sm text-gray-600">总引用数</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">
                  {Object.keys(stats.technology_coverage || {}).length}
                </div>
                <div className="text-sm text-gray-600">技术覆盖</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 搜索结果 */}
      {literature.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>搜索结果 ({literature.length})</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {literature.map((lit, index) => (
              <div
                key={index}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => onLiteratureSelect?.(lit)}
              >
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-gray-900 flex-1 pr-4">{lit.title}</h3>
                  {lit.impact_factor && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <Star className="h-3 w-3" />
                      IF: {lit.impact_factor}
                    </Badge>
                  )}
                </div>
                
                <p className="text-sm text-gray-600 mb-2">
                  {formatAuthors(lit.authors)} • {lit.journal} ({lit.publication_year})
                </p>
                
                <div className="flex flex-wrap gap-2 mb-3">
                  <Badge className={getCategoryColor(lit.category)}>
                    {lit.category}
                  </Badge>
                  {lit.technology_tags.slice(0, 3).map((tag, tagIndex) => (
                    <Badge key={tagIndex} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
                
                <p className="text-sm text-gray-700 mb-2 line-clamp-2">
                  {lit.key_findings}
                </p>
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>引用: {lit.citation_count}</span>
                  <span>相关性: {Math.round(lit.relevance_score * 100)}%</span>
                  {lit.doi && (
                    <a
                      href={`https://doi.org/${lit.doi}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <ExternalLink className="h-3 w-3" />
                      DOI
                    </a>
                  )}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* 热门文献 */}
      {trendingLiterature.length > 0 && literature.length === 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              热门文献
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {trendingLiterature.map((lit, index) => (
              <div
                key={index}
                className="border border-gray-200 rounded-lg p-3 hover:shadow-sm transition-shadow cursor-pointer"
                onClick={() => onLiteratureSelect?.(lit)}
              >
                <h4 className="font-medium text-gray-900 mb-1 text-sm">{lit.title}</h4>
                <p className="text-xs text-gray-600 mb-2">
                  {formatAuthors(lit.authors)} • {lit.journal} ({lit.publication_year})
                </p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>IF: {lit.impact_factor}</span>
                  <span>引用: {lit.citation_count}</span>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
