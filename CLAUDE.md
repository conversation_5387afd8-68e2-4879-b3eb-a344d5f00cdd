# CellForge AI - 智能单细胞生物学研究平台

## 项目概述

CellForge AI 是一个AI驱动的单细胞生物学研究平台，旨在为研究人员提供智能化的研究支持和个性化解决方案。通过整合先进的AI技术，平台能够理解用户的研究意图，生成个性化的研究方向建议，并提供精准的文献资源和关键词策略。

## 项目目标

### 核心目标
- **智能意图理解**: 深度分析用户的研究需求和背景，提供精准的意图识别
- **个性化方案生成**: 基于用户特征和研究目标，生成定制化的研究解决方案
- **智能资源整合**: 自动生成相关文献链接和优化的搜索关键词
- **全程研究支持**: 从研究规划到实施，提供全方位的智能化支持

### 技术目标
- 构建高性能的AI服务架构
- 实现模块化和可扩展的服务设计
- 提供统一和标准化的API接口
- 建立完善的数据模型和存储方案

## 系统架构

### 核心服务架构

```
┌─────────────────────────────────────────────────────────────┐
│                    CellForge AI Platform                   │
├─────────────────────────────────────────────────────────────┤
│                    API Gateway Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Authentication  │  │  Rate Limiting  │  │   Logging    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                   Core AI Services                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ IntelligentRes- │  │ SmartLiterature │  │ DynamicKey-  │ │
│  │ earchAdvisor    │  │ Linker          │  │ wordGen      │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│                          │                                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │         PersonalizedSolutionGenerator                   │ │
│  │           (整合所有AI服务的核心组件)                        │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                   Supporting Services                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   AI Service    │  │   Web Profile   │  │  Customer    │ │
│  │   (LLM)         │  │   Analysis      │  │  Profile     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Data Layer                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  User Profiles  │  │ Research Intent │  │  Solution    │ │
│  │  & Learning     │  │  & Directions   │  │  Framework   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 核心AI服务详解

### 1. IntelligentResearchAdvisor (智能研究顾问)

**功能概述**: AI驱动的研究意图理解和个性化方案生成核心服务

**核心能力**:
- 深度用户意图分析
- 个性化研究方向推荐
- 用户研究档案管理
- 智能方案定制

**技术实现**:
- 使用大语言模型进行意图分析
- 基于用户历史和偏好的个性化推荐算法
- 动态学习用户研究模式

**API端点**:
- `POST /intelligent-research/intent-analysis` - 用户意图分析
- `POST /intelligent-research/research-directions` - 研究方向生成

### 2. SmartLiteratureLinker (智能文献链接生成器)

**功能概述**: 基于研究意图动态生成最相关的文献搜索链接

**核心能力**:
- 多平台文献搜索优化
- 智能链接相关性评分
- 搜索策略自动生成
- 平台特定优化

**支持平台**:
- PubMed - 生物医学权威文献
- Google Scholar - 综合学术搜索
- PMC - 开放获取全文
- bioRxiv - 生物学预印本
- ScienceDirect - Elsevier期刊
- Semantic Scholar - AI驱动语义搜索

**API端点**:
- `POST /intelligent-research/literature-links` - 单方向文献链接生成
- `POST /intelligent-research/batch-literature-links` - 批量文献链接生成

### 3. DynamicKeywordGenerator (动态关键词生成器)

**功能概述**: 基于用户研究意图和上下文动态生成最相关的搜索关键词

**核心能力**:
- AI驱动的关键词扩展
- 关键词聚类分析
- 自适应关键词生成
- 趋势关键词识别
- 平台优化策略

**关键词类型**:
- 核心概念关键词
- 技术方法关键词
- 生物学上下文关键词
- 自适应关键词
- 趋势关键词

**API端点**:
- `POST /intelligent-research/dynamic-keywords` - 动态关键词生成
- `POST /intelligent-research/keyword-platform-optimization` - 平台优化

### 4. PersonalizedSolutionGenerator (个性化方案生成器)

**功能概述**: 整合所有AI服务，生成完整的个性化研究解决方案

**核心能力**:
- 综合方案整合
- 学习路径设计
- 资源配置规划
- 风险评估分析
- 成功指标制定

**方案组件**:
- 个性化推荐
- 学习路径
- 资源配置
- 风险评估
- 成功指标
- 执行指导

**API端点**:
- `POST /intelligent-research/comprehensive-solution` - 综合解决方案生成

## 数据模型架构

### 用户画像模型

```python
# UserResearchProfile - 用户研究档案
- user_id: 用户ID
- research_experience_level: 研究经验水平
- primary_research_domains: 主要研究领域
- technical_proficiency: 技术熟练度
- budget_sensitivity: 预算敏感度
- interaction_patterns: 交互模式
- confidence_score: 档案置信度

# UserLearningProfile - 用户学习档案
- user_id: 用户ID
- information_depth_preference: 信息深度偏好
- decision_making_pattern: 决策模式
- learning_style_preferences: 学习风格偏好
```

### 研究意图模型

```python
# ResearchIntentSession - 研究意图会话
- session_id: 会话ID
- user_id: 用户ID
- original_query: 原始查询
- intent_analysis_result: 意图分析结果
- analysis_confidence: 分析置信度

# ResearchDirectionAnalysis - 研究方向分析
- session_id: 会话ID
- direction_id: 方向ID
- direction_title: 方向标题
- user_suitability_score: 用户适配分数
```

### 动态推荐模型

```python
# DynamicKeywordGeneration - 动态关键词生成
- session_id: 会话ID
- generation_strategy: 生成策略
- keyword_clusters: 关键词聚类
- total_keywords_generated: 生成关键词总数

# PersonalizedSolutionFramework - 个性化方案框架
- solution_id: 方案ID
- personalization_level: 个性化水平
- solution_complexity: 方案复杂度
- estimated_success_rate: 预估成功率
```

## API接口规范

### 统一响应格式

```json
{
  "success": true,
  "status": "success|error|warning|partial",
  "message": "响应消息",
  "data": {}, // 响应数据
  "error": {}, // 错误信息 (仅在出错时)
  "metadata": {}, // 元数据
  "timestamp": "2024-01-01T00:00:00Z",
  "processing_time_ms": 1500.0,
  "request_id": "req_123456"
}
```

### 核心API端点

```
智能研究API (/intelligent-research)
├── POST /intent-analysis           # 用户意图分析
├── POST /research-directions       # 研究方向生成
├── POST /literature-links          # 文献链接生成
├── POST /dynamic-keywords          # 动态关键词生成
├── POST /comprehensive-solution    # 综合解决方案
├── POST /batch-literature-links    # 批量文献链接
├── POST /keyword-platform-optimization # 关键词平台优化
├── GET  /health                    # 健康检查
└── GET  /service-info              # 服务信息
```

## 技术栈

### 后端技术
- **框架**: FastAPI (Python)
- **数据库**: SQLAlchemy + PostgreSQL/SQLite
- **AI服务**: LangChain + OpenAI/其他LLM
- **缓存**: Redis (可选)
- **异步处理**: asyncio

### 前端技术
- **框架**: Next.js (React)
- **状态管理**: React Context / Zustand
- **UI组件**: Tailwind CSS + Shadcn/ui
- **类型系统**: TypeScript

## 部署架构

### 开发环境
```
Local Development
├── Backend: FastAPI (uvicorn)
├── Frontend: Next.js (dev server)
├── Database: SQLite
└── AI Service: Mock/OpenAI API
```

### 生产环境
```
Production Deployment
├── Load Balancer (Nginx)
├── API Gateway
├── Backend Services (Docker containers)
├── Database Cluster (PostgreSQL)
├── Redis Cache
└── Monitoring & Logging
```

## 项目结构

```
CellForge AI/
├── backend/
│   ├── app/
│   │   ├── api/
│   │   │   ├── endpoints/
│   │   │   │   ├── intelligent_research_api.py  # 新智能研究API
│   │   │   │   ├── comprehensive_solution.py
│   │   │   │   └── ...
│   │   │   ├── response_models.py              # 统一响应格式
│   │   │   └── router.py
│   │   ├── services/
│   │   │   ├── intelligent_research_advisor.py  # 智能研究顾问
│   │   │   ├── smart_literature_linker.py      # 智能文献链接
│   │   │   ├── dynamic_keyword_generator.py    # 动态关键词生成
│   │   │   ├── personalized_solution_generator.py # 个性化方案生成
│   │   │   └── ...
│   │   ├── models/
│   │   │   ├── research_intent.py              # 研究意图模型
│   │   │   ├── dynamic_recommendation.py       # 动态推荐模型
│   │   │   └── ...
│   │   └── core/
│   └── requirements.txt
├── frontend/
│   ├── app/
│   ├── components/
│   ├── lib/
│   └── package.json
├── docs/
└── CLAUDE.md                                  # 本文档
```

## 开发指南

### 环境配置

1. **后端环境**:
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --port 8000
```

2. **前端环境**:
```bash
cd frontend
npm install
npm run dev
```

3. **数据库初始化**:
```bash
# 数据库迁移和初始化
alembic upgrade head
```

### API测试

使用内置的健康检查端点测试服务状态:
```bash
# 整体健康检查
GET /api/health

# 智能研究服务健康检查
GET /api/intelligent-research/health

# 服务信息查询
GET /api/intelligent-research/service-info
```

### 开发规范

1. **代码规范**:
   - 使用 Python Type Hints
   - 遵循 FastAPI 最佳实践
   - 统一使用异步编程模式

2. **API设计**:
   - 使用统一的响应格式
   - 提供完整的错误处理
   - 包含详细的API文档

3. **测试要求**:
   - 单元测试覆盖率 > 80%
   - 集成测试覆盖核心流程
   - 性能测试验证响应时间

## 性能指标

### 服务响应时间目标
- 意图分析: < 3秒
- 研究方向生成: < 5秒
- 文献链接生成: < 2秒
- 动态关键词生成: < 2秒
- 综合解决方案: < 10秒

### 系统可用性目标
- 服务可用性: 99.5%
- 数据持久性: 99.99%
- 错误率: < 1%

## 安全考虑

### 数据安全
- 用户隐私数据加密存储
- API访问令牌认证
- 敏感信息脱敏处理

### 访问控制
- 基于角色的访问控制(RBAC)
- API限流和防护
- 安全审计日志

## 监控和日志

### 系统监控
- 服务健康状态监控
- 性能指标监控
- 错误率和异常监控

### 日志管理
- 结构化日志记录
- 错误和异常跟踪
- 用户行为分析

## 后续发展计划

### 短期目标 (3个月)
- [ ] 完善AdaptiveLearningEngine自适应学习机制
- [ ] 增强用户画像准确性
- [ ] 优化AI服务响应速度
- [ ] 完善错误处理和降级机制

### 中期目标 (6个月)
- [ ] 集成更多文献搜索平台
- [ ] 增加多语言支持
- [ ] 实现实时协作功能
- [ ] 构建知识图谱

### 长期目标 (12个月)
- [ ] 构建单细胞生物学专业知识库
- [ ] 实现自动化实验设计建议
- [ ] 集成数据分析工具链
- [ ] 支持多学科交叉研究

## 贡献指南

### 开发流程
1. 功能需求分析
2. 技术方案设计
3. 代码实现和测试
4. 代码审查和优化
5. 部署和监控

### 提交规范
- 使用语义化的提交消息
- 包含完整的测试用例
- 更新相关文档

## 联系信息

**项目负责人**: CellForge AI Team
**技术支持**: <EMAIL>
**文档更新**: 2024-01-01

---

> 本文档记录了CellForge AI项目的完整架构和实现细节。随着项目的发展，将持续更新此文档以反映最新的技术栈和功能特性。