"use client"

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogTrigger } from '@/components/ui/dialog'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  ExternalLink, 
  BookOpen, 
  Star, 
  TrendingUp, 
  Users, 
  Calendar,
  Award,
  Copy,
  Heart,
  Share2,
  Download
} from 'lucide-react'
import { toast } from 'sonner'

interface Literature {
  id?: number
  title: string
  authors: string[]
  journal: string
  publication_year: number
  doi?: string
  pubmed_id?: string
  abstract: string
  category: string
  technology_tags: string[]
  application_tags: string[]
  impact_factor?: number
  citation_count: number
  relevance_score: number
  key_findings: string
  methodology_summary: string
  business_value: string
}

interface InteractiveLiteratureCitationProps {
  literature: Literature
  relevanceExplanation?: string
  supportPoints?: string[]
  showQualityIndicators?: boolean
  onAddToCollection?: (literature: Literature) => void
  onShare?: (literature: Literature) => void
  className?: string
}

export function InteractiveLiteratureCitation({
  literature,
  relevanceExplanation,
  supportPoints = [],
  showQualityIndicators = true,
  onAddToCollection,
  onShare,
  className = ''
}: InteractiveLiteratureCitationProps) {
  const [isCollected, setIsCollected] = useState(false)
  const [showFullAbstract, setShowFullAbstract] = useState(false)

  const formatAuthors = (authors: string[]) => {
    if (authors.length <= 3) {
      return authors.join(', ')
    }
    return `${authors.slice(0, 3).join(', ')}等`
  }

  const getQualityLevel = (impactFactor?: number, citationCount?: number) => {
    if (!impactFactor && !citationCount) return 'unknown'
    
    const ifScore = impactFactor ? (impactFactor > 20 ? 3 : impactFactor > 10 ? 2 : 1) : 0
    const citationScore = citationCount ? (citationCount > 1000 ? 3 : citationCount > 100 ? 2 : 1) : 0
    const totalScore = ifScore + citationScore
    
    if (totalScore >= 5) return 'excellent'
    if (totalScore >= 3) return 'good'
    return 'standard'
  }

  const getQualityColor = (level: string) => {
    switch (level) {
      case 'excellent': return 'text-green-700 bg-green-100 border-green-200'
      case 'good': return 'text-blue-700 bg-blue-100 border-blue-200'
      case 'standard': return 'text-gray-700 bg-gray-100 border-gray-200'
      default: return 'text-gray-500 bg-gray-50 border-gray-200'
    }
  }

  const handleCopyDOI = () => {
    if (literature.doi) {
      navigator.clipboard.writeText(`https://doi.org/${literature.doi}`)
      toast.success('DOI链接已复制到剪贴板')
    }
  }

  const handleAddToCollection = () => {
    setIsCollected(!isCollected)
    if (onAddToCollection) {
      onAddToCollection(literature)
    }
    toast.success(isCollected ? '已从收藏中移除' : '已添加到文献收藏')
  }

  const handleShare = () => {
    if (onShare) {
      onShare(literature)
    } else {
      // 默认分享行为
      const shareText = `推荐文献：${literature.title} - ${literature.journal} (${literature.publication_year})`
      if (navigator.share) {
        navigator.share({
          title: literature.title,
          text: shareText,
          url: literature.doi ? `https://doi.org/${literature.doi}` : undefined
        })
      } else {
        navigator.clipboard.writeText(shareText)
        toast.success('文献信息已复制到剪贴板')
      }
    }
  }

  const qualityLevel = getQualityLevel(literature.impact_factor, literature.citation_count)

  return (
    <Card className={`literature-citation hover:shadow-md transition-shadow ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 pr-4">
            <CardTitle className="text-base font-semibold text-gray-900 leading-tight">
              {literature.title}
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">
              {formatAuthors(literature.authors)} • {literature.journal} ({literature.publication_year})
            </p>
          </div>
          
          {showQualityIndicators && (
            <div className="flex flex-col gap-1">
              {literature.impact_factor && (
                <Badge variant="secondary" className="text-xs">
                  <Star className="h-3 w-3 mr-1" />
                  IF: {literature.impact_factor}
                </Badge>
              )}
              <Badge variant="outline" className={`text-xs ${getQualityColor(qualityLevel)}`}>
                {qualityLevel === 'excellent' ? '顶级期刊' : 
                 qualityLevel === 'good' ? '优质期刊' : '标准期刊'}
              </Badge>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* 分类标签 */}
        <div className="flex flex-wrap gap-1 mb-3">
          <Badge className="text-xs bg-blue-100 text-blue-800">
            {literature.category}
          </Badge>
          {literature.technology_tags.slice(0, 2).map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>

        {/* 关键发现 */}
        <div className="mb-3">
          <p className="text-sm text-gray-700 leading-relaxed">
            <span className="font-medium">关键发现：</span>
            {literature.key_findings}
          </p>
        </div>

        {/* 相关性说明 */}
        {relevanceExplanation && (
          <div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-xs text-blue-800">
              <span className="font-medium">相关性：</span>
              {relevanceExplanation}
            </p>
          </div>
        )}

        {/* 支持要点 */}
        {supportPoints.length > 0 && (
          <div className="mb-3">
            <p className="text-xs font-medium text-gray-700 mb-1">支持要点：</p>
            <ul className="text-xs text-gray-600 space-y-1">
              {supportPoints.slice(0, 2).map((point, index) => (
                <li key={index} className="flex items-start gap-1">
                  <span className="text-blue-600 mt-0.5">•</span>
                  <span>{point}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* 统计信息 */}
        <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
          <div className="flex items-center gap-3">
            <span className="flex items-center gap-1">
              <TrendingUp className="h-3 w-3" />
              引用: {literature.citation_count}
            </span>
            <span className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              相关性: {Math.round(literature.relevance_score * 100)}%
            </span>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {/* 查看详情按钮 */}
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="text-xs">
                  <BookOpen className="h-3 w-3 mr-1" />
                  详情
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="text-lg">{literature.title}</DialogTitle>
                </DialogHeader>
                
                <Tabs defaultValue="overview" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="overview">概览</TabsTrigger>
                    <TabsTrigger value="abstract">摘要</TabsTrigger>
                    <TabsTrigger value="methodology">方法</TabsTrigger>
                    <TabsTrigger value="relevance">相关性</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="overview" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium mb-2">基本信息</h4>
                        <div className="space-y-2 text-sm">
                          <p><span className="font-medium">期刊：</span>{literature.journal}</p>
                          <p><span className="font-medium">年份：</span>{literature.publication_year}</p>
                          <p><span className="font-medium">作者：</span>{literature.authors.join(', ')}</p>
                          {literature.impact_factor && (
                            <p><span className="font-medium">影响因子：</span>{literature.impact_factor}</p>
                          )}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2">质量指标</h4>
                        <div className="space-y-2 text-sm">
                          <p><span className="font-medium">引用次数：</span>{literature.citation_count}</p>
                          <p><span className="font-medium">相关性评分：</span>{Math.round(literature.relevance_score * 100)}%</p>
                          <p><span className="font-medium">分类：</span>{literature.category}</p>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="abstract">
                    <div className="prose prose-sm max-w-none">
                      <h4 className="font-medium mb-2">摘要</h4>
                      <p className="text-sm leading-relaxed">{literature.abstract}</p>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="methodology">
                    <div className="prose prose-sm max-w-none">
                      <h4 className="font-medium mb-2">方法学总结</h4>
                      <p className="text-sm leading-relaxed">{literature.methodology_summary}</p>
                      
                      <h4 className="font-medium mb-2 mt-4">技术标签</h4>
                      <div className="flex flex-wrap gap-2">
                        {literature.technology_tags.map((tag, index) => (
                          <Badge key={index} variant="outline">{tag}</Badge>
                        ))}
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="relevance">
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-2">商业价值</h4>
                        <p className="text-sm leading-relaxed">{literature.business_value}</p>
                      </div>
                      
                      {supportPoints.length > 0 && (
                        <div>
                          <h4 className="font-medium mb-2">支持要点</h4>
                          <ul className="space-y-1">
                            {supportPoints.map((point, index) => (
                              <li key={index} className="text-sm flex items-start gap-2">
                                <span className="text-blue-600 mt-1">•</span>
                                <span>{point}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </DialogContent>
            </Dialog>

            {/* 收藏按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleAddToCollection}
              className={`text-xs ${isCollected ? 'text-red-600' : 'text-gray-600'}`}
            >
              <Heart className={`h-3 w-3 mr-1 ${isCollected ? 'fill-current' : ''}`} />
              {isCollected ? '已收藏' : '收藏'}
            </Button>
          </div>

          <div className="flex items-center gap-1">
            {/* DOI链接 */}
            {literature.doi && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopyDOI}
                className="text-xs text-gray-600"
              >
                <Copy className="h-3 w-3 mr-1" />
                DOI
              </Button>
            )}

            {/* 外部链接 */}
            {literature.doi && (
              <Button
                variant="ghost"
                size="sm"
                asChild
                className="text-xs text-gray-600"
              >
                <a
                  href={`https://doi.org/${literature.doi}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  原文
                </a>
              </Button>
            )}

            {/* 分享按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleShare}
              className="text-xs text-gray-600"
            >
              <Share2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
