"use client"

import React, { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  BookOpen, 
  Search, 
  AlertTriangle, 
  Shield, 
  Target, 
  TrendingUp,
  ExternalLink,
  Copy,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'

interface ComprehensiveSolutionDisplayProps {
  solutionData: {
    personalized_solution: any
    literature_recommendations: any
    search_keywords: any  
    pain_point_analysis: any
    risk_assessment: any
    综合建议: any
  }
}

export function ComprehensiveSolutionDisplay({ solutionData }: ComprehensiveSolutionDisplayProps) {
  const [copiedKeyword, setCopiedKeyword] = useState<string | null>(null)
  
  const copyToClipboard = (text: string, id: string) => {
    navigator.clipboard.writeText(text)
    setCopiedKeyword(id)
    setTimeout(() => setCopiedKeyword(null), 2000)
  }

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case '低': return 'text-green-600 bg-green-50 border-green-200'
      case '中': case '中等': return 'text-yellow-600 bg-yellow-50 border-yellow-200'  
      case '高': case '中高': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case '高': return <AlertTriangle className="h-4 w-4 text-red-500" />
      case '中': return <Clock className="h-4 w-4 text-yellow-500" />
      case '低': return <CheckCircle className="h-4 w-4 text-green-500" />
      default: return <CheckCircle className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          🧬 单细胞测序解决方案
        </h2>
        <p className="text-gray-600">
          基于您的需求生成的专业化单细胞测序方案、文献推荐和风险分析
        </p>
      </div>

      <Tabs defaultValue="solution" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="solution" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            技术方案
          </TabsTrigger>
          <TabsTrigger value="literature" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            文献推荐
          </TabsTrigger>
          <TabsTrigger value="keywords" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            搜索关键词
          </TabsTrigger>
          <TabsTrigger value="painpoints" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            领域痛点
          </TabsTrigger>
          <TabsTrigger value="risks" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            风险评估
          </TabsTrigger>
        </TabsList>

        {/* 技术方案 */}
        <TabsContent value="solution" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                个性化技术方案
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">推荐技术路线</h4>
                  <Badge variant="outline" className="mb-2">
                    {solutionData.personalized_solution?.技术路线 || '10x Genomics标准流程'}
                  </Badge>
                  <div className="space-y-2">
                    {solutionData.personalized_solution?.核心优势?.map((advantage: string, index: number) => (
                      <div key={index} className="flex items-start gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{advantage}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">预期成果</h4>
                  <div className="space-y-2">
                    {Object.entries(solutionData.personalized_solution?.预期成果 || {}).map(([key, value]) => (
                      <div key={key} className="bg-blue-50 p-3 rounded-lg">
                        <h5 className="font-medium text-blue-900 mb-1">{key}</h5>
                        <p className="text-sm text-blue-700">{value as string}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {solutionData.personalized_solution?.专业化流程 && (
                <div className="mt-6">
                  <h4 className="font-semibold mb-3">专业化实验流程</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {Object.entries(solutionData.personalized_solution.专业化流程).map(([step, details]) => (
                      <Card key={step} className="border-l-4 border-l-blue-500">
                        <CardContent className="p-4">
                          <h5 className="font-semibold text-blue-900 mb-2">{step}</h5>
                          {typeof details === 'object' ? (
                            <div className="space-y-1">
                              {Object.entries(details as Record<string, any>).map(([key, value]) => (
                                <div key={key} className="text-sm">
                                  <span className="font-medium">{key}:</span>
                                  <span className="ml-1 text-gray-600">{value}</span>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-sm text-gray-600">{details as string}</p>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 文献推荐 */}
        <TabsContent value="literature" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                智能文献推荐
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg mb-4">
                <h4 className="font-semibold text-blue-900 mb-2">推荐策略</h4>
                <p className="text-sm text-blue-700">
                  {solutionData.literature_recommendations?.推荐策略}
                </p>
              </div>

              {solutionData.literature_recommendations?.分类推荐 && (
                <div className="space-y-4">
                  {Object.entries(solutionData.literature_recommendations.分类推荐).map(([category, papers]) => (
                    <div key={category}>
                      <h4 className="font-semibold mb-3 flex items-center gap-2">
                        <Badge variant="secondary">{category}</Badge>
                      </h4>
                      <div className="space-y-3">
                        {(Array.isArray(papers) ? papers : [papers]).map((paper: any, index: number) => (
                          <Card key={index} className="border-l-4 border-l-green-500">
                            <CardContent className="p-4">
                              <div className="flex items-start justify-between gap-4">
                                <div className="flex-1">
                                  <h5 className="font-semibold text-gray-900 mb-2">
                                    {paper.title || `${category}相关文献 ${index + 1}`}
                                  </h5>
                                  {paper.reason && (
                                    <p className="text-sm text-gray-600 mb-2">{paper.reason}</p>
                                  )}
                                  {paper.authors && (
                                    <p className="text-xs text-gray-500">
                                      作者: {Array.isArray(paper.authors) ? paper.authors.join(', ') : paper.authors}
                                    </p>
                                  )}
                                  {paper.journal && paper.year && (
                                    <p className="text-xs text-gray-500">
                                      {paper.journal} ({paper.year})
                                    </p>
                                  )}
                                </div>
                                <div className="flex flex-col items-end gap-2">
                                  {paper.priority && (
                                    <Badge variant={paper.priority === '高' ? 'destructive' : 'secondary'}>
                                      {paper.priority}
                                    </Badge>
                                  )}
                                  {paper.doi && (
                                    <Button size="sm" variant="outline" asChild>
                                      <a href={`https://doi.org/${paper.doi}`} target="_blank" rel="noopener noreferrer">
                                        <ExternalLink className="h-3 w-3 mr-1" />
                                        查看
                                      </a>
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {solutionData.literature_recommendations?.阅读建议 && (
                <Alert>
                  <BookOpen className="h-4 w-4" />
                  <AlertDescription className="space-y-2">
                    <div>
                      <strong>阅读建议:</strong>
                      <p className="mt-1">{solutionData.literature_recommendations.阅读建议.优先顺序}</p>
                      <p className="text-sm text-gray-600 mt-1">
                        {solutionData.literature_recommendations.阅读建议.实用提示}
                      </p>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 搜索关键词 */}
        <TabsContent value="keywords" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                智能搜索关键词
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-green-50 p-4 rounded-lg mb-4">
                <h4 className="font-semibold text-green-900 mb-2">搜索策略</h4>
                <p className="text-sm text-green-700">
                  {solutionData.search_keywords?.搜索策略}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {['主要关键词', '技术关键词', '热门方向', '个性化关键词'].map((category) => (
                  <div key={category}>
                    <h4 className="font-semibold mb-3">{category}</h4>
                    <div className="flex flex-wrap gap-2">
                      {solutionData.search_keywords?.[category]?.map((keyword: string, index: number) => (
                        <Badge 
                          key={index}
                          variant="outline" 
                          className="cursor-pointer hover:bg-blue-50 transition-colors"
                          onClick={() => copyToClipboard(keyword, `${category}-${index}`)}
                        >
                          {keyword}
                          {copiedKeyword === `${category}-${index}` ? (
                            <CheckCircle className="h-3 w-3 ml-1 text-green-500" />
                          ) : (
                            <Copy className="h-3 w-3 ml-1" />
                          )}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              {solutionData.search_keywords?.组合搜索建议 && (
                <div className="mt-6">
                  <h4 className="font-semibold mb-3">组合搜索建议</h4>
                  <div className="space-y-2">
                    {solutionData.search_keywords.组合搜索建议.map((query: any, index: number) => {
                      const queryText = typeof query === 'string' ? query : query?.name || JSON.stringify(query)
                      return (
                      <div key={index} className="bg-gray-50 p-3 rounded-lg font-mono text-sm flex items-center justify-between">
                        <span>{queryText}</span>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(queryText, `combo-${index}`)}
                        >
                          {copiedKeyword === `combo-${index}` ? (
                            <CheckCircle className="h-3 w-3 text-green-500" />
                          ) : (
                            <Copy className="h-3 w-3" />
                          )}
                        </Button>
                      </div>
                      )
                    })}
                  </div>
                </div>
              )}

              {solutionData.search_keywords?.数据库建议 && (
                <div className="mt-6">
                  <h4 className="font-semibold mb-3">推荐数据库</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {Object.entries(solutionData.search_keywords.数据库建议).map(([db, description]) => (
                      <div key={db} className="border rounded-lg p-3">
                        <h5 className="font-medium">{db}</h5>
                        <p className="text-sm text-gray-600 mt-1">{description as string}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 领域痛点 */}
        <TabsContent value="painpoints" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                领域痛点分析
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-orange-50 p-4 rounded-lg mb-4">
                <h4 className="font-semibold text-orange-900 mb-2">分析总结</h4>
                <p className="text-sm text-orange-700">
                  {solutionData.pain_point_analysis?.痛点分析总结}
                </p>
              </div>

              {solutionData.pain_point_analysis?.主要痛点 && (
                <div className="space-y-4">
                  <h4 className="font-semibold">主要痛点识别</h4>
                  {solutionData.pain_point_analysis.主要痛点.map((painPoint: any, index: number) => (
                    <Card key={index} className={`border-l-4 ${
                      painPoint.影响程度 === '高' ? 'border-l-red-500' :
                      painPoint.影响程度 === '中高' ? 'border-l-orange-500' : 'border-l-yellow-500'
                    }`}>
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between gap-4 mb-3">
                          <div>
                            <h5 className="font-semibold">{painPoint.痛点}</h5>
                            <Badge className="mt-1" variant="secondary">{painPoint.类别}</Badge>
                          </div>
                          <Badge className={getRiskLevelColor(painPoint.影响程度)}>
                            {painPoint.影响程度}
                          </Badge>
                        </div>
                        
                        <div className="space-y-3">
                          <div>
                            <h6 className="font-medium text-sm mb-1">具体表现:</h6>
                            <ul className="text-sm space-y-1">
                              {painPoint.具体表现?.map((symptom: string, i: number) => (
                                <li key={i} className="flex items-start gap-2">
                                  <XCircle className="h-3 w-3 text-red-500 mt-0.5 flex-shrink-0" />
                                  {symptom}
                                </li>
                              ))}
                            </ul>
                          </div>
                          
                          <div>
                            <h6 className="font-medium text-sm mb-1">解决方案:</h6>
                            <ul className="text-sm space-y-1">
                              {painPoint.解决方案?.map((solution: string, i: number) => (
                                <li key={i} className="flex items-start gap-2">
                                  <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                                  {solution}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {solutionData.pain_point_analysis?.缓解策略 && (
                <Alert>
                  <TrendingUp className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-2">
                      <strong>缓解策略建议:</strong>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                        {Object.entries(solutionData.pain_point_analysis.缓解策略).map(([type, strategies]) => (
                          <div key={type}>
                            <h6 className="font-medium mb-1">{type}:</h6>
                            <ul className="text-sm space-y-1">
                              {(strategies as string[]).map((strategy: string, i: number) => (
                                <li key={i} className="flex items-start gap-2">
                                  <div className="w-1 h-1 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                                  {strategy}
                                </li>
                              ))}
                            </ul>
                          </div>
                        ))}
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 风险评估 */}
        <TabsContent value="risks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                项目风险评估
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {solutionData.risk_assessment?.风险评估总结 && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  <Card className="text-center">
                    <CardContent className="p-4">
                      <div className={`text-2xl font-bold ${
                        solutionData.risk_assessment.风险评估总结.整体风险等级 === '低' ? 'text-green-600' :
                        solutionData.risk_assessment.风险评估总结.整体风险等级 === '中等' ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {solutionData.risk_assessment.风险评估总结.整体风险等级}
                      </div>
                      <div className="text-sm text-gray-600">整体风险等级</div>
                    </CardContent>
                  </Card>
                  <Card className="text-center">
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold text-blue-600">
                        {solutionData.risk_assessment.风险评估总结.成功概率}
                      </div>
                      <div className="text-sm text-gray-600">成功概率</div>
                    </CardContent>
                  </Card>
                  <Card className="text-center">
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold text-purple-600">
                        {solutionData.risk_assessment.风险评估总结.风险分数}/3
                      </div>
                      <div className="text-sm text-gray-600">风险分数</div>
                    </CardContent>
                  </Card>
                  <Card className="text-center">
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold text-orange-600">
                        {solutionData.risk_assessment.风险评估总结.识别风险数}
                      </div>
                      <div className="text-sm text-gray-600">识别风险数</div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {solutionData.risk_assessment?.详细风险列表 && (
                <div className="space-y-4">
                  <h4 className="font-semibold">详细风险列表</h4>
                  {solutionData.risk_assessment.详细风险列表.map((risk: any, index: number) => (
                    <Card key={index} className={`border-l-4 ${
                      risk.风险等级 === '高' || risk.风险等级 === '中高' ? 'border-l-red-500' :
                      risk.风险等级 === '中' || risk.风险等级 === '中等' ? 'border-l-yellow-500' : 'border-l-green-500'
                    }`}>
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between gap-4 mb-3">
                          <div>
                            <h5 className="font-semibold">{risk.具体风险}</h5>
                            <Badge className="mt-1" variant="secondary">{risk.风险类别}</Badge>
                          </div>
                          <Badge className={getRiskLevelColor(risk.风险等级)}>
                            {risk.风险等级}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h6 className="font-medium text-sm mb-2">主要风险:</h6>
                            <ul className="text-sm space-y-1">
                              {risk.主要风险?.map((mainRisk: string, i: number) => (
                                <li key={i} className="flex items-start gap-2">
                                  <AlertTriangle className="h-3 w-3 text-orange-500 mt-0.5 flex-shrink-0" />
                                  {mainRisk}
                                </li>
                              ))}
                            </ul>
                          </div>
                          
                          <div>
                            <h6 className="font-medium text-sm mb-2">缓解措施:</h6>
                            <ul className="text-sm space-y-1">
                              {risk.缓解措施?.map((mitigation: string, i: number) => (
                                <li key={i} className="flex items-start gap-2">
                                  <Shield className="h-3 w-3 text-blue-500 mt-0.5 flex-shrink-0" />
                                  {mitigation}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {solutionData.risk_assessment?.风险管理建议 && (
                <Alert>
                  <Shield className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-3">
                      <strong>风险管理建议:</strong>
                      {Object.entries(solutionData.risk_assessment.风险管理建议).map(([priority, suggestions]) => (
                        <div key={priority}>
                          <h6 className="font-medium flex items-center gap-2 mb-1">
                            {getPriorityIcon(priority.includes('高') ? '高' : priority.includes('一般') ? '中' : '低')}
                            {priority}:
                          </h6>
                          <ul className="text-sm space-y-1 ml-6">
                            {(suggestions as string[]).map((suggestion: string, i: number) => (
                              <li key={i} className="flex items-start gap-2">
                                <div className="w-1 h-1 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                                {suggestion}
                              </li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 综合建议 */}
      {solutionData.综合建议 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              综合建议与行动计划
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Object.entries(solutionData.综合建议.执行优先级 || {}).map(([priority, actions]) => (
                <Card key={priority} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <h4 className="font-semibold mb-3">{priority}</h4>
                    <ul className="space-y-2">
                      {(actions as string[]).map((action: string, i: number) => (
                        <li key={i} className="text-sm flex items-start gap-2">
                          <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                          {action}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">成功关键因素</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {solutionData.综合建议.成功关键因素?.map((factor: string, index: number) => (
                  <div key={index} className="flex items-center gap-2 text-sm text-blue-800">
                    <CheckCircle className="h-4 w-4 text-blue-600" />
                    {factor}
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}