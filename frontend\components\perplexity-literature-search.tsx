"use client"

import React, { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Search, 
  Sparkles, 
  BookOpen, 
  Zap, 
  ChevronRight, 
  ExternalLink,
  RefreshCw,
  Lightbulb,
  TrendingUp,
  Star,
  Clock,
  Users,
  ArrowRight,
  Copy,
  Download
} from 'lucide-react'
import { toast } from 'sonner'
import { literatureApi, smartLiteratureApi } from '@/lib/api'

interface RequirementData {
  speciesType: string
  experimentType: string
  researchGoal: string
  sampleType: string
  budget: string
  timeline: string
  analysisType: string
  // ... 其他字段
}

interface SearchResult {
  query: string
  sources: string[]
  papers: any[]
  summary: string
  relatedTopics: string[]
  searchTime: number
}

interface PerplexityLiteratureSearchProps {
  requirements?: RequirementData
  onSearchComplete?: (results: SearchResult) => void
  className?: string
}

export function PerplexityLiteratureSearch({ 
  requirements, 
  onSearchComplete,
  className = "" 
}: PerplexityLiteratureSearchProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [searchProgress, setSearchProgress] = useState(0)
  const [currentStatus, setCurrentStatus] = useState("")
  const [searchResults, setSearchResults] = useState<SearchResult | null>(null)
  const [streamingResponse, setStreamingResponse] = useState("")
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [relatedQueries, setRelatedQueries] = useState<string[]>([])
  
  const resultRef = useRef<HTMLDivElement>(null)

  // 基于问卷信息生成智能搜索建议
  useEffect(() => {
    if (requirements) {
      generateSmartQueries()
    }
  }, [requirements])

  const generateSmartQueries = async () => {
    if (!requirements) return

    try {
      const queryResult = await smartLiteratureApi.generateSmartQueries({
        requirements,
        max_queries: 6
      })

      setSuggestions(queryResult.suggested_queries)
      
      // 自动设置第一个建议作为默认查询
      if (queryResult.primary_query && !searchQuery) {
        setSearchQuery(queryResult.primary_query)
      }
    } catch (error) {
      console.error('生成智能查询失败:', error)
      // 使用备用查询生成逻辑
      generateFallbackQueries()
    }
  }

  const generateFallbackQueries = () => {
    if (!requirements) return

    const queries = []
    const { speciesType, experimentType, researchGoal, sampleType } = requirements

    // 基于物种和实验类型生成查询
    if (speciesType && experimentType) {
      const species = speciesType.split('(')[0].trim()
      const tech = experimentType.split('(')[0].trim()
      queries.push(`${species} ${tech} 最新研究进展`)
    }

    // 基于研究目标生成查询
    if (researchGoal) {
      queries.push(`单细胞${researchGoal}最佳实践`)
      queries.push(`${researchGoal}关键技术挑战`)
    }

    // 基于样本类型生成查询
    if (sampleType) {
      queries.push(`${sampleType}单细胞测序技术方案`)
    }

    // 综合查询
    const comprehensiveQuery = [
      species?.replace('(', '').replace(')', ''),
      experimentType?.split('(')[0].trim(),
      researchGoal
    ].filter(Boolean).join(' ')

    if (comprehensiveQuery) {
      queries.unshift(comprehensiveQuery)
    }

    setSuggestions(queries.slice(0, 6))
    
    // 自动设置第一个建议作为默认查询
    if (queries.length > 0 && !searchQuery) {
      setSearchQuery(queries[0])
    }
  }

  // 智能提示词优化
  const optimizeSearchQuery = async (query: string): Promise<string> => {
    // 添加专业术语和上下文
    const optimizedTerms = []
    
    // 添加单细胞相关术语
    if (!query.includes('single cell') && !query.includes('单细胞')) {
      optimizedTerms.push('single cell')
    }
    
    // 基于问卷添加上下文
    if (requirements) {
      if (requirements.experimentType?.includes('scRNA-seq')) {
        optimizedTerms.push('scRNA-seq')
      }
      if (requirements.speciesType?.includes('人类')) {
        optimizedTerms.push('human')
      }
      if (requirements.speciesType?.includes('小鼠')) {
        optimizedTerms.push('mouse')
      }
    }

    return `${query} ${optimizedTerms.join(' ')}`.trim()
  }

  // 执行Perplexity风格的搜索
  const executeSearch = async (query: string) => {
    setIsSearching(true)
    setSearchProgress(0)
    setStreamingResponse("")
    setSearchResults(null)

    const startTime = Date.now()

    try {
      // 调用Perplexity风格搜索API
      setCurrentStatus("🤖 优化搜索查询...")
      setSearchProgress(20)

      const searchResponse = await smartLiteratureApi.perplexitySearch({
        query,
        requirements,
        include_analysis: true,
        max_papers: 10
      })

      // 模拟进度更新
      setCurrentStatus("📚 搜索文献数据库...")
      setSearchProgress(50)
      await new Promise(resolve => setTimeout(resolve, 800))

      setCurrentStatus("🧠 AI分析文献相关性...")
      setSearchProgress(75)
      await new Promise(resolve => setTimeout(resolve, 1000))

      setCurrentStatus("✨ 生成智能摘要...")
      setSearchProgress(90)

      // 模拟流式响应
      const summaryParts = searchResponse.ai_summary.split('\n')
      for (const part of summaryParts) {
        await new Promise(resolve => setTimeout(resolve, 150))
        setStreamingResponse(prev => prev + part + '\n')
      }

      setSearchProgress(100)
      setCurrentStatus("✅ 搜索完成")

      const results: SearchResult = {
        query: searchResponse.optimized_query,
        sources: searchResponse.sources,
        papers: searchResponse.papers,
        summary: searchResponse.ai_summary,
        relatedTopics: searchResponse.related_topics,
        searchTime: searchResponse.search_time_ms
      }

      setSearchResults(results)
      onSearchComplete?.(results)

      // 滚动到结果区域
      setTimeout(() => {
        resultRef.current?.scrollIntoView({ behavior: 'smooth' })
      }, 300)

    } catch (error) {
      console.error('搜索失败:', error)
      toast.error('搜索失败，请稍后重试')
      
      // 降级到本地搜索
      await fallbackSearch(query)
    } finally {
      setIsSearching(false)
      setCurrentStatus("")
    }
  }

  // 降级搜索方法
  const fallbackSearch = async (query: string) => {
    try {
      setCurrentStatus("🔄 使用本地文献库搜索...")
      setSearchProgress(50)

      const optimizedQuery = await optimizeSearchQuery(query)
      const searchParams = {
        query: optimizedQuery,
        top_k: 10,
        category: undefined,
        technology_tags: requirements?.experimentType ? [requirements.experimentType] : undefined
      }

      const literatureResults = await literatureApi.searchLiterature(searchParams)
      
      setSearchProgress(80)
      const summary = await generateIntelligentSummary(literatureResults, optimizedQuery)
      
      setSearchProgress(100)
      setCurrentStatus("✅ 搜索完成")

      const searchTime = Date.now() - Date.now()
      const results: SearchResult = {
        query: optimizedQuery,
        sources: ['本地文献库'],
        papers: literatureResults,
        summary: summary,
        relatedTopics: generateRelatedTopics(optimizedQuery, literatureResults),
        searchTime
      }

      setSearchResults(results)
      onSearchComplete?.(results)

    } catch (error) {
      console.error('降级搜索也失败:', error)
      toast.error('搜索服务暂时不可用')
    }
  }

  // AI分析结果相关性
  const analyzeResults = async (papers: any[], query: string) => {
    // 模拟AI分析过程
    return papers.map(paper => ({
      ...paper,
      relevanceScore: Math.random() * 0.4 + 0.6, // 0.6-1.0
      aiInsights: generateAIInsights(paper, query)
    }))
  }

  // 生成AI洞察
  const generateAIInsights = (paper: any, query: string): string => {
    const insights = [
      `该研究与您的${requirements?.researchGoal || '研究目标'}高度相关`,
      `采用了与您需求相似的${requirements?.experimentType || '实验技术'}`,
      `在${requirements?.sampleType || '样本类型'}研究中具有参考价值`,
      `方法学上为您的研究提供重要指导`,
      `数据分析策略可直接应用于您的项目`
    ]
    
    return insights[Math.floor(Math.random() * insights.length)]
  }

  // 生成智能摘要
  const generateIntelligentSummary = async (papers: any[], query: string): Promise<string> => {
    // 模拟流式响应
    const summaryParts = [
      `基于您的研究需求"${query}"，我找到了 ${papers.length} 篇高度相关的文献。`,
      `\n\n**主要发现：**\n`,
      `• **技术趋势**：10x Genomics平台在${requirements?.speciesType || '相关物种'}研究中应用最为广泛\n`,
      `• **方法学创新**：最新研究采用改进的细胞分离和质量控制策略\n`,
      `• **分析进展**：新的生物信息学工具显著提升了数据解读能力\n`,
      `\n**关键推荐**：\n`,
      `基于文献分析，建议您采用10x Genomics Chromium平台，`,
      `配合最新的数据分析流程，可以获得最佳的研究结果。`,
      `\n\n**成本效益**：参考文献显示，当前方案的成本效益比较优秀，`,
      `符合您的${requirements?.budget || '预算要求'}。`
    ]

    for (const part of summaryParts) {
      await new Promise(resolve => setTimeout(resolve, 200))
      setStreamingResponse(prev => prev + part)
    }

    return summaryParts.join('')
  }

  // 生成相关主题
  const generateRelatedTopics = (query: string, papers: any[]): string[] => {
    const topics = [
      '单细胞测序质量控制',
      '细胞分离技术优化',
      '数据预处理最佳实践',
      '成本效益分析',
      '技术平台比较',
      '样本制备策略',
      '生物信息学分析流程',
      '结果验证方法'
    ]
    
    return topics.slice(0, 4)
  }

  // 处理建议查询点击
  const handleSuggestionClick = (suggestion: string) => {
    setSearchQuery(suggestion)
  }

  // 处理搜索
  const handleSearch = () => {
    if (!searchQuery.trim()) {
      toast.error('请输入搜索内容')
      return
    }
    executeSearch(searchQuery)
  }

  // 处理回车搜索
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isSearching) {
      handleSearch()
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 搜索界面 */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg text-blue-900">
            <Sparkles className="h-5 w-5" />
            AI文献研究助手
          </CardTitle>
          <p className="text-sm text-blue-700">
            基于您的需求信息，为您提供精准的文献搜索和AI分析
          </p>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* 搜索输入框 */}
          <div className="relative">
            <div className="relative flex">
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入您的研究问题，AI将为您搜索最相关的文献..."
                className="pr-12 h-12 text-base"
                disabled={isSearching}
              />
              <Button
                onClick={handleSearch}
                disabled={isSearching || !searchQuery.trim()}
                className="absolute right-1 top-1 h-10 px-4"
              >
                {isSearching ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {/* 智能建议 */}
          {suggestions.length > 0 && !isSearching && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-slate-600">
                <Lightbulb className="h-4 w-4" />
                <span>基于您的需求推荐：</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {suggestions.map((suggestion, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="text-xs bg-white hover:bg-blue-50 border-blue-200"
                  >
                    {suggestion}
                    <ChevronRight className="h-3 w-3 ml-1" />
                  </Button>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 搜索进度 */}
      {isSearching && (
        <Card className="border-amber-200 bg-gradient-to-r from-amber-50 to-yellow-50">
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <RefreshCw className="h-5 w-5 animate-spin text-amber-600" />
                <span className="font-medium text-amber-800">正在搜索...</span>
              </div>
              
              <Progress value={searchProgress} className="h-2 bg-amber-100" />
              
              <div className="text-sm text-amber-700">
                {currentStatus}
              </div>
              
              <div className="text-xs text-amber-600">
                正在分析 PubMed • Semantic Scholar • bioRxiv 等数据源...
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 搜索结果 */}
      {searchResults && (
        <div ref={resultRef} className="space-y-6">
          {/* AI摘要 */}
          <Card className="border-green-200 bg-gradient-to-r from-green-50 to-emerald-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-900">
                <Zap className="h-5 w-5" />
                AI智能分析
                <Badge variant="secondary" className="ml-auto">
                  <Clock className="h-3 w-3 mr-1" />
                  {(searchResults.searchTime / 1000).toFixed(1)}s
                </Badge>
              </CardTitle>
            </CardHeader>
            
            <CardContent>
              <div className="prose prose-sm max-w-none text-green-800">
                {streamingResponse && (
                  <div className="whitespace-pre-wrap">{streamingResponse}</div>
                )}
              </div>
              
              {/* 数据源信息 */}
              <div className="mt-4 pt-4 border-t border-green-200">
                <div className="flex items-center gap-2 text-xs text-green-600">
                  <Users className="h-3 w-3" />
                  <span>数据来源：</span>
                  {searchResults.sources.map((source, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {source}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 文献列表 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                相关文献 ({searchResults.papers.length})
              </CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {searchResults.papers.map((paper, index) => (
                <div
                  key={index}
                  className="p-4 border border-slate-200 rounded-lg hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-slate-900 flex-1 pr-4">
                      {paper.title}
                    </h3>
                    {paper.impact_factor && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <Star className="h-3 w-3" />
                        IF: {paper.impact_factor}
                      </Badge>
                    )}
                  </div>
                  
                  <p className="text-sm text-slate-600 mb-2">
                    {paper.authors?.slice(0, 3).join(', ')}
                    {paper.authors?.length > 3 && '等'} • {paper.journal} ({paper.publication_year})
                  </p>
                  
                  <div className="flex flex-wrap gap-2 mb-3">
                    <Badge className="bg-blue-100 text-blue-800">
                      {paper.category}
                    </Badge>
                    {paper.technology_tags?.slice(0, 2).map((tag: string, tagIndex: number) => (
                      <Badge key={tagIndex} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  
                  {paper.aiInsights && (
                    <div className="p-2 bg-blue-50 rounded border-l-4 border-blue-400 mb-3">
                      <div className="flex items-center gap-1 text-xs text-blue-700">
                        <Sparkles className="h-3 w-3" />
                        <span className="font-medium">AI洞察：</span>
                      </div>
                      <p className="text-xs text-blue-600 mt-1">{paper.aiInsights}</p>
                    </div>
                  )}
                  
                  <p className="text-sm text-slate-700 mb-3 line-clamp-2">
                    {paper.key_findings}
                  </p>
                  
                  <div className="flex items-center justify-between text-xs text-slate-500">
                    <div className="flex items-center gap-4">
                      <span>引用: {paper.citation_count}</span>
                      <span>相关性: {Math.round((paper.relevanceScore || paper.relevance_score) * 100)}%</span>
                    </div>
                    {paper.doi && (
                      <a
                        href={`https://doi.org/${paper.doi}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
                      >
                        <ExternalLink className="h-3 w-3" />
                        查看原文
                      </a>
                    )}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* 相关主题探索 */}
          {searchResults.relatedTopics.length > 0 && (
            <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-pink-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-purple-900">
                  <TrendingUp className="h-5 w-5" />
                  继续探索
                </CardTitle>
                <p className="text-sm text-purple-700">
                  点击下方主题深入研究相关领域
                </p>
              </CardHeader>
              
              <CardContent>
                <div className="grid grid-cols-2 gap-2">
                  {searchResults.relatedTopics.map((topic, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => handleSuggestionClick(topic)}
                      className="justify-start bg-white hover:bg-purple-50 border-purple-200"
                    >
                      <ArrowRight className="h-3 w-3 mr-2" />
                      {topic}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 操作按钮 */}
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-center gap-3">
                <Button variant="outline" size="sm">
                  <Copy className="h-4 w-4 mr-2" />
                  复制结果
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  导出报告
                </Button>
                <Button variant="outline" size="sm" onClick={() => {
                  setSearchResults(null)
                  setStreamingResponse("")
                }}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  新搜索
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

export default PerplexityLiteratureSearch