# CellForge AI 环境配置文件
# 复制此文件为 .env 并配置相应的API密钥

# 基础配置
DEBUG=true
SECRET_KEY=your-secret-key-change-in-production

# AI服务配置 (DeepSeek API)
# DeepSeek API支持两种模型：
# - deepseek-chat: DeepSeek-V3-0324，适合通用对话和分析任务
# - deepseek-reasoner: DeepSeek-R1-0528，专门优化推理和复杂分析任务
OPENAI_API_KEY=your-deepseek-api-key-here
OPENAI_API_BASE=https://api.deepseek.com/v1
OPENAI_MODEL=deepseek-chat

# 可选的DeepSeek原生配置（优先级更高）
# DEEPSEEK_API_KEY=your-deepseek-api-key-here
# DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
# DEEPSEEK_MODEL=deepseek-chat

# 注意: 也可以使用 https://api.deepseek.com 作为base_url（与OpenAI兼容）
# 推荐使用 https://api.deepseek.com/v1 以保持明确的API版本

# 文献搜索配置
LITERATURE_SEARCH_ENABLED=true

# === 核心文献搜索API配置 ===

# 1. NCBI/PubMed API (生物医学文献 - 推荐配置)
# 申请地址: https://ncbiinsights.ncbi.nlm.nih.gov/2017/11/02/new-api-keys-for-the-e-utilities/
PUBMED_API_KEY=your-ncbi-api-key-here

# 2. Google Scholar (通过SerpAPI - 强烈推荐用于跨学科文献搜索)  
# 申请地址: https://serpapi.com/ (免费账户每月100次搜索)
# 优势: 获取跨学科学术文献、引用分析、预印本、最新研究
# SERPAPI_KEY=your-serpapi-key-here
GOOGLE_SCHOLAR_ENABLED=true

# 3. Semantic Scholar API (学术论文 - 可选)
# 申请地址: https://www.semanticscholar.org/product/api
# SEMANTIC_SCHOLAR_API_KEY=your-semantic-scholar-api-key

# 4. bioRxiv API (预印本 - 免费，无需密钥)
BIORXIV_API_ENABLED=true

# === API限流配置 ===
PUBMED_RATE_LIMIT=3
SEMANTIC_SCHOLAR_RATE_LIMIT=10
BIORXIV_RATE_LIMIT=10
GOOGLE_SCHOLAR_RATE_LIMIT=5

# === 文献搜集配置 ===
LITERATURE_COLLECTION_MAX_PAPERS=50
LITERATURE_RELEVANCE_THRESHOLD=0.7
LITERATURE_CACHE_TTL=3600

# === 推荐的最小配置 ===
# 只需要配置以下两个即可获得良好效果：
# 1. OPENAI_API_KEY (用于AI分析)
# 2. PUBMED_API_KEY (用于文献搜索)

# === 高级配置 ===
# 如果想要最佳效果，额外配置：
# 3. SERPAPI_KEY (Google Scholar搜索)