# CellForge AI - 智能单细胞测序方案生成系统

## 📋 项目 概述

CellForge AI 是一个基于人工智能的单细胞测序实验方案智能推荐系统。通过对话式交互收集客户需求，结合文献资源和专业知识库，自动生成个性化的实验方案和分析流程建议。

### 🎯 核心功能
- **文献资源集成**: 自动检索和推荐相关科学文献
- **客户画像分析**: 多维度客户需求分析和行为预测
- **方案智能生成**: 个性化实验方案和成本估算
- **实时协作**: WebSocket 支持的流式对话体验

### 🏗️ 技术架构
- **前端**: Next.js 15 + React 19 + TypeScript + Tailwind CSS
- **后端**: FastAPI + Python 3.11+ + SQLAlchemy
- **数据库**: SQLite (开发) / PostgreSQL (生产) + Redis (缓存)
- **AI引擎**: DeepSeek API + LangChain + 向量搜索
- **部署**: Docker + Docker Compose

## 🚀 项目状态

### 🔄 开发中功能 (30%)
- **用户认证系统** : JWT认证、权限管理、密码安全
- **智能对话系统** : AI驱动咨询、安全提示词管理
- **客户画像系统** : 多维画像构建、行为分析
- **网页画像分析**  URL分析、关键词提取
- **文献资源系统** : 文献搜索、智能推荐
- **前端界面** : 完整的用户界面和交互体验


### ❌ 待实现功能 (0%)
- **方案生成引擎**: 智能配置和成本估算
- **数据分析平台**: 业务指标和用户洞察
- **向量数据库集成**: ChromaDB语义搜索
- **数据库迁移系统**: Alembic迁移管理
- **知识库管理** : 基础搜索框架，向量化存储待完善
- **WebSocket实时对话** : 基础框架，流式响应待优化
- **Redis缓存系统** : 配置完成，实际集成待实现

## 🚀 快速开始

### 环境要求
- Node.js 18+ (前端)
- Python 3.11+ (后端)
- Git
- Docker (可选)

### 1. 克隆项目
```bash
git clone https://gitlab.local.azenta.com/ngs_bi_data/cellforge-ai.git
cd cellforge-ai
```

### 2. 后端启动

```bash
# 进入后端目录
cd cellforge-backend

# 安装Python依赖
pip install -r requirements.txt

# 初始化数据库 (可选，SQLite会自动创建)
python testpy/init_simple_db.py

# 启动后端服务
python testpy/test_startup.py
# 或者直接启动
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 3. 前端启动

```bash
# 新开终端，进入前端目录
cd cellforge-ai-system

# 安装依赖
npm install
# 或使用 yarn
yarn install

# 启动开发服务器
npm run dev
# 或使用 yarn
yarn dev
```

### 4. 访问应用

- **前端应用**: http://localhost:3000
- **后端API文档**: http://localhost:8000/docs
- **后端ReDoc**: http://localhost:8000/redoc

### 5. 默认登录账户

```
邮箱: <EMAIL>
密码: admin123
```

## 🔧 环境配置

### 后端环境变量 (可选)

创建 `cellforge-backend/.env` 文件：

```bash
# AI服务配置 (可选，默认使用模拟AI)
OPENAI_API_KEY=your_deepseek_api_key
OPENAI_API_BASE=https://api.deepseek.com/v1
OPENAI_MODEL=deepseek-chat

# 文献搜索功能 (可选)
LITERATURE_SEARCH_ENABLED=true
PUBMED_API_KEY=your_pubmed_api_key
SEMANTIC_SCHOLAR_API_KEY=your_semantic_scholar_api_key
BIORXIV_API_ENABLED=true

# 数据库配置 (生产环境)
DATABASE_URL=postgresql+asyncpg://user:pass@localhost:5432/cellforge
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your_secret_key_here
```

### 前端环境变量 (可选)

创建 `cellforge-ai-system/.env.local` 文件：

```bash
# 后端API地址
NEXT_PUBLIC_API_URL=http://localhost:8000

# NextAuth配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret
```

## 🐳 Docker 部署

### 使用 Docker Compose 一键启动

```bash
# 在项目根目录
cd cellforge-backend
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 单独构建和运行

```bash
# 构建后端镜像
cd cellforge-backend
docker build -t cellforge-backend .

# 运行后端容器
docker run -d -p 8000:8000 --name cellforge-backend cellforge-backend

# 构建前端镜像
cd cellforge-ai-system
docker build -t cellforge-frontend .

# 运行前端容器
docker run -d -p 3000:3000 --name cellforge-frontend cellforge-frontend
```

## 📁 项目结构

```
cellforge-ai/
├── cellforge-ai-system/          # 前端 Next.js 应用
│   ├── app/                      # Next.js 13+ App Router
│   ├── components/               # React 组件
│   ├── contexts/                 # React Context
│   ├── hooks/                    # 自定义 Hooks
│   ├── lib/                      # 工具函数和API
│   ├── public/                   # 静态资源
│   └── styles/                   # 样式文件
├── cellforge-backend/            # 后端 FastAPI 应用
│   ├── app/                      # 应用核心代码
│   │   ├── api/                  # API 路由
│   │   ├── core/                 # 核心配置
│   │   ├── models/               # 数据模型
│   │   ├── schemas/              # Pydantic 模式
│   │   ├── services/             # 业务逻辑
│   │   └── main.py               # 应用入口
│   ├── testpy/                   # 测试和初始化脚本
│   ├── docs/                     # 文档
│   ├── requirements.txt          # Python 依赖
│   └── docker-compose.yml        # Docker 编排
├── prompt/                       # 开发文档和计划
│   ├── cellforge_integration_plan.md
│   ├── LITERATURE_INTEGRATION_IMPLEMENTATION.md
│   ├── remaining_tasks.md
│   └── single_cell_ai_prompts.md
└── README.md                     # 项目说明文档
```

## 🔗 主要 API 端点

### 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `GET /api/v1/auth/me` - 获取当前用户信息
- `POST /api/v1/auth/change-password` - 修改密码

### 智能对话
- `POST /api/v1/conversation/chat` - AI 对话
- `WS /api/v1/conversation/ws/{client_id}` - 实时对话
- `GET /api/v1/conversation/history` - 对话历史

### 客户画像
- `GET /api/v1/customer/profile/{user_id}` - 获取客户画像
- `PUT /api/v1/customer/profile/{user_id}` - 更新客户画像
- `POST /api/v1/customer/analyze` - 分析客户行为

### 文献资源
- `POST /api/v1/literature/search` - 搜索文献
- `POST /api/v1/literature/recommendations` - 获取文献推荐
- `GET /api/v1/literature/search-status` - 检查文献搜索功能状态

### 网页画像分析
- `POST /api/v1/profile-analysis/analyze-url` - 分析URL生成画像
- `POST /api/v1/profile-analysis/analyze-text` - 分析文本生成画像

## 🎯 当前开发重点

### 🔥 优先任务：文献推荐功能完善

当前系统的文献推荐功能是下一个开发重点，需要解决以下问题：

#### 1. 文献数据源集成
- **PubMed API**: 生物医学文献检索
- **Semantic Scholar API**: 学术论文语义搜索
- **bioRxiv API**: 预印本论文获取

#### 2. 智能推荐算法
- 基于用户需求的相关性评分
- 文献质量评估（影响因子、引用数）
- 个性化推荐策略

#### 3. 用户体验优化
- 文献搜索开关组件
- 实时状态检查和反馈
- 配置建议和故障排除

### 📋 实施计划

1. **完善后端文献服务** (1-2天)
   - 优化文献搜索算法
   - 实现缓存机制
   - 添加错误处理和降级策略

2. **前端集成优化** (1天)
   - 改进文献展示组件
   - 添加加载状态和错误提示
   - 实现文献引用格式化

3. **测试和调优** (1天)
   - API 集成测试
   - 用户体验测试
   - 性能优化

## 🧪 测试

### 后端测试

```bash
cd cellforge-backend

# 运行基础测试
python testpy/simple_test.py

# 测试用户创建
python testpy/create_test_user.py

# 测试关键词搜索
python testpy/test_keyword_search.py

# 测试安全功能
python testpy/test_security.py
```

### 前端测试

```bash
cd cellforge-ai-system

# 运行测试
npm test

# 运行端到端测试
npm run test:e2e

# 类型检查
npm run type-check
```

## 🔧 开发工具

### 推荐的开发环境
- **IDE**: VSCode / Cursor / WebStorm
- **Python**: PyCharm / VSCode Python 扩展
- **数据库**: DBeaver / pgAdmin
- **API测试**: Postman / Insomnia
- **Git**: GitKraken / SourceTree

### 有用的 VSCode 扩展
- Python
- TypeScript and JavaScript Language Features
- Tailwind CSS IntelliSense
- GitLens
- Docker
- Thunder Client (API 测试)

## 📝 Git 工作流程

### 分支策略
```bash
# 主分支
main                    # 生产环境代码
develop                 # 开发环境代码

# 功能分支
feature/literature-integration    # 文献集成功能
feature/customer-profiling       # 客户画像功能
feature/solution-generation      # 方案生成功能

# 修复分支
hotfix/critical-bug-fix         # 紧急修复
```

### 提交规范
```bash
# 功能开发
git commit -m "feat: 添加文献搜索API端点"

# 问题修复
git commit -m "fix: 修复用户认证token过期问题"

# 文档更新
git commit -m "docs: 更新API文档和使用说明"

# 代码重构
git commit -m "refactor: 重构客户画像服务代码结构"

# 性能优化
git commit -m "perf: 优化数据库查询性能"

# 测试相关
git commit -m "test: 添加文献搜索功能单元测试"
```

### 常用 Git 命令
```bash
# 克隆项目
git clone https://gitlab.local.azenta.com/ngs_bi_data/cellforge-ai.git

# 创建并切换到新分支
git checkout -b feature/new-feature

# 查看状态
git status

# 添加文件到暂存区
git add .

# 提交更改
git commit -m "feat: 添加新功能"

# 推送到远程分支
git push origin feature/new-feature

# 合并分支
git checkout develop
git merge feature/new-feature

# 拉取最新代码
git pull origin develop

# 查看提交历史
git log --oneline --graph
```

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 后端启动失败
```bash
# 检查Python版本
python --version  # 需要 3.11+

# 检查依赖安装
pip list | grep fastapi

# 重新安装依赖
pip install -r requirements.txt --force-reinstall

# 检查端口占用
netstat -an | grep 8000
```

#### 2. 前端启动失败
```bash
# 检查Node.js版本
node --version  # 需要 18+

# 清理缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install

# 检查端口占用
netstat -an | grep 3000
```

#### 3. 数据库连接问题
```bash
# 检查SQLite文件权限
ls -la cellforge-backend/cellforge.db

# 重新初始化数据库
cd cellforge-backend
python testpy/init_simple_db.py

# 检查数据库内容
sqlite3 cellforge.db ".tables"
```

#### 4. AI服务不可用
```bash
# 检查环境变量
echo $OPENAI_API_KEY

# 测试API连接
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.deepseek.com/v1/models

# 使用模拟AI服务
# 在后端代码中设置 USE_MOCK_AI=True
```

#### 5. 文献搜索功能异常
```bash
# 检查文献搜索状态
curl http://localhost:8000/api/v1/literature/search-status

# 检查API密钥配置
echo $PUBMED_API_KEY
echo $SEMANTIC_SCHOLAR_API_KEY

# 禁用文献搜索功能
export LITERATURE_SEARCH_ENABLED=false
```

### 日志查看
```bash
# 后端日志
cd cellforge-backend
tail -f logs/app.log

# Docker日志
docker-compose logs -f cellforge-backend

# 前端日志
cd cellforge-ai-system
npm run dev  # 开发模式下直接在终端显示
```

## 📚 相关文档

### 项目文档
- [集成开发计划](prompt/cellforge_integration_plan.md)
- [文献集成实施方案](prompt/LITERATURE_INTEGRATION_IMPLEMENTATION.md)
- [客户画像系统设计](prompt/CUSTOMER_PROFILE_MASTER_PLAN.md)
- [剩余开发任务](prompt/remaining_tasks.md)

### 技术文档
- [FastAPI 官方文档](https://fastapi.tiangolo.com/)
- [Next.js 官方文档](https://nextjs.org/docs)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [SQLAlchemy 文档](https://docs.sqlalchemy.org/)

### API 参考
- [DeepSeek API 文档](https://platform.deepseek.com/api-docs/)
- [PubMed API 文档](https://www.ncbi.nlm.nih.gov/books/NBK25501/)
- [Semantic Scholar API](https://api.semanticscholar.org/)

## 🤝 贡献指南

### 开发流程
1. **Fork 项目** 或创建新分支
2. **本地开发** 并测试功能
3. **代码审查** 确保代码质量
4. **提交 PR** 并描述更改内容
5. **合并代码** 到主分支

### 代码规范
- **Python**: 遵循 PEP 8 规范
- **TypeScript**: 使用 ESLint 和 Prettier
- **提交信息**: 使用约定式提交格式
- **文档**: 及时更新相关文档

### 测试要求
- 新功能必须包含单元测试
- API 更改需要更新文档
- 重要功能需要集成测试
- 性能敏感代码需要性能测试

---

**最后更新**: 2024年1月
**文档版本**: v0.1
**项目状态**: 积极开发中
