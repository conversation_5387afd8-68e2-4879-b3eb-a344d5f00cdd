"use client"

import { FormattedMessage } from "@/components/formatted-message"

const sampleAIResponse = "🎯 **CellForge AI** 专业方案建议\n\n" +
  "感谢您提供详细的需求信息！基于您的研究目标和实际条件，我为您制定了以下**个性化技术方案**：\n\n" +
  "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n" +
  "📊 需求分析总结\n\n" +
  "🎯 研究目标：细胞类型鉴定\n" +
  "🧪 样本类型：PBMC (外周血单核细胞)\n" +
  "🔢 细胞数量：5,000-10,000\n" +
  "💰 预算范围：5-10万\n" +
  "⏰ 项目周期：2-3个月\n\n" +
  "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n" +
  "🔬 推荐技术方案\n\n" +
  "🌟 推荐平台：**10x Genomics Chromium**\n\n" +
  "**选择理由：**\n" +
  "✅ 行业金标准，技术成熟稳定\n" +
  "✅ 高通量、高质量数据产出\n" +
  "✅ 完善的分析流程和工具支持\n\n" +
  "**技术优势：**\n" +
  "• 细胞通量：`500-10,000 cells/sample`\n" +
  "• 基因覆盖：*全转录组检测*\n" +
  "• 数据质量：低双联率(**<5%**)，高基因检测数\n\n" +
  "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n" +
  "💰 成本分析与优化\n\n" +
  "🎯 标准方案\n\n" +
  "总预算：8-12万元\n\n" +
  "费用明细：\n" +
  "• 样本制备：¥8,000-12,000（专业细胞分选）\n" +
  "• 文库构建：¥25,000-35,000（10x Genomics标准流程）\n" +
  "• 测序服务：¥20,000-30,000（高深度测序）\n" +
  "• 数据分析：¥15,000-25,000（深度分析+可视化）\n\n" +
  "包含服务：\n" +
  "✅ 完整的实验设计咨询\n" +
  "✅ 专业的数据分析报告\n" +
  "✅ 3个月技术支持\n\n" +
  "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n" +
  "🎯 下一步行动建议\n\n" +
  "1. 📋 确认方案细节：请确认技术平台和预算方案\n" +
  "2. 🧪 样本准备指导：我们提供详细的样本制备指南\n" +
  "3. 📅 时间安排协调：确定项目启动时间和关键节点\n" +
  "4. 🤝 签署服务协议：明确服务内容和质量标准\n\n" +
  "💡 温馨提示：\n" +
  "我们建议在正式启动前进行一次技术交流会，确保方案完全符合您的研究需求。\n\n" +
  "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n" +
  "CellForge AI - 您的单细胞测序专业伙伴 🧬✨"

export default function TestFormattingPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">AI Response Formatting Test</h1>

        <div className="bg-white border border-slate-200 rounded-2xl p-6 shadow-sm">
          <FormattedMessage content={sampleAIResponse} />
        </div>
      </div>
    </div>
  )
}
