"""
网页画像分析API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, HttpUrl, Field

from app.core.database import get_db
from app.core.auth import get_current_active_user
from app.models.user import User
from app.services.web_profile_analyzer import WebProfileAnalyzer

router = APIRouter()
analyzer = WebProfileAnalyzer()


class URLAnalysisRequest(BaseModel):
    """URL分析请求"""
    url: HttpUrl = Field(..., description="要分析的网页URL")
    analysis_type: str = Field("comprehensive", description="分析类型：basic/comprehensive/focused")
    focus_areas: Optional[List[str]] = Field(None, description="重点关注领域")


class TextAnalysisRequest(BaseModel):
    """文本分析请求"""
    text_content: str = Field(..., min_length=10, description="要分析的文本内容")
    source_description: Optional[str] = Field(None, description="文本来源描述")
    analysis_type: str = Field("comprehensive", description="分析类型")


class MultiSourceAnalysisRequest(BaseModel):
    """多源分析请求"""
    sources: List[Dict[str, str]] = Field(..., description="数据源列表，每个包含type和content")
    merge_strategy: str = Field("comprehensive", description="合并策略")


class ProfileAnalysisResponse(BaseModel):
    """画像分析响应"""
    analysis_timestamp: str
    source_url: Optional[str] = None
    confidence_score: float
    basic_info: Dict[str, Any]
    research_profile: Dict[str, Any]
    technical_profile: Dict[str, Any]
    business_profile: Dict[str, Any]
    communication_strategy: Dict[str, Any]
    risk_assessment: Dict[str, Any]
    action_recommendations: List[Dict[str, str]]


@router.post("/analyze-url", response_model=ProfileAnalysisResponse)
async def analyze_url_profile(
    request: URLAnalysisRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    分析URL并生成客户画像
    """
    try:
        # 权限检查：只有销售和管理员可以使用
        if current_user.role not in ["super_admin", "sales", "operations"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限使用画像分析功能"
            )

        # 执行URL分析（支持增强搜索）
        enable_keyword_search = request.analysis_type == "enhanced"
        analysis_result = await analyzer.analyze_url(str(request.url), enable_keyword_search)

        if "error" in analysis_result:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"URL分析失败: {analysis_result['error']}"
            )

        # 提取profile_data中的数据以匹配响应模型
        profile_data = analysis_result.get("profile_data", {})
        response_data = {
            "analysis_timestamp": profile_data.get("analysis_timestamp", ""),
            "source_url": profile_data.get("source_url"),
            "confidence_score": analysis_result.get("confidence_score", 0.0),
            "basic_info": profile_data.get("basic_info", {}),
            "research_profile": profile_data.get("research_profile", {}),
            "technical_profile": profile_data.get("technical_profile", {}),
            "business_profile": profile_data.get("business_profile", {}),
            "communication_strategy": profile_data.get("communication_strategy", {}),
            "risk_assessment": profile_data.get("risk_assessment", {}),
            "action_recommendations": profile_data.get("action_recommendations", [])
        }

        return ProfileAnalysisResponse(**response_data)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析过程中发生错误: {str(e)}"
        )


@router.post("/analyze-text")
async def analyze_text_profile(
    request: TextAnalysisRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    分析文本内容并生成客户画像
    """
    try:
        # 权限检查
        if current_user.role not in ["super_admin", "sales", "operations"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限使用画像分析功能"
            )

        # 构建分析源
        sources = [{
            "type": "text",
            "content": request.text_content
        }]

        # 执行文本分析
        analysis_result = await analyzer.analyze_multiple_sources(sources)

        if "error" in analysis_result:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"文本分析失败: {analysis_result['error']}"
            )

        return analysis_result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析过程中发生错误: {str(e)}"
        )


@router.post("/analyze-multiple-sources")
async def analyze_multiple_sources(
    request: MultiSourceAnalysisRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    分析多个数据源并生成综合画像
    """
    try:
        # 权限检查
        if current_user.role not in ["super_admin", "sales", "operations"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限使用画像分析功能"
            )

        # 验证数据源格式
        for source in request.sources:
            if "type" not in source or "content" not in source:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="数据源格式错误，必须包含type和content字段"
                )

            if source["type"] not in ["url", "text"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="不支持的数据源类型，只支持url和text"
                )

        # 执行多源分析
        analysis_result = await analyzer.analyze_multiple_sources(request.sources)

        if "error" in analysis_result:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"多源分析失败: {analysis_result['error']}"
            )

        return analysis_result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析过程中发生错误: {str(e)}"
        )


@router.get("/analysis-templates")
async def get_analysis_templates(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取分析模板和示例
    """
    try:
        # 权限检查
        if current_user.role not in ["super_admin", "sales", "operations"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问分析模板"
            )

        templates = {
            "url_examples": [
                {
                    "type": "PI个人页面",
                    "description": "大学教授或研究员的个人主页",
                    "example": "https://university.edu/faculty/professor-name",
                    "expected_info": ["职位", "研究兴趣", "发表论文", "联系方式"]
                },
                {
                    "type": "实验室页面",
                    "description": "研究实验室的官方页面",
                    "example": "https://university.edu/labs/single-cell-lab",
                    "expected_info": ["研究方向", "团队成员", "设备资源", "合作项目"]
                },
                {
                    "type": "学术档案",
                    "description": "Google Scholar、ResearchGate等学术平台",
                    "example": "https://scholar.google.com/citations?user=xxx",
                    "expected_info": ["发表记录", "引用情况", "合作网络", "研究领域"]
                }
            ],
            "text_templates": [
                {
                    "type": "个人简介",
                    "template": "姓名：[姓名]\n职位：[职位]\n机构：[机构]\n研究方向：[研究方向]\n主要成果：[主要成果]\n联系方式：[联系方式]",
                    "description": "标准的个人学术简介格式"
                },
                {
                    "type": "项目描述",
                    "template": "项目名称：[项目名称]\n负责人：[负责人]\n参与机构：[参与机构]\n研究内容：[研究内容]\n技术路线：[技术路线]\n预期成果：[预期成果]",
                    "description": "研究项目的详细描述"
                }
            ],
            "analysis_focus_areas": [
                "单细胞基因组学",
                "计算生物学",
                "癌症研究",
                "免疫学",
                "神经科学",
                "发育生物学",
                "系统生物学",
                "生物信息学"
            ],
            "supported_platforms": [
                "大学官网",
                "Google Scholar",
                "ResearchGate",
                "ORCID",
                "LinkedIn",
                "PubMed",
                "机构研究页面"
            ]
        }

        return templates

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取模板失败: {str(e)}"
        )


class QuickAnalysisRequest(BaseModel):
    """快速分析请求"""
    url_or_text: str = Field(..., description="URL或文本内容")
    analysis_type: str = Field("auto", description="分析类型：auto/url/text")


@router.post("/quick-analysis")
async def quick_profile_analysis(
    request: QuickAnalysisRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    快速画像分析（自动识别URL或文本）
    """
    try:
        # 权限检查
        if current_user.role not in ["super_admin", "sales", "operations"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限使用画像分析功能"
            )

        # 自动识别输入类型
        if request.analysis_type == "auto":
            if request.url_or_text.startswith(("http://", "https://")):
                input_type = "url"
            else:
                input_type = "text"
        else:
            input_type = request.analysis_type

        # 执行相应的分析
        if input_type == "url":
            analysis_result = await analyzer.analyze_url(request.url_or_text)
        else:
            sources = [{"type": "text", "content": request.url_or_text}]
            analysis_result = await analyzer.analyze_multiple_sources(sources)

        if "error" in analysis_result:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"快速分析失败: {analysis_result['error']}"
            )

        # 简化响应，只返回关键信息
        simplified_result = {
            "input_type": input_type,
            "confidence_score": analysis_result.get("confidence_score", 0),
            "key_insights": {
                "research_maturity": analysis_result.get("research_profile", {}).get("maturity_level", "unknown"),
                "single_cell_relevance": analysis_result.get("research_profile", {}).get("single_cell_relevance", {}),
                "potential_value": analysis_result.get("business_profile", {}).get("potential_value", "unknown"),
                "recommended_actions": analysis_result.get("action_recommendations", [])[:3]
            },
            "contact_info": analysis_result.get("basic_info", {}).get("contact_info", {}),
            "institutions": analysis_result.get("basic_info", {}).get("institutions", []),
            "research_areas": analysis_result.get("research_profile", {}).get("research_areas", {})
        }

        return simplified_result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"快速分析过程中发生错误: {str(e)}"
        )


@router.get("/analysis-history")
async def get_analysis_history(
    limit: int = 20,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取分析历史记录
    """
    try:
        # 权限检查
        if current_user.role not in ["super_admin", "sales", "operations"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限查看分析历史"
            )

        # 这里应该从数据库获取历史记录
        # 暂时返回模拟数据
        history = [
            {
                "id": 1,
                "timestamp": "2024-01-15T10:30:00Z",
                "source_type": "url",
                "source": "https://university.edu/faculty/john-doe",
                "confidence_score": 0.85,
                "key_findings": ["Professor", "Single Cell Expert", "High Budget"],
                "analyst": current_user.username
            },
            {
                "id": 2,
                "timestamp": "2024-01-15T09:15:00Z",
                "source_type": "text",
                "source": "Manual input about research lab",
                "confidence_score": 0.72,
                "key_findings": ["Research Lab", "Intermediate Level", "Medium Budget"],
                "analyst": current_user.username
            }
        ]

        return {
            "total": len(history),
            "limit": limit,
            "history": history[:limit]
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取历史记录失败: {str(e)}"
        )
