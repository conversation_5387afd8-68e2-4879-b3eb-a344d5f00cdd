import type { ReactNode } from "react"
import { cn } from "@/lib/utils"

interface DashboardLayoutProps {
  children: ReactNode
  sidebar?: ReactNode
  className?: string
}

export function DashboardLayout({ children, sidebar, className }: DashboardLayoutProps) {
  return (
    <div className={cn("flex flex-col lg:flex-row min-h-[calc(100vh-64px)]", className)}>
      {sidebar && <div className="w-full lg:w-80 border-r border-slate-200">{sidebar}</div>}
      <div className="flex-1">{children}</div>
    </div>
  )
}
