"""
Rate limiting utilities for keyword generation service
"""
import time
import asyncio
import logging
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>
from collections import defaultdict
from datetime import datetime, timedelta

from app.config.keyword_config import get_keyword_config
from app.exceptions.keyword_exceptions import RateLimitError
from app.utils.cache_manager import get_cache_manager


logger = logging.getLogger(__name__)


class InMemoryRateLimiter:
    """In-memory rate limiter with sliding window"""
    
    def __init__(self):
        self.config = get_keyword_config()
        self.requests: Dict[str, list] = defaultdict(list)
        self.locks: Dict[str, asyncio.Lock] = defaultdict(asyncio.Lock)
    
    async def check_rate_limit(self, identifier: str) -> Tuple[bool, Dict[str, int]]:
        """
        Check if request is within rate limits
        
        Args:
            identifier: User/IP identifier
            
        Returns:
            Tuple of (allowed, rate_limit_info)
            
        Raises:
            RateLimitError: If rate limit exceeded
        """
        if not self.config.ENABLE_RATE_LIMITING:
            return True, {"remaining": float('inf'), "reset_time": 0}
        
        async with self.locks[identifier]:
            current_time = time.time()
            window_start = current_time - self.config.RATE_LIMIT_WINDOW
            
            # Clean old requests
            self.requests[identifier] = [
                req_time for req_time in self.requests[identifier]
                if req_time >= window_start
            ]
            
            # Check current count
            current_requests = len(self.requests[identifier])
            
            if current_requests >= self.config.RATE_LIMIT_REQUESTS:
                # Calculate retry after time
                oldest_request = min(self.requests[identifier])
                retry_after = int(oldest_request + self.config.RATE_LIMIT_WINDOW - current_time)
                
                raise RateLimitError(
                    f"Rate limit exceeded. Try again in {retry_after} seconds.",
                    service="keyword_generation",
                    limit=self.config.RATE_LIMIT_REQUESTS,
                    window=self.config.RATE_LIMIT_WINDOW,
                    retry_after=retry_after
                )
            
            # Add current request
            self.requests[identifier].append(current_time)
            
            # Calculate rate limit info
            remaining = self.config.RATE_LIMIT_REQUESTS - (current_requests + 1)
            reset_time = int(window_start + self.config.RATE_LIMIT_WINDOW)
            
            return True, {
                "remaining": remaining,
                "reset_time": reset_time,
                "window": self.config.RATE_LIMIT_WINDOW
            }
    
    def get_stats(self) -> Dict[str, int]:
        """Get rate limiter statistics"""
        current_time = time.time()
        window_start = current_time - self.config.RATE_LIMIT_WINDOW
        
        active_users = 0
        total_requests = 0
        
        for identifier, requests in self.requests.items():
            # Clean old requests
            active_requests = [
                req_time for req_time in requests
                if req_time >= window_start
            ]
            
            if active_requests:
                active_users += 1
                total_requests += len(active_requests)
        
        return {
            "active_users": active_users,
            "total_requests_in_window": total_requests,
            "total_tracked_users": len(self.requests)
        }


class RedisRateLimiter:
    """Redis-based distributed rate limiter"""
    
    def __init__(self):
        self.config = get_keyword_config()
        self.cache_manager = None
    
    async def initialize(self):
        """Initialize Redis connection"""
        self.cache_manager = await get_cache_manager()
    
    async def check_rate_limit(self, identifier: str) -> Tuple[bool, Dict[str, int]]:
        """
        Check if request is within rate limits using Redis
        
        Args:
            identifier: User/IP identifier
            
        Returns:
            Tuple of (allowed, rate_limit_info)
            
        Raises:
            RateLimitError: If rate limit exceeded
        """
        if not self.config.ENABLE_RATE_LIMITING:
            return True, {"remaining": float('inf'), "reset_time": 0}
        
        if not self.cache_manager or not self.cache_manager.is_available():
            # Fallback to in-memory limiter
            fallback_limiter = InMemoryRateLimiter()
            return await fallback_limiter.check_rate_limit(identifier)
        
        try:
            current_time = int(time.time())
            window_start = current_time - self.config.RATE_LIMIT_WINDOW
            
            # Use Redis sorted set for sliding window
            key = f"rate_limit:{identifier}"
            
            # Use Lua script for atomic operations
            lua_script = """
            local key = KEYS[1]
            local window_start = ARGV[1]
            local current_time = ARGV[2]
            local limit = ARGV[3]
            local window = ARGV[4]
            
            -- Remove old entries
            redis.call('ZREMRANGEBYSCORE', key, '-inf', window_start)
            
            -- Get current count
            local current_count = redis.call('ZCARD', key)
            
            if current_count >= tonumber(limit) then
                -- Get oldest entry for retry calculation
                local oldest = redis.call('ZRANGE', key, 0, 0, 'WITHSCORES')
                local retry_after = 0
                if oldest[2] then
                    retry_after = math.ceil(tonumber(oldest[2]) + tonumber(window) - tonumber(current_time))
                end
                return {false, current_count, retry_after}
            else
                -- Add current request
                redis.call('ZADD', key, current_time, current_time)
                redis.call('EXPIRE', key, window)
                return {true, current_count + 1, 0}
            end
            """
            
            # Execute Lua script
            result = await self.cache_manager.redis_client.eval(
                lua_script,
                1,
                key,
                str(window_start),
                str(current_time),
                str(self.config.RATE_LIMIT_REQUESTS),
                str(self.config.RATE_LIMIT_WINDOW)
            )
            
            allowed, current_count, retry_after = result
            
            if not allowed:
                raise RateLimitError(
                    f"Rate limit exceeded. Try again in {retry_after} seconds.",
                    service="keyword_generation",
                    limit=self.config.RATE_LIMIT_REQUESTS,
                    window=self.config.RATE_LIMIT_WINDOW,
                    retry_after=retry_after
                )
            
            remaining = self.config.RATE_LIMIT_REQUESTS - current_count
            reset_time = current_time + self.config.RATE_LIMIT_WINDOW
            
            return True, {
                "remaining": remaining,
                "reset_time": reset_time,
                "window": self.config.RATE_LIMIT_WINDOW
            }
            
        except Exception as e:
            logger.error(f"Redis rate limiter error: {e}")
            # Fallback to in-memory limiter
            fallback_limiter = InMemoryRateLimiter()
            return await fallback_limiter.check_rate_limit(identifier)
    
    async def get_stats(self) -> Dict[str, int]:
        """Get rate limiter statistics from Redis"""
        if not self.cache_manager or not self.cache_manager.is_available():
            return {"error": "Redis not available"}
        
        try:
            # Get all rate limit keys
            pattern = "rate_limit:*"
            active_users = 0
            total_requests = 0
            
            async for key in self.cache_manager.redis_client.scan_iter(match=pattern):
                count = await self.cache_manager.redis_client.zcard(key)
                if count > 0:
                    active_users += 1
                    total_requests += count
            
            return {
                "active_users": active_users,
                "total_requests_in_window": total_requests
            }
            
        except Exception as e:
            logger.error(f"Failed to get rate limiter stats: {e}")
            return {"error": str(e)}


class RateLimitDecorator:
    """Decorator for applying rate limiting to functions"""
    
    def __init__(self, limiter: Optional[RedisRateLimiter] = None):
        self.limiter = limiter or RedisRateLimiter()
    
    def __call__(self, func):
        """Apply rate limiting to function"""
        async def wrapper(*args, **kwargs):
            # Extract identifier (could be from context, user ID, etc.)
            identifier = kwargs.get('user_id', 'anonymous')
            
            # Check rate limit
            await self.limiter.check_rate_limit(identifier)
            
            # Execute function
            return await func(*args, **kwargs)
        
        return wrapper


# Global rate limiter instances
memory_rate_limiter = InMemoryRateLimiter()
redis_rate_limiter = RedisRateLimiter()


async def get_rate_limiter() -> RedisRateLimiter:
    """Get rate limiter instance"""
    if not redis_rate_limiter.cache_manager:
        await redis_rate_limiter.initialize()
    return redis_rate_limiter


def rate_limit(identifier_key: str = 'user_id'):
    """
    Decorator for rate limiting endpoints
    
    Args:
        identifier_key: Key to extract identifier from kwargs
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            limiter = await get_rate_limiter()
            identifier = kwargs.get(identifier_key, 'anonymous')
            
            # Check rate limit
            allowed, info = await limiter.check_rate_limit(identifier)
            
            # Add rate limit info to response (if it's a FastAPI endpoint)
            result = await func(*args, **kwargs)
            
            # If result is a dict, add rate limit headers
            if isinstance(result, dict):
                result['_rate_limit'] = info
            
            return result
        
        return wrapper
    return decorator