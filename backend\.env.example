# CellForge AI 后端环境配置示例
# 复制此文件为 .env 并填入实际配置值

# 基础配置
PROJECT_NAME="CellForge AI"
VERSION="1.0.0"
DEBUG=true

# 数据库配置
DATABASE_URL="sqlite:///./cellforge.db"

# JWT配置
SECRET_KEY="your-secret-key-here"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI服务配置
DEEPSEEK_API_KEY="your-deepseek-api-key"
DEEPSEEK_BASE_URL="https://api.deepseek.com"
USE_REAL_AI=true

# 文献检索配置
LITERATURE_SEARCH_ENABLED=false

# 外部API配置 - 文献数据源
# PubMed API (可选，提高搜索质量)
PUBMED_API_KEY=""

# Semantic Scholar API (可选，提供引用数据)
SEMANTIC_SCHOLAR_API_KEY=""

# bioRxiv API (默认启用，无需密钥)
BIORXIV_API_ENABLED=true

# API限流配置
PUBMED_RATE_LIMIT=3
SEMANTIC_SCHOLAR_RATE_LIMIT=10
BIORXIV_RATE_LIMIT=10

# 文献搜集配置
LITERATURE_COLLECTION_MAX_PAPERS=50
LITERATURE_RELEVANCE_THRESHOLD=0.7
LITERATURE_CACHE_TTL=3600

# 单细胞分析配置
SCANPY_SETTINGS_VERBOSITY=1
SCANPY_SETTINGS_N_JOBS=-1

# 日志配置
LOG_LEVEL="INFO"

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# ================================
# 文献搜索功能配置说明
# ================================

# 1. 启用文献搜索功能
#    设置 LITERATURE_SEARCH_ENABLED=true

# 2. 配置外部API密钥（可选）
#    - PUBMED_API_KEY: 提高PubMed搜索限制和质量
#    - SEMANTIC_SCHOLAR_API_KEY: 获取更详细的引用和影响因子数据

# 3. 获取API密钥的方法：
#    - PubMed: 访问 https://www.ncbi.nlm.nih.gov/account/
#    - Semantic Scholar: 访问 https://www.semanticscholar.org/product/api

# 4. 即使不配置API密钥，系统也可以使用：
#    - bioRxiv公开API（无需密钥）
#    - 基础的PubMed搜索（有限制）

# 5. 推荐配置：
#    - 开发环境：只启用bioRxiv即可测试
#    - 生产环境：建议配置所有API密钥以获得最佳体验
