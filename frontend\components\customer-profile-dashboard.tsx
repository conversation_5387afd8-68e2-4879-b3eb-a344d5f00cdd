"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { 
  User, 
  Brain, 
  TrendingUp, 
  Target, 
  BookOpen, 
  MessageSquare,
  BarChart3,
  Lightbulb,
  Star,
  Clock
} from "lucide-react"
import { useAuth } from "@/contexts/auth-context"

interface CustomerProfile {
  id: number
  user_id: number
  confidence_score: number
  completeness_score: number
  research_maturity: string
  technical_expertise: any
  budget_range: string
  engagement_level: string
  analysis_complexity: string
  conversion_probability: number
  lifetime_value_prediction: number
  churn_risk_score: number
  last_updated: string
}

interface ProfileInsight {
  insight_type: string
  title: string
  description: string
  confidence: number
  actionable_recommendations: string[]
}

interface PersonalizedRecommendations {
  content_recommendations: string[]
  service_recommendations: string[]
  learning_path: string[]
  next_best_actions: string[]
}

export function CustomerProfileDashboard() {
  const { user } = useAuth()
  const [profile, setProfile] = useState<CustomerProfile | null>(null)
  const [insights, setInsights] = useState<ProfileInsight[]>([])
  const [recommendations, setRecommendations] = useState<PersonalizedRecommendations | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user?.id) {
      loadProfileData()
    }
  }, [user?.id])

  const loadProfileData = async () => {
    try {
      setLoading(true)
      
      // 模拟API调用 - 实际实现中应该调用真实的API
      // const profileResponse = await fetch(`/api/v1/customer/profile/${user.id}`)
      // const profile = await profileResponse.json()
      
      // 模拟数据
      const mockProfile: CustomerProfile = {
        id: 1,
        user_id: user?.id || 1,
        confidence_score: 0.78,
        completeness_score: 0.85,
        research_maturity: "中级",
        technical_expertise: {
          platforms: ["10x Genomics", "Smart-seq"],
          analysis_tools: ["Seurat", "Scanpy"],
          programming_skills: ["R", "Python"]
        },
        budget_range: "20-50万",
        engagement_level: "高",
        analysis_complexity: "中级",
        conversion_probability: 0.72,
        lifetime_value_prediction: 450000,
        churn_risk_score: 0.15,
        last_updated: "2024-01-15T10:30:00Z"
      }

      const mockInsights: ProfileInsight[] = [
        {
          insight_type: "engagement",
          title: "高参与度用户",
          description: "该用户表现出很高的平台参与度，经常进行深度对话",
          confidence: 0.8,
          actionable_recommendations: [
            "提供高级技术内容",
            "邀请参与专家讨论",
            "推荐定制化解决方案"
          ]
        },
        {
          insight_type: "growth",
          title: "技术需求递增",
          description: "用户的技术需求复杂度呈上升趋势，显示出学习进步",
          confidence: 0.7,
          actionable_recommendations: [
            "推荐进阶课程",
            "提供技术咨询服务",
            "介绍高端解决方案"
          ]
        }
      ]

      const mockRecommendations: PersonalizedRecommendations = {
        content_recommendations: [
          "高级分析方法教程",
          "多组学整合分析指南",
          "自定义分析流程设计"
        ],
        service_recommendations: [
          "专属项目管理服务",
          "定制化分析方案",
          "一对一技术支持"
        ],
        learning_path: [
          "掌握高级聚类算法",
          "学习轨迹分析方法",
          "理解多组学数据整合",
          "应用机器学习方法"
        ],
        next_best_actions: [
          "邀请参与beta测试",
          "推荐专家咨询服务",
          "提供早期访问权限"
        ]
      }

      setProfile(mockProfile)
      setInsights(mockInsights)
      setRecommendations(mockRecommendations)
      
    } catch (error) {
      console.error('加载画像数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getMaturityColor = (maturity: string) => {
    switch (maturity) {
      case "初学者": return "bg-blue-100 text-blue-800"
      case "中级": return "bg-green-100 text-green-800"
      case "专家": return "bg-purple-100 text-purple-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getEngagementColor = (level: string) => {
    switch (level) {
      case "低": return "bg-red-100 text-red-800"
      case "中": return "bg-yellow-100 text-yellow-800"
      case "高": return "bg-green-100 text-green-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getRiskColor = (score: number) => {
    if (score < 0.3) return "text-green-600"
    if (score < 0.6) return "text-yellow-600"
    return "text-red-600"
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-500">加载画像数据中...</p>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="text-center py-8">
        <p className="text-slate-500">暂无画像数据</p>
        <Button onClick={loadProfileData} className="mt-4">
          重新加载
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 画像概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <User className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-slate-500">研究成熟度</p>
                <Badge className={getMaturityColor(profile.research_maturity)}>
                  {profile.research_maturity}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-slate-500">参与度</p>
                <Badge className={getEngagementColor(profile.engagement_level)}>
                  {profile.engagement_level}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-slate-500">转化概率</p>
                <p className="text-lg font-semibold">
                  {Math.round(profile.conversion_probability * 100)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-slate-500">预测价值</p>
                <p className="text-lg font-semibold">
                  ¥{(profile.lifetime_value_prediction / 10000).toFixed(1)}万
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细画像信息 */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">画像概览</TabsTrigger>
          <TabsTrigger value="insights">智能洞察</TabsTrigger>
          <TabsTrigger value="recommendations">个性化推荐</TabsTrigger>
          <TabsTrigger value="analytics">行为分析</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 画像完整度 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  画像完整度
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>整体完整度</span>
                    <span>{Math.round(profile.completeness_score * 100)}%</span>
                  </div>
                  <Progress value={profile.completeness_score * 100} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>数据置信度</span>
                    <span>{Math.round(profile.confidence_score * 100)}%</span>
                  </div>
                  <Progress value={profile.confidence_score * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>

            {/* 技术画像 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  技术画像
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm text-slate-500 mb-2">技术平台</p>
                  <div className="flex flex-wrap gap-1">
                    {profile.technical_expertise?.platforms?.map((platform: string, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {platform}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <p className="text-sm text-slate-500 mb-2">分析工具</p>
                  <div className="flex flex-wrap gap-1">
                    {profile.technical_expertise?.analysis_tools?.map((tool: string, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tool}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <p className="text-sm text-slate-500 mb-2">编程技能</p>
                  <div className="flex flex-wrap gap-1">
                    {profile.technical_expertise?.programming_skills?.map((skill: string, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 商业画像 */}
          <Card>
            <CardHeader>
              <CardTitle>商业画像</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm text-slate-500">预算范围</p>
                  <p className="text-lg font-medium">{profile.budget_range}</p>
                </div>
                <div>
                  <p className="text-sm text-slate-500">分析复杂度</p>
                  <p className="text-lg font-medium">{profile.analysis_complexity}</p>
                </div>
                <div>
                  <p className="text-sm text-slate-500">流失风险</p>
                  <p className={`text-lg font-medium ${getRiskColor(profile.churn_risk_score)}`}>
                    {Math.round(profile.churn_risk_score * 100)}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          {insights.map((insight, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-500" />
                  {insight.title}
                  <Badge variant="outline" className="ml-auto">
                    置信度: {Math.round(insight.confidence * 100)}%
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-600 mb-4">{insight.description}</p>
                <div>
                  <p className="text-sm font-medium text-slate-700 mb-2">建议行动:</p>
                  <ul className="list-disc list-inside space-y-1">
                    {insight.actionable_recommendations.map((rec, idx) => (
                      <li key={idx} className="text-sm text-slate-600">{rec}</li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          {recommendations && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    内容推荐
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {recommendations.content_recommendations.map((content, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-yellow-500" />
                        <span className="text-sm">{content}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    服务推荐
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {recommendations.service_recommendations.map((service, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-blue-500" />
                        <span className="text-sm">{service}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    学习路径
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {recommendations.learning_path.map((step, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 text-xs flex items-center justify-center font-medium">
                          {index + 1}
                        </div>
                        <span className="text-sm">{step}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    下一步行动
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {recommendations.next_best_actions.map((action, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        <span className="text-sm">{action}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                行为分析
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-slate-500 text-center py-8">
                行为分析功能开发中...
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
