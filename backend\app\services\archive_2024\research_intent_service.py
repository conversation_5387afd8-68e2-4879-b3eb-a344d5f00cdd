"""
研究意图关键词整合服务
基于COMPREHENSIVE_SOLUTION_FRAMEWORK_INTEGRATION.md的设计
智能分析用户研究意图，生成精准的搜索关键词和一键搜索链接
"""
from typing import Dict, List, Any, Optional
import urllib.parse
import logging
from datetime import datetime

from app.services.ai_service import AIService
from app.services.unified_literature_api_service import get_unified_literature_api_service

logger = logging.getLogger(__name__)


class ResearchIntentService:
    """研究意图关键词整合服务"""
    
    def __init__(self):
        self.ai_service = AIService()
        
        # 从配置文件加载关键词
        self.core_tech_keywords = {}
        self.domain_specific_keywords = {}
        
        # 加载配置
        self._load_configurations()
        
        # 搜索平台配置
        self.search_platforms = {
            "pubmed": {
                "base_url": "https://pubmed.ncbi.nlm.nih.gov/?term=",
                "query_format": self._format_query_for_platform,
                "name": "PubMed",
                "description": "生物医学文献数据库",
                "icon": "🏥"
            },
            "google_scholar": {
                "base_url": "https://scholar.google.com/scholar?q=",
                "query_format": self._format_query_for_platform,
                "name": "Google Scholar", 
                "description": "学术搜索引擎",
                "icon": "🎓"
            },
            "semantic_scholar": {
                "base_url": "https://www.semanticscholar.org/search?q=",
                "query_format": self._format_query_for_platform,
                "name": "Semantic Scholar",
                "description": "AI驱动的学术搜索",
                "icon": "🧠"
            },
            "biorxiv": {
                "base_url": "https://www.biorxiv.org/search/",
                "query_format": self._format_query_for_platform,
                "name": "bioRxiv",
                "description": "生物学预印本",
                "icon": "🧬"
            }
        }
    
    def _format_query_for_platform(self, query: str, platform: str = "general") -> str:
        """为不同平台格式化查询字符串"""
        # URL编码查询字符串
        import urllib.parse
        return urllib.parse.quote(query)
    
    def _load_configurations(self):
        """从配置文件加载关键词配置"""
        try:
            # 直接使用默认配置，简化配置管理
            self._load_default_configurations()
            logger.info(f"已加载默认配置: {len(self.domain_specific_keywords)} 个领域, {len(self.core_tech_keywords)} 个技术类别")
            
        except Exception as e:
            logger.warning(f"加载配置失败，使用默认值: {e}")
            self._load_default_configurations()
    
    def _load_default_configurations(self):
        """加载默认配置作为回退"""
        self.core_tech_keywords = {
            "platforms": [
                "10x Genomics", "Smart-seq", "Drop-seq", "inDrop", "BD Rhapsody", 
                "MARS-seq", "CEL-seq", "STRT-seq", "Fluidigm C1"
            ],
            "analysis_methods": [
                "scRNA-seq", "single cell RNA sequencing", "transcriptomics",
                "cell clustering", "trajectory analysis", "pseudotime",
                "differential expression", "cell type annotation"
            ],
            "bioinformatics_tools": [
                "Seurat", "Scanpy", "Cell Ranger", "Monocle", "SingleR",
                "CellTypist", "scType", "Azimuth", "Symphony", "SCENIC"
            ]
        }
        
        self.domain_specific_keywords = {
            "cancer": {
                "core": ["tumor microenvironment", "cancer", "oncology", "metastasis", "malignant"],
                "cellular": ["cancer cell", "tumor cell", "circulating tumor cell", "CAR-T"],
                "mechanisms": ["drug resistance", "immune escape", "EMT", "stemness"],
                "applications": ["immunotherapy", "precision medicine", "liquid biopsy"]
            },
            "immunology": {
                "core": ["immune", "immunology", "T cell", "B cell", "NK cell"],
                "cellular": ["PBMC", "immune cell", "lymphocyte", "macrophage", "dendritic cell"],
                "mechanisms": ["immune response", "activation", "exhaustion", "memory"],
                "applications": ["vaccine", "autoimmune", "transplantation", "infection"]
            },
            "neuroscience": {
                "core": ["brain", "neuron", "neural", "neuronal", "CNS"],
                "cellular": ["neuron", "astrocyte", "microglia", "oligodendrocyte"],
                "mechanisms": ["synaptic", "connectivity", "plasticity", "development"],
                "applications": ["Alzheimer", "Parkinson", "autism", "depression"]
            },
            "development": {
                "core": ["development", "developmental", "embryo", "organogenesis"],
                "cellular": ["stem cell", "progenitor", "differentiation", "lineage"],
                "mechanisms": ["cell fate", "trajectory", "reprogramming", "regeneration"],
                "applications": ["organoid", "tissue engineering", "regenerative medicine"]
            }
        }
    
    def reload_configurations(self):
        """重新加载配置文件"""
        logger.info("Reloading configurations...")
        self._load_configurations()
    
    async def generate_literature_search_with_ai_analysis(
        self,
        requirements: Dict[str, Any],
        user_message: str = ""
    ) -> Dict[str, Any]:
        """
        基于研究意图生成文献搜索和AI解析
        集成统一文献API服务进行搜索和结果分析
        """
        try:
            logger.info("🔍 开始基于研究意图的文献搜索和AI解析")
            
            # 1. 分析研究意图
            intent_analysis = await self.analyze_research_intent_and_generate_keywords(
                requirements, user_message
            )
            
            # 2. 使用统一文献API服务进行搜索
            unified_service = await get_unified_literature_api_service()
            literature_results = await unified_service.search_by_research_intent(
                research_intent=intent_analysis,
                max_results=20
            )
            
            # 3. 生成可点击的搜索链接
            search_links = self._generate_clickable_search_links(intent_analysis)
            
            # 4. 整合结果
            return {
                "research_intent_analysis": intent_analysis,
                "literature_search_results": literature_results,
                "clickable_search_links": search_links,
                "analysis_timestamp": datetime.now().isoformat(),
                "total_papers_found": literature_results.get("research_intent_based_results", {})
            }
            
        except Exception as e:
            logger.error(f"基于研究意图的文献搜索失败: {e}")
            return self._empty_literature_analysis_result()
    
    def _generate_clickable_search_links(self, intent_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成可点击的文献搜索链接"""
        try:
            search_queries = intent_analysis.get("dynamic_terminology", {}).get("comprehensive_search_queries", [])
            
            if not search_queries:
                return {"links": {}, "total_links": 0}
            
            clickable_links = {}
            
            for query_info in search_queries:
                query = query_info.get("english_query", "")
                query_type = query_info.get("query_type", "general")
                target_research = query_info.get("target_research", "")
                
                if not query:
                    continue
                
                # 为每个平台生成链接
                platform_links = {}
                for platform_key, platform_config in self.search_platforms.items():
                    try:
                        formatted_query = platform_config["query_format"](query, platform_key)
                        search_url = platform_config["base_url"] + formatted_query
                        
                        platform_links[platform_key] = {
                            "url": search_url,
                            "platform_name": platform_config["name"],
                            "platform_description": platform_config["description"],
                            "platform_icon": platform_config["icon"],
                            "query": query,
                            "search_focus": target_research
                        }
                    except Exception as e:
                        logger.warning(f"生成{platform_key}链接失败: {e}")
                
                if platform_links:
                    clickable_links[query_type] = {
                        "query_description": f"{target_research} - {query_type}",
                        "english_query": query,
                        "platform_links": platform_links
                    }
            
            return {
                "links": clickable_links,
                "total_links": len(clickable_links),
                "usage_instruction": "点击下方链接可直接在对应平台进行精确文献搜索"
            }
            
        except Exception as e:
            logger.error(f"生成可点击搜索链接失败: {e}")
            return {"links": {}, "total_links": 0}
    
    def _empty_literature_analysis_result(self) -> Dict[str, Any]:
        """返回空的文献分析结果"""
        return {
            "research_intent_analysis": {},
            "literature_search_results": {},
            "clickable_search_links": {"links": {}, "total_links": 0},
            "analysis_timestamp": datetime.now().isoformat(),
            "total_papers_found": 0
        }
    
    async def analyze_research_intent_and_generate_keywords(
        self,
        requirements: Dict[str, Any],
        user_message: str = ""
    ) -> Dict[str, Any]:
        """
        全面的研究意图分析和信息拆解
        基于客户填写的信息进行意图拆解，支持多种研究可能性
        为不同模块提供所需的信息格式（整体/单独）
        """
        try:
            logger.info("🔍 开始全面的研究意图分析和信息拆解")
            
            # 1. 全面分析用户研究意图，识别多种可能性
            comprehensive_intent_analysis = await self._analyze_comprehensive_research_intent(
                requirements, user_message
            )
            
            # 2. AI动态生成英文术语（无预定义映射）
            dynamic_terminology = await self._generate_dynamic_terminology(
                requirements, comprehensive_intent_analysis
            )
            
            # 3. 生成全面的文献搜索策略，覆盖多种研究方向
            comprehensive_literature_search = await self._generate_comprehensive_literature_search(
                comprehensive_intent_analysis, dynamic_terminology
            )
            
            # 4. 模块化信息拆解，为不同组件提供所需格式
            modular_information = self._create_modular_information_structure(
                requirements, comprehensive_intent_analysis, dynamic_terminology
            )
            
            # 简化版本，不再使用复杂的指标收集
            
            return {
                "comprehensive_intent_analysis": comprehensive_intent_analysis,
                "dynamic_terminology": dynamic_terminology,
                "comprehensive_literature_search": comprehensive_literature_search,
                "modular_information": modular_information,
                "generation_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "method": "comprehensive_intent_analysis",
                    "confidence_score": comprehensive_intent_analysis.get("overall_confidence", 0.85),
                    "research_possibilities_count": len(comprehensive_intent_analysis.get("research_possibilities", []))
                }
            }
            
        except Exception as e:
            logger.error(f"全面研究意图分析失败: {e}")
            return self._get_fallback_comprehensive_analysis(requirements)
    
    async def _analyze_comprehensive_research_intent(
        self,
        requirements: Dict[str, Any],
        user_message: str
    ) -> Dict[str, Any]:
        """全面分析用户研究意图，识别多种研究可能性"""
        
        if self.ai_service:
            try:
                # 构建分析提示词
                analysis_prompt = f"""
作为资深单细胞生物学专家，请全面分析用户的研究需求，识别多种可能的研究方向。

用户信息：
- 物种类型：{requirements.get('speciesType', '')}
- 样本类型：{requirements.get('sampleType', '')}  
- 研究目标：{requirements.get('researchGoal', '')}
- 实验类型：{requirements.get('experimentType', '')}
- 细胞数量：{requirements.get('cellCount', '')}
- 细胞活力：{requirements.get('cellViability', '')}
- 预算范围：{requirements.get('budget', '')}
- 项目周期：{requirements.get('timeline', '')}
- 用户描述：{user_message}

请提供全面的分析结果（JSON格式）：
{{
    "primary_research_domain": "主要研究领域",
    "research_possibilities": [
        {{
            "possibility_name": "研究可能性1",
            "probability": 0.8,
            "research_focus": "具体研究焦点",
            "key_questions": ["关键研究问题1", "关键研究问题2"],
            "expected_outcomes": ["预期结果1", "预期结果2"],
            "technical_challenges": ["技术挑战1", "技术挑战2"]
        }}
    ],
    "sample_specific_considerations": ["样本特异性考虑1", "样本特异性考虑2"],
    "species_specific_factors": ["物种特异性因素1", "物种特异性因素2"],
    "technical_requirements": {{
        "platform_recommendations": ["推荐平台1", "推荐平台2"],
        "quality_control_points": ["质控要点1", "质控要点2"],
        "data_analysis_approaches": ["分析方法1", "分析方法2"]
    }},
    "overall_confidence": 0.85,
    "complexity_assessment": "high/medium/low"
}}

要求：
1. 基于用户信息识别2-4个最可能的研究方向
2. 每个可能性都要有具体的概率评估
3. 考虑样本和物种的特异性
4. 提供技术实现建议
"""
                
                response = await self.ai_service.generate_response(
                    message=analysis_prompt,
                    context={"task": "comprehensive_intent_analysis"},
                    conversation_type="analysis"
                )
                
                # 尝试解析AI返回的JSON
                import json
                try:
                    comprehensive_analysis = json.loads(response.content.strip())
                    logger.info(f"AI完成全面研究意图分析，识别到 {len(comprehensive_analysis.get('research_possibilities', []))} 种研究可能性")
                    return comprehensive_analysis
                except json.JSONDecodeError:
                    logger.warning("AI返回格式不是有效JSON，使用规则分析")
                    
            except Exception as e:
                logger.error(f"AI全面分析失败: {e}")
        
        # AI失败时的规则分析（后备方案）
        return self._fallback_comprehensive_analysis(requirements, user_message)

    def _fallback_comprehensive_analysis(
        self,
        requirements: Dict[str, Any],
        user_message: str
    ) -> Dict[str, Any]:
        """规则-based全面分析后备方案"""
        
        research_goal = requirements.get('researchGoal', '').lower()
        sample_type = requirements.get('sampleType', '').lower()
        species_type = requirements.get('speciesType', '').lower()
        combined_text = (research_goal + " " + sample_type + " " + user_message).lower()
        
        # 识别多种研究可能性
        research_possibilities = []
        
        # 可能性1：基于研究目标的直接分析
        if "发育轨迹" in research_goal or "trajectory" in combined_text:
            research_possibilities.append({
                "possibility_name": "发育轨迹分析",
                "probability": 0.85,
                "research_focus": f"{species_type}的{sample_type}发育轨迹重建",
                "key_questions": [
                    f"{sample_type}如何沿着发育轨迹分化",
                    "关键转录因子在发育过程中的作用",
                    "发育分支点的分子机制"
                ],
                "expected_outcomes": ["发育轨迹图谱", "关键调控因子", "时序表达模式"],
                "technical_challenges": ["伪时间排序准确性", "分支点识别", "细胞状态转换"]
            })
        
        # 可能性2：基于样本类型的功能分析
        if "肿瘤" in sample_type or "tumor" in combined_text:
            research_possibilities.append({
                "possibility_name": "肿瘤微环境分析",
                "probability": 0.75,
                "research_focus": f"{species_type}肿瘤组织的细胞异质性分析",
                "key_questions": [
                    "肿瘤细胞与免疫细胞的相互作用",
                    "肿瘤微环境的免疫抑制机制",
                    "治疗敏感性相关的细胞亚群"
                ],
                "expected_outcomes": ["肿瘤细胞图谱", "免疫微环境特征", "潜在治疗靶点"],
                "technical_challenges": ["细胞类型注释", "细胞间通讯分析", "空间信息缺失"]
            })
        
        # 可能性3：基于物种的比较分析
        research_possibilities.append({
            "possibility_name": "细胞类型鉴定与功能注释",
            "probability": 0.90,
            "research_focus": f"{species_type}{sample_type}的细胞异质性研究",
            "key_questions": [
                f"{sample_type}中包含哪些细胞亚群",
                "各细胞亚群的功能特征是什么",
                "细胞亚群之间如何相互作用"
            ],
            "expected_outcomes": ["细胞类型图谱", "功能标志基因", "细胞通讯网络"],
            "technical_challenges": ["细胞类型注释准确性", "稀有细胞群体检测", "批次效应校正"]
        })
        
        # 如果提到了特定的生物学过程，添加相应的研究可能性
        if any(word in combined_text for word in ["免疫", "immune", "炎症", "inflammation"]):
            research_possibilities.append({
                "possibility_name": "免疫细胞功能分析",
                "probability": 0.70,
                "research_focus": f"{species_type}{sample_type}的免疫细胞功能状态",
                "key_questions": [
                    "免疫细胞的激活状态和功能",
                    "免疫反应的调控机制",
                    "免疫细胞亚群的特异性功能"
                ],
                "expected_outcomes": ["免疫细胞功能图谱", "激活标志物", "调控网络"],
                "technical_challenges": ["功能状态评估", "细胞激活程度量化", "动态变化捕获"]
            })
        
        return {
            "primary_research_domain": self._determine_primary_domain(combined_text),
            "research_possibilities": research_possibilities[:4],  # 最多4个可能性
            "sample_specific_considerations": [
                f"{sample_type}样本的特殊处理要求",
                f"{sample_type}细胞的存活条件维持",
                f"{sample_type}特异性标志基因的识别"
            ],
            "species_specific_factors": [
                f"{species_type}特异性参考基因组",
                f"{species_type}细胞类型注释数据库",
                f"{species_type}特有的生物学特征"
            ],
            "technical_requirements": {
                "platform_recommendations": ["10x Genomics", "Smart-seq3", "BD Rhapsody"],
                "quality_control_points": ["细胞活力检测", "RNA完整性评估", "测序深度控制"],
                "data_analysis_approaches": ["Seurat分析流程", "Scanpy工具包", "CellTypist注释"]
            },
            "overall_confidence": 0.80,
            "complexity_assessment": "medium"
        }

    def _determine_primary_domain(self, combined_text: str) -> str:
        """确定主要研究领域"""
        if any(word in combined_text for word in ["发育", "trajectory", "轨迹", "分化"]):
            return "developmental_biology"
        elif any(word in combined_text for word in ["肿瘤", "tumor", "cancer", "癌症"]):
            return "oncology"
        elif any(word in combined_text for word in ["免疫", "immune", "炎症"]):
            return "immunology"
        elif any(word in combined_text for word in ["神经", "neural", "brain", "neuron"]):
            return "neuroscience"
        else:
            return "general_biology"

    async def _generate_dynamic_terminology(
        self,
        requirements: Dict[str, Any],
        comprehensive_intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """AI动态生成英文术语，无预定义映射"""
        
        if self.ai_service:
            try:
                # 使用增强提示词管理器
                template_id = "keyword_generation"
                template_variables = {
                    "user_query": f"物种: {requirements.get('speciesType', '')} 样本: {requirements.get('sampleType', '')} 目标: {requirements.get('researchGoal', '')}",
                    "context": json.dumps(comprehensive_intent_analysis, ensure_ascii=False),
                    "max_keywords": "10"
                }
                
                terminology_prompt = self.enhanced_prompt_manager.render_prompt(
                    template_id,
                    template_variables
                )
                
                if not terminology_prompt:
                    # 回退到原始提示词
                    terminology_prompt = f"""
作为生物医学术语专家，请为以下单细胞研究需求动态生成准确的英文术语。

用户信息：
- 物种类型：{requirements.get('speciesType', '')}
- 样本类型：{requirements.get('sampleType', '')}
- 研究目标：{requirements.get('researchGoal', '')}
- 主要研究领域：{primary_domain}

研究可能性：
{chr(10).join([f"- {poss.get('possibility_name', '')}: {poss.get('research_focus', '')}" for poss in research_possibilities])}

请生成准确的英文术语（JSON格式）：
{{
    "species_terminology": {{
        "standard_name": "标准物种英文名称",
        "common_variants": ["常用变体1", "常用变体2"],
        "taxonomy_info": "分类学信息"
    }},
    "sample_terminology": {{
        "precise_term": "精确的样本类型英文术语",
        "alternative_terms": ["备选术语1", "备选术语2"],
        "context_specific_modifiers": ["上下文修饰词1", "上下文修饰词2"]
    }},
    "research_goal_terminology": {{
        "primary_term": "主要研究目标英文术语",
        "technical_terms": ["技术术语1", "技术术语2"],
        "methodology_terms": ["方法学术语1", "方法学术语2"]
    }},
    "comprehensive_search_queries": [
        {{
            "query_type": "broad_exploration",
            "english_query": "宽泛探索性查询",
            "target_research": "针对的研究类型"
        }},
        {{
            "query_type": "specific_focus",
            "english_query": "特定焦点查询", 
            "target_research": "针对的研究类型"
        }}
    ],
    "domain_specific_terms": {{
        "{primary_domain}": ["领域特异术语1", "领域特异术语2", "领域特异术语3"]
    }}
}}

要求：
1. 所有英文术语必须是标准生物医学术语
2. 为每种研究可能性生成对应的搜索查询
3. 术语要考虑样本和物种的特异性
4. 查询要涵盖不同的研究角度（宽泛、特定、方法学、比较）
"""
                
                response = await self.ai_service.generate_response(
                    message=terminology_prompt,
                    context={"task": "dynamic_terminology_generation"},
                    conversation_type="terminology"
                )
                
                # 尝试解析AI返回的JSON
                import json
                try:
                    dynamic_terminology = json.loads(response.content.strip())
                    logger.info(f"AI动态生成术语完成，包含 {len(dynamic_terminology.get('comprehensive_search_queries', []))} 个搜索查询")
                    return dynamic_terminology
                except json.JSONDecodeError:
                    logger.warning("AI术语生成返回格式无效，使用备用方案")
                    
            except Exception as e:
                logger.error(f"AI术语生成失败: {e}")
        
        # AI失败时使用规则-based术语生成
        return self._fallback_dynamic_terminology(requirements, comprehensive_intent_analysis)

    def _fallback_dynamic_terminology(
        self,
        requirements: Dict[str, Any],
        comprehensive_intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """备用的术语生成方案"""
        
        species_type = requirements.get('speciesType', '')
        sample_type = requirements.get('sampleType', '')
        research_goal = requirements.get('researchGoal', '')
        primary_domain = comprehensive_intent_analysis.get("primary_research_domain", "general_biology")
        
        # 基础术语生成逻辑
        species_en = self._translate_species_to_english(species_type)
        sample_en = self._translate_sample_to_english(sample_type)
        goal_en = self._translate_goal_to_english(research_goal)
        
        # 生成多种搜索查询
        comprehensive_queries = [
            {
                "query_type": "broad_exploration",
                "english_query": f"{species_en} {sample_en} single cell RNA sequencing",
                "target_research": "基础细胞图谱构建"
            },
            {
                "query_type": "specific_focus", 
                "english_query": f"{goal_en} {sample_en} scRNA-seq {species_en}",
                "target_research": research_goal
            },
            {
                "query_type": "methodological",
                "english_query": f"single cell {sample_en} analysis methods {species_en}",
                "target_research": "技术方法优化"
            },
            {
                "query_type": "comparative",
                "english_query": f"{sample_en} cell heterogeneity {species_en} comparative analysis",
                "target_research": "比较生物学研究"
            }
        ]
        
        return {
            "species_terminology": {
                "standard_name": species_en,
                "common_variants": [species_en.lower(), species_type],
                "taxonomy_info": f"{species_en} model organism"
            },
            "sample_terminology": {
                "precise_term": sample_en,
                "alternative_terms": [sample_type, f"{sample_en} tissue"],
                "context_specific_modifiers": ["fresh", "frozen", "fixed"]
            },
            "research_goal_terminology": {
                "primary_term": goal_en,
                "technical_terms": ["transcriptomics", "gene expression", "cell typing"],
                "methodology_terms": ["single cell sequencing", "droplet-based", "plate-based"]
            },
            "comprehensive_search_queries": comprehensive_queries,
            "domain_specific_terms": {
                primary_domain: self._get_domain_terms(primary_domain)
            }
        }

    def _translate_species_to_english(self, species_type: str) -> str:
        """物种类型英文翻译"""
        species_mapping = {
            "人类": "human", "小鼠": "mouse", "大鼠": "rat", 
            "斑马鱼": "zebrafish", "果蝇": "drosophila"
        }
        return species_mapping.get(species_type, species_type.lower())

    def _translate_sample_to_english(self, sample_type: str) -> str:
        """样本类型英文翻译"""
        sample_mapping = {
            "肿瘤组织": "tumor tissue", "血液": "blood", "骨髓": "bone marrow",
            "肺组织": "lung tissue", "肝脏": "liver", "肾脏": "kidney",
            "大脑": "brain", "心脏": "heart", "胚胎": "embryo"
        }
        return sample_mapping.get(sample_type, sample_type.lower())

    def _translate_goal_to_english(self, research_goal: str) -> str:
        """研究目标英文翻译"""
        goal_mapping = {
            "发育轨迹分析": "developmental trajectory analysis",
            "细胞类型鉴定": "cell type identification", 
            "差异表达分析": "differential expression analysis",
            "免疫细胞功能": "immune cell function",
            "肿瘤异质性": "tumor heterogeneity"
        }
        return goal_mapping.get(research_goal, research_goal.lower())

    def _get_domain_terms(self, domain: str) -> List[str]:
        """获取领域特异术语"""
        domain_terms = {
            "developmental_biology": ["development", "differentiation", "lineage", "trajectory"],
            "oncology": ["cancer", "tumor", "metastasis", "oncogenes"],
            "immunology": ["immune", "T cell", "B cell", "cytokine"],
            "neuroscience": ["neuron", "synapse", "neural", "brain"],
            "general_biology": ["cell biology", "transcriptomics", "gene expression"]
        }
        return domain_terms.get(domain, domain_terms["general_biology"])

    async def _generate_comprehensive_literature_search(
        self,
        comprehensive_intent_analysis: Dict[str, Any],
        dynamic_terminology: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成全面的文献搜索策略，覆盖多种研究方向"""
        
        research_possibilities = comprehensive_intent_analysis.get("research_possibilities", [])
        search_queries = dynamic_terminology.get("comprehensive_search_queries", [])
        
        # 为每个搜索平台生成多种查询链接
        comprehensive_search_links = {}
        
        for platform_key, platform_config in self.search_platforms.items():
            platform_links = []
            
            # 为每种搜索查询类型生成链接
            for query_info in search_queries:
                try:
                    query = query_info.get("english_query", "")
                    query_type = query_info.get("query_type", "")
                    target_research = query_info.get("target_research", "")
                    
                    formatted_query = platform_config["query_format"](query, target_research)
                    search_url = platform_config["base_url"] + urllib.parse.quote(formatted_query)
                    
                    platform_links.append({
                        "url": search_url,
                        "query_type": query_type,
                        "query_description": f"{target_research} - {query_type}",
                        "english_query": query
                    })
                    
                except Exception as e:
                    logger.warning(f"生成{platform_key}的{query_info.get('query_type', '')}链接失败: {e}")
            
            if platform_links:
                comprehensive_search_links[platform_key] = {
                    "platform_name": platform_config["name"],
                    "platform_description": platform_config["description"],
                    "platform_icon": platform_config["icon"],
                    "search_links": platform_links
                }
        
        # 为每个研究可能性生成专门的搜索建议
        possibility_specific_searches = []
        for possibility in research_possibilities:
            possibility_name = possibility.get("possibility_name", "")
            research_focus = possibility.get("research_focus", "")
            probability = possibility.get("probability", 0.0)
            
            possibility_specific_searches.append({
                "possibility_name": possibility_name,
                "research_focus": research_focus,
                "probability": probability,
                "recommended_search_strategy": f"重点关注 {possibility_name} 相关的最新研究进展",
                "key_search_terms": [
                    research_focus,
                    f"{possibility_name} single cell",
                    f"scRNA-seq {possibility_name.lower()}"
                ]
            })
        
        return {
            "comprehensive_search_links": comprehensive_search_links,
            "possibility_specific_searches": possibility_specific_searches,
            "search_strategy_summary": f"基于 {len(research_possibilities)} 种研究可能性的全面文献搜索策略",
            "total_search_queries": len(search_queries),
            "coverage_assessment": "全面覆盖多种研究方向和技术角度"
        }

    def _create_modular_information_structure(
        self,
        requirements: Dict[str, Any],
        comprehensive_intent_analysis: Dict[str, Any],
        dynamic_terminology: Dict[str, Any]
    ) -> Dict[str, Any]:
        """模块化信息拆解，为不同组件提供所需格式"""
        
        # 为方案概览模块提供的整体信息
        solution_overview_info = {
            "primary_research_focus": comprehensive_intent_analysis.get("primary_research_domain", ""),
            "main_research_possibilities": [
                {
                    "name": poss.get("possibility_name", ""),
                    "probability": poss.get("probability", 0.0),
                    "focus": poss.get("research_focus", "")
                }
                for poss in comprehensive_intent_analysis.get("research_possibilities", [])[:3]
            ],
            "species_sample_combination": {
                "species": dynamic_terminology.get("species_terminology", {}).get("standard_name", ""),
                "sample": dynamic_terminology.get("sample_terminology", {}).get("precise_term", ""),
                "research_goal": dynamic_terminology.get("research_goal_terminology", {}).get("primary_term", "")
            }
        }
        
        # 为文献搜索模块提供的专门信息
        literature_search_info = {
            "research_focus": f"{requirements.get('speciesType', '')} {requirements.get('sampleType', '')} {requirements.get('researchGoal', '')}",
            "precision_search_links": {},
            "search_possibilities": []
        }
        
        # 从comprehensive_search_links中提取链接
        comprehensive_links = dynamic_terminology.get("comprehensive_search_queries", [])
        if comprehensive_links:
            # 为文献搜索组件格式化链接
            for platform_key, platform_config in self.search_platforms.items():
                if comprehensive_links:
                    primary_query = comprehensive_links[0].get("english_query", "")
                    try:
                        formatted_query = platform_config["query_format"](primary_query, "")
                        search_url = platform_config["base_url"] + urllib.parse.quote(formatted_query)
                        literature_search_info["precision_search_links"][platform_key] = search_url
                    except:
                        pass
        
        # 为风险评估模块提供的单独信息
        risk_assessment_info = {
            "technical_challenges": [],
            "sample_specific_risks": comprehensive_intent_analysis.get("sample_specific_considerations", []),
            "species_specific_factors": comprehensive_intent_analysis.get("species_specific_factors", []),
            "complexity_level": comprehensive_intent_analysis.get("complexity_assessment", "medium")
        }
        
        # 收集所有研究可能性的技术挑战
        for possibility in comprehensive_intent_analysis.get("research_possibilities", []):
            risk_assessment_info["technical_challenges"].extend(
                possibility.get("technical_challenges", [])
            )
        
        return {
            "solution_overview_module": solution_overview_info,
            "literature_search_module": literature_search_info,
            "risk_assessment_module": risk_assessment_info,
            "technical_requirements_module": comprehensive_intent_analysis.get("technical_requirements", {}),
            "modular_design_metadata": {
                "total_modules": 4,
                "information_completeness": "comprehensive",
                "integration_ready": True
            }
        }

    def _get_fallback_comprehensive_analysis(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """全面分析的降级方案"""
        sample_type = requirements.get('sampleType', 'cells')
        research_goal = requirements.get('researchGoal', 'analysis')
        species_type = requirements.get('speciesType', 'human')
        
        return {
            "comprehensive_intent_analysis": {
                "primary_research_domain": "general_biology",
                "research_possibilities": [
                    {
                        "possibility_name": "基础细胞分析",
                        "probability": 0.8,
                        "research_focus": f"{species_type} {sample_type} 细胞异质性分析",
                        "key_questions": [f"{sample_type}的细胞组成", "基因表达特征"],
                        "expected_outcomes": ["细胞图谱", "标志基因"],
                        "technical_challenges": ["细胞分离", "数据质量"]
                    }
                ],
                "overall_confidence": 0.6,
                "complexity_assessment": "medium"
            },
            "dynamic_terminology": {
                "comprehensive_search_queries": [
                    {
                        "query_type": "broad_exploration",
                        "english_query": f"single cell RNA sequencing {sample_type}",
                        "target_research": research_goal
                    }
                ]
            },
            "comprehensive_literature_search": {
                "comprehensive_search_links": {
                    "pubmed": {
                        "platform_name": "PubMed",
                        "search_links": [{
                            "url": f"https://pubmed.ncbi.nlm.nih.gov/?term={urllib.parse.quote(f'single cell RNA sequencing {sample_type}')}",
                            "query_type": "basic",
                            "query_description": "基础搜索"
                        }]
                    }
                }
            },
            "modular_information": {
                "literature_search_module": {
                    "research_focus": f"{species_type} {sample_type} {research_goal}",
                    "precision_search_links": {
                        "pubmed": f"https://pubmed.ncbi.nlm.nih.gov/?term={urllib.parse.quote(f'single cell RNA sequencing {sample_type}')}"
                    }
                }
            }
        }


# 全局服务实例
research_intent_service = ResearchIntentService()


def get_research_intent_service() -> ResearchIntentService:
    """获取研究意图服务实例"""
    return research_intent_service