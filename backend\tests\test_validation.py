"""
Test suite for input validation utilities
"""
import pytest
from unittest.mock import patch

from app.utils.validation import InputValida<PERSON>, SecurityValidator
from app.exceptions.keyword_exceptions import InvalidInputError, ValidationError


class TestInputValidator:
    """Test cases for InputValidator"""
    
    @pytest.fixture
    def validator(self):
        """Create validator instance"""
        return InputValidator()
    
    def test_valid_query_validation(self, validator):
        """Test validation of valid queries"""
        valid_queries = [
            "单细胞RNA测序",
            "T cell analysis",
            "10x Genomics数据分析",
            "single cell transcriptomics research"
        ]
        
        for query in valid_queries:
            result = validator.validate_query(query)
            assert isinstance(result, str)
            assert len(result) > 0
    
    def test_empty_query_validation(self, validator):
        """Test validation of empty queries"""
        with pytest.raises(InvalidInputError):
            validator.validate_query("")
    
    def test_too_short_query(self, validator):
        """Test validation of too short queries"""
        with pytest.raises(InvalidInputError):
            validator.validate_query("a")
    
    def test_too_long_query(self, validator):
        """Test validation of too long queries"""
        long_query = "x" * 2000
        with pytest.raises(InvalidInputError):
            validator.validate_query(long_query)
    
    def test_malicious_content_detection(self, validator):
        """Test detection of malicious content"""
        malicious_queries = [
            "<script>alert('xss')</script>",
            "javascript:alert(1)",
            "data:text/html,<h1>test</h1>",
            "'; DROP TABLE users; --"
        ]
        
        for query in malicious_queries:
            with pytest.raises(InvalidInputError):
                validator.validate_query(query)
    
    def test_special_characters_limit(self, validator):
        """Test excessive special characters detection"""
        excessive_special = "!@#$%^&*()!@#$%^&*()!@#$%^&*()"
        with pytest.raises(InvalidInputError):
            validator.validate_query(excessive_special)
    
    def test_language_validation(self, validator):
        """Test language validation"""
        # Valid queries with Chinese or English
        valid_queries = [
            "单细胞研究",
            "cell analysis",
            "单细胞 RNA sequencing"
        ]
        
        for query in valid_queries:
            result = validator.validate_query(query)
            assert result is not None
        
        # Invalid query with no meaningful text
        with pytest.raises(InvalidInputError):
            validator.validate_query("123456789")
    
    def test_context_validation(self, validator):
        """Test context validation"""
        # Valid context
        valid_context = {
            "user_profile": {"expertise": "bioinformatics"},
            "preferences": ["methodology", "application"],
            "filters": {"year": 2023}
        }
        
        result = validator.validate_context(valid_context)
        assert isinstance(result, dict)
        
        # Invalid context type
        with pytest.raises(ValidationError):
            validator.validate_context("not a dict")
        
        # None context should be allowed
        result = validator.validate_context(None)
        assert result is None
    
    def test_max_keywords_validation(self, validator):
        """Test max keywords validation"""
        # Valid values
        valid_values = [1, 5, 10, 15, 20]
        for value in valid_values:
            result = validator.validate_max_keywords(value)
            assert result == value
        
        # Invalid values
        invalid_values = [0, -1, 100, "5", 1.5]
        for value in invalid_values:
            with pytest.raises(InvalidInputError):
                validator.validate_max_keywords(value)
    
    def test_sanitization(self, validator):
        """Test input sanitization"""
        dirty_query = "  <b>单细胞</b>  \n\t  测序  "
        clean_query = validator.validate_query(dirty_query)
        
        # Should remove HTML tags and normalize whitespace
        assert "<b>" not in clean_query
        assert "</b>" not in clean_query
        assert clean_query.strip() == clean_query
    
    def test_unicode_normalization(self, validator):
        """Test Unicode normalization"""
        # Query with Unicode variations
        unicode_query = "单细胞"  # Contains Unicode characters
        result = validator.validate_query(unicode_query)
        assert isinstance(result, str)
        assert len(result) > 0


class TestSecurityValidator:
    """Test cases for SecurityValidator"""
    
    def test_rate_limit_checking(self):
        """Test rate limit checking"""
        # Within limits
        assert SecurityValidator.check_rate_limit("user1", 50, 3600) == True
        
        # Exceeding limits  
        assert SecurityValidator.check_rate_limit("user1", 150, 3600) == False
    
    def test_api_key_validation(self):
        """Test API key validation"""
        # Valid API keys
        valid_keys = [
            "sk-1234567890abcdef1234567890abcdef",
            "api_key_12345678901234567890",
            "valid.api-key_123"
        ]
        
        for key in valid_keys:
            assert SecurityValidator.validate_api_key(key) == True
        
        # Invalid API keys
        invalid_keys = [
            None,
            "",
            "short",
            "x" * 200,  # Too long
            "invalid key with spaces",
            "key@with#special!chars"
        ]
        
        for key in invalid_keys:
            assert SecurityValidator.validate_api_key(key) == False
    
    def test_filename_sanitization(self):
        """Test filename sanitization"""
        test_cases = [
            ("normal_file.txt", "normal_file.txt"),
            ("../../../etc/passwd", "etcpasswd"),
            ("file with spaces.doc", "filewithspaces.doc"),
            ("file@#$%^&*().txt", "file.txt"),
            ("", "unnamed"),
            ("x" * 200 + ".txt", "x" * 95 + ".txt")
        ]
        
        for input_filename, expected in test_cases:
            result = SecurityValidator.sanitize_filename(input_filename)
            assert result == expected
    
    def test_directory_traversal_prevention(self):
        """Test prevention of directory traversal attacks"""
        malicious_filenames = [
            "../../../etc/passwd",
            "..\\..\\windows\\system32\\config\\sam",
            "file../../secret.txt",
            "/etc/passwd",
            "\\windows\\system32\\config\\sam"
        ]
        
        for filename in malicious_filenames:
            sanitized = SecurityValidator.sanitize_filename(filename)
            assert "../" not in sanitized
            assert "..\\" not in sanitized
            assert not sanitized.startswith("/")
            assert not sanitized.startswith("\\")


class TestValidationIntegration:
    """Integration tests for validation components"""
    
    @pytest.fixture
    def validator(self):
        """Create validator instance"""
        return InputValidator()
    
    def test_comprehensive_query_validation(self, validator):
        """Test comprehensive query validation workflow"""
        test_cases = [
            {
                "query": "单细胞RNA测序数据分析",
                "context": {"user_profile": {"expertise": "bioinformatics"}},
                "max_keywords": 10,
                "should_pass": True
            },
            {
                "query": "",
                "context": None,
                "max_keywords": 5,
                "should_pass": False
            },
            {
                "query": "<script>alert('xss')</script>",
                "context": None,
                "max_keywords": 5,
                "should_pass": False
            },
            {
                "query": "valid query",
                "context": "invalid context",
                "max_keywords": 5,
                "should_pass": False
            }
        ]
        
        for case in test_cases:
            try:
                # Validate all components
                validator.validate_query(case["query"])
                validator.validate_context(case["context"])
                validator.validate_max_keywords(case["max_keywords"])
                
                if not case["should_pass"]:
                    pytest.fail(f"Expected validation to fail for case: {case}")
                    
            except (InvalidInputError, ValidationError):
                if case["should_pass"]:
                    pytest.fail(f"Expected validation to pass for case: {case}")
    
    def test_edge_cases(self, validator):
        """Test edge cases and boundary conditions"""
        # Minimum valid query length
        min_query = "ab"
        result = validator.validate_query(min_query)
        assert result == min_query
        
        # Maximum valid query length
        max_query = "x" * 1000  # Just under the limit
        result = validator.validate_query(max_query)
        assert len(result) <= 1000
        
        # Minimum valid max_keywords
        assert validator.validate_max_keywords(1) == 1
        
        # Maximum valid max_keywords
        max_allowed = validator.config.MAX_KEYWORDS_PER_QUERY
        assert validator.validate_max_keywords(max_allowed) == max_allowed
    
    def test_multilingual_support(self, validator):
        """Test multilingual query support"""
        multilingual_queries = [
            "单细胞 RNA sequencing 分析",  # Chinese + English
            "T細胞 immune response",       # Traditional Chinese + English
            "scRNA-seq 数据处理",          # Technical term + Chinese
            "10x Genomics 平台分析"        # Brand name + Chinese
        ]
        
        for query in multilingual_queries:
            result = validator.validate_query(query)
            assert isinstance(result, str)
            assert len(result) > 0
    
    def test_performance_validation(self, validator):
        """Test validation performance with various inputs"""
        import time
        
        # Test with different query lengths
        queries = [
            "short",
            "medium length query for testing",
            "very long query " * 20,  # Still within limits
        ]
        
        for query in queries:
            start_time = time.time()
            validator.validate_query(query)
            duration = time.time() - start_time
            
            # Validation should be fast (under 100ms)
            assert duration < 0.1
    
    def test_context_edge_cases(self, validator):
        """Test context validation edge cases"""
        edge_cases = [
            {},  # Empty dict
            {"simple": "value"},  # Simple key-value
            {"nested": {"deep": {"value": 123}}},  # Nested structure
            {"list": [1, 2, 3]},  # List values
            {"mixed": {"string": "test", "number": 42, "list": [1, 2]}},  # Mixed types
        ]
        
        for context in edge_cases:
            result = validator.validate_context(context)
            assert isinstance(result, dict)
    
    def test_error_message_quality(self, validator):
        """Test quality and helpfulness of error messages"""
        try:
            validator.validate_query("")
        except InvalidInputError as e:
            assert "empty" in str(e).lower()
            assert e.field == "query"
        
        try:
            validator.validate_max_keywords(-1)
        except InvalidInputError as e:
            assert "at least 1" in str(e).lower()
            assert e.field == "max_keywords"
        
        try:
            validator.validate_context("not a dict")
        except ValidationError as e:
            assert "dictionary" in str(e).lower()