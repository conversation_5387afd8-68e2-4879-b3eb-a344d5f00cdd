"""
综合解决方案框架API端点
支持完整的方案框架生成和精准搜索链接
"""
from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

from app.services.comprehensive_solution_service import get_comprehensive_solution_service
from app.core.database import get_db
from sqlalchemy.orm import Session
from fastapi import Depends

logger = logging.getLogger(__name__)

router = APIRouter()

# 请求模型
class ComprehensiveSolutionRequest(BaseModel):
    """综合方案框架请求模型"""
    requirements: Dict[str, Any]
    user_message: Optional[str] = ""
    framework_template: Optional[str] = "standard"  # standard/detailed/simplified
    enable_literature_search: Optional[bool] = True
    user_preferences: Optional[Dict[str, Any]] = {}

class ResearchIntentRequest(BaseModel):
    """研究意图分析请求模型"""
    requirements: Dict[str, Any]
    user_message: Optional[str] = ""

# 响应模型
class ComprehensiveSolutionResponse(BaseModel):
    """综合方案框架响应模型"""
    success: bool
    framework_id: str
    data: Dict[str, Any]
    generation_time: str
    template_used: str

class ResearchIntentResponse(BaseModel):
    """研究意图分析响应模型"""
    success: bool
    intent_analysis: Dict[str, Any]
    integrated_keywords: Dict[str, Any]
    precision_search_links: Dict[str, Any]
    research_focus_display: Dict[str, Any]

# 获取服务实例
comprehensive_service = get_comprehensive_solution_service()


@router.post("/comprehensive-framework", response_model=ComprehensiveSolutionResponse)
async def generate_comprehensive_framework(
    request: ComprehensiveSolutionRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    生成完整的方案框架
    集成研究意图分析、精准搜索链接、风险评估等所有功能
    """
    try:
        print(f"🚀🚀🚀 [DEBUG] 接收综合方案框架生成请求 - 模板: {request.framework_template}")
        print(f"🚀🚀🚀 [DEBUG] 请求参数: {request.requirements}")
        logger.info(f"🚀 接收综合方案框架生成请求 - 模板: {request.framework_template}")
        
        # 验证请求参数
        if not request.requirements:
            raise HTTPException(status_code=400, detail="需求信息不能为空")
        
        # 验证框架模板
        valid_templates = ["standard", "detailed", "simplified"]
        if request.framework_template not in valid_templates:
            request.framework_template = "standard"
        
        # 生成综合方案框架
        framework_result = await comprehensive_service.generate_comprehensive_framework(
            requirements=request.requirements,
            user_message=request.user_message,
            framework_template=request.framework_template,
            enable_literature_search=request.enable_literature_search
        )
        
        # 后台任务：保存方案到数据库
        background_tasks.add_task(
            save_comprehensive_solution_to_db,
            framework_result,
            request.requirements,
            db
        )
        
        # 记录成功日志
        logger.info(f"✅ 综合方案框架生成成功 - ID: {framework_result.get('framework_id')}")
        
        return ComprehensiveSolutionResponse(
            success=True,
            framework_id=framework_result.get("solution_id", framework_result.get("framework_id", f"CellForge-{datetime.now().strftime('%Y%m%d-%H%M%S')}")),
            data=framework_result,
            generation_time=framework_result.get("generated_at", datetime.now().isoformat()),
            template_used=framework_result.get("template_used", request.framework_template)
        )
        
    except Exception as e:
        logger.error(f"❌ 综合方案框架生成失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"方案框架生成失败: {str(e)}"
        )


@router.post("/research-intent-analysis", response_model=ResearchIntentResponse)
async def analyze_research_intent_and_keywords(
    request: ResearchIntentRequest
):
    """
    单独的研究意图分析和关键词生成
    返回精准搜索链接和研究焦点
    """
    try:
        logger.info("🔍 接收研究意图分析请求")
        
        # 验证请求
        if not request.requirements:
            raise HTTPException(status_code=400, detail="需求信息不能为空")
        
        # 调用研究意图服务
        research_intent_service = comprehensive_service.research_intent_service
        
        intent_result = await research_intent_service.analyze_research_intent_and_generate_keywords(
            requirements=request.requirements,
            user_message=request.user_message
        )
        
        logger.info("✅ 研究意图分析完成")
        
        return ResearchIntentResponse(
            success=True,
            intent_analysis=intent_result.get("intent_analysis", {}),
            integrated_keywords=intent_result.get("integrated_keywords", {}),
            precision_search_links=intent_result.get("precision_search_links", {}),
            research_focus_display=intent_result.get("research_focus_display", {})
        )
        
    except Exception as e:
        logger.error(f"❌ 研究意图分析失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"研究意图分析失败: {str(e)}"
        )


@router.get("/framework/{framework_id}")
async def get_comprehensive_framework(
    framework_id: str,
    db: Session = Depends(get_db)
):
    """
    获取已生成的方案框架
    """
    try:
        # 从数据库获取方案
        # 这里需要实现数据库查询逻辑
        
        logger.info(f"📋 获取方案框架: {framework_id}")
        
        # 临时返回，实际应从数据库查询
        return {
            "success": True,
            "message": f"方案框架 {framework_id} 查询功能开发中"
        }
        
    except Exception as e:
        logger.error(f"❌ 获取方案框架失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取方案框架失败: {str(e)}"
        )


@router.put("/framework/{framework_id}/optimize")
async def optimize_comprehensive_framework(
    framework_id: str,
    feedback: Dict[str, Any],
    adjustment_preferences: Optional[Dict[str, Any]] = None
):
    """
    基于用户反馈优化方案框架
    """
    try:
        logger.info(f"🔧 优化方案框架: {framework_id}")
        
        # 这里实现方案优化逻辑
        # 基于用户反馈调整推荐
        
        return {
            "success": True,
            "message": "方案优化功能开发中",
            "framework_id": framework_id,
            "optimization_applied": True
        }
        
    except Exception as e:
        logger.error(f"❌ 方案优化失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"方案优化失败: {str(e)}"
        )


@router.get("/templates")
async def get_available_templates():
    """
    获取可用的方案框架模板
    """
    try:
        templates = {
            "standard": {
                "name": "标准版方案",
                "description": "包含基础方案推荐和文献搜索",
                "components": ["方案概览", "文献推荐", "精准搜索", "风险评估"],
                "suitable_for": "一般研究人员",
                "estimated_time": "2-3分钟生成"
            },
            "detailed": {
                "name": "详细版方案",
                "description": "包含全面的分析和比较",
                "components": ["方案概览", "关键要素", "文献推荐", "平台对比", "风险评估", "实施计划"],
                "suitable_for": "项目负责人、技术专家",
                "estimated_time": "3-5分钟生成"
            },
            "simplified": {
                "name": "简化版方案",
                "description": "快速生成基础推荐",
                "components": ["方案概览", "精准搜索", "基础推荐"],
                "suitable_for": "初学者、快速决策",
                "estimated_time": "1-2分钟生成"
            }
        }
        
        return {
            "success": True,
            "templates": templates,
            "default_template": "standard"
        }
        
    except Exception as e:
        logger.error(f"❌ 获取模板信息失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取模板信息失败: {str(e)}"
        )


@router.get("/search-platforms")
async def get_search_platforms():
    """
    获取支持的文献搜索平台信息
    """
    try:
        platforms = {
            "pubmed": {
                "name": "PubMed",
                "description": "生物医学文献数据库",
                "icon": "🏥",
                "base_url": "https://pubmed.ncbi.nlm.nih.gov",
                "features": ["MeSH terms支持", "高质量同行评议", "医学权威性"],
                "best_for": ["生物医学研究", "临床相关文献", "高影响因子论文"]
            },
            "google_scholar": {
                "name": "Google Scholar",
                "description": "学术搜索引擎",
                "icon": "🎓",
                "base_url": "https://scholar.google.com",
                "features": ["全文搜索", "引用统计", "多学科覆盖"],
                "best_for": ["综合学术搜索", "引用分析", "灰色文献"]
            },
            "semantic_scholar": {
                "name": "Semantic Scholar",
                "description": "AI驱动的学术搜索",
                "icon": "🧠",
                "base_url": "https://www.semanticscholar.org",
                "features": ["语义理解", "AI推荐", "关联分析"],
                "best_for": ["智能文献发现", "相关性排序", "研究趋势"]
            },
            "biorxiv": {
                "name": "bioRxiv",
                "description": "生物学预印本",
                "icon": "🧬",
                "base_url": "https://www.biorxiv.org",
                "features": ["最新研究", "预印本", "快速发布"],
                "best_for": ["最新进展", "前沿研究", "方法学创新"]
            }
        }
        
        return {
            "success": True,
            "platforms": platforms,
            "usage_tips": [
                "PubMed适合寻找权威的生物医学文献",
                "Google Scholar提供最全面的搜索覆盖",
                "Semantic Scholar帮助发现相关研究",
                "bioRxiv获取最新的研究进展"
            ]
        }
        
    except Exception as e:
        logger.error(f"❌ 获取搜索平台信息失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取搜索平台信息失败: {str(e)}"
        )


# 后台任务函数
async def save_comprehensive_solution_to_db(
    framework_result: Dict[str, Any],
    requirements: Dict[str, Any],
    db: Session
):
    """
    将综合方案保存到数据库
    """
    try:
        logger.info(f"💾 保存方案框架到数据库: {framework_result.get('framework_id')}")
        
        # 这里实现数据库保存逻辑
        # 需要根据实际的数据模型进行实现
        
        logger.info("✅ 方案框架保存成功")
        
    except Exception as e:
        logger.error(f"❌ 保存方案框架失败: {str(e)}")


# 健康检查端点
@router.get("/health")
async def health_check():
    """
    综合方案服务健康检查
    """
    try:
        # 检查服务状态
        service_status = {
            "comprehensive_solution_service": "healthy",
            "research_intent_service": "healthy",
            "database_connection": "healthy",
            "ai_service": "healthy"
        }
        
        return {
            "success": True,
            "status": "healthy",
            "services": service_status,
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
    except Exception as e:
        logger.error(f"❌ 健康检查失败: {str(e)}")
        return {
            "success": False,
            "status": "unhealthy",
            "error": str(e)
        }