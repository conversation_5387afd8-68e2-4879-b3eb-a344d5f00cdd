"use client"

import React, { useState } from 'react'
import { LiteraturePanel } from '@/components/literature-panel'
import { FormattedMessage } from '@/components/formatted-message'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { literatureApi } from '@/lib/api'
import { toast } from 'sonner'

interface Literature {
  id?: number
  title: string
  authors: string[]
  journal: string
  publication_year: number
  doi?: string
  pubmed_id?: string
  abstract: string
  category: string
  technology_tags: string[]
  application_tags: string[]
  impact_factor?: number
  citation_count: number
  relevance_score: number
  key_findings: string
  methodology_summary: string
  business_value: string
}

export default function TestLiteraturePage() {
  const [selectedLiterature, setSelectedLiterature] = useState<Literature | null>(null)
  const [aiResponse, setAiResponse] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const handleLiteratureSelect = (literature: Literature) => {
    setSelectedLiterature(literature)
    toast.success(`已选择文献: ${literature.title.substring(0, 50)}...`)
  }

  const generateAIResponseWithLiterature = async () => {
    if (!selectedLiterature) {
      toast.error('请先选择一篇文献')
      return
    }

    setLoading(true)
    try {
      // 模拟获取文献推荐
      const recommendations = await literatureApi.getLiteratureRecommendations({
        context: {
          user_message: "请基于这篇文献为我推荐单细胞测序方案",
          user_profile: {
            organization: "测试机构",
            role: "研究员"
          },
          requirements: {
            researchGoal: "基于文献的研究方案",
            sampleType: "细胞系",
            budget: "10-20万"
          }
        },
        recommendation_type: "methodology",
        top_k: 3
      })

      // 生成包含文献引用的AI回复
      const mockResponse = `🎯 **基于文献的专业方案建议**

感谢您选择了这篇高质量的文献作为参考！基于 **${selectedLiterature.title}** 的研究成果，我为您制定以下技术方案：

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🔬 **技术方案推荐**

基于该文献的方法学优势，我推荐采用以下技术路线：

**🌟 推荐平台**: 10x Genomics Chromium
- **选择理由**: 与文献中使用的技术平台一致
- **技术优势**: 高通量、高质量数据产出
- **预期效果**: 可重现文献中的关键发现

**📊 实验设计**:
• 样本处理: 参考文献的样本制备协议
• 测序深度: ${selectedLiterature.category === 'methodology' ? '深度测序(50K reads/cell)' : '标准测序(20K reads/cell)'}
• 质控标准: 严格按照文献标准执行

**💰 成本预算**:
• 总预算: 12-18万元
• 测序费用: 8-12万元
• 分析费用: 2-3万元
• 其他费用: 2-3万元

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📚 **相关文献支持**

**[1] ${selectedLiterature.title}**
*${selectedLiterature.authors.slice(0, 3).join(', ')}${selectedLiterature.authors.length > 3 ? '等' : ''}*
📖 ${selectedLiterature.journal} (${selectedLiterature.publication_year})
${selectedLiterature.impact_factor ? `🏆 影响因子: ${selectedLiterature.impact_factor}` : ''}
💡 **相关性**: 高质量研究，${selectedLiterature.citation_count}次引用，与您的需求高度匹配
🔑 **支持要点**: ${selectedLiterature.key_findings}
${selectedLiterature.doi ? `🔗 DOI: ${selectedLiterature.doi}` : ''}

**[2] 相关技术文献**
*Butler, A., Hoffman, P., Smibert, P.等*
📖 Nature Biotechnology (2018)
🏆 影响因子: 36.558
💡 **相关性**: 数据分析方法学支持
🔑 **支持要点**: Seurat v3 provides comprehensive tools for single-cell data integration
🔗 DOI: 10.1038/nbt.4096

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🎯 **实施建议**

1. **前期准备**: 详细阅读参考文献的方法学部分
2. **实验执行**: 严格按照文献协议进行样本处理
3. **数据分析**: 采用文献中验证的分析流程
4. **结果验证**: 对比文献结果，确保数据质量

💎 **置信度**: 95% (基于高质量文献支持)`

      setAiResponse(mockResponse)
      toast.success('已生成基于文献的AI回复')
    } catch (error) {
      console.error('生成AI回复失败:', error)
      toast.error('生成失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">文献集成测试页面</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：文献面板 */}
          <div>
            <LiteraturePanel 
              onLiteratureSelect={handleLiteratureSelect}
              className="h-full"
            />
          </div>

          {/* 右侧：选中的文献和AI回复 */}
          <div className="space-y-6">
            {/* 选中的文献 */}
            {selectedLiterature && (
              <Card>
                <CardHeader>
                  <CardTitle>已选择文献</CardTitle>
                </CardHeader>
                <CardContent>
                  <h3 className="font-semibold text-lg mb-2">{selectedLiterature.title}</h3>
                  <p className="text-gray-600 mb-2">
                    {selectedLiterature.authors.slice(0, 3).join(', ')}
                    {selectedLiterature.authors.length > 3 ? '等' : ''} • 
                    {selectedLiterature.journal} ({selectedLiterature.publication_year})
                  </p>
                  {selectedLiterature.impact_factor && (
                    <p className="text-sm text-gray-500 mb-2">
                      影响因子: {selectedLiterature.impact_factor}
                    </p>
                  )}
                  <p className="text-sm text-gray-700 mb-4">
                    {selectedLiterature.key_findings}
                  </p>
                  <Button 
                    onClick={generateAIResponseWithLiterature}
                    disabled={loading}
                    className="w-full"
                  >
                    {loading ? '生成中...' : '基于此文献生成AI方案'}
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* AI回复 */}
            {aiResponse && (
              <Card>
                <CardHeader>
                  <CardTitle>AI智能回复（含文献引用）</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <FormattedMessage content={aiResponse} />
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 说明 */}
            {!selectedLiterature && (
              <Card>
                <CardHeader>
                  <CardTitle>使用说明</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm text-gray-600">
                    <p>1. 在左侧文献面板中搜索或浏览文献</p>
                    <p>2. 点击选择感兴趣的文献</p>
                    <p>3. 点击"基于此文献生成AI方案"按钮</p>
                    <p>4. 查看包含文献引用的AI智能回复</p>
                    <p className="text-blue-600 font-medium">
                      💡 这个功能展示了如何将文献资源集成到AI回复中，避免AI幻觉问题
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
