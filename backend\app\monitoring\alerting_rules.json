{"alerting_rules": {"performance_alerts": [{"name": "high_response_time", "description": "API响应时间过长", "metric": "api_response_time", "condition": "avg_over_time(5m) > 5000", "severity": "warning", "actions": ["log", "console"]}, {"name": "high_error_rate", "description": "API错误率过高", "metric": "api_error_rate", "condition": "rate(5m) > 0.05", "severity": "critical", "actions": ["log", "console", "auto_scale"]}, {"name": "memory_pressure", "description": "内存使用率过高", "metric": "memory_usage_percent", "condition": "current > 85", "severity": "warning", "actions": ["log", "console"]}], "business_alerts": [{"name": "low_literature_success_rate", "description": "文献搜索成功率过低", "metric": "literature_success_rate", "condition": "avg_over_time(10m) < 0.8", "severity": "warning", "actions": ["log", "console"]}, {"name": "ai_service_degradation", "description": "AI服务质量下降", "metric": "ai_service_quality_score", "condition": "avg_over_time(10m) < 0.7", "severity": "warning", "actions": ["log", "console"]}, {"name": "low_cache_hit_rate", "description": "缓存命中率过低", "metric": "cache_hit_rate", "condition": "avg_over_time(15m) < 0.6", "severity": "info", "actions": ["log"]}], "service_alerts": [{"name": "database_connection_failure", "description": "数据库连接失败", "metric": "database_health_check", "condition": "status != 'healthy'", "severity": "critical", "actions": ["log", "console", "auto_restart"]}, {"name": "external_api_failure", "description": "外部API服务异常", "metric": "external_api_health_check", "condition": "success_rate < 0.9", "severity": "warning", "actions": ["log", "console"]}]}, "notification_settings": {"console": {"enabled": true, "format": "[{timestamp}] {severity}: {name} - {description}"}, "log": {"enabled": true, "file": "monitoring_alerts.log", "format": "{timestamp} | {severity} | {name} | {description} | {metric_value}"}, "auto_actions": {"auto_scale": {"enabled": false, "description": "自动扩展服务实例"}, "auto_restart": {"enabled": false, "description": "自动重启服务"}}}, "escalation_policy": {"warning": {"initial_delay": 300, "repeat_interval": 1800, "max_repeats": 5}, "critical": {"initial_delay": 60, "repeat_interval": 300, "max_repeats": 10}}}