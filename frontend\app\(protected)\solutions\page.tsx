"use client"

import { useAuth } from "@/contexts/auth-context"
import { SolutionPresentation } from "@/components/solution-presentation"
import { AccessDenied } from "@/components/access-denied"

export default function SolutionsPage() {
  const { hasPermission } = useAuth()

  return (
    <div className="min-h-[calc(100vh-64px)]">
      {hasPermission("view_solutions") ? <SolutionPresentation /> : <AccessDenied />}
    </div>
  )
}
