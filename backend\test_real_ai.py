#!/usr/bin/env python3
"""
测试真实AI服务的简单脚本
"""

import requests
import json
from datetime import datetime

# API 基础URL
BASE_URL = "http://localhost:8000/api"

def test_real_ai_intent_analysis():
    """测试真实AI意图分析"""
    print("🧠 测试真实AI意图分析...")
    
    payload = {
        "user_input": "我需要进行人类PBMC的单细胞RNA测序实验，主要研究免疫细胞的功能状态",
        "requirements": {
            "speciesType": "人类 (Homo sapiens)",
            "experimentType": "单细胞RNA测序 (scRNA-seq)",
            "researchGoal": "免疫细胞功能分析",
            "sampleType": "PBMC (外周血单核细胞)",
            "budget": "10-20万",
            "timeline": "3-6个月"
        },
        "user_id": 1,
        "user_context": {
            "expertise_level": "intermediate",
            "research_background": "immunology"
        }
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/intelligent-research/intent-analysis",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60  # 增加超时时间，因为真实AI需要更多时间
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 真实AI意图分析成功!")
            print(f"📊 置信度: {data.get('data', {}).get('intent_analysis', {}).get('confidence_score', 'N/A')}")
            print(f"🎯 研究领域: {data.get('data', {}).get('intent_analysis', {}).get('research_domain', 'N/A')}")
            print(f"⏱️  处理时间: {data.get('processing_time_ms', 'N/A')}ms")
            
            # 检查是否使用了真实AI（通过响应内容的复杂性判断）
            intent_analysis = data.get('data', {}).get('intent_analysis', {})
            if len(str(intent_analysis)) > 500:  # 真实AI响应通常更详细
                print("🎉 确认使用了真实AI服务!")
            else:
                print("⚠️  可能仍在使用模拟响应")
                
            return True
        else:
            print(f"❌ 请求失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时 - 这可能表明AI服务正在处理中")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_health_check():
    """测试健康检查"""
    print("🏥 测试健康检查...")
    
    try:
        response = requests.get(f"{BASE_URL}/intelligent-research/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 智能研究API健康检查通过")
            print(f"📋 服务状态: {data.get('data', {}).get('service_status', 'N/A')}")
            return True
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始真实AI服务测试")
    print("=" * 50)
    
    # 1. 健康检查
    health_ok = test_health_check()
    print()
    
    if not health_ok:
        print("❌ 健康检查失败，跳过AI测试")
        return
    
    # 2. 真实AI意图分析测试
    ai_ok = test_real_ai_intent_analysis()
    print()
    
    # 总结
    print("=" * 50)
    print("📊 测试总结:")
    print(f"健康检查: {'✅ 通过' if health_ok else '❌ 失败'}")
    print(f"真实AI测试: {'✅ 通过' if ai_ok else '❌ 失败'}")
    
    if health_ok and ai_ok:
        print("🎉 真实AI服务测试成功!")
    else:
        print("⚠️  部分测试失败，请检查服务配置")

if __name__ == "__main__":
    main()
