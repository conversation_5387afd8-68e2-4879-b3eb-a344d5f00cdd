import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ShieldAlert } from "lucide-react"
import Link from "next/link"

export function AccessDenied() {
  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-64px)]">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
              <ShieldAlert className="h-6 w-6 text-red-600" />
            </div>
          </div>
          <CardTitle className="text-xl">访问被拒绝</CardTitle>
          <CardDescription>您没有权限访问此页面</CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="mb-4 text-slate-600">请联系系统管理员获取访问权限，或返回首页。</p>
          <Button asChild>
            <Link href="/">返回首页</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
