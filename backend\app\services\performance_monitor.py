"""
性能监控服务模块
为CellForge AI提供全面的性能监控和指标收集
"""
import time
import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque
import psutil
import threading

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """性能指标数据结构"""
    service: str
    method: str
    start_time: float
    end_time: Optional[float] = None
    duration_ms: Optional[float] = None
    status: str = "running"  # running, success, error
    error_message: Optional[str] = None
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class PerformanceMonitor:
    """性能监控服务"""
    
    def __init__(self):
        self.metrics: deque = deque(maxlen=10000)  # 保留最近10000条记录
        self.service_stats: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            "total_calls": 0,
            "success_calls": 0,
            "error_calls": 0,
            "total_duration_ms": 0,
            "avg_duration_ms": 0,
            "min_duration_ms": float('inf'),
            "max_duration_ms": 0,
            "last_24h_calls": deque(maxlen=1440),  # 24小时，每分钟一个数据点
            "error_rate_percent": 0
        })
        
        # 系统资源监控
        self.system_stats = {
            "cpu_usage": deque(maxlen=60),  # 最近60个数据点
            "memory_usage": deque(maxlen=60),
            "disk_usage": deque(maxlen=60)
        }
        
        # 启动后台监控任务
        self._monitoring_active = True
        self._start_background_monitoring()
    
    def start_monitoring(self, service: str, method: str, **metadata) -> str:
        """开始监控一个操作"""
        metric_id = f"{service}_{method}_{int(time.time() * 1000)}"
        
        metric = PerformanceMetric(
            service=service,
            method=method,
            start_time=time.time(),
            memory_usage_mb=psutil.Process().memory_info().rss / 1024 / 1024,
            cpu_usage_percent=psutil.Process().cpu_percent(),
            metadata=metadata
        )
        
        self.metrics.append((metric_id, metric))
        logger.debug(f"开始监控: {service}.{method}")
        return metric_id
    
    def end_monitoring(self, metric_id: str, status: str = "success", error_message: Optional[str] = None):
        """结束监控一个操作"""
        # 查找对应的监控记录
        for i, (mid, metric) in enumerate(self.metrics):
            if mid == metric_id:
                metric.end_time = time.time()
                metric.duration_ms = (metric.end_time - metric.start_time) * 1000
                metric.status = status
                metric.error_message = error_message
                
                # 更新服务统计
                self._update_service_stats(metric)
                
                logger.debug(f"结束监控: {metric.service}.{metric.method} - {metric.duration_ms:.2f}ms")
                break
    
    def _update_service_stats(self, metric: PerformanceMetric):
        """更新服务统计信息"""
        service_key = f"{metric.service}.{metric.method}"
        stats = self.service_stats[service_key]
        
        stats["total_calls"] += 1
        if metric.status == "success":
            stats["success_calls"] += 1
        else:
            stats["error_calls"] += 1
        
        if metric.duration_ms:
            stats["total_duration_ms"] += metric.duration_ms
            stats["avg_duration_ms"] = stats["total_duration_ms"] / stats["total_calls"]
            stats["min_duration_ms"] = min(stats["min_duration_ms"], metric.duration_ms)
            stats["max_duration_ms"] = max(stats["max_duration_ms"], metric.duration_ms)
        
        # 计算错误率
        stats["error_rate_percent"] = (stats["error_calls"] / stats["total_calls"]) * 100
        
        # 记录24小时调用统计
        current_minute = datetime.now().replace(second=0, microsecond=0)
        stats["last_24h_calls"].append({
            "timestamp": current_minute.isoformat(),
            "calls": 1,
            "errors": 1 if metric.status == "error" else 0
        })
    
    def get_service_stats(self, service: Optional[str] = None) -> Dict[str, Any]:
        """获取服务统计信息"""
        if service:
            return {
                key: stats for key, stats in self.service_stats.items()
                if key.startswith(service)
            }
        return dict(self.service_stats)
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统资源统计"""
        return {
            "cpu_usage": list(self.system_stats["cpu_usage"]),
            "memory_usage": list(self.system_stats["memory_usage"]),
            "disk_usage": list(self.system_stats["disk_usage"]),
            "current_stats": {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage('/').percent
            }
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能汇总信息"""
        total_calls = sum(stats["total_calls"] for stats in self.service_stats.values())
        total_errors = sum(stats["error_calls"] for stats in self.service_stats.values())
        
        # 计算最慢的服务
        slowest_services = sorted(
            [(service, stats["avg_duration_ms"]) for service, stats in self.service_stats.items()],
            key=lambda x: x[1],
            reverse=True
        )[:5]
        
        # 计算错误率最高的服务
        error_prone_services = sorted(
            [(service, stats["error_rate_percent"]) for service, stats in self.service_stats.items()],
            key=lambda x: x[1],
            reverse=True
        )[:5]
        
        return {
            "overview": {
                "total_calls": total_calls,
                "total_errors": total_errors,
                "overall_error_rate": (total_errors / total_calls * 100) if total_calls > 0 else 0,
                "monitored_services": len(self.service_stats)
            },
            "slowest_services": slowest_services,
            "error_prone_services": error_prone_services,
            "system_health": self.get_system_stats()["current_stats"]
        }
    
    def _start_background_monitoring(self):
        """启动后台系统监控"""
        def monitor_system():
            while self._monitoring_active:
                try:
                    # 收集系统资源信息
                    self.system_stats["cpu_usage"].append({
                        "timestamp": datetime.now().isoformat(),
                        "value": psutil.cpu_percent()
                    })
                    
                    memory = psutil.virtual_memory()
                    self.system_stats["memory_usage"].append({
                        "timestamp": datetime.now().isoformat(),
                        "value": memory.percent,
                        "available_gb": memory.available / 1024 / 1024 / 1024
                    })
                    
                    disk = psutil.disk_usage('/')
                    self.system_stats["disk_usage"].append({
                        "timestamp": datetime.now().isoformat(),
                        "value": (disk.used / disk.total) * 100,
                        "free_gb": disk.free / 1024 / 1024 / 1024
                    })
                    
                    time.sleep(60)  # 每分钟收集一次
                    
                except Exception as e:
                    logger.error(f"系统监控异常: {str(e)}")
                    time.sleep(60)
        
        # 在后台线程中运行系统监控
        monitor_thread = threading.Thread(target=monitor_system, daemon=True)
        monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控服务"""
        self._monitoring_active = False
        logger.info("性能监控服务已停止")
    
    def clear_metrics(self):
        """清空监控数据"""
        self.metrics.clear()
        self.service_stats.clear()
        self.system_stats = {
            "cpu_usage": deque(maxlen=60),
            "memory_usage": deque(maxlen=60),
            "disk_usage": deque(maxlen=60)
        }
        logger.info("监控数据已清空")


# 全局性能监控实例
performance_monitor = PerformanceMonitor()


def monitor_performance(service: str, method: Optional[str] = None):
    """性能监控装饰器"""
    def decorator(func: Callable):
        actual_method = method or func.__name__
        
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                metric_id = performance_monitor.start_monitoring(
                    service=service,
                    method=actual_method,
                    args_count=len(args),
                    kwargs_keys=list(kwargs.keys())
                )
                
                try:
                    result = await func(*args, **kwargs)
                    performance_monitor.end_monitoring(metric_id, "success")
                    return result
                except Exception as e:
                    performance_monitor.end_monitoring(metric_id, "error", str(e))
                    raise
            
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                metric_id = performance_monitor.start_monitoring(
                    service=service,
                    method=actual_method,
                    args_count=len(args),
                    kwargs_keys=list(kwargs.keys())
                )
                
                try:
                    result = func(*args, **kwargs)
                    performance_monitor.end_monitoring(metric_id, "success")
                    return result
                except Exception as e:
                    performance_monitor.end_monitoring(metric_id, "error", str(e))
                    raise
            
            return sync_wrapper
    
    return decorator