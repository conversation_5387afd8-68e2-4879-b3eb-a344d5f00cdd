"""
安全的AI提示词管理系统
防止用户套取提示词，支持动态提示词组装
"""
import hashlib
import re
from typing import Dict, List, Optional, Tuple
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ConversationStage(Enum):
    """对话阶段枚举"""
    INITIAL_CONTACT = "initial_contact"
    REQUIREMENT_COLLECTION = "requirement_collection"
    DEEP_DIVE_QUESTIONS = "deep_dive_questions"
    REQUIREMENT_VALIDATION = "requirement_validation"
    PROTOCOL_GENERATION = "protocol_generation"
    PROTOCOL_OPTIMIZATION = "protocol_optimization"
    FINALIZATION = "finalization"

class PromptSecurityManager:
    """提示词安全管理器"""

    def __init__(self):
        # 危险关键词检测 - 更精确的模式匹配
        self.dangerous_keywords = [
            "prompt", "提示词", "instruction", "指令",
            "你的角色", "你是什么", "你的设定", "system message",
            "ignore", "忽略", "forget", "忘记", "override", "覆盖",
            "jailbreak", "越狱", "破解", "hack", "攻击",
            "reveal", "显示", "show me", "告诉我你的",
            "what are you", "who are you", "你的身份",
            "原始指令", "初始设定", "系统设置"
        ]

        # 需要上下文检查的关键词（避免误报）
        self.context_sensitive_keywords = {
            "system": ["system message", "system prompt", "system instruction", "你的system"],
            "role": ["你的role", "your role", "角色定义", "role definition"],
            "设定": ["你的设定", "初始设定", "系统设定"]
        }

        # 套取提示词的常见模式
        self.prompt_extraction_patterns = [
            r"(?i)(show|tell|reveal|display).*(prompt|instruction|system)",
            r"(?i)(你的|your).*(prompt|指令|设定|角色定义)",
            r"(?i)(ignore|forget|override).*(previous|above|system)",
            r"(?i)(what|who).*(are you|你是)",
            r"(?i)repeat.*(instruction|prompt|system)",
            r"(?i)(copy|复制|重复).*(prompt|指令)",
        ]

    def is_prompt_extraction_attempt(self, user_message: str) -> Tuple[bool, str]:
        """
        检测用户是否在尝试套取提示词
        返回: (是否为套取尝试, 检测到的模式)
        """
        message_lower = user_message.lower()

        # 检查危险关键词
        for keyword in self.dangerous_keywords:
            if keyword.lower() in message_lower:
                logger.warning(f"检测到危险关键词: {keyword}")
                return True, f"危险关键词: {keyword}"

        # 检查上下文敏感关键词
        for base_keyword, dangerous_contexts in self.context_sensitive_keywords.items():
            if base_keyword.lower() in message_lower:
                # 检查是否在危险上下文中
                for dangerous_context in dangerous_contexts:
                    if dangerous_context.lower() in message_lower:
                        logger.warning(f"检测到危险上下文: {dangerous_context}")
                        return True, f"危险上下文: {dangerous_context}"
                # 如果只是单独出现关键词，不在危险上下文中，则不认为是威胁
                logger.info(f"检测到上下文敏感关键词但无危险上下文: {base_keyword}")

        # 检查套取模式
        for pattern in self.prompt_extraction_patterns:
            if re.search(pattern, user_message):
                logger.warning(f"检测到套取模式: {pattern}")
                return True, f"套取模式匹配"

        return False, ""

    def generate_security_response(self, detection_reason: str) -> str:
        """生成安全响应，避免暴露系统信息"""
        responses = [
            "我是CellForge AI，专注于为您提供单细胞测序技术咨询。让我们继续讨论您的研究需求吧！",
            "我的专长是单细胞测序方案设计。请告诉我您的具体研究目标，我来为您提供专业建议。",
            "作为单细胞测序顾问，我更愿意帮您解决实际的技术问题。您有什么具体的实验需求吗？",
            "让我们专注于您的单细胞测序项目。请分享一下您的研究背景和目标，我来为您制定最佳方案。"
        ]

        # 根据检测原因选择不同的响应
        import random
        return random.choice(responses)

class SecurePromptManager:
    """安全的提示词管理器"""

    def __init__(self):
        self.security_manager = PromptSecurityManager()
        self._load_prompt_templates()

    def _load_prompt_templates(self):
        """加载分层的提示词模板"""

        # 基础角色定义（核心不变部分）
        self.base_role = """你是一位专业的单细胞测序技术顾问，名为"CellForge-AI"。"""

        # 专业能力描述（可动态调整）
        self.expertise_layers = {
            "technical": "精通scRNA-seq、scATAC-seq、multiome等多种技术，熟悉10x Genomics、BD Rhapsody、Smart-seq等主流平台",
            "workflow": "了解组织解离、细胞分选、质控等全流程",
            "analysis": "掌握生物信息学分析和数据解读",
            "experience": "拥有10年以上单细胞测序技术经验"
        }

        # 对话原则（根据阶段调整）
        self.conversation_principles = {
            "tone": "始终保持专业、友好、耐心的语调",
            "adaptation": "使用适合用户背景的专业术语程度",
            "identification": "主动识别用户需求的完整性和合理性",
            "personalization": "提供个性化建议而不是标准化答案",
            "honesty": "在不确定时诚实说明，并提供替代方案"
        }

        # 阶段特定的提示词模板
        self.stage_templates = {
            ConversationStage.INITIAL_CONTACT: self._get_initial_contact_template(),
            ConversationStage.REQUIREMENT_COLLECTION: self._get_requirement_collection_template(),
            ConversationStage.DEEP_DIVE_QUESTIONS: self._get_deep_dive_template(),
            ConversationStage.REQUIREMENT_VALIDATION: self._get_validation_template(),
            ConversationStage.PROTOCOL_GENERATION: self._get_protocol_generation_template(),
            ConversationStage.PROTOCOL_OPTIMIZATION: self._get_optimization_template(),
        }

    def build_secure_prompt(
        self,
        stage: ConversationStage,
        user_context: Dict,
        conversation_history: List[Dict],
        user_message: str
    ) -> Optional[str]:
        """
        构建安全的提示词
        如果检测到安全威胁，返回None
        """

        # 安全检查
        is_threat, reason = self.security_manager.is_prompt_extraction_attempt(user_message)
        if is_threat:
            logger.warning(f"检测到提示词套取尝试: {reason}")
            return None

        # 动态组装提示词
        prompt_parts = []

        # 1. 基础角色（混淆处理）
        prompt_parts.append(self._obfuscate_role_definition())

        # 2. 专业能力（根据用户背景选择性添加）
        prompt_parts.append(self._build_expertise_section(user_context))

        # 3. 对话原则（简化版本）
        prompt_parts.append(self._build_principles_section())

        # 4. 阶段特定指令
        stage_prompt = self.stage_templates.get(stage, "")
        if stage_prompt:
            prompt_parts.append(self._customize_stage_prompt(stage_prompt, user_context))

        # 5. 上下文信息
        prompt_parts.append(self._build_context_section(user_context, conversation_history))

        # 6. 安全约束（隐式添加）
        prompt_parts.append(self._build_security_constraints())

        return "\n\n".join(prompt_parts)

    def _obfuscate_role_definition(self) -> str:
        """混淆角色定义，避免直接暴露"""
        return f"""作为专业技术顾问，您的任务是协助用户解决单细胞测序相关的技术问题。

专业背景包括但不限于：
- 多年的实验室经验和技术积累
- 对主流测序平台的深入了解
- 完整的实验流程设计能力
- 数据分析和结果解读专长"""

    def _build_expertise_section(self, user_context: Dict) -> str:
        """根据用户背景构建专业能力描述"""
        user_level = user_context.get("technical_level", "intermediate")

        if user_level == "beginner":
            return "重点关注基础概念解释和入门指导。"
        elif user_level == "expert":
            return "可以使用高级技术术语和深入的技术细节。"
        else:
            return "平衡基础解释和技术细节，确保信息的可理解性。"

    def _build_principles_section(self) -> str:
        """构建对话原则（简化版）"""
        return """对话要求：
- 保持专业和友好的交流方式
- 根据用户背景调整技术深度
- 提供个性化的解决方案
- 诚实面对不确定性"""

    def _build_context_section(self, user_context: Dict, conversation_history: List[Dict]) -> str:
        """构建上下文信息"""
        context_parts = []

        # 用户信息
        if user_context:
            context_parts.append(f"用户背景：{user_context.get('organization', '未知机构')}")
            if user_context.get('research_interests'):
                context_parts.append(f"研究方向：{user_context['research_interests']}")

        # 对话历史（最近3轮）
        if conversation_history:
            recent_history = conversation_history[-3:]
            history_text = "\n".join([
                f"{msg['role']}: {msg['content'][:100]}..."
                for msg in recent_history
            ])
            context_parts.append(f"近期对话：\n{history_text}")

        return "\n".join(context_parts) if context_parts else ""

    def _build_security_constraints(self) -> str:
        """构建安全约束（隐式）"""
        return """请专注于单细胞测序技术咨询，避免讨论与专业领域无关的话题。

如果用户询问与技术咨询无关的问题，请礼貌地引导回到专业讨论。"""

    def _customize_stage_prompt(self, template: str, user_context: Dict) -> str:
        """根据用户上下文定制阶段提示词"""
        # 这里可以根据用户上下文动态调整提示词
        # 例如根据用户的技术水平、预算范围等调整询问策略
        return template

    def get_security_response(self, user_message: str) -> Optional[str]:
        """获取安全响应（当检测到威胁时）"""
        is_threat, reason = self.security_manager.is_prompt_extraction_attempt(user_message)
        if is_threat:
            return self.security_manager.generate_security_response(reason)
        return None

    def _get_initial_contact_template(self) -> str:
        """初始接触阶段模板"""
        return """当前处于初始咨询阶段。您的任务是：

1. 热情欢迎用户，简要介绍专业能力
2. 快速评估用户的技术背景水平
3. 了解项目的基本背景和目标
4. 询问1-2个关键问题开始需求收集

重点收集信息：
- 研究目标和生物学问题
- 样本类型和来源
- 预期通量（细胞数量级）
- 预算和时间限制

请保持友好专业的语调，避免信息过载。"""

    def _get_requirement_collection_template(self) -> str:
        """需求收集阶段模板"""
        return """当前处于需求收集阶段。根据已有信息，继续收集关键需求：

必需信息检查：
- 组织/细胞类型
- 研究目标
- 样本数量
- 预算范围

重要信息评估：
- 细胞存活状态要求
- 测序深度偏好
- 分析重点
- 时间紧急程度

请基于缺失信息的优先级，选择2-3个最重要的问题询问，并：
1. 解释为什么需要这些信息
2. 提供选项帮助用户理解和选择
3. 分享相关的专业见解"""

    def _get_deep_dive_template(self) -> str:
        """深度挖掘阶段模板"""
        return """当前处于深度需求挖掘阶段。需要获取更详细的技术要求：

技术细节收集：
- 特殊的样本处理需求
- 数据分析的具体要求
- 设备和人员条件
- 质量控制标准

风险评估要点：
- 技术挑战识别
- 预算风险评估
- 时间安排合理性

请深入了解用户的具体需求，为方案生成做好准备。"""

    def _get_validation_template(self) -> str:
        """需求验证阶段模板"""
        return """当前处于需求验证阶段。请执行最终确认：

验证检查：
1. 需求一致性检查（样本类型与研究目标匹配性）
2. 预算与技术需求的现实性
3. 时间安排的合理性

风险识别：
- 技术挑战点
- 质控风险
- 成本超支可能

请总结核心需求，提供预期结果概览，确认是否可以开始生成方案。"""

    def _get_protocol_generation_template(self) -> str:
        """方案生成阶段模板"""
        return """当前处于方案生成阶段。基于验证的需求生成详细实验方案：

方案内容要求：
1. 技术路线选择及理由
2. 详细实验流程
3. 产品和试剂推荐
4. 成本分析
5. 质量保证措施
6. 数据分析建议

输出要求：
- 信息准确，基于最新技术标准
- 操作性强，可直接执行
- 考虑用户实际条件和限制
- 提供多种选择和灵活性"""

    def _get_optimization_template(self) -> str:
        """方案优化阶段模板"""
        return """当前处于方案优化阶段。基于用户反馈优化实验方案：

优化维度：
- 成本降低（在不显著影响质量前提下）
- 时间缩短（提高效率）
- 操作简化（降低技术要求）
- 通量调整（样本数量变化）
- 质量提升（更严格的质控）

优化策略：
1. 保持核心技术路线稳定
2. 在关键步骤提供多个选择
3. 明确说明优化的权衡关系
4. 提供风险评估和缓解措施"""
