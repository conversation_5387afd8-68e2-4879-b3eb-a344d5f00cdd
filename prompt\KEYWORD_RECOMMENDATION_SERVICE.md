# 文献关键词推荐服务 (Literature Keywords Recommendation Service)

## 概述

文献关键词推荐服务是 CellForge AI 的核心模块之一，专门为单细胞生物学研究提供智能关键词提取、翻译和文献搜索增强功能。该服务将用户的中文查询转换为精确的英文科学术语，并提供领域分类和搜索策略建议。

## 核心功能

### 1. AI 驱动的关键词提取
- **基于规则的提取**: 使用专业术语映射表和正则表达式模式
- **AI 增强提取**: 集成 OpenAI API 进行智能关键词识别
- **混合策略**: 结合规则和 AI 方法，确保准确性和覆盖率

### 2. 中英文术语翻译
- **专业术语库**: 包含 80+ 单细胞生物学专业术语的中英对照
- **智能匹配**: 支持部分匹配和模糊翻译
- **领域优化**: 针对生命科学研究优化的翻译准确性

### 3. 学科领域分类
支持以下研究领域的自动分类：
- **免疫学** (Immunology)
- **肿瘤学** (Oncology) 
- **神经科学** (Neuroscience)
- **发育生物学** (Developmental Biology)
- **代谢学** (Metabolism)
- **干细胞研究** (Stem Cell Research)

### 4. 增强文献搜索
- **关键词驱动搜索**: 基于提取的关键词进行多轮搜索
- **智能去重**: 避免重复结果
- **相关性排序**: 按照综合评分排序结果

## API 端点

### 1. 关键词推荐 
```
POST /api/literature/keyword-recommendation
```

**请求体**:
```json
{
    "user_query": "T细胞在肿瘤微环境中的单细胞分析",
    "context": {
        "user_profile": {},
        "research_domain": "immunology"
    },
    "max_keywords": 15
}
```

**响应**:
```json
{
    "user_query": "T细胞在肿瘤微环境中的单细胞分析",
    "extracted_keywords": {
        "chinese": ["T细胞", "肿瘤微环境", "单细胞"],
        "english": ["T cell", "tumor microenvironment", "single cell"]
    },
    "domain_classification": {
        "immunology": ["T cell", "immune"],
        "oncology": ["tumor", "cancer"]
    },
    "keyword_analysis": {
        "total_keywords": 8,
        "confidence_score": 0.92,
        "coverage_domains": ["immunology", "oncology"]
    },
    "search_strategies": {
        "primary_search_terms": ["T cell", "tumor microenvironment"],
        "combined_queries": ["T cell AND tumor microenvironment"]
    },
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### 2. 增强文献搜索
```
POST /api/literature/search-enhanced
```

**请求体**:
```json
{
    "user_query": "PBMC免疫细胞10x测序",
    "context": {},
    "use_keyword_enhancement": true,
    "max_keywords": 10,
    "top_k": 10
}
```

**响应**:
```json
{
    "search_request": {
        "user_query": "PBMC免疫细胞10x测序",
        "use_keyword_enhancement": true,
        "search_terms_used": ["PBMC免疫细胞10x测序", "PBMC", "immune cells", "10x Genomics"]
    },
    "keyword_analysis": {
        "extracted_keywords": {...},
        "domain_classification": {...}
    },
    "literature_results": [...],
    "search_statistics": {
        "total_results_found": 25,
        "returned_results": 10,
        "search_terms_count": 4,
        "keyword_enhancement_used": true
    }
}
```

### 3. 关键词域信息
```
GET /api/literature/keyword-domains
```

返回支持的学科领域、术语映射示例和搜索建议。

## 技术架构

### 核心类: AIKeywordGenerator

```python
class AIKeywordGenerator:
    def __init__(self):
        self.ai_service = AIService()
        self.terminology_mapping = {...}  # 中英术语映射
        self.domain_keywords = {...}      # 领域关键词
    
    async def generate_keywords_from_query(self, user_query, context, max_keywords):
        # 主要入口方法
        pass
    
    def _extract_rule_based_keywords(self, query):
        # 基于规则的关键词提取
        pass
    
    async def _generate_ai_enhanced_keywords(self, query, context):
        # AI 增强的关键词生成
        pass
    
    def _translate_to_english(self, chinese_keywords):
        # 中英文翻译
        pass
    
    def _classify_domain(self, keywords):
        # 领域分类
        pass
```

### 与 AI 服务集成

AI 服务新增了 `generate_keyword_enhanced_response` 方法，可以在生成回复时自动集成关键词分析：

```python
async def generate_keyword_enhanced_response(
    self,
    message: str,
    context: Dict,
    use_keyword_enhancement: bool = True,
    enable_literature_search: bool = False
) -> AIResponse:
    # 集成关键词提取和文献推荐的增强回复
    pass
```

## 专业术语映射

### 细胞类型
| 中文 | 英文 |
|------|------|
| 单细胞 | single cell |
| T细胞 | T cell |
| B细胞 | B cell |
| NK细胞 | NK cell |
| 巨噬细胞 | macrophage |
| 神经元 | neuron |

### 技术平台
| 中文 | 英文 |
|------|------|
| 10x基因组学 | 10x Genomics |
| Smart-seq | Smart-seq |
| 空间转录组 | spatial transcriptomics |

### 生物过程
| 中文 | 英文 |
|------|------|
| 细胞分化 | cell differentiation |
| 基因表达 | gene expression |
| 通路分析 | pathway analysis |
| 细胞周期 | cell cycle |

## 使用示例

### Python 客户端示例

```python
import asyncio
from app.services.ai_keyword_generator import get_keyword_generator

async def example_usage():
    # 获取关键词生成器
    generator = await get_keyword_generator()
    
    # 生成关键词
    result = await generator.generate_keywords_from_query(
        user_query="我想研究心肌细胞在心脏发育过程中的单细胞转录组",
        max_keywords=12
    )
    
    print("提取的英文关键词:", result['extracted_keywords']['english'])
    print("领域分类:", result['domain_classification'])
    print("搜索策略:", result['search_strategies'])

# 运行示例
asyncio.run(example_usage())
```

### API 调用示例

```bash
# 关键词推荐
curl -X POST "http://localhost:8000/api/literature/keyword-recommendation" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
       "user_query": "PBMC免疫细胞亚群分析",
       "max_keywords": 10
     }'

# 增强搜索
curl -X POST "http://localhost:8000/api/literature/search-enhanced" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
       "user_query": "干细胞分化轨迹分析",
       "use_keyword_enhancement": true,
       "top_k": 5
     }'
```

## 错误处理

服务包含多层错误处理机制：

1. **AI 服务故障**: 自动回退到基于规则的方法
2. **关键词提取失败**: 返回原始查询词
3. **翻译失败**: 保持原文或使用部分匹配
4. **搜索失败**: 提供详细错误信息

## 配置选项

在 `app/core/config.py` 中可配置：

```python
# 是否使用真实 AI 服务
USE_REAL_AI = True

# AI 模型配置
OPENAI_MODEL = "gpt-3.5-turbo"
OPENAI_API_KEY = "your-api-key"

# 关键词服务配置
MAX_KEYWORDS_DEFAULT = 15
CONFIDENCE_THRESHOLD = 0.7
```

## 性能优化

1. **缓存策略**: 常用查询结果缓存
2. **批量处理**: 支持批量关键词提取
3. **异步处理**: 全异步架构避免阻塞
4. **资源管理**: 合理的超时和重试机制

## 测试

运行测试脚本验证功能：

```bash
cd backend
python3 test_keyword_service.py
```

测试覆盖：
- ✅ 基础关键词提取
- ✅ 异步功能测试
- ✅ API 端点验证
- ✅ 错误处理机制

## 扩展计划

1. **更多领域支持**: 添加更多生物学研究领域
2. **智能学习**: 基于用户反馈优化关键词准确性
3. **多语言支持**: 支持更多语言的术语翻译
4. **实时更新**: 定期更新术语库和领域分类

## 联系支持

如有问题或建议，请联系 CellForge AI 技术团队。