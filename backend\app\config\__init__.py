"""
配置系统初始化脚本
用于初始化和测试配置系统
"""
from app.config.config_manager import get_config_manager
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def initialize_config_system():
    """初始化配置系统"""
    try:
        logger.info("正在初始化配置系统...")
        
        # 获取配置管理器
        config_manager = get_config_manager()
        
        # 启用自动重载
        config_manager.enable_auto_reload(True, interval=1.0)
        
        # 验证所有配置文件
        validation_results = config_manager.validate_all_configs()
        logger.info(f"配置验证结果: {validation_results}")
        
        # 列出可用配置
        available_configs = config_manager.list_available_configs()
        logger.info(f"可用配置文件: {available_configs}")
        
        # 获取缓存信息
        cache_info = config_manager.get_cache_info()
        logger.info(f"缓存信息: {cache_info}")
        
        # 测试加载每个配置文件
        test_configs = ['literature_categories', 'terminology_mapping', 'domain_keywords', 'platform_config']
        for config_name in test_configs:
            try:
                config = config_manager.get_config(config_name)
                logger.info(f"✅ 成功加载配置 {config_name}: {len(config)} 项")
            except Exception as e:
                logger.error(f"❌ 加载配置 {config_name} 失败: {e}")
        
        logger.info("配置系统初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"配置系统初始化失败: {e}")
        return False


def test_configuration_usage():
    """测试配置系统的使用"""
    try:
        logger.info("测试配置系统使用...")
        
        config_manager = get_config_manager()
        
        # 测试文献分类配置
        literature_config = config_manager.get_config('literature_categories')
        categories = literature_config.get('categories', [])
        logger.info(f"文献分类数量: {len(categories)}")
        
        # 测试术语映射配置
        terminology_config = config_manager.get_config('terminology_mapping')
        mappings = terminology_config.get('terminology_mapping', {})
        total_mappings = sum(len(v) if isinstance(v, dict) else 0 for v in mappings.values())
        logger.info(f"术语映射总数: {total_mappings}")
        
        # 测试领域关键词配置
        domain_config = config_manager.get_config('domain_keywords')
        domains = domain_config.get('domain_keywords', {})
        logger.info(f"研究领域数量: {len(domains)}")
        
        # 测试平台配置
        platform_config = config_manager.get_config('platform_config')
        api_endpoints = platform_config.get('api_endpoints', {})
        logger.info(f"API端点数量: {len(api_endpoints)}")
        
        # 测试配置片段获取
        test_section = config_manager.get_config_section(
            'platform_config', 
            'api_endpoints.literature_search.enabled', 
            False
        )
        logger.info(f"文献搜索功能启用状态: {test_section}")
        
        logger.info("配置系统使用测试完成")
        return True
        
    except Exception as e:
        logger.error(f"配置系统使用测试失败: {e}")
        return False


def demonstrate_hot_reload():
    """演示热重载功能"""
    try:
        logger.info("演示热重载功能...")
        
        config_manager = get_config_manager()
        
        # 获取初始配置
        initial_config = config_manager.get_config('platform_config')
        initial_app_name = initial_config.get('platform_settings', {}).get('application_name', '')
        logger.info(f"初始应用名称: {initial_app_name}")
        
        # 模拟配置更新
        update_data = {
            'platform_settings': {
                'application_name': 'CellForge AI - Updated',
                'version': '1.0.1'
            }
        }
        
        config_manager.update_config('platform_config', update_data, save_to_file=True)
        logger.info("配置已更新")
        
        # 获取更新后的配置
        updated_config = config_manager.get_config('platform_config')
        updated_app_name = updated_config.get('platform_settings', {}).get('application_name', '')
        logger.info(f"更新后应用名称: {updated_app_name}")
        
        # 恢复原始配置
        restore_data = {
            'platform_settings': {
                'application_name': 'CellForge AI',
                'version': '1.0.0'
            }
        }
        config_manager.update_config('platform_config', restore_data, save_to_file=True)
        logger.info("配置已恢复")
        
        logger.info("热重载功能演示完成")
        return True
        
    except Exception as e:
        logger.error(f"热重载功能演示失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("🚀 开始配置系统初始化和测试")
    
    success_count = 0
    total_tests = 3
    
    # 1. 初始化配置系统
    if initialize_config_system():
        success_count += 1
        logger.info("✅ 配置系统初始化: 成功")
    else:
        logger.error("❌ 配置系统初始化: 失败")
    
    # 2. 测试配置使用
    if test_configuration_usage():
        success_count += 1
        logger.info("✅ 配置系统使用测试: 成功")
    else:
        logger.error("❌ 配置系统使用测试: 失败")
    
    # 3. 演示热重载
    if demonstrate_hot_reload():
        success_count += 1
        logger.info("✅ 热重载功能演示: 成功")
    else:
        logger.error("❌ 热重载功能演示: 失败")
    
    # 总结
    logger.info(f"🎯 测试完成: {success_count}/{total_tests} 项测试通过")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过，配置系统已准备就绪！")
        return True
    else:
        logger.warning("⚠️ 部分测试失败，请检查配置系统")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)