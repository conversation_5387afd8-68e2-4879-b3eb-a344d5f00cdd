# CellForge AI 项目个人化记忆库

## 📋 项目基本信息

**项目名称**: CellForge AI - 单细胞测序方案咨询系统  
**创建时间**: 2024年12月  
**用户环境**: Windows 10 (10.0.26100), PowerShell  
**工作目录**: C:\Users\<USER>\Desktop\Dev\CellForge AI  

## 🛠️ 个人化配置调整

### 端口配置优化 (2024-12-19)
**问题**: 用户环境中已有多个 Docker 容器服务，存在端口冲突风险  
**解决方案**: 调整所有服务端口，使用更高端口号避免冲突

**端口映射调整**:
- FastAPI Backend: `8000` → `8800`
- PostgreSQL: `5432` → `15432`
- Redis: `6379` → `16379`
- ChromaDB: `8001` → `8801`
- Nginx (生产): `80/443` → `8080/8443`
- Prometheus: `9090` → `19090`
- Grafana: `3001` → `13001`

**访问地址更新**:
- 后端 API: `http://localhost:8800`
- API 文档: `http://localhost:8800/docs`
- 数据库连接: `localhost:15432`
- Redis 连接: `localhost:16379`
- ChromaDB: `http://localhost:8801`
- 监控面板: `http://localhost:13001` (Grafana)

## 🎯 技术偏好和要求

### 编程语言偏好
- **后端**: Python (FastAPI)
- **前端**: TypeScript + React/Next.js
- **数据库**: PostgreSQL + Redis + ChromaDB
- **部署**: Docker + Docker Compose

### 开发习惯
- 使用中文注释和文档
- 偏好详细的 README 和技术文档
- 重视代码的可维护性和扩展性
- 关注性能优化和安全性

## 🏗️ 架构设计决策

### 后端框架选择: FastAPI
**原因**:
1. 与前端 Next.js 完美配合
2. 原生异步支持，适合 AI 应用
3. 自动生成 API 文档
4. 强大的类型检查和验证
5. Python 生态对单细胞分析友好

### 数据库架构
- **主数据库**: PostgreSQL (关系型数据，用户信息、对话记录等)
- **缓存层**: Redis (会话缓存、频繁查询缓存)
- **向量数据库**: ChromaDB (知识库语义搜索)

### AI 服务集成
- **模型选择**: OpenAI GPT-4
- **增强技术**: RAG (检索增强生成)
- **专业领域**: 单细胞测序方案咨询

## 📝 项目需求记录

### 核心功能模块
1. **智能对话系统** - AI 驱动的专业咨询
2. **知识库管理** - 文档上传、语义搜索、版本控制
3. **客户画像分析** - 多维用户行为分析
4. **方案生成引擎** - 智能配置和成本估算
5. **数据分析平台** - 业务指标和用户洞察

### 特殊要求
- 支持实时对话 (WebSocket)
- 流式响应处理
- 多语言支持 (主要中文)
- 移动端友好设计
- 高并发处理能力

## 🔧 开发环境配置

### 容器化部署
- 开发环境: `docker-compose up -d`
- 生产环境: Kubernetes + 负载均衡
- 监控方案: Prometheus + Grafana

### 安全配置
- JWT 认证机制
- RBAC 权限控制
- API 频率限制
- HTTPS 强制加密

## 💡 优化建议和计划

### 性能优化
- [ ] 实现智能缓存策略
- [ ] 数据库查询优化
- [ ] CDN 配置
- [ ] 负载均衡配置

### 功能扩展
- [ ] 多模态输入支持 (图片、文档)
- [ ] 批量方案生成
- [ ] 高级数据可视化
- [ ] 移动端 App 开发

## 📚 学习和参考资源

### 单细胞测序相关
- Scanpy 文档和教程
- Single Cell Best Practices
- 10X Genomics 技术文档

### 技术栈文档
- FastAPI 官方文档
- LangChain 开发指南
- ChromaDB 使用手册

## 🚨 注意事项和提醒

### 开发注意点
1. 所有 API 响应使用中文
2. 错误处理要友好和详细
3. 日志记录要完整
4. 单元测试覆盖率 > 80%

### 部署注意点
1. 环境变量安全配置
2. 数据库备份策略
3. 监控告警配置
4. 性能基准测试

### 安全注意点
1. API 密钥保护
2. 用户数据隐私
3. 输入验证和过滤
4. 定期安全审计

---

**最后更新**: 2024-12-19  
**维护者**: CellForge AI 开发团队 