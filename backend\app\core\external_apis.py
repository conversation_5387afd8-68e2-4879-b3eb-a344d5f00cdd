"""
外部API配置和管理
统一管理所有外部API的配置、认证和限流
"""
from typing import Optional, Dict, Any
from dataclasses import dataclass
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


@dataclass
class APIConfig:
    """API配置类"""
    name: str
    base_url: str
    api_key: Optional[str] = None
    rate_limit: int = 10
    enabled: bool = True
    headers: Optional[Dict[str, str]] = None
    
    def is_available(self) -> bool:
        """检查API是否可用"""
        return self.enabled and (self.api_key is not None or not self.requires_key())
    
    def requires_key(self) -> bool:
        """检查是否需要API密钥"""
        return self.name in ["pubmed", "semantic_scholar", "google_scholar"]  # OpenAlex不需要API密钥
    
    def get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {"User-Agent": "CellForge-AI/1.0"}
        
        if self.headers:
            headers.update(self.headers)
            
        if self.api_key:
            if self.name == "semantic_scholar":
                headers["x-api-key"] = self.api_key
            elif self.name == "pubmed":
                # PubMed通常不需要在header中放API key，而是在参数中
                pass
                
        return headers


class ExternalAPIManager:
    """外部API管理器"""
    
    def __init__(self):
        self.apis = self._initialize_apis()
        self._log_api_status()
    
    def _initialize_apis(self) -> Dict[str, APIConfig]:
        """初始化API配置"""
        apis = {
            "pubmed": APIConfig(
                name="pubmed",
                base_url="https://eutils.ncbi.nlm.nih.gov/entrez/eutils",
                api_key=settings.PUBMED_API_KEY,
                rate_limit=settings.PUBMED_RATE_LIMIT,
                enabled=True
            ),
            "google_scholar": APIConfig(
                name="google_scholar",
                base_url="https://serpapi.com/search",  # 使用SerpAPI
                api_key=settings.SERPAPI_KEY,
                rate_limit=settings.GOOGLE_SCHOLAR_RATE_LIMIT,
                enabled=settings.GOOGLE_SCHOLAR_ENABLED
            ),
            "semantic_scholar": APIConfig(
                name="semantic_scholar",
                base_url="https://api.semanticscholar.org/graph/v1",
                api_key=settings.SEMANTIC_SCHOLAR_API_KEY,
                rate_limit=settings.SEMANTIC_SCHOLAR_RATE_LIMIT,
                enabled=True
            ),
            "biorxiv": APIConfig(
                name="biorxiv",
                base_url="https://api.biorxiv.org",
                rate_limit=settings.BIORXIV_RATE_LIMIT,
                enabled=settings.BIORXIV_API_ENABLED
            ),
            "openalex": APIConfig(
                name="openalex",
                base_url="https://api.openalex.org",
                rate_limit=10,  # OpenAlex默认限制
                enabled=True  # OpenAlex是免费的，默认启用
            )
        }
        
        return apis
    
    def get_api_config(self, api_name: str) -> Optional[APIConfig]:
        """获取API配置"""
        return self.apis.get(api_name)
    
    def is_api_available(self, api_name: str) -> bool:
        """检查API是否可用"""
        config = self.get_api_config(api_name)
        return config.is_available() if config else False
    
    def get_available_apis(self) -> Dict[str, APIConfig]:
        """获取所有可用的API"""
        return {name: config for name, config in self.apis.items() 
                if config.is_available()}
    
    def is_literature_search_enabled(self) -> bool:
        """检查文献搜索功能是否启用"""
        if not settings.LITERATURE_SEARCH_ENABLED:
            return False

        # 文献搜索功能启用时，即使没有外部API也可以使用本地文献库
        return True
    
    def _log_api_status(self):
        """记录API状态"""
        logger.info("=== 外部API状态检查 ===")
        logger.info(f"文献搜索功能: {'启用' if settings.LITERATURE_SEARCH_ENABLED else '禁用'}")
        
        for name, config in self.apis.items():
            status = "可用" if config.is_available() else "不可用"
            reason = ""
            
            if not config.enabled:
                reason = "(已禁用)"
            elif config.requires_key() and not config.api_key:
                reason = "(缺少API密钥)"
            
            logger.info(f"  {name}: {status} {reason}")
        
        if self.is_literature_search_enabled():
            logger.info("✅ 文献搜索功能已就绪")
        else:
            logger.warning("⚠️ 文献搜索功能不可用")


# 全局API管理器实例
api_manager = ExternalAPIManager()


def get_api_manager() -> ExternalAPIManager:
    """获取API管理器实例"""
    return api_manager


def check_literature_prerequisites() -> Dict[str, Any]:
    """检查文献搜索功能的前置条件"""
    manager = get_api_manager()
    available_apis = manager.get_available_apis()
    
    return {
        "literature_search_enabled": settings.LITERATURE_SEARCH_ENABLED,
        "available_apis": list(available_apis.keys()),
        "total_apis": len(manager.apis),
        "available_count": len(available_apis),
        "is_ready": manager.is_literature_search_enabled(),
        "recommendations": _get_setup_recommendations(manager)
    }


def _get_setup_recommendations(manager: ExternalAPIManager) -> list:
    """获取设置建议"""
    recommendations = []
    
    if not settings.LITERATURE_SEARCH_ENABLED:
        recommendations.append("在配置中启用 LITERATURE_SEARCH_ENABLED")
    
    for name, config in manager.apis.items():
        if not config.is_available() and config.requires_key():
            recommendations.append(f"配置 {name.upper()}_API_KEY 以启用{name}搜索")
    
    if len(manager.get_available_apis()) == 0:
        recommendations.append("至少配置一个外部API以启用文献搜索功能")
    
    return recommendations
