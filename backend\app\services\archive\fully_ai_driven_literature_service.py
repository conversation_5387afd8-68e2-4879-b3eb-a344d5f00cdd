"""
完全AI驱动的文献服务 - 兼容性接口
将AI驱动的文献推荐功能重定向到现有的统一文献API服务
"""
from typing import Dict, List, Any, Optional
import logging

from app.services.unified_literature_api_service import get_unified_literature_api_service
from app.services.ai_service import AIService

logger = logging.getLogger(__name__)


class FullyAIDrivenRecommendationService:
    """完全AI驱动的推荐服务 - 基于统一文献API服务的兼容性实现"""
    
    def __init__(self):
        self.literature_service = get_unified_literature_api_service()
        self.ai_service = AIService()
    
    async def generate_ai_driven_recommendations(
        self,
        requirements: Dict[str, Any],
        user_context: str = ""
    ) -> Dict[str, Any]:
        """
        生成AI驱动的推荐
        """
        try:
            # 从需求中提取关键词
            research_goal = requirements.get("researchGoal", "")
            experiment_type = requirements.get("experimentType", "")
            sample_type = requirements.get("sampleType", "")
            
            # 构建搜索查询
            search_query = f"{research_goal} {experiment_type} {sample_type} single cell sequencing"
            
            # 搜索文献
            literature_results = await self.literature_service.search_literature(
                query=search_query,
                max_results=15
            )
            
            # 使用AI分析和推荐
            ai_analysis = await self._analyze_literature_with_ai(
                literature_results.get("papers", []),
                requirements,
                user_context
            )
            
            return {
                "ai_driven_recommendations": {
                    "literature_analysis": ai_analysis,
                    "recommended_papers": literature_results.get("papers", [])[:10],
                    "research_insights": ai_analysis.get("insights", []),
                    "methodology_recommendations": ai_analysis.get("methodologies", []),
                    "technical_considerations": ai_analysis.get("technical_notes", [])
                },
                "search_metadata": {
                    "query_used": search_query,
                    "total_papers_found": len(literature_results.get("papers", [])),
                    "ai_analysis_confidence": ai_analysis.get("confidence", "medium")
                }
            }
            
        except Exception as e:
            logger.error(f"AI驱动推荐失败: {str(e)}")
            return {
                "error": f"AI推荐失败: {str(e)}",
                "fallback_recommendations": {
                    "message": "请手动搜索相关文献或咨询专家",
                    "suggested_keywords": [research_goal, experiment_type, sample_type]
                }
            }
    
    async def _analyze_literature_with_ai(
        self,
        papers: List[Dict[str, Any]],
        requirements: Dict[str, Any],
        user_context: str
    ) -> Dict[str, Any]:
        """
        使用AI分析文献并生成洞察
        """
        try:
            if not papers:
                return {
                    "insights": ["未找到相关文献"],
                    "methodologies": ["建议咨询专家"],
                    "technical_notes": ["需要更多信息"],
                    "confidence": "low"
                }
            
            # 构建AI分析提示
            papers_summary = []
            for paper in papers[:5]:  # 只分析前5篇论文
                title = paper.get("title", "")
                abstract = paper.get("abstract", "")[:200]  # 限制摘要长度
                papers_summary.append(f"标题: {title}\n摘要: {abstract}")
            
            prompt = f"""
            基于以下文献信息和用户需求，提供专业的单细胞测序研究建议：
            
            用户需求：
            - 研究目标: {requirements.get('researchGoal', '')}
            - 实验类型: {requirements.get('experimentType', '')}
            - 样本类型: {requirements.get('sampleType', '')}
            - 用户背景: {user_context}
            
            相关文献：
            {chr(10).join(papers_summary)}
            
            请提供：
            1. 关键研究洞察（3-5个要点）
            2. 推荐的方法学（2-3个方法）
            3. 技术注意事项（2-3个要点）
            
            请用简洁专业的语言回答。
            """
            
            ai_response = await self.ai_service.generate_response(prompt)
            
            # 解析AI响应（简化版本）
            response_text = ai_response.get("content", "")
            
            return {
                "insights": self._extract_insights(response_text),
                "methodologies": self._extract_methodologies(response_text),
                "technical_notes": self._extract_technical_notes(response_text),
                "confidence": "medium",
                "ai_analysis_text": response_text
            }
            
        except Exception as e:
            logger.error(f"AI文献分析失败: {str(e)}")
            return {
                "insights": ["AI分析暂时不可用"],
                "methodologies": ["请参考标准流程"],
                "technical_notes": ["建议咨询专家"],
                "confidence": "low"
            }
    
    def _extract_insights(self, text: str) -> List[str]:
        """从AI响应中提取洞察"""
        # 简化的提取逻辑
        lines = text.split('\n')
        insights = []
        for line in lines:
            if '洞察' in line or '要点' in line or '发现' in line:
                insights.append(line.strip())
        return insights[:5] if insights else ["需要进一步分析"]
    
    def _extract_methodologies(self, text: str) -> List[str]:
        """从AI响应中提取方法学"""
        lines = text.split('\n')
        methodologies = []
        for line in lines:
            if '方法' in line or '技术' in line or '平台' in line:
                methodologies.append(line.strip())
        return methodologies[:3] if methodologies else ["标准单细胞测序流程"]
    
    def _extract_technical_notes(self, text: str) -> List[str]:
        """从AI响应中提取技术注意事项"""
        lines = text.split('\n')
        notes = []
        for line in lines:
            if '注意' in line or '建议' in line or '考虑' in line:
                notes.append(line.strip())
        return notes[:3] if notes else ["请咨询技术专家"]


# 全局服务实例
fully_ai_driven_service = FullyAIDrivenRecommendationService()


def get_fully_ai_driven_service() -> FullyAIDrivenRecommendationService:
    """获取完全AI驱动服务实例"""
    return fully_ai_driven_service
