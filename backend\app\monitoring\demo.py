"""
监控系统启动和演示脚本
"""

import asyncio
import logging
import time
import requests
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_monitoring_system():
    """测试监控系统功能"""
    base_url = "http://localhost:8000"
    
    print("🚀 开始测试CellForge AI监控系统...")
    
    # 测试基础健康检查
    print("\n1. 测试基础健康检查")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"   ✅ 基础健康检查: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"   ❌ 基础健康检查失败: {e}")
    
    # 测试监控系统健康检查
    print("\n2. 测试监控系统健康检查")
    try:
        response = requests.get(f"{base_url}/api/v1/api/monitoring/health")
        print(f"   ✅ 监控健康检查: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   📊 系统状态: {data['data']['summary']['overall_status']}")
            print(f"   📈 健康服务数: {data['data']['summary']['healthy_services']}")
            print(f"   ⚠️  降级服务数: {data['data']['summary']['degraded_services']}")
            print(f"   ❌ 不健康服务数: {data['data']['summary']['unhealthy_services']}")
    except Exception as e:
        print(f"   ❌ 监控健康检查失败: {e}")
    
    # 生成一些测试流量
    print("\n3. 生成测试流量以收集指标")
    test_endpoints = [
        "/api/v1/health",
        "/api/v1/api/monitoring/status",
        "/api/v1/api/monitoring/metrics",
    ]
    
    for endpoint in test_endpoints:
        for i in range(3):
            try:
                response = requests.get(f"{base_url}{endpoint}")
                print(f"   📡 请求 {endpoint}: {response.status_code}")
                time.sleep(0.5)  # 模拟间隔
            except Exception as e:
                print(f"   ❌ 请求失败 {endpoint}: {e}")
    
    # 等待指标收集
    print("\n4. 等待指标收集...")
    time.sleep(2)
    
    # 测试性能指标
    print("\n5. 测试性能指标")
    try:
        response = requests.get(f"{base_url}/api/v1/api/monitoring/metrics")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 性能指标获取成功")
            print(f"   📊 API指标数: {len(data['data']['api_metrics'])}")
            print(f"   📈 系统指标: {list(data['data']['system_metrics'].keys())}")
            print(f"   💼 业务指标数: {len(data['data']['business_metrics'])}")
        else:
            print(f"   ⚠️  性能指标状态: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 性能指标获取失败: {e}")
    
    # 测试性能分析
    print("\n6. 测试性能分析")
    try:
        response = requests.get(f"{base_url}/api/v1/api/monitoring/performance")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 性能分析成功")
            print(f"   🎯 整体性能得分: {data['data']['overall_score']}")
            print(f"   ⚠️  性能问题数: {len(data['data']['issues'])}")
            print(f"   💡 建议数: {len(data['data']['recommendations'])}")
            
            # 显示性能问题
            if data['data']['issues']:
                print("   📋 性能问题详情:")
                for issue in data['data']['issues'][:3]:  # 只显示前3个
                    print(f"      - {issue['severity']}: {issue['description']}")
        else:
            print(f"   ⚠️  性能分析状态: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 性能分析失败: {e}")
    
    # 测试告警信息
    print("\n7. 测试告警信息")
    try:
        response = requests.get(f"{base_url}/api/v1/api/monitoring/alerts")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 告警信息获取成功")
            print(f"   🚨 总告警数: {data['data']['total_alerts']}")
            print(f"   🔴 严重告警: {data['data']['critical_alerts']}")
            print(f"   🟡 警告告警: {data['data']['warning_alerts']}")
            
            # 显示最近的告警
            if data['data']['alerts']:
                print("   📋 最近告警:")
                for alert in data['data']['alerts'][:3]:  # 只显示前3个
                    print(f"      - {alert['severity']}: {alert['message']}")
        else:
            print(f"   ⚠️  告警信息状态: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 告警信息获取失败: {e}")
    
    # 测试监控状态
    print("\n8. 测试监控系统状态")
    try:
        response = requests.get(f"{base_url}/api/v1/api/monitoring/status")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 监控状态获取成功")
            print(f"   🔄 指标收集器: {data['data']['metrics_collector']['status']}")
            print(f"   🏥 健康检查器: {data['data']['health_checker']['status']}")
            print(f"   📊 性能分析器: {data['data']['performance_analyzer']['status']}")
        else:
            print(f"   ⚠️  监控状态码: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 监控状态获取失败: {e}")
    
    print("\n🎉 监控系统测试完成！")
    print("\n📖 使用说明:")
    print("   - 访问 http://localhost:8000/docs 查看完整API文档")
    print("   - 监控端点均在 /api/v1/api/monitoring/ 路径下")
    print("   - 系统会自动收集所有API请求的性能指标")
    print("   - 健康检查每30秒运行一次")
    print("   - 性能分析每5分钟缓存一次结果")


if __name__ == "__main__":
    test_monitoring_system()