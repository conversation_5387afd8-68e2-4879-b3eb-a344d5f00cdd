"""
SmartLiteratureLinker - 智能文献链接生成系统
基于用户研究意图和方向分析，动态生成最相关的文献搜索链接
"""
import asyncio
import json
import logging
import urllib.parse
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass

from app.services.ai_service import AIService

logger = logging.getLogger(__name__)


@dataclass
class SearchLink:
    """搜索链接数据结构"""
    platform: str
    title: str
    url: str
    description: str
    relevance_score: float
    search_strategy: str
    expected_results: str


@dataclass
class SearchLinkPackage:
    """搜索链接包数据结构"""
    research_focus: str
    total_links: int
    links: List[SearchLink]
    generation_timestamp: str
    optimization_notes: str


class SmartLiteratureLinker:
    """智能文献链接生成器 - AI驱动的个性化搜索链接生成"""
    
    def __init__(self):
        self.ai_service = AIService()
        
        # 主要学术搜索平台配置
        self.platforms = {
            "pubmed": {
                "base_url": "https://pubmed.ncbi.nlm.nih.gov/",
                "search_path": "?term=",
                "name": "PubMed",
                "specialty": "生物医学文献",
                "advanced_features": ["MeSH terms", "Publication types", "Date filters"]
            },
            "google_scholar": {
                "base_url": "https://scholar.google.com/scholar",
                "search_path": "?q=",
                "name": "Google Scholar",
                "specialty": "跨学科学术搜索",
                "advanced_features": ["Citation tracking", "Author profiles", "Related articles"]
            },
            "pubmed_central": {
                "base_url": "https://www.ncbi.nlm.nih.gov/pmc/",
                "search_path": "?term=",
                "name": "PMC",
                "specialty": "开放获取全文",
                "advanced_features": ["Full-text search", "Figure search", "Data availability"]
            },
            "openalex": {
                "base_url": "https://openalex.org/works",
                "search_path": "?search=",
                "name": "OpenAlex",
                "specialty": "免费学术文献摘要数据库",
                "advanced_features": ["Citation analysis", "Institution tracking", "Free API", "Rich metadata"]
            },
            "biorxiv": {
                "base_url": "https://www.biorxiv.org/search/",
                "search_path": "",
                "name": "bioRxiv",
                "specialty": "预印本文献",
                "advanced_features": ["Latest research", "Subject categories", "Author networks"]
            },
            "science_direct": {
                "base_url": "https://www.sciencedirect.com/search",
                "search_path": "?qs=",
                "name": "ScienceDirect",
                "specialty": "Elsevier期刊",
                "advanced_features": ["Journal filtering", "Article types", "Advanced search"]
            },
            "semantic_scholar": {
                "base_url": "https://www.semanticscholar.org/search",
                "search_path": "?q=",
                "name": "Semantic Scholar",
                "specialty": "AI驱动的语义搜索",
                "advanced_features": ["Citation context", "Influential papers", "Research trends"]
            }
        }
        
        # AI提示词模板
        self.link_generation_template = """
        你是一位专业的文献检索专家，擅长为单细胞生物学研究人员生成最优的文献搜索策略。

        用户研究方向分析:
        {research_direction}

        用户研究意图:
        {intent_analysis}

        用户特征:
        {user_profile}

        可用搜索平台:
        {available_platforms}

        请为用户生成6-8个高度优化的文献搜索链接，确保涵盖不同的搜索策略和平台优势。

        输出JSON格式：
        {{
            "search_strategy_overview": {{
                "primary_strategy": "主要搜索策略描述",
                "coverage_approach": "覆盖范围策略",
                "optimization_focus": "优化重点说明"
            }},
            "generated_links": [
                {{
                    "platform": "pubmed/google_scholar/pubmed_central/openalex/biorxiv/science_direct/semantic_scholar",
                    "title": "搜索目标标题（简洁且描述性）",
                    "search_terms": "English search terms combination (必须是英文关键词)",
                    "url": "完整的搜索URL",
                    "description": "为什么这个搜索策略对用户有价值",
                    "relevance_score": 0.95,
                    "search_strategy": "broad_discovery/focused_technical/methodological/recent_advances/review_papers/case_studies",
                    "expected_results": "预期能找到什么类型的文献（50字内）",
                    "optimization_notes": "为用户定制的搜索技巧"
                }}
            ]
        }}

        搜索关键词生成要求：
        1. **必须使用英文关键词** - 所有搜索词必须是英文，适合国际学术文献搜索
        2. 结合用户的具体研究方向和技术需求
        3. 使用领域专业术语和MeSH标准词汇（英文）
        4. 考虑同义词、缩写词和相关概念（英文）
        5. 平衡搜索的广度和精度
        6. 适应不同平台的搜索语法特点

        链接优化原则：
        1. 每个链接服务于不同的搜索目的
        2. 搜索词组合要科学合理，避免过于宽泛或狭窄
        3. 充分利用各平台的独特优势
        4. 考虑用户的经验水平调整搜索复杂度
        5. 包含最新研究和经典文献的平衡策略

        确保所有URL都是有效的、可访问的搜索链接。
        """

    async def generate_research_links(
        self,
        research_direction: Dict[str, Any],
        intent_analysis: Dict[str, Any],
        user_profile: Optional[Dict[str, Any]] = None
    ) -> SearchLinkPackage:
        """
        为特定研究方向生成智能文献搜索链接
        
        Args:
            research_direction: 研究方向分析结果
            intent_analysis: 用户意图分析
            user_profile: 用户档案信息
            
        Returns:
            SearchLinkPackage: 完整的搜索链接包
        """
        try:
            start_time = datetime.now()
            
            logger.info(f"开始生成智能文献链接 - 研究方向: {research_direction.get('title', 'Unknown')}")
            
            # 构建AI生成提示
            platform_info = json.dumps(self.platforms, ensure_ascii=False, indent=2)
            user_context = user_profile or {"experience_level": "intermediate"}
            
            generation_prompt = self.link_generation_template.format(
                research_direction=json.dumps(research_direction, ensure_ascii=False, indent=2),
                intent_analysis=json.dumps(intent_analysis, ensure_ascii=False, indent=2),
                user_profile=json.dumps(user_context, ensure_ascii=False, indent=2),
                available_platforms=platform_info
            )
            
            # 执行AI生成
            logger.info("执行AI链接生成...")
            ai_response = await self.ai_service.generate_response(
                message=generation_prompt,
                context={"conversation_type": "literature_link_generation"}
            )
            
            # 解析AI响应
            generation_result = await self._parse_ai_response(ai_response.get("content", ""))
            
            # 构建搜索链接
            search_links = []
            generated_links = generation_result.get("generated_links", [])
            
            for link_data in generated_links:
                search_link = await self._build_search_link(link_data)
                if search_link:
                    search_links.append(search_link)
            
            # 验证和优化链接
            validated_links = await self._validate_and_optimize_links(search_links)
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # 构建链接包
            link_package = SearchLinkPackage(
                research_focus=research_direction.get("title", "研究方向"),
                total_links=len(validated_links),
                links=validated_links,
                generation_timestamp=datetime.now().isoformat(),
                optimization_notes=generation_result.get("search_strategy_overview", {}).get("optimization_focus", "")
            )
            
            logger.info(f"智能链接生成完成 - 生成{len(validated_links)}个链接，用时{processing_time:.2f}ms")
            
            return link_package
            
        except Exception as e:
            logger.error(f"智能链接生成失败: {str(e)}")
            return await self._generate_fallback_links(research_direction)

    async def generate_multi_direction_links(
        self,
        research_directions: List[Dict[str, Any]],
        intent_analysis: Dict[str, Any],
        user_profile: Optional[Dict[str, Any]] = None
    ) -> Dict[str, SearchLinkPackage]:
        """
        为多个研究方向批量生成链接
        
        Args:
            research_directions: 多个研究方向列表
            intent_analysis: 用户意图分析
            user_profile: 用户档案信息
            
        Returns:
            Dict[str, SearchLinkPackage]: 按方向ID索引的链接包字典
        """
        try:
            logger.info(f"批量生成文献链接 - {len(research_directions)}个研究方向")
            
            # 并发生成所有方向的链接
            link_generation_tasks = []
            for direction in research_directions:
                task = self.generate_research_links(direction, intent_analysis, user_profile)
                link_generation_tasks.append(task)
            
            # 等待所有任务完成
            link_packages = await asyncio.gather(*link_generation_tasks, return_exceptions=True)
            
            # 构建结果字典
            results = {}
            for i, direction in enumerate(research_directions):
                direction_id = direction.get("direction_id", f"direction_{i}")
                
                if isinstance(link_packages[i], Exception):
                    logger.error(f"方向 {direction_id} 链接生成失败: {link_packages[i]}")
                    results[direction_id] = await self._generate_fallback_links(direction)
                else:
                    results[direction_id] = link_packages[i]
            
            logger.info(f"批量链接生成完成 - 成功生成{len(results)}个方向的链接")
            return results
            
        except Exception as e:
            logger.error(f"批量链接生成失败: {str(e)}")
            return {}

    async def optimize_search_terms(
        self,
        base_terms: List[str],
        research_context: Dict[str, Any],
        platform: str = "pubmed"
    ) -> Dict[str, Any]:
        """
        为特定平台优化搜索词
        
        Args:
            base_terms: 基础搜索词列表
            research_context: 研究上下文
            platform: 目标平台
            
        Returns:
            优化后的搜索策略
        """
        try:
            platform_config = self.platforms.get(platform, self.platforms["pubmed"])
            
            optimization_prompt = f"""
            请为{platform_config['name']}平台优化以下搜索词组合：

            基础搜索词: {base_terms}
            研究上下文: {json.dumps(research_context, ensure_ascii=False)}
            平台特点: {platform_config['specialty']}
            平台功能: {platform_config['advanced_features']}

            请提供优化建议：
            {{
                "optimized_terms": ["优化后的搜索词"],
                "search_syntax": "适合该平台的搜索语法",
                "filter_suggestions": ["建议的筛选条件"],
                "alternative_strategies": ["备选搜索策略"]
            }}
            """
            
            ai_response = await self.ai_service.generate_response(
                message=optimization_prompt,
                context={"conversation_type": "search_optimization"}
            )
            
            return await self._parse_ai_response(ai_response.get("content", ""))
            
        except Exception as e:
            logger.error(f"搜索词优化失败: {str(e)}")
            return {"optimized_terms": base_terms}

    async def _build_search_link(self, link_data: Dict[str, Any]) -> Optional[SearchLink]:
        """构建搜索链接对象"""
        try:
            platform = link_data.get("platform", "pubmed")
            search_terms = link_data.get("search_terms", "")
            
            if platform not in self.platforms:
                logger.warning(f"未知平台: {platform}")
                platform = "pubmed"
            
            platform_config = self.platforms[platform]
            
            # 构建URL
            if platform == "biorxiv":
                # bioRxiv 使用特殊的URL格式
                encoded_terms = urllib.parse.quote_plus(search_terms)
                url = f"{platform_config['base_url']}{encoded_terms}"
            else:
                encoded_terms = urllib.parse.quote_plus(search_terms)
                url = f"{platform_config['base_url']}{platform_config['search_path']}{encoded_terms}"
            
            # 验证URL是否在link_data中提供，如果提供则使用
            if link_data.get("url") and link_data["url"].startswith("http"):
                url = link_data["url"]
            
            return SearchLink(
                platform=platform_config["name"],
                title=link_data.get("title", f"{platform_config['name']}搜索"),
                url=url,
                description=link_data.get("description", ""),
                relevance_score=float(link_data.get("relevance_score", 0.8)),
                search_strategy=link_data.get("search_strategy", "general"),
                expected_results=link_data.get("expected_results", "")
            )
            
        except Exception as e:
            logger.error(f"构建搜索链接失败: {str(e)}")
            return None

    async def _validate_and_optimize_links(self, search_links: List[SearchLink]) -> List[SearchLink]:
        """验证和优化搜索链接"""
        try:
            validated_links = []
            
            for link in search_links:
                # 基本验证
                if not link.url or not link.url.startswith("http"):
                    logger.warning(f"无效URL跳过: {link.url}")
                    continue
                
                # URL格式修正
                if "pubmed.ncbi.nlm.nih.gov" in link.url and "?term=" not in link.url:
                    link.url = link.url.replace("?", "?term=", 1)
                
                validated_links.append(link)
            
            # 按相关性分数排序
            validated_links.sort(key=lambda x: x.relevance_score, reverse=True)
            
            # 确保平台多样性（最多3个相同平台）
            platform_counts = {}
            final_links = []
            
            for link in validated_links:
                platform_count = platform_counts.get(link.platform, 0)
                if platform_count < 3:
                    final_links.append(link)
                    platform_counts[link.platform] = platform_count + 1
            
            return final_links
            
        except Exception as e:
            logger.error(f"链接验证失败: {str(e)}")
            return search_links

    async def _parse_ai_response(self, content: str) -> Dict[str, Any]:
        """解析AI响应内容"""
        try:
            # 尝试直接解析JSON
            if content.strip().startswith('{'):
                return json.loads(content)
            
            # 提取JSON块
            import re
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            
            # 查找大括号内的内容
            json_match = re.search(r'(\{.*\})', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            
            # 如果都失败，返回包装的文本内容
            return {
                "generated_links": [],
                "search_strategy_overview": {
                    "primary_strategy": "fallback_mode",
                    "optimization_focus": "基础搜索策略"
                }
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return {
                "generated_links": [],
                "parse_error": str(e)
            }

    async def _generate_fallback_links(self, research_direction: Dict[str, Any]) -> SearchLinkPackage:
        """生成降级搜索链接"""
        try:
            direction_title = research_direction.get("title", "single cell analysis")
            research_focus = research_direction.get("research_focus", "single cell RNA sequencing")
            
            # 基础搜索词提取
            basic_terms = [
                "single cell RNA sequencing",
                "scRNA-seq",
                "single cell transcriptomics",
                research_focus
            ]
            
            fallback_links = []
            
            # PubMed基础搜索
            pubmed_terms = " OR ".join([f'"{term}"' for term in basic_terms[:3]])
            fallback_links.append(SearchLink(
                platform="PubMed",
                title="PubMed基础文献搜索",
                url=f"https://pubmed.ncbi.nlm.nih.gov/?term={urllib.parse.quote_plus(pubmed_terms)}",
                description="在PubMed中搜索相关的单细胞研究文献",
                relevance_score=0.7,
                search_strategy="broad_discovery",
                expected_results="生物医学相关的单细胞研究论文"
            ))
            
            # Google Scholar广泛搜索
            scholar_terms = basic_terms[0]
            fallback_links.append(SearchLink(
                platform="Google Scholar",
                title="Google Scholar综合搜索",
                url=f"https://scholar.google.com/scholar?q={urllib.parse.quote_plus(scholar_terms)}",
                description="在学术搜索引擎中查找跨学科的单细胞研究",
                relevance_score=0.6,
                search_strategy="comprehensive",
                expected_results="跨学科的单细胞分析相关研究"
            ))
            
            return SearchLinkPackage(
                research_focus=direction_title,
                total_links=len(fallback_links),
                links=fallback_links,
                generation_timestamp=datetime.now().isoformat(),
                optimization_notes="基础搜索策略 - 系统降级模式"
            )
            
        except Exception as e:
            logger.error(f"生成降级链接失败: {str(e)}")
            return SearchLinkPackage(
                research_focus="默认搜索",
                total_links=0,
                links=[],
                generation_timestamp=datetime.now().isoformat(),
                optimization_notes="无法生成搜索链接"
            )


# 全局服务实例
smart_literature_linker = SmartLiteratureLinker()


def get_smart_literature_linker() -> SmartLiteratureLinker:
    """获取智能文献链接生成器实例"""
    return smart_literature_linker