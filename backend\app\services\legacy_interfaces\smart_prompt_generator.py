"""
智能提示词生成服务
基于用户需求信息生成优化的文献搜索查询
"""
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

from app.services.ai_service import AIService

logger = logging.getLogger(__name__)


class SmartPromptGenerator:
    """智能提示词生成器"""
    
    def __init__(self):
        self.ai_service = AIService()
    
    async def generate_literature_search_queries(
        self,
        requirements: Dict[str, Any],
        user_query: Optional[str] = None,
        max_queries: int = 6
    ) -> Dict[str, Any]:
        """
        基于需求信息生成文献搜索查询
        
        Args:
            requirements: 用户需求信息
            user_query: 用户原始查询（可选）
            max_queries: 最大查询数量
            
        Returns:
            包含优化查询和解释的字典
        """
        try:
            # 1. 分析需求信息
            analysis = self._analyze_requirements(requirements)
            
            # 2. 生成基础查询
            base_queries = self._generate_base_queries(analysis)
            
            # 3. AI优化查询
            optimized_queries = await self._ai_optimize_queries(base_queries, requirements, user_query)
            
            # 4. 生成相关主题
            related_topics = self._generate_related_topics(analysis)
            
            return {
                "primary_query": optimized_queries[0] if optimized_queries else "",
                "suggested_queries": optimized_queries[:max_queries],
                "related_topics": related_topics,
                "search_strategy": analysis,
                "optimization_notes": self._generate_optimization_notes(analysis),
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"生成智能提示词失败: {e}")
            return self._get_fallback_queries(requirements, user_query)
    
    def _analyze_requirements(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """分析需求信息"""
        analysis = {
            "species": self._extract_species(requirements.get("speciesType", "")),
            "technology": self._extract_technology(requirements.get("experimentType", "")),
            "research_goal": requirements.get("researchGoal", ""),
            "sample_type": requirements.get("sampleType", ""),
            "analysis_focus": requirements.get("analysisType", ""),
            "priority_keywords": [],
            "context_keywords": [],
            "technical_terms": []
        }
        
        # 提取优先关键词
        if analysis["species"]:
            analysis["priority_keywords"].append(analysis["species"])
        
        if analysis["technology"]:
            analysis["priority_keywords"].append(analysis["technology"])
            analysis["technical_terms"].append(analysis["technology"])
        
        # 添加研究目标关键词
        goal_keywords = self._extract_goal_keywords(analysis["research_goal"])
        analysis["context_keywords"].extend(goal_keywords)
        
        # 添加样本类型关键词
        sample_keywords = self._extract_sample_keywords(analysis["sample_type"])
        analysis["context_keywords"].extend(sample_keywords)
        
        return analysis
    
    def _extract_species(self, species_type: str) -> str:
        """提取物种信息"""
        species_mapping = {
            "人类": "human",
            "小鼠": "mouse",
            "大鼠": "rat",
            "斑马鱼": "zebrafish",
            "果蝇": "drosophila",
            "线虫": "C. elegans",
            "拟南芥": "arabidopsis"
        }
        
        for cn_name, en_name in species_mapping.items():
            if cn_name in species_type:
                return en_name
        
        return ""
    
    def _extract_technology(self, experiment_type: str) -> str:
        """提取技术类型"""
        tech_mapping = {
            "单细胞RNA测序": "scRNA-seq",
            "单细胞ATAC测序": "scATAC-seq",
            "单细胞多组学": "multiome",
            "空间转录组学": "spatial transcriptomics",
            "单细胞蛋白质组学": "CITE-seq"
        }
        
        for cn_name, en_name in tech_mapping.items():
            if cn_name in experiment_type:
                return en_name
        
        return "single cell"
    
    def _extract_goal_keywords(self, research_goal: str) -> List[str]:
        """提取研究目标关键词"""
        goal_mapping = {
            "细胞类型鉴定": ["cell type identification", "cell classification"],
            "发育轨迹分析": ["trajectory analysis", "pseudotime", "developmental"],
            "疾病机制研究": ["disease mechanism", "pathology"],
            "药物筛选": ["drug screening", "therapeutics"],
            "肿瘤异质性": ["tumor heterogeneity", "cancer"],
            "免疫细胞分析": ["immune cells", "immunology"],
            "神经发育": ["neurodevelopment", "neurogenesis"]
        }
        
        keywords = []
        for cn_goal, en_keywords in goal_mapping.items():
            if cn_goal in research_goal:
                keywords.extend(en_keywords)
        
        return keywords
    
    def _extract_sample_keywords(self, sample_type: str) -> List[str]:
        """提取样本类型关键词"""
        sample_mapping = {
            "PBMC": ["PBMC", "peripheral blood"],
            "肿瘤组织": ["tumor tissue", "cancer"],
            "脑组织": ["brain tissue", "neural"],
            "肝脏组织": ["liver tissue", "hepatic"],
            "肺组织": ["lung tissue", "pulmonary"],
            "血液样本": ["blood sample", "hematology"]
        }
        
        keywords = []
        for sample_key, sample_keywords in sample_mapping.items():
            if sample_key in sample_type:
                keywords.extend(sample_keywords)
        
        return keywords
    
    def _generate_base_queries(self, analysis: Dict[str, Any]) -> List[str]:
        """生成基础查询"""
        queries = []
        
        # 1. 核心技术查询
        if analysis["priority_keywords"]:
            core_query = " ".join(analysis["priority_keywords"])
            queries.append(core_query)
        
        # 2. 技术+应用查询
        if analysis["technology"] and analysis["context_keywords"]:
            for context in analysis["context_keywords"][:2]:
                tech_app_query = f"{analysis['technology']} {context}"
                queries.append(tech_app_query)
        
        # 3. 物种特异性查询
        if analysis["species"] and analysis["technology"]:
            species_query = f"{analysis['species']} {analysis['technology']}"
            if analysis["context_keywords"]:
                species_query += f" {analysis['context_keywords'][0]}"
            queries.append(species_query)
        
        # 4. 方法学查询
        if analysis["technology"]:
            method_queries = [
                f"{analysis['technology']} protocol",
                f"{analysis['technology']} best practices",
                f"{analysis['technology']} data analysis"
            ]
            queries.extend(method_queries)
        
        # 5. 综合查询
        if len(analysis["priority_keywords"]) > 1 and analysis["context_keywords"]:
            comprehensive_query = " ".join([
                *analysis["priority_keywords"][:2],
                analysis["context_keywords"][0]
            ])
            queries.append(comprehensive_query)
        
        return list(set(queries))  # 去重
    
    async def _ai_optimize_queries(
        self,
        base_queries: List[str],
        requirements: Dict[str, Any],
        user_query: Optional[str] = None
    ) -> List[str]:
        """AI优化查询"""
        if not self.ai_service.use_real_ai:
            return self._rule_based_optimize(base_queries, requirements)
        
        try:
            # 构建AI优化提示词
            optimization_prompt = self._build_optimization_prompt(
                base_queries, requirements, user_query
            )
            
            # 调用AI服务
            ai_response = await self.ai_service.generate_response(
                optimization_prompt,
                context={"requirements": requirements},
                conversation_type="literature_search_optimization"
            )
            
            # 解析AI响应
            optimized_queries = self._parse_ai_response(ai_response.content)
            
            return optimized_queries if optimized_queries else base_queries
            
        except Exception as e:
            logger.warning(f"AI优化失败，使用规则优化: {e}")
            return self._rule_based_optimize(base_queries, requirements)
    
    def _build_optimization_prompt(
        self,
        base_queries: List[str],
        requirements: Dict[str, Any],
        user_query: Optional[str] = None
    ) -> str:
        """构建AI优化提示词"""
        prompt = f"""
作为文献搜索专家，请优化以下搜索查询，使其更适合学术文献检索。

用户需求背景：
- 物种类型：{requirements.get('speciesType', '未指定')}
- 实验类型：{requirements.get('experimentType', '未指定')}
- 研究目标：{requirements.get('researchGoal', '未指定')}
- 样本类型：{requirements.get('sampleType', '未指定')}

原始查询：
{chr(10).join(f"{i+1}. {query}" for i, query in enumerate(base_queries))}

用户原始问题：{user_query or '无'}

请提供6个优化的英文搜索查询，要求：
1. 使用标准学术术语
2. 包含关键技术词汇
3. 适合PubMed等学术数据库
4. 按相关性排序
5. 每个查询独占一行，格式：Query: [查询内容]

优化查询：
"""
        return prompt
    
    def _parse_ai_response(self, response: str) -> List[str]:
        """解析AI响应"""
        queries = []
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if line.startswith('Query:'):
                query = line.replace('Query:', '').strip()
                if query:
                    queries.append(query)
            elif line and not line.startswith(('优化', '请提供', '1.', '2.', '3.', '4.', '5.')):
                # 处理其他格式的查询
                if len(line.split()) >= 2:  # 至少包含2个词
                    queries.append(line)
        
        return queries[:6]  # 最多返回6个查询
    
    def _rule_based_optimize(
        self,
        base_queries: List[str],
        requirements: Dict[str, Any]
    ) -> List[str]:
        """基于规则的查询优化"""
        optimized = []
        
        for query in base_queries:
            # 添加单细胞前缀（如果没有）
            if 'single cell' not in query.lower() and 'sc' not in query.lower():
                query = f"single cell {query}"
            
            # 添加技术后缀
            if requirements.get('experimentType'):
                if 'RNA' in requirements['experimentType'] and 'RNA' not in query:
                    query += " RNA-seq"
                elif 'ATAC' in requirements['experimentType'] and 'ATAC' not in query:
                    query += " ATAC-seq"
            
            optimized.append(query)
        
        return optimized[:6]
    
    def _generate_related_topics(self, analysis: Dict[str, Any]) -> List[str]:
        """生成相关主题"""
        topics = []
        
        # 基于技术的相关主题
        if analysis["technology"]:
            tech_topics = [
                f"{analysis['technology']} quality control",
                f"{analysis['technology']} data preprocessing",
                f"{analysis['technology']} computational methods",
                f"{analysis['technology']} experimental design"
            ]
            topics.extend(tech_topics)
        
        # 基于研究目标的相关主题
        goal_topics = [
            "cell type annotation",
            "batch effect correction",
            "differential expression analysis",
            "pathway enrichment analysis",
            "biomarker discovery",
            "therapeutic targets"
        ]
        topics.extend(goal_topics)
        
        return topics[:8]  # 返回前8个主题
    
    def _generate_optimization_notes(self, analysis: Dict[str, Any]) -> List[str]:
        """生成优化说明"""
        notes = []
        
        if analysis["species"]:
            notes.append(f"已针对{analysis['species']}物种优化搜索词")
        
        if analysis["technology"]:
            notes.append(f"包含{analysis['technology']}技术特异性术语")
        
        if analysis["context_keywords"]:
            notes.append("添加了研究背景相关关键词")
        
        notes.append("使用标准学术术语，适合PubMed等数据库")
        notes.append("按相关性和研究价值排序")
        
        return notes
    
    def _get_fallback_queries(
        self,
        requirements: Dict[str, Any],
        user_query: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取后备查询"""
        fallback_queries = [
            "single cell RNA sequencing",
            "scRNA-seq data analysis",
            "single cell genomics",
            "cell type identification",
            "single cell transcriptomics",
            "computational single cell biology"
        ]
        
        # 如果有用户查询，将其作为首选
        if user_query:
            fallback_queries.insert(0, f"single cell {user_query}")
        
        return {
            "primary_query": fallback_queries[0],
            "suggested_queries": fallback_queries[:6],
            "related_topics": [
                "quality control",
                "data preprocessing", 
                "cell clustering",
                "differential expression"
            ],
            "search_strategy": {"fallback": True},
            "optimization_notes": ["使用通用单细胞研究查询"],
            "generated_at": datetime.utcnow().isoformat()
        }


# 全局服务实例
smart_prompt_generator = SmartPromptGenerator()


def get_smart_prompt_generator() -> SmartPromptGenerator:
    """获取智能提示词生成器实例"""
    return smart_prompt_generator