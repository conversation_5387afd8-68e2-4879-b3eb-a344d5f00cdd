{"ai_service_config": {"version": "2.0", "description": "AI服务配置文件", "last_updated": "2024-01-20T00:00:00Z", "model_settings": {"default_model": "gpt-3.5-turbo", "temperature": 0.3, "max_tokens": 2000, "timeout": 60, "stream": false}, "retry_configuration": {"max_attempts": 3, "base_delay": 1.0, "max_delay": 30.0, "backoff_multiplier": 2.0, "jitter_range": 0.2, "timeout": 60.0, "retry_on_exceptions": ["TimeoutError", "ConnectionError", "RetryableError"], "stop_on_exceptions": ["NonRetryableError", "AuthenticationError", "InvalidRequestError"]}, "circuit_breaker": {"failure_threshold": 5, "success_threshold": 3, "timeout": 60.0, "half_open_max_calls": 3}, "performance_monitoring": {"enabled": true, "metrics_collection": {"response_time": true, "token_usage": true, "success_rate": true, "quality_score": true, "error_tracking": true}, "alert_rules": [{"name": "high_response_time", "metric": "ai_response_time", "condition": ">", "threshold": 10.0, "duration": 300, "level": "warning"}, {"name": "low_success_rate", "metric": "ai_success_rate", "condition": "<", "threshold": 0.9, "duration": 600, "level": "error"}, {"name": "poor_quality_score", "metric": "ai_quality_score", "condition": "<", "threshold": 0.7, "duration": 900, "level": "warning"}]}, "template_management": {"cache_ttl": 3600, "version_retention_days": 30, "max_versions_per_template": 10, "auto_optimization": true, "ab_testing": {"enabled": true, "default_traffic_split": 0.5, "min_sample_size": 100, "confidence_threshold": 0.95, "test_duration_days": 7}}, "quality_assurance": {"quality_threshold": 0.8, "confidence_threshold": 0.85, "min_response_length": 50, "max_response_length": 3000, "required_elements": ["professional_terms", "structured_format", "relevance_check"]}, "security": {"prompt_injection_detection": true, "content_filtering": true, "rate_limiting": {"requests_per_minute": 60, "requests_per_hour": 1000, "requests_per_day": 10000}, "allowed_domains": ["*.cellforge.ai", "localhost"]}, "streaming_support": {"enabled": true, "chunk_size": 100, "max_stream_duration": 120, "heartbeat_interval": 30}, "fallback_options": {"mock_response_enabled": true, "fallback_templates": ["general_error_response", "service_unavailable_response", "rate_limit_response"], "graceful_degradation": true}, "logging": {"level": "INFO", "include_request_details": true, "include_response_details": false, "mask_sensitive_data": true, "retention_days": 90}}, "environment_specific": {"development": {"model_settings": {"temperature": 0.5}, "logging": {"level": "DEBUG"}, "fallback_options": {"mock_response_enabled": true}}, "staging": {"model_settings": {"temperature": 0.4}, "performance_monitoring": {"alert_rules": [{"name": "staging_performance_check", "metric": "ai_response_time", "condition": ">", "threshold": 5.0, "duration": 180, "level": "info"}]}}, "production": {"model_settings": {"temperature": 0.3, "max_tokens": 1500}, "security": {"rate_limiting": {"requests_per_minute": 100, "requests_per_hour": 2000, "requests_per_day": 20000}}, "fallback_options": {"mock_response_enabled": false}, "logging": {"level": "WARN", "include_request_details": false}}}}