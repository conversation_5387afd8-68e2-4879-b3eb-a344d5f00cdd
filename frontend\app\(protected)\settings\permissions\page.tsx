"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { RoleBadge } from "@/components/role-badge"
import { Search, Plus, Save, History } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { PermissionGate } from "@/components/permission-gate"

// 权限模块定义
const permissionModules = [
  {
    id: "dashboard",
    name: "仪表盘",
    permissions: [
      { id: "view_dashboard", name: "查看仪表盘", description: "允许用户查看数据分析仪表盘" },
      { id: "export_dashboard", name: "导出报表", description: "允许用户导出仪表盘数据和报表" },
      { id: "customize_dashboard", name: "自定义仪表盘", description: "允许用户自定义仪表盘视图和指标" },
    ],
  },
  {
    id: "customers",
    name: "客户管理",
    permissions: [
      { id: "view_customers", name: "查看客户", description: "允许用户查看客户信息" },
      { id: "add_customers", name: "添加客户", description: "允许用户添加新客户" },
      { id: "edit_customers", name: "编辑客户", description: "允许用户编辑客户信息" },
      { id: "delete_customers", name: "删除客户", description: "允许用户删除客户记录" },
      { id: "export_customers", name: "导出客户", description: "允许用户导出客户数据" },
    ],
  },
  {
    id: "solutions",
    name: "方案管理",
    permissions: [
      { id: "view_solutions", name: "查看方案", description: "允许用户查看解决方案" },
      { id: "create_solutions", name: "创建方案", description: "允许用户创建新的解决方案" },
      { id: "edit_solutions", name: "编辑方案", description: "允许用户编辑现有解决方案" },
      { id: "delete_solutions", name: "删除方案", description: "允许用户删除解决方案" },
      { id: "approve_solutions", name: "审批方案", description: "允许用户审批解决方案" },
      { id: "share_solutions", name: "分享方案", description: "允许用户与客户分享解决方案" },
    ],
  },
  {
    id: "knowledge",
    name: "知识库",
    permissions: [
      { id: "view_knowledge", name: "查看知识库", description: "允许用户查看知识库内容" },
      { id: "add_knowledge", name: "添加内容", description: "允许用户添加知识库内容" },
      { id: "edit_knowledge", name: "编辑内容", description: "允许用户编辑知识库内容" },
      { id: "delete_knowledge", name: "删除内容", description: "允许用户删除知识库内容" },
      { id: "approve_knowledge", name: "审核内容", description: "允许用户审核知识库内容" },
    ],
  },
  {
    id: "users",
    name: "用户管理",
    permissions: [
      { id: "view_users", name: "查看用户", description: "允许查看系统用户" },
      { id: "add_users", name: "添加用户", description: "允许添加新用户" },
      { id: "edit_users", name: "编辑用户", description: "允许编辑用户信息" },
      { id: "delete_users", name: "删除用户", description: "允许删除用户账户" },
      { id: "manage_permissions", name: "管理权限", description: "允许管理用户权限" },
    ],
  },
  {
    id: "system",
    name: "系统设置",
    permissions: [
      { id: "view_settings", name: "查看设置", description: "允许查看系统设置" },
      { id: "edit_settings", name: "编辑设置", description: "允许编辑系统设置" },
      { id: "view_logs", name: "查看日志", description: "允许查看系统日志" },
      { id: "manage_integrations", name: "管理集成", description: "允许管理第三方集成" },
      { id: "backup_restore", name: "备份恢复", description: "允许执行系统备份和恢复" },
    ],
  },
]

export default function PermissionsPage() {
  const { permissions } = useAuth()
  const [selectedRole, setSelectedRole] = useState("super_admin")
  const [searchQuery, setSearchQuery] = useState("")
  const [rolePermissions, setRolePermissions] = useState<Record<string, string[]>>({
    super_admin: permissionModules.flatMap((module) => module.permissions.map((p) => p.id)),
    sales: [
      "view_dashboard",
      "export_dashboard",
      "view_customers",
      "add_customers",
      "edit_customers",
      "export_customers",
      "view_solutions",
      "create_solutions",
      "edit_solutions",
      "share_solutions",
      "view_knowledge",
    ],
    operations: [
      "view_knowledge",
      "add_knowledge",
      "edit_knowledge",
      "delete_knowledge",
      "approve_knowledge",
      "view_dashboard",
      "view_customers",
      "view_solutions",
    ],
    customer: ["view_solutions", "view_knowledge"],
  })

  // 过滤权限模块
  const filteredModules = permissionModules.filter((module) => {
    if (!searchQuery) return true
    if (module.name.toLowerCase().includes(searchQuery.toLowerCase())) return true
    return module.permissions.some(
      (p) =>
        p.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        p.description.toLowerCase().includes(searchQuery.toLowerCase()),
    )
  })

  // 切换权限
  const togglePermission = (permissionId: string) => {
    setRolePermissions((prev) => {
      const current = { ...prev }
      if (current[selectedRole].includes(permissionId)) {
        current[selectedRole] = current[selectedRole].filter((id) => id !== permissionId)
      } else {
        current[selectedRole] = [...current[selectedRole], permissionId]
      }
      return current
    })
  }

  // 切换模块所有权限
  const toggleModulePermissions = (moduleId: string, enabled: boolean) => {
    const modulePermissionIds = permissionModules.find((m) => m.id === moduleId)?.permissions.map((p) => p.id) || []

    setRolePermissions((prev) => {
      const current = { ...prev }
      if (enabled) {
        // 添加所有模块权限
        const newPermissions = [...current[selectedRole]]
        modulePermissionIds.forEach((id) => {
          if (!newPermissions.includes(id)) {
            newPermissions.push(id)
          }
        })
        current[selectedRole] = newPermissions
      } else {
        // 移除所有模块权限
        current[selectedRole] = current[selectedRole].filter((id) => !modulePermissionIds.includes(id))
      }
      return current
    })
  }

  // 检查模块权限状态
  const getModulePermissionState = (moduleId: string) => {
    const modulePermissionIds = permissionModules.find((m) => m.id === moduleId)?.permissions.map((p) => p.id) || []
    const enabledCount = modulePermissionIds.filter((id) => rolePermissions[selectedRole].includes(id)).length

    if (enabledCount === 0) return "none"
    if (enabledCount === modulePermissionIds.length) return "all"
    return "partial"
  }

  return (
    <div className="container py-10">
      <PermissionGate permission="manage_permissions" fallback={<div>您没有权限访问此页面</div>}>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">权限管理</h1>
            <p className="text-muted-foreground">管理用户角色和权限设置</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <History className="h-4 w-4 mr-2" />
              权限变更历史
            </Button>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              创建自定义角色
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="md:col-span-1">
            <CardHeader>
              <CardTitle>角色</CardTitle>
              <CardDescription>选择要编辑的角色</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div
                  className={`p-3 rounded-md cursor-pointer flex items-center justify-between ${
                    selectedRole === "super_admin" ? "bg-blue-50 border border-blue-200" : "hover:bg-slate-50"
                  }`}
                  onClick={() => setSelectedRole("super_admin")}
                >
                  <div className="font-medium">超级管理员</div>
                  <RoleBadge role="super_admin" />
                </div>
                <div
                  className={`p-3 rounded-md cursor-pointer flex items-center justify-between ${
                    selectedRole === "sales" ? "bg-blue-50 border border-blue-200" : "hover:bg-slate-50"
                  }`}
                  onClick={() => setSelectedRole("sales")}
                >
                  <div className="font-medium">销售人员</div>
                  <RoleBadge role="sales" />
                </div>
                <div
                  className={`p-3 rounded-md cursor-pointer flex items-center justify-between ${
                    selectedRole === "operations" ? "bg-blue-50 border border-blue-200" : "hover:bg-slate-50"
                  }`}
                  onClick={() => setSelectedRole("operations")}
                >
                  <div className="font-medium">运维人员</div>
                  <RoleBadge role="operations" />
                </div>
                <div
                  className={`p-3 rounded-md cursor-pointer flex items-center justify-between ${
                    selectedRole === "customer" ? "bg-blue-50 border border-blue-200" : "hover:bg-slate-50"
                  }`}
                  onClick={() => setSelectedRole("customer")}
                >
                  <div className="font-medium">客户</div>
                  <RoleBadge role="customer" />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="md:col-span-3">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>
                      <span className="mr-2">权限设置</span>
                      <RoleBadge role={selectedRole as any} />
                    </CardTitle>
                    <CardDescription>配置该角色可以访问的功能</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="search"
                        placeholder="搜索权限..."
                        className="pl-8 w-[200px]"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                      />
                    </div>
                    <Button>
                      <Save className="h-4 w-4 mr-2" />
                      保存更改
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="modules" className="w-full">
                  <TabsList className="mb-4">
                    <TabsTrigger value="modules">按模块</TabsTrigger>
                    <TabsTrigger value="list">权限列表</TabsTrigger>
                  </TabsList>

                  <TabsContent value="modules">
                    <div className="space-y-6">
                      {filteredModules.map((module) => {
                        const moduleState = getModulePermissionState(module.id)
                        return (
                          <div key={module.id} className="border rounded-md">
                            <div className="flex items-center justify-between p-4 border-b bg-slate-50">
                              <div className="font-medium">{module.name}</div>
                              <div className="flex items-center gap-2">
                                <Checkbox
                                  id={`module-${module.id}`}
                                  checked={moduleState === "all"}
                                  indeterminate={moduleState === "partial"}
                                  onCheckedChange={(checked) => toggleModulePermissions(module.id, !!checked)}
                                />
                                <Label htmlFor={`module-${module.id}`}>
                                  {moduleState === "all"
                                    ? "全部启用"
                                    : moduleState === "partial"
                                      ? "部分启用"
                                      : "全部禁用"}
                                </Label>
                              </div>
                            </div>
                            <div className="p-4 space-y-3">
                              {module.permissions.map((permission) => (
                                <div key={permission.id} className="flex items-start space-x-2">
                                  <Checkbox
                                    id={permission.id}
                                    checked={rolePermissions[selectedRole].includes(permission.id)}
                                    onCheckedChange={() => togglePermission(permission.id)}
                                  />
                                  <div className="grid gap-1.5 leading-none">
                                    <Label htmlFor={permission.id} className="font-medium">
                                      {permission.name}
                                    </Label>
                                    <p className="text-sm text-muted-foreground">{permission.description}</p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </TabsContent>

                  <TabsContent value="list">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>权限名称</TableHead>
                          <TableHead>描述</TableHead>
                          <TableHead>模块</TableHead>
                          <TableHead className="w-[100px] text-right">状态</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredModules.flatMap((module) =>
                          module.permissions.map((permission) => (
                            <TableRow key={permission.id}>
                              <TableCell className="font-medium">{permission.name}</TableCell>
                              <TableCell>{permission.description}</TableCell>
                              <TableCell>{module.name}</TableCell>
                              <TableCell className="text-right">
                                <Checkbox
                                  checked={rolePermissions[selectedRole].includes(permission.id)}
                                  onCheckedChange={() => togglePermission(permission.id)}
                                />
                              </TableCell>
                            </TableRow>
                          )),
                        )}
                      </TableBody>
                    </Table>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </PermissionGate>
    </div>
  )
}
