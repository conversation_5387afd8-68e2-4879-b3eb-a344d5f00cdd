{"domain_keywords": {"immunology": {"name": "免疫学", "description": "免疫细胞、免疫应答、免疫疾病相关研究", "core_keywords": ["immune", "immunity", "immunology", "T cell", "B cell", "NK cell", "dendritic", "macrophage", "cytokine", "antibody"], "cellular_keywords": ["PBMC", "immune cell", "lymphocyte", "macrophage", "dendritic cell", "neutrophil", "eosinophil", "basophil"], "mechanism_keywords": ["immune response", "activation", "exhaustion", "memory", "tolerance", "inflammation", "signaling"], "application_keywords": ["vaccine", "autoimmune", "transplantation", "infection", "allergy", "immunotherapy"], "weight": 1.4, "related_domains": ["oncology", "infectious_disease"]}, "oncology": {"name": "肿瘤学", "description": "癌症、肿瘤微环境、肿瘤生物学研究", "core_keywords": ["cancer", "tumor", "oncology", "metastasis", "carcinoma", "malignant", "chemotherapy", "radiotherapy"], "cellular_keywords": ["cancer cell", "tumor cell", "circulating tumor cell", "CAR-T", "tumor-infiltrating"], "mechanism_keywords": ["drug resistance", "immune escape", "EMT", "stemness", "angiogenesis", "invasion"], "application_keywords": ["immunotherapy", "precision medicine", "liquid biopsy", "targeted therapy"], "weight": 1.5, "related_domains": ["immunology", "pharmacology"]}, "neuroscience": {"name": "神经科学", "description": "神经元、大脑功能、神经系统疾病研究", "core_keywords": ["neuron", "brain", "neural", "neuroscience", "synapse", "neurotransmitter", "cognitive", "CNS"], "cellular_keywords": ["neuron", "astrocyte", "microglia", "oligodendrocyte", "neural progenitor", "glial cell"], "mechanism_keywords": ["synaptic", "connectivity", "plasticity", "development", "degeneration", "regeneration"], "application_keywords": ["Alzheimer", "<PERSON>", "autism", "depression", "schizophrenia", "stroke"], "weight": 1.3, "related_domains": ["development", "psychiatry"]}, "developmental_biology": {"name": "发育生物学", "description": "细胞分化、发育轨迹、胚胎发育研究", "core_keywords": ["development", "differentiation", "embryonic", "developmental", "morphogenesis", "organogenesis"], "cellular_keywords": ["stem cell", "progenitor", "differentiation", "lineage", "embryonic stem cell", "induced pluripotent"], "mechanism_keywords": ["cell fate", "trajectory", "reprogramming", "regeneration", "specification", "commitment"], "application_keywords": ["organoid", "tissue engineering", "regenerative medicine", "disease modeling"], "weight": 1.2, "related_domains": ["stem_cell", "regenerative_medicine"]}, "metabolism": {"name": "代谢学", "description": "代谢过程、能量代谢、代谢疾病研究", "core_keywords": ["metabolism", "metabolic", "glucose", "insulin", "lipid", "metabolome", "energy"], "cellular_keywords": ["hepatocyte", "adipocyte", "pancreatic beta cell", "muscle cell", "metabolic cell"], "mechanism_keywords": ["glycolysis", "oxidative phosphorylation", "lipogenesis", "gluconeogenesis", "autophagy"], "application_keywords": ["diabetes", "obesity", "metabolic syndrome", "fatty liver", "metabolic engineering"], "weight": 1.1, "related_domains": ["endocrinology", "nutrition"]}, "stem_cell": {"name": "干细胞研究", "description": "干细胞生物学、再生医学研究", "core_keywords": ["stem cell", "pluripotent", "multipotent", "regenerative", "self-renewal"], "cellular_keywords": ["embryonic stem cell", "induced pluripotent stem cell", "mesenchymal stem cell", "hematopoietic stem cell"], "mechanism_keywords": ["pluripotency", "reprogramming", "differentiation", "self-renewal", "lineage commitment"], "application_keywords": ["regenerative medicine", "cell therapy", "tissue engineering", "disease modeling"], "weight": 1.3, "related_domains": ["development", "regenerative_medicine"]}, "microbiome": {"name": "微生物组学", "description": "微生物群落、宿主-微生物相互作用研究", "core_keywords": ["microbiome", "microbiota", "microbial", "bacterial", "viral", "fungal"], "cellular_keywords": ["bacteria", "virus", "fungi", "archaea", "microbial community"], "mechanism_keywords": ["host-microbe interaction", "dysbiosis", "colonization", "metabolic interaction"], "application_keywords": ["gut microbiome", "skin microbiome", "oral microbiome", "probiotic", "antibiotic"], "weight": 1.2, "related_domains": ["immunology", "metabolism"]}, "cardiovascular": {"name": "心血管研究", "description": "心脏、血管、循环系统相关研究", "core_keywords": ["cardiovascular", "cardiac", "heart", "vascular", "endothelial", "cardiomyocyte"], "cellular_keywords": ["cardiomyocyte", "endothelial cell", "smooth muscle cell", "pericyte", "cardiac fibroblast"], "mechanism_keywords": ["angiogenesis", "vasculogenesis", "cardiac development", "heart failure", "atherosclerosis"], "application_keywords": ["myocardial infarction", "hypertension", "stroke", "cardiac regeneration"], "weight": 1.2, "related_domains": ["development", "metabolism"]}}, "cross_domain_keywords": {"inflammation": {"domains": ["immunology", "oncology", "cardiovascular", "neuroscience"], "keywords": ["inflammation", "inflammatory", "cytokine", "chemokine", "NF-kappaB"]}, "apoptosis": {"domains": ["oncology", "neuroscience", "development", "immunology"], "keywords": ["apoptosis", "cell death", "programmed cell death", "caspase", "p53"]}, "signaling": {"domains": ["all"], "keywords": ["signaling", "pathway", "signal transduction", "receptor", "ligand"]}, "single_cell_technologies": {"domains": ["all"], "keywords": ["scRNA-seq", "single cell", "10x Genomics", "Smart-seq", "Drop-seq"]}}, "technology_specific_keywords": {"sequencing_technologies": {"scRNA-seq": ["single cell RNA sequencing", "scRNA-seq", "droplet-based", "plate-based"], "scATAC-seq": ["single cell ATAC-seq", "chromatin accessibility", "open chromatin"], "scDNA-seq": ["single cell DNA sequencing", "copy number variation", "genomic instability"], "spatial": ["spatial transcriptomics", "spatial resolution", "tissue architecture", "in situ"]}, "analysis_methods": {"clustering": ["clustering", "cell type identification", "unsupervised learning"], "trajectory": ["trajectory inference", "pseudotime", "lineage tracing", "developmental path"], "integration": ["data integration", "batch correction", "multi-modal", "cross-platform"], "visualization": ["t-SNE", "UMAP", "PCA", "dimensionality reduction"]}, "computational_tools": {"Seurat": ["<PERSON><PERSON><PERSON>", "R package", "data integration", "visualization"], "Scanpy": ["Scanpy", "Python", "single cell analysis", "preprocessing"], "CellRanger": ["Cell Ranger", "10x Genomics", "alignment", "quantification"], "Monocle": ["<PERSON><PERSON><PERSON>", "trajectory inference", "pseudotime", "differential expression"]}}, "priority_weights": {"high_impact": 2.0, "medium_impact": 1.5, "standard": 1.0, "specialized": 1.2, "emerging": 1.3}, "search_strategies": {"broad_search": {"description": "使用核心关键词进行广泛搜索", "keyword_types": ["core_keywords"], "max_keywords": 5}, "focused_search": {"description": "使用特定机制或应用关键词进行精确搜索", "keyword_types": ["mechanism_keywords", "application_keywords"], "max_keywords": 8}, "comprehensive_search": {"description": "结合多种关键词类型进行全面搜索", "keyword_types": ["core_keywords", "cellular_keywords", "mechanism_keywords", "application_keywords"], "max_keywords": 12}}, "metadata": {"version": "1.0.0", "created_date": "2024-01-15", "last_modified": "2024-01-15", "total_domains": 8, "total_keywords": 300, "description": "生物医学研究领域关键词配置文件"}}