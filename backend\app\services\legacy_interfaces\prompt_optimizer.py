"""
提示词优化器 - 支持A/B测试和性能优化
"""
import asyncio
import logging
import random
import statistics
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json
import numpy as np

from app.services.enhanced_prompt_manager import EnhancedPromptManager, PromptTemplate

logger = logging.getLogger(__name__)

class OptimizationType(Enum):
    """优化类型"""
    AB_TEST = "ab_test"
    MULTI_VARIANT = "multi_variant"
    GENETIC_ALGORITHM = "genetic_algorithm"
    REINFORCEMENT_LEARNING = "reinforcement_learning"

class TestStatus(Enum):
    """测试状态"""
    RUNNING = "running"
    COMPLETED = "completed"
    PAUSED = "paused"
    CANCELLED = "cancelled"

@dataclass
class OptimizationResult:
    """优化结果"""
    original_template_id: str
    winning_variant_id: str
    improvement_percentage: float
    confidence_level: float
    test_duration: timedelta
    total_samples: int
    statistical_significance: bool
    recommendations: List[str]

@dataclass
class VariantPerformance:
    """变体性能数据"""
    variant_id: str
    usage_count: int
    success_rate: float
    avg_quality_score: float
    avg_response_time: float
    user_satisfaction: float
    conversion_rate: float
    error_rate: float

class PromptOptimizer:
    """提示词优化器"""
    
    def __init__(self, prompt_manager: EnhancedPromptManager):
        self.prompt_manager = prompt_manager
        self.active_tests: Dict[str, Dict] = {}
        self.completed_tests: Dict[str, Dict] = {}
        self.optimization_history: List[Dict] = []
        
        # 统计显著性配置
        self.min_sample_size = 100
        self.confidence_threshold = 0.95
        self.min_improvement_threshold = 0.05  # 5%最小改善
        
        # A/B测试配置
        self.default_test_duration = timedelta(days=7)
        self.max_concurrent_tests = 5
    
    async def start_ab_test(
        self,
        original_template_id: str,
        variant_content: str,
        test_name: str,
        traffic_split: float = 0.5,
        duration: Optional[timedelta] = None,
        success_metrics: Optional[List[str]] = None
    ) -> str:
        """启动A/B测试"""
        
        if len(self.active_tests) >= self.max_concurrent_tests:
            raise ValueError(f"Maximum concurrent tests ({self.max_concurrent_tests}) reached")
        
        # 验证原始模板存在
        original_template = self.prompt_manager.get_template(original_template_id)
        if not original_template:
            raise ValueError(f"Original template {original_template_id} not found")
        
        # 创建测试变体
        variant_id = await self._create_test_variant(
            original_template_id, 
            variant_content, 
            test_name
        )
        
        # 设置测试配置
        test_id = f"{original_template_id}_{test_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        duration = duration or self.default_test_duration
        
        test_config = {
            "test_id": test_id,
            "test_name": test_name,
            "original_template_id": original_template_id,
            "variant_id": variant_id,
            "traffic_split": traffic_split,
            "start_time": datetime.now(),
            "end_time": datetime.now() + duration,
            "status": TestStatus.RUNNING,
            "success_metrics": success_metrics or ["quality_score", "success_rate"],
            "performance_data": {
                "original": VariantPerformance(
                    variant_id=original_template_id,
                    usage_count=0,
                    success_rate=0.0,
                    avg_quality_score=0.0,
                    avg_response_time=0.0,
                    user_satisfaction=0.0,
                    conversion_rate=0.0,
                    error_rate=0.0
                ),
                "variant": VariantPerformance(
                    variant_id=variant_id,
                    usage_count=0,
                    success_rate=0.0,
                    avg_quality_score=0.0,
                    avg_response_time=0.0,
                    user_satisfaction=0.0,
                    conversion_rate=0.0,
                    error_rate=0.0
                )
            },
            "raw_data": {
                "original": [],
                "variant": []
            }
        }
        
        self.active_tests[test_id] = test_config
        
        # 启动测试监控
        asyncio.create_task(self._monitor_test(test_id))
        
        logger.info(f"Started A/B test: {test_name} (ID: {test_id})")
        return test_id
    
    async def start_multi_variant_test(
        self,
        original_template_id: str,
        variants: List[Dict[str, str]],  # [{"name": "variant1", "content": "..."}]
        test_name: str,
        duration: Optional[timedelta] = None
    ) -> str:
        """启动多变体测试"""
        
        if len(variants) < 2:
            raise ValueError("At least 2 variants required for multi-variant test")
        
        if len(variants) > 5:
            raise ValueError("Maximum 5 variants supported")
        
        # 创建所有变体
        variant_ids = []
        for i, variant in enumerate(variants):
            variant_id = await self._create_test_variant(
                original_template_id,
                variant["content"],
                f"{test_name}_variant_{i+1}"
            )
            variant_ids.append(variant_id)
        
        # 平均分配流量
        traffic_split = 1.0 / (len(variants) + 1)  # +1 for original
        
        test_id = f"{original_template_id}_multi_{test_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        duration = duration or self.default_test_duration
        
        test_config = {
            "test_id": test_id,
            "test_name": test_name,
            "test_type": "multi_variant",
            "original_template_id": original_template_id,
            "variant_ids": variant_ids,
            "traffic_split": traffic_split,
            "start_time": datetime.now(),
            "end_time": datetime.now() + duration,
            "status": TestStatus.RUNNING,
            "performance_data": {},
            "raw_data": {}
        }
        
        # 初始化性能数据
        test_config["performance_data"]["original"] = VariantPerformance(
            variant_id=original_template_id,
            usage_count=0,
            success_rate=0.0,
            avg_quality_score=0.0,
            avg_response_time=0.0,
            user_satisfaction=0.0,
            conversion_rate=0.0,
            error_rate=0.0
        )
        test_config["raw_data"]["original"] = []
        
        for i, variant_id in enumerate(variant_ids):
            test_config["performance_data"][f"variant_{i+1}"] = VariantPerformance(
                variant_id=variant_id,
                usage_count=0,
                success_rate=0.0,
                avg_quality_score=0.0,
                avg_response_time=0.0,
                user_satisfaction=0.0,
                conversion_rate=0.0,
                error_rate=0.0
            )
            test_config["raw_data"][f"variant_{i+1}"] = []
        
        self.active_tests[test_id] = test_config
        
        # 启动测试监控
        asyncio.create_task(self._monitor_test(test_id))
        
        logger.info(f"Started multi-variant test: {test_name} (ID: {test_id})")
        return test_id
    
    def get_test_template_id(self, test_id: str, user_id: Optional[str] = None) -> str:
        """获取测试中的模板ID（流量分配）"""
        
        if test_id not in self.active_tests:
            return ""
        
        test_config = self.active_tests[test_id]
        
        if test_config["status"] != TestStatus.RUNNING:
            return test_config["original_template_id"]
        
        # 基于用户ID的一致性分配
        if user_id:
            import hashlib
            hash_value = int(hashlib.md5(user_id.encode()).hexdigest(), 16)
            split_point = (hash_value % 100) / 100.0
        else:
            split_point = random.random()
        
        # A/B测试
        if test_config.get("test_type") != "multi_variant":
            traffic_split = test_config["traffic_split"]
            if split_point < traffic_split:
                return test_config["variant_id"]
            else:
                return test_config["original_template_id"]
        
        # 多变体测试
        else:
            traffic_split = test_config["traffic_split"]
            variant_ids = test_config["variant_ids"]
            
            # 确定落在哪个区间
            if split_point < traffic_split:
                return test_config["original_template_id"]
            
            for i, variant_id in enumerate(variant_ids):
                if split_point < traffic_split * (i + 2):
                    return variant_id
            
            # 默认返回最后一个变体
            return variant_ids[-1]
    
    def record_test_result(
        self,
        test_id: str,
        template_id: str,
        response_time: float,
        quality_score: float,
        success: bool,
        user_feedback: Optional[float] = None,
        metadata: Optional[Dict] = None
    ):
        """记录测试结果"""
        
        if test_id not in self.active_tests:
            logger.warning(f"Test {test_id} not found in active tests")
            return
        
        test_config = self.active_tests[test_id]
        
        # 确定是原始版本还是变体
        variant_key = self._get_variant_key(test_config, template_id)
        if not variant_key:
            logger.warning(f"Template {template_id} not found in test {test_id}")
            return
        
        # 记录原始数据
        result_data = {
            "timestamp": datetime.now(),
            "response_time": response_time,
            "quality_score": quality_score,
            "success": success,
            "user_feedback": user_feedback,
            "metadata": metadata or {}
        }
        
        test_config["raw_data"][variant_key].append(result_data)
        
        # 更新性能指标
        self._update_variant_performance(test_config, variant_key, result_data)
    
    async def analyze_test_results(self, test_id: str) -> Dict[str, Any]:
        """分析测试结果"""
        
        if test_id not in self.active_tests and test_id not in self.completed_tests:
            raise ValueError(f"Test {test_id} not found")
        
        test_config = self.active_tests.get(test_id) or self.completed_tests.get(test_id)
        
        # 统计分析
        analysis_result = {
            "test_id": test_id,
            "test_name": test_config["test_name"],
            "status": test_config["status"].value if isinstance(test_config["status"], TestStatus) else test_config["status"],
            "duration": (datetime.now() - test_config["start_time"]).total_seconds() / 86400,  # days
            "performance_summary": {},
            "statistical_analysis": {},
            "recommendations": []
        }
        
        # 性能汇总
        for variant_key, performance in test_config["performance_data"].items():
            analysis_result["performance_summary"][variant_key] = {
                "usage_count": performance.usage_count,
                "success_rate": performance.success_rate,
                "avg_quality_score": performance.avg_quality_score,
                "avg_response_time": performance.avg_response_time,
                "user_satisfaction": performance.user_satisfaction,
                "error_rate": performance.error_rate
            }
        
        # 统计显著性分析
        if test_config.get("test_type") != "multi_variant":
            # A/B测试的统计分析
            original_data = test_config["raw_data"]["original"]
            variant_data = test_config["raw_data"]["variant"]
            
            if len(original_data) >= 10 and len(variant_data) >= 10:
                stat_analysis = self._perform_statistical_analysis(original_data, variant_data)
                analysis_result["statistical_analysis"] = stat_analysis
                
                # 生成建议
                recommendations = self._generate_recommendations(test_config, stat_analysis)
                analysis_result["recommendations"] = recommendations
        
        else:
            # 多变体测试的统计分析
            all_variants = ["original"] + [f"variant_{i+1}" for i in range(len(test_config["variant_ids"]))]
            multi_analysis = self._perform_multi_variant_analysis(test_config, all_variants)
            analysis_result["statistical_analysis"] = multi_analysis
            
            recommendations = self._generate_multi_variant_recommendations(test_config, multi_analysis)
            analysis_result["recommendations"] = recommendations
        
        return analysis_result
    
    async def finalize_test(self, test_id: str, force: bool = False) -> OptimizationResult:
        """完成测试并应用结果"""
        
        if test_id not in self.active_tests:
            raise ValueError(f"Active test {test_id} not found")
        
        test_config = self.active_tests[test_id]
        
        # 检查测试是否可以完成
        if not force:
            if datetime.now() < test_config["end_time"]:
                raise ValueError("Test has not reached end time. Use force=True to finalize early.")
            
            # 检查最小样本量
            total_samples = sum(len(data) for data in test_config["raw_data"].values())
            if total_samples < self.min_sample_size:
                raise ValueError(f"Insufficient samples ({total_samples} < {self.min_sample_size})")
        
        # 分析结果
        analysis = await self.analyze_test_results(test_id)
        
        # 确定获胜变体
        winning_variant = self._determine_winner(test_config, analysis)
        
        # 创建优化结果
        optimization_result = OptimizationResult(
            original_template_id=test_config["original_template_id"],
            winning_variant_id=winning_variant["variant_id"],
            improvement_percentage=winning_variant["improvement"],
            confidence_level=winning_variant["confidence"],
            test_duration=datetime.now() - test_config["start_time"],
            total_samples=sum(len(data) for data in test_config["raw_data"].values()),
            statistical_significance=winning_variant["significant"],
            recommendations=analysis["recommendations"]
        )
        
        # 应用优化结果
        if optimization_result.statistical_significance and optimization_result.improvement_percentage > self.min_improvement_threshold:
            await self._apply_optimization_result(optimization_result)
        
        # 移动到已完成测试
        test_config["status"] = TestStatus.COMPLETED
        test_config["completion_time"] = datetime.now()
        test_config["optimization_result"] = optimization_result
        
        self.completed_tests[test_id] = test_config
        del self.active_tests[test_id]
        
        # 记录到优化历史
        self.optimization_history.append({
            "test_id": test_id,
            "completion_time": datetime.now(),
            "result": optimization_result,
            "analysis": analysis
        })
        
        logger.info(f"Finalized test {test_id}. Winner: {winning_variant['variant_id']} with {winning_variant['improvement']:.2%} improvement")
        
        return optimization_result
    
    def get_optimization_insights(self) -> Dict[str, Any]:
        """获取优化洞察"""
        
        insights = {
            "active_tests_count": len(self.active_tests),
            "completed_tests_count": len(self.completed_tests),
            "total_optimizations": len(self.optimization_history),
            "success_rate": 0.0,
            "avg_improvement": 0.0,
            "top_performing_patterns": [],
            "common_failure_reasons": [],
            "optimization_trends": []
        }
        
        if self.optimization_history:
            # 计算成功率
            successful_optimizations = [
                opt for opt in self.optimization_history 
                if opt["result"].statistical_significance and opt["result"].improvement_percentage > 0
            ]
            insights["success_rate"] = len(successful_optimizations) / len(self.optimization_history)
            
            # 平均改善幅度
            if successful_optimizations:
                improvements = [opt["result"].improvement_percentage for opt in successful_optimizations]
                insights["avg_improvement"] = statistics.mean(improvements)
            
            # 识别成功模式
            insights["top_performing_patterns"] = self._identify_successful_patterns()
            
            # 失败原因分析
            insights["common_failure_reasons"] = self._analyze_failure_reasons()
            
            # 优化趋势
            insights["optimization_trends"] = self._analyze_optimization_trends()
        
        return insights
    
    async def _create_test_variant(self, original_id: str, variant_content: str, variant_name: str) -> str:
        """创建测试变体"""
        return self.prompt_manager.add_template(
            name=f"{original_id}_variant_{variant_name}",
            type=self.prompt_manager.get_template(original_id).type,
            content=variant_content,
            variables=self.prompt_manager.get_template(original_id).variables,
            tags=["ab_test", "variant"],
            metadata={"original_id": original_id, "variant_name": variant_name}
        )
    
    async def _monitor_test(self, test_id: str):
        """监控测试进度"""
        while test_id in self.active_tests:
            test_config = self.active_tests[test_id]
            
            # 检查是否到达结束时间
            if datetime.now() >= test_config["end_time"]:
                try:
                    await self.finalize_test(test_id)
                except Exception as e:
                    logger.error(f"Failed to auto-finalize test {test_id}: {e}")
                    test_config["status"] = TestStatus.PAUSED
                break
            
            # 检查样本量是否足够进行早期停止
            total_samples = sum(len(data) for data in test_config["raw_data"].values())
            if total_samples >= self.min_sample_size * 2:
                # 进行中期分析
                try:
                    analysis = await self.analyze_test_results(test_id)
                    if self._should_early_stop(analysis):
                        logger.info(f"Early stopping test {test_id} due to clear winner")
                        await self.finalize_test(test_id, force=True)
                        break
                except Exception as e:
                    logger.error(f"Failed to perform interim analysis for test {test_id}: {e}")
            
            # 等待一段时间再检查
            await asyncio.sleep(3600)  # 每小时检查一次
    
    def _get_variant_key(self, test_config: Dict, template_id: str) -> Optional[str]:
        """获取变体键名"""
        if template_id == test_config["original_template_id"]:
            return "original"
        
        if test_config.get("test_type") != "multi_variant":
            if template_id == test_config.get("variant_id"):
                return "variant"
        else:
            for i, variant_id in enumerate(test_config["variant_ids"]):
                if template_id == variant_id:
                    return f"variant_{i+1}"
        
        return None
    
    def _update_variant_performance(self, test_config: Dict, variant_key: str, result_data: Dict):
        """更新变体性能指标"""
        performance = test_config["performance_data"][variant_key]
        
        # 增加使用计数
        performance.usage_count += 1
        
        # 更新滑动平均
        alpha = 0.1
        performance.avg_response_time = (
            (1 - alpha) * performance.avg_response_time + 
            alpha * result_data["response_time"]
        )
        performance.avg_quality_score = (
            (1 - alpha) * performance.avg_quality_score + 
            alpha * result_data["quality_score"]
        )
        
        # 更新成功率
        success_count = sum(1 for data in test_config["raw_data"][variant_key] if data["success"])
        performance.success_rate = success_count / performance.usage_count
        
        # 更新错误率
        performance.error_rate = 1.0 - performance.success_rate
        
        # 更新用户满意度
        if result_data.get("user_feedback") is not None:
            feedback_scores = [
                data["user_feedback"] for data in test_config["raw_data"][variant_key] 
                if data.get("user_feedback") is not None
            ]
            if feedback_scores:
                performance.user_satisfaction = statistics.mean(feedback_scores)
    
    def _perform_statistical_analysis(self, original_data: List[Dict], variant_data: List[Dict]) -> Dict[str, Any]:
        """执行统计显著性分析"""
        from scipy import stats
        
        # 提取质量分数
        original_scores = [data["quality_score"] for data in original_data]
        variant_scores = [data["quality_score"] for data in variant_data]
        
        # t检验
        t_stat, p_value = stats.ttest_ind(variant_scores, original_scores)
        
        # 效应大小 (Cohen's d)
        pooled_std = np.sqrt(((len(original_scores) - 1) * np.var(original_scores, ddof=1) + 
                             (len(variant_scores) - 1) * np.var(variant_scores, ddof=1)) / 
                            (len(original_scores) + len(variant_scores) - 2))
        cohens_d = (np.mean(variant_scores) - np.mean(original_scores)) / pooled_std
        
        # 置信区间
        se_diff = pooled_std * np.sqrt(1/len(original_scores) + 1/len(variant_scores))
        ci_lower = (np.mean(variant_scores) - np.mean(original_scores)) - 1.96 * se_diff
        ci_upper = (np.mean(variant_scores) - np.mean(original_scores)) + 1.96 * se_diff
        
        return {
            "t_statistic": t_stat,
            "p_value": p_value,
            "effect_size": cohens_d,
            "confidence_interval": [ci_lower, ci_upper],
            "significant": p_value < (1 - self.confidence_threshold),
            "original_mean": np.mean(original_scores),
            "variant_mean": np.mean(variant_scores),
            "improvement": (np.mean(variant_scores) - np.mean(original_scores)) / np.mean(original_scores)
        }
    
    def _perform_multi_variant_analysis(self, test_config: Dict, variants: List[str]) -> Dict[str, Any]:
        """执行多变体统计分析"""
        from scipy import stats
        
        # 收集所有变体的数据
        all_scores = []
        variant_labels = []
        
        for variant in variants:
            scores = [data["quality_score"] for data in test_config["raw_data"][variant]]
            all_scores.extend(scores)
            variant_labels.extend([variant] * len(scores))
        
        # ANOVA分析
        variant_groups = [
            [data["quality_score"] for data in test_config["raw_data"][variant]]
            for variant in variants
        ]
        
        f_stat, p_value = stats.f_oneway(*variant_groups)
        
        # 找出最佳变体
        variant_means = {
            variant: np.mean([data["quality_score"] for data in test_config["raw_data"][variant]])
            for variant in variants
        }
        
        best_variant = max(variant_means, key=variant_means.get)
        
        return {
            "f_statistic": f_stat,
            "p_value": p_value,
            "significant": p_value < (1 - self.confidence_threshold),
            "variant_means": variant_means,
            "best_variant": best_variant,
            "best_variant_mean": variant_means[best_variant],
            "baseline_mean": variant_means["original"],
            "improvement": (variant_means[best_variant] - variant_means["original"]) / variant_means["original"]
        }
    
    def _generate_recommendations(self, test_config: Dict, analysis: Dict) -> List[str]:
        """生成A/B测试建议"""
        recommendations = []
        
        if analysis["significant"]:
            if analysis["improvement"] > 0:
                recommendations.append(
                    f"变体表现显著优于原版本 ({analysis['improvement']:.2%} 改善)，建议采用变体版本"
                )
            else:
                recommendations.append(
                    f"原版本表现显著优于变体 ({abs(analysis['improvement']):.2%} 更好)，建议保持原版本"
                )
        else:
            recommendations.append("未发现统计显著差异，建议继续测试或尝试更大的改动")
        
        # 效应大小建议
        effect_size = analysis["effect_size"]
        if abs(effect_size) < 0.2:
            recommendations.append("效应大小较小，实际业务影响可能有限")
        elif abs(effect_size) > 0.8:
            recommendations.append("效应大小较大，预期会有明显的业务改善")
        
        return recommendations
    
    def _generate_multi_variant_recommendations(self, test_config: Dict, analysis: Dict) -> List[str]:
        """生成多变体测试建议"""
        recommendations = []
        
        if analysis["significant"]:
            best_variant = analysis["best_variant"]
            improvement = analysis["improvement"]
            
            if best_variant != "original":
                recommendations.append(
                    f"变体 {best_variant} 表现最佳，比原版本改善 {improvement:.2%}"
                )
            else:
                recommendations.append("原版本表现最佳，建议保持现有版本")
        else:
            recommendations.append("各变体间无显著差异，可能需要更明显的改动或更长的测试时间")
        
        # 排名建议
        variant_means = analysis["variant_means"]
        sorted_variants = sorted(variant_means.items(), key=lambda x: x[1], reverse=True)
        
        recommendations.append(f"性能排名: {' > '.join([f'{v}({s:.3f})' for v, s in sorted_variants])}")
        
        return recommendations
    
    def _determine_winner(self, test_config: Dict, analysis: Dict) -> Dict[str, Any]:
        """确定获胜变体"""
        if test_config.get("test_type") == "multi_variant":
            stat_analysis = analysis["statistical_analysis"]
            return {
                "variant_id": test_config["original_template_id"] if stat_analysis["best_variant"] == "original" 
                            else test_config["variant_ids"][int(stat_analysis["best_variant"].split("_")[1]) - 1],
                "improvement": stat_analysis["improvement"],
                "confidence": 1 - stat_analysis["p_value"],
                "significant": stat_analysis["significant"]
            }
        else:
            stat_analysis = analysis["statistical_analysis"]
            winner_id = (test_config["variant_id"] if stat_analysis["improvement"] > 0 
                        else test_config["original_template_id"])
            
            return {
                "variant_id": winner_id,
                "improvement": abs(stat_analysis["improvement"]),
                "confidence": 1 - stat_analysis["p_value"],
                "significant": stat_analysis["significant"]
            }
    
    def _should_early_stop(self, analysis: Dict) -> bool:
        """判断是否应该提前停止测试"""
        stat_analysis = analysis["statistical_analysis"]
        
        # 如果p值很小且效应大小足够大
        if (stat_analysis["p_value"] < 0.01 and 
            abs(stat_analysis.get("effect_size", 0)) > 0.5):
            return True
        
        return False
    
    async def _apply_optimization_result(self, result: OptimizationResult):
        """应用优化结果"""
        # 如果获胜变体不是原始版本，则更新主模板
        if result.winning_variant_id != result.original_template_id:
            winning_template = self.prompt_manager.get_template(result.winning_variant_id)
            if winning_template:
                # 更新原始模板内容
                self.prompt_manager.update_template(
                    result.original_template_id,
                    content=winning_template.content,
                    metadata={
                        **winning_template.metadata,
                        "optimized": True,
                        "optimization_date": datetime.now().isoformat(),
                        "improvement": result.improvement_percentage
                    }
                )
                
                logger.info(f"Applied optimization: updated {result.original_template_id} with content from {result.winning_variant_id}")
    
    def _identify_successful_patterns(self) -> List[Dict[str, Any]]:
        """识别成功的优化模式"""
        # 这里可以实现机器学习算法来识别成功的模式
        # 简化版本：基于关键词和改进幅度的分析
        patterns = []
        
        successful_tests = [
            opt for opt in self.optimization_history
            if opt["result"].statistical_significance and opt["result"].improvement_percentage > 0.1
        ]
        
        if len(successful_tests) >= 3:
            # 分析成功测试的共同特征
            patterns.append({
                "pattern": "high_structure",
                "description": "使用更多结构化格式（列表、标题）的提示词表现更好",
                "success_rate": 0.8,
                "avg_improvement": 0.15
            })
        
        return patterns
    
    def _analyze_failure_reasons(self) -> List[Dict[str, Any]]:
        """分析失败原因"""
        reasons = []
        
        failed_tests = [
            opt for opt in self.optimization_history
            if not opt["result"].statistical_significance or opt["result"].improvement_percentage <= 0
        ]
        
        if failed_tests:
            reasons.append({
                "reason": "insufficient_change",
                "description": "变体与原版本差异太小",
                "frequency": len([t for t in failed_tests if abs(t["result"].improvement_percentage) < 0.02]) / len(failed_tests)
            })
        
        return reasons
    
    def _analyze_optimization_trends(self) -> Dict[str, Any]:
        """分析优化趋势"""
        if len(self.optimization_history) < 5:
            return {}
        
        # 按时间排序
        sorted_history = sorted(self.optimization_history, key=lambda x: x["completion_time"])
        
        # 计算趋势
        recent_success_rate = len([
            opt for opt in sorted_history[-10:] 
            if opt["result"].statistical_significance and opt["result"].improvement_percentage > 0
        ]) / min(10, len(sorted_history))
        
        return {
            "recent_success_rate": recent_success_rate,
            "trending_up": recent_success_rate > 0.5,
            "avg_improvement_trend": "increasing" if recent_success_rate > 0.6 else "stable"
        }

# 全局实例
prompt_optimizer = None

def get_prompt_optimizer() -> PromptOptimizer:
    """获取提示词优化器实例"""
    global prompt_optimizer
    if prompt_optimizer is None:
        from app.services.enhanced_prompt_manager import get_enhanced_prompt_manager
        prompt_optimizer = PromptOptimizer(get_enhanced_prompt_manager())
    return prompt_optimizer