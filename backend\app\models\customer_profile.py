"""
客户画像数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, <PERSON>olean, Float, JSON, Foreign<PERSON>ey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import enum

from app.core.database import Base


class ProfileDimensionType(str, enum.Enum):
    """画像维度类型"""
    RESEARCH_PROFILE = "research_profile"      # 研究画像
    TECHNICAL_PROFILE = "technical_profile"    # 技术画像
    BUSINESS_PROFILE = "business_profile"      # 商业画像
    BEHAVIORAL_PROFILE = "behavioral_profile"  # 行为画像
    PREFERENCE_PROFILE = "preference_profile"  # 偏好画像


class BehaviorEventType(str, enum.Enum):
    """行为事件类型"""
    PAGE_VIEW = "page_view"
    FEATURE_USE = "feature_use"
    CONVERSATION_START = "conversation_start"
    REQUIREMENT_SUBMIT = "requirement_submit"
    LITERATURE_ACCESS = "literature_access"
    DOWNLOAD_RESOURCE = "download_resource"
    FEEDBACK_SUBMIT = "feedback_submit"


class CustomerProfile(Base):
    """客户画像主表"""
    __tablename__ = "customer_profiles"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, nullable=False, index=True)
    
    # 画像基础信息
    profile_version = Column(String(20), default="1.0")
    last_updated = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    confidence_score = Column(Float, default=0.0)  # 画像置信度 0-1
    completeness_score = Column(Float, default=0.0)  # 画像完整度 0-1
    
    # 研究画像维度
    research_maturity = Column(String(50))  # 研究成熟度：初学者/中级/专家
    research_focus = Column(Text)  # JSON: 主要研究方向
    publication_level = Column(String(50))  # 发表水平：无/会议/期刊/高影响因子
    collaboration_preference = Column(String(50))  # 合作偏好：独立/小团队/大团队
    
    # 技术画像维度
    technical_expertise = Column(Text)  # JSON: 技术专长评估
    platform_preference = Column(Text)  # JSON: 平台偏好
    analysis_complexity = Column(String(50))  # 分析复杂度需求：基础/中级/高级
    data_handling_capability = Column(String(50))  # 数据处理能力
    
    # 商业画像维度
    budget_range = Column(String(50))  # 预算范围
    decision_authority = Column(String(50))  # 决策权限：建议者/影响者/决策者
    procurement_cycle = Column(String(50))  # 采购周期：快速/标准/长期
    cost_sensitivity = Column(Float, default=0.5)  # 成本敏感度 0-1
    
    # 行为画像维度
    engagement_level = Column(String(50))  # 参与度：低/中/高
    learning_style = Column(String(50))  # 学习风格：视觉/文本/交互
    communication_preference = Column(String(50))  # 沟通偏好：简洁/详细/技术性
    response_speed_preference = Column(String(50))  # 响应速度偏好：即时/标准/深思熟虑
    
    # 偏好画像维度
    content_preference = Column(Text)  # JSON: 内容偏好
    interaction_style = Column(String(50))  # 交互风格：探索型/目标导向/系统性
    information_depth = Column(String(50))  # 信息深度偏好：概览/详细/专业
    
    # 预测性指标
    conversion_probability = Column(Float, default=0.0)  # 转化概率
    lifetime_value_prediction = Column(Float, default=0.0)  # 预测生命周期价值
    churn_risk_score = Column(Float, default=0.0)  # 流失风险评分
    next_best_action = Column(Text)  # JSON: 下一步最佳行动建议
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    dimensions = relationship("ProfileDimension", back_populates="profile", cascade="all, delete-orphan")
    behavior_events = relationship("BehaviorEvent", back_populates="profile", cascade="all, delete-orphan")
    requirement_history = relationship("RequirementHistory", back_populates="profile", cascade="all, delete-orphan")


class ProfileDimension(Base):
    """画像维度详细数据"""
    __tablename__ = "profile_dimensions"

    id = Column(Integer, primary_key=True, index=True)
    profile_id = Column(Integer, ForeignKey("customer_profiles.id"), nullable=False, index=True)
    dimension_type = Column(Enum(ProfileDimensionType), nullable=False)
    dimension_name = Column(String(100), nullable=False)  # 具体维度名称
    
    # 维度值
    string_value = Column(String(500))
    numeric_value = Column(Float)
    json_value = Column(JSON)
    boolean_value = Column(Boolean)
    
    # 元数据
    confidence = Column(Float, default=0.0)  # 该维度的置信度
    source = Column(String(100))  # 数据来源
    last_updated = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    profile = relationship("CustomerProfile", back_populates="dimensions")


class BehaviorEvent(Base):
    """用户行为事件记录"""
    __tablename__ = "behavior_events"

    id = Column(Integer, primary_key=True, index=True)
    profile_id = Column(Integer, ForeignKey("customer_profiles.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 事件信息
    event_type = Column(Enum(BehaviorEventType), nullable=False)
    event_name = Column(String(100), nullable=False)
    event_data = Column(JSON)  # 事件详细数据
    
    # 上下文信息
    session_id = Column(String(100))
    page_url = Column(String(500))
    user_agent = Column(String(500))
    ip_address = Column(String(50))
    
    # 时间信息
    event_timestamp = Column(DateTime(timezone=True), server_default=func.now())
    session_duration = Column(Integer)  # 会话持续时间（秒）
    
    # 关系
    profile = relationship("CustomerProfile", back_populates="behavior_events")


class RequirementHistory(Base):
    """需求历史记录"""
    __tablename__ = "requirement_history"

    id = Column(Integer, primary_key=True, index=True)
    profile_id = Column(Integer, ForeignKey("customer_profiles.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 需求信息
    requirement_data = Column(JSON, nullable=False)  # 完整的需求数据
    requirement_type = Column(String(50))  # 需求类型：initial/update/refinement
    completeness_score = Column(Float, default=0.0)  # 需求完整度
    
    # 分析结果
    extracted_insights = Column(JSON)  # 从需求中提取的洞察
    recommended_solutions = Column(JSON)  # 推荐的解决方案
    estimated_budget = Column(Float)  # 估算预算
    complexity_score = Column(Float, default=0.0)  # 复杂度评分
    
    # 时间信息
    submitted_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    profile = relationship("CustomerProfile", back_populates="requirement_history")
