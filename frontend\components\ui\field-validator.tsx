"use client"

import * as React from "react"
import { AlertCircle, CheckCircle, Info, AlertTriangle } from "lucide-react"
import { cn } from "@/lib/utils"

export interface ValidationRule {
  type: "required" | "minLength" | "maxLength" | "pattern" | "custom"
  message: string
  value?: any
  validator?: (value: any) => boolean
}

export interface ValidationResult {
  isValid: boolean
  level: "error" | "warning" | "info" | "success"
  message: string
}

interface FieldValidatorProps {
  value: any
  rules: ValidationRule[]
  children: React.ReactNode
  className?: string
  showValidation?: boolean
  onValidationChange?: (result: ValidationResult | null) => void
}

export function FieldValidator({
  value,
  rules,
  children,
  className,
  showValidation = true,
  onValidationChange
}: FieldValidatorProps) {
  const [validationResult, setValidationResult] = React.useState<ValidationResult | null>(null)

  const validateField = React.useCallback((fieldValue: any): ValidationResult | null => {
    // 如果没有值且没有必需规则，则不显示验证
    if (!fieldValue && !rules.some(rule => rule.type === "required")) {
      return null
    }

    for (const rule of rules) {
      switch (rule.type) {
        case "required":
          if (!fieldValue || (typeof fieldValue === "string" && fieldValue.trim() === "")) {
            return {
              isValid: false,
              level: "error",
              message: rule.message
            }
          }
          break

        case "minLength":
          if (fieldValue && fieldValue.length < rule.value) {
            return {
              isValid: false,
              level: "error",
              message: rule.message
            }
          }
          break

        case "maxLength":
          if (fieldValue && fieldValue.length > rule.value) {
            return {
              isValid: false,
              level: "error",
              message: rule.message
            }
          }
          break

        case "pattern":
          if (fieldValue && !rule.value.test(fieldValue)) {
            return {
              isValid: false,
              level: "error",
              message: rule.message
            }
          }
          break

        case "custom":
          if (rule.validator && !rule.validator(fieldValue)) {
            return {
              isValid: false,
              level: "error",
              message: rule.message
            }
          }
          break
      }
    }

    // 如果有值且通过所有验证，显示成功状态
    if (fieldValue) {
      return {
        isValid: true,
        level: "success",
        message: "验证通过"
      }
    }

    return null
  }, [rules])

  React.useEffect(() => {
    const result = validateField(value)
    setValidationResult(result)
    onValidationChange?.(result)
  }, [value, validateField, onValidationChange])

  const getValidationIcon = (level: string) => {
    switch (level) {
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-amber-500" />
      case "info":
        return <Info className="h-4 w-4 text-blue-500" />
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return null
    }
  }

  const getValidationStyles = (level: string) => {
    switch (level) {
      case "error":
        return "border-red-300 focus-within:border-red-500 focus-within:ring-red-500"
      case "warning":
        return "border-amber-300 focus-within:border-amber-500 focus-within:ring-amber-500"
      case "info":
        return "border-blue-300 focus-within:border-blue-500 focus-within:ring-blue-500"
      case "success":
        return "border-green-300 focus-within:border-green-500 focus-within:ring-green-500"
      default:
        return ""
    }
  }

  return (
    <div className={cn("space-y-1", className)}>
      <div className={cn(
        "relative",
        validationResult && getValidationStyles(validationResult.level)
      )}>
        {children}
        
        {/* 验证图标 */}
        {validationResult && showValidation && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
            {getValidationIcon(validationResult.level)}
          </div>
        )}
      </div>

      {/* 验证消息 */}
      {validationResult && showValidation && validationResult.level !== "success" && (
        <div className={cn(
          "flex items-center gap-1 text-xs",
          {
            "text-red-600": validationResult.level === "error",
            "text-amber-600": validationResult.level === "warning",
            "text-blue-600": validationResult.level === "info",
            "text-green-600": validationResult.level === "success"
          }
        )}>
          {getValidationIcon(validationResult.level)}
          <span>{validationResult.message}</span>
        </div>
      )}
    </div>
  )
}

// 预定义的验证规则
export const ValidationRules = {
  required: (message = "此字段为必填项"): ValidationRule => ({
    type: "required",
    message
  }),

  minLength: (length: number, message?: string): ValidationRule => ({
    type: "minLength",
    value: length,
    message: message || `最少需要 ${length} 个字符`
  }),

  maxLength: (length: number, message?: string): ValidationRule => ({
    type: "maxLength",
    value: length,
    message: message || `最多允许 ${length} 个字符`
  }),

  cellCount: (): ValidationRule => ({
    type: "pattern",
    value: /^[\d,\-\s<>]+$/,
    message: "请输入有效的细胞数量格式，如：5,000-10,000"
  }),

  budget: (): ValidationRule => ({
    type: "custom",
    validator: (value: string) => {
      if (!value) return false
      return ["5万以下", "5-10万", "10-20万", "20-50万", "50万以上"].includes(value)
    },
    message: "请选择有效的预算范围"
  }),

  speciesCompatibility: (experimentType: string): ValidationRule => ({
    type: "custom",
    validator: (speciesType: string) => {
      // 检查物种和实验类型的兼容性
      if (!speciesType || !experimentType) return true
      
      // 植物样本不支持某些实验类型
      if (speciesType.includes("拟南芥") && experimentType.includes("ATAC")) {
        return false
      }
      
      return true
    },
    message: "所选物种类型与实验类型不兼容"
  })
}

// 智能建议组件
interface SmartSuggestionProps {
  field: string
  value: any
  onSuggestionApply: (suggestion: string) => void
  className?: string
}

export function SmartSuggestion({
  field,
  value,
  onSuggestionApply,
  className
}: SmartSuggestionProps) {
  const getSuggestions = () => {
    switch (field) {
      case "cellCount":
        if (!value) {
          return [
            { text: "5,000-10,000", reason: "适合大多数研究" },
            { text: "10,000-20,000", reason: "适合复杂组织" }
          ]
        }
        break
      
      case "sequencingDepth":
        if (!value) {
          return [
            { text: "50,000 reads/cell", reason: "标准深度" },
            { text: "100,000 reads/cell", reason: "高质量分析" }
          ]
        }
        break
    }
    return []
  }

  const suggestions = getSuggestions()

  if (suggestions.length === 0) return null

  return (
    <div className={cn("mt-2 space-y-1", className)}>
      <div className="text-xs text-slate-500 mb-1">💡 智能建议：</div>
      {suggestions.map((suggestion, index) => (
        <button
          key={index}
          onClick={() => onSuggestionApply(suggestion.text)}
          className="w-full text-left p-2 text-xs bg-blue-50 hover:bg-blue-100 rounded border border-blue-200 transition-colors"
        >
          <div className="font-medium text-blue-800">{suggestion.text}</div>
          <div className="text-blue-600">{suggestion.reason}</div>
        </button>
      ))}
    </div>
  )
}
