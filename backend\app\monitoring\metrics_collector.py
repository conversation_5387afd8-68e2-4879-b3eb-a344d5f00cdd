"""
指标收集器
收集系统性能指标、API指标和业务指标
"""

import time
import threading
import psutil
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)


@dataclass
class MetricPoint:
    """指标数据点"""
    timestamp: float
    metric_name: str
    value: float
    tags: Dict[str, str]
    

@dataclass
class APIMetrics:
    """API性能指标"""
    endpoint: str
    method: str
    response_time: float
    status_code: int
    timestamp: float
    user_id: Optional[str] = None
    

@dataclass
class BusinessMetrics:
    """业务指标"""
    metric_type: str
    value: float
    context: Dict[str, Any]
    timestamp: float


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, config_path: str = None):
        self.config = self._load_config(config_path)
        self.is_running = False
        self.collection_thread = None
        
        # 内存中的指标存储
        self.api_metrics: deque = deque(maxlen=10000)
        self.business_metrics: deque = deque(maxlen=10000) 
        self.system_metrics: deque = deque(maxlen=10000)
        
        # 聚合统计
        self.api_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0,
            'error_count': 0,
            'response_times': deque(maxlen=1000)
        })
        
        # 业务指标统计
        self.business_stats = defaultdict(lambda: {
            'success_count': 0,
            'failure_count': 0,
            'values': deque(maxlen=1000)
        })
        
        # 系统资源使用历史
        self.system_history = {
            'cpu_usage': deque(maxlen=100),
            'memory_usage': deque(maxlen=100),
            'disk_usage': deque(maxlen=100)
        }
        
        # 初始化数据库
        self._init_database()
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置"""
        if not config_path:
            config_path = "/mnt/c/Users/<USER>/Desktop/Dev/CellForge AI/backend/app/monitoring/monitoring_config.json"
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载监控配置失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "monitoring": {
                "enabled": True,
                "collection_interval": 60,
                "retention_days": 30,
                "metrics": {
                    "api_performance": {"enabled": True},
                    "business_metrics": {"enabled": True},
                    "system_metrics": {"enabled": True}
                }
            }
        }
    
    def _init_database(self):
        """初始化数据库表"""
        try:
            conn = sqlite3.connect('/mnt/c/Users/<USER>/Desktop/Dev/CellForge AI/backend/cellforge.db')
            cursor = conn.cursor()
            
            # 创建指标表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS metrics_api (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL,
                    endpoint TEXT,
                    method TEXT,
                    response_time REAL,
                    status_code INTEGER,
                    user_id TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS metrics_business (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL,
                    metric_type TEXT,
                    value REAL,
                    context TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS metrics_system (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL,
                    metric_name TEXT,
                    value REAL,
                    tags TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_api_timestamp ON metrics_api(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_api_endpoint ON metrics_api(endpoint)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_business_type ON metrics_business(metric_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_system_metric ON metrics_system(metric_name)')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"初始化数据库失败: {e}")
    
    def start_collection(self):
        """开始收集指标"""
        if self.is_running:
            return
            
        self.is_running = True
        self.collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        self.collection_thread.start()
        logger.info("指标收集器已启动")
    
    def stop_collection(self):
        """停止收集指标"""
        self.is_running = False
        if self.collection_thread:
            self.collection_thread.join(timeout=5)
        logger.info("指标收集器已停止")
    
    def _collection_loop(self):
        """指标收集循环"""
        interval = self.config.get("monitoring", {}).get("collection_interval", 60)
        
        while self.is_running:
            try:
                self._collect_system_metrics()
                self._persist_metrics()
                self._cleanup_old_metrics()
                time.sleep(interval)
            except Exception as e:
                logger.error(f"指标收集错误: {e}")
                time.sleep(10)
    
    def record_api_metric(self, endpoint: str, method: str, response_time: float, 
                         status_code: int, user_id: str = None):
        """记录API指标"""
        timestamp = time.time()
        
        metric = APIMetrics(
            endpoint=endpoint,
            method=method,
            response_time=response_time,
            status_code=status_code,
            timestamp=timestamp,
            user_id=user_id
        )
        
        self.api_metrics.append(metric)
        
        # 更新统计
        key = f"{method}:{endpoint}"
        stats = self.api_stats[key]
        stats['count'] += 1
        stats['total_time'] += response_time
        stats['response_times'].append(response_time)
        
        if status_code >= 400:
            stats['error_count'] += 1
    
    def record_business_metric(self, metric_type: str, value: float, context: Dict[str, Any] = None):
        """记录业务指标"""
        timestamp = time.time()
        
        metric = BusinessMetrics(
            metric_type=metric_type,
            value=value,
            context=context or {},
            timestamp=timestamp
        )
        
        self.business_metrics.append(metric)
        
        # 更新统计
        stats = self.business_stats[metric_type]
        stats['values'].append(value)
        
        if metric_type.endswith('_success_rate') or metric_type.endswith('_hit_rate'):
            if value > 0.5:  # 成功率阈值
                stats['success_count'] += 1
            else:
                stats['failure_count'] += 1
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        timestamp = time.time()
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self._add_system_metric('cpu_usage_percent', cpu_percent, timestamp)
            self.system_history['cpu_usage'].append((timestamp, cpu_percent))
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self._add_system_metric('memory_usage_percent', memory_percent, timestamp)
            self._add_system_metric('memory_used_bytes', memory.used, timestamp)
            self.system_history['memory_usage'].append((timestamp, memory_percent))
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self._add_system_metric('disk_usage_percent', disk_percent, timestamp)
            self.system_history['disk_usage'].append((timestamp, disk_percent))
            
            # 网络IO
            net_io = psutil.net_io_counters()
            self._add_system_metric('network_bytes_sent', net_io.bytes_sent, timestamp)
            self._add_system_metric('network_bytes_recv', net_io.bytes_recv, timestamp)
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
    
    def _add_system_metric(self, name: str, value: float, timestamp: float):
        """添加系统指标"""
        metric = MetricPoint(
            timestamp=timestamp,
            metric_name=name,
            value=value,
            tags={"source": "system"}
        )
        self.system_metrics.append(metric)
    
    def _persist_metrics(self):
        """持久化指标到数据库"""
        try:
            conn = sqlite3.connect('/mnt/c/Users/<USER>/Desktop/Dev/CellForge AI/backend/cellforge.db')
            cursor = conn.cursor()
            
            # 批量插入API指标
            api_data = []
            for metric in list(self.api_metrics):
                if hasattr(metric, '_persisted'):
                    continue
                api_data.append((
                    metric.timestamp, metric.endpoint, metric.method,
                    metric.response_time, metric.status_code, metric.user_id
                ))
                metric._persisted = True
            
            if api_data:
                cursor.executemany('''
                    INSERT INTO metrics_api (timestamp, endpoint, method, response_time, status_code, user_id)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', api_data)
            
            # 批量插入业务指标
            business_data = []
            for metric in list(self.business_metrics):
                if hasattr(metric, '_persisted'):
                    continue
                business_data.append((
                    metric.timestamp, metric.metric_type, 
                    metric.value, json.dumps(metric.context)
                ))
                metric._persisted = True
            
            if business_data:
                cursor.executemany('''
                    INSERT INTO metrics_business (timestamp, metric_type, value, context)
                    VALUES (?, ?, ?, ?)
                ''', business_data)
            
            # 批量插入系统指标
            system_data = []
            for metric in list(self.system_metrics):
                if hasattr(metric, '_persisted'):
                    continue
                system_data.append((
                    metric.timestamp, metric.metric_name,
                    metric.value, json.dumps(metric.tags)
                ))
                metric._persisted = True
            
            if system_data:
                cursor.executemany('''
                    INSERT INTO metrics_system (timestamp, metric_name, value, tags)
                    VALUES (?, ?, ?, ?)
                ''', system_data)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"持久化指标失败: {e}")
    
    def _cleanup_old_metrics(self):
        """清理过期指标"""
        try:
            retention_days = self.config.get("monitoring", {}).get("retention_days", 30)
            cutoff_time = time.time() - (retention_days * 24 * 3600)
            
            conn = sqlite3.connect('/mnt/c/Users/<USER>/Desktop/Dev/CellForge AI/backend/cellforge.db')
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM metrics_api WHERE timestamp < ?', (cutoff_time,))
            cursor.execute('DELETE FROM metrics_business WHERE timestamp < ?', (cutoff_time,))
            cursor.execute('DELETE FROM metrics_system WHERE timestamp < ?', (cutoff_time,))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"清理过期指标失败: {e}")
    
    def get_api_stats(self, endpoint: str = None, time_window: int = 3600) -> Dict:
        """获取API统计信息"""
        current_time = time.time()
        start_time = current_time - time_window
        
        stats = {}
        
        for key, data in self.api_stats.items():
            if endpoint and endpoint not in key:
                continue
                
            response_times = [rt for rt in data['response_times'] if rt > 0]
            
            if response_times:
                stats[key] = {
                    'request_count': data['count'],
                    'error_count': data['error_count'],
                    'error_rate': data['error_count'] / max(data['count'], 1),
                    'avg_response_time': sum(response_times) / len(response_times),
                    'min_response_time': min(response_times),
                    'max_response_time': max(response_times),
                    'p95_response_time': self._calculate_percentile(response_times, 95),
                    'p99_response_time': self._calculate_percentile(response_times, 99)
                }
        
        return stats
    
    def get_business_stats(self, metric_type: str = None) -> Dict:
        """获取业务指标统计"""
        stats = {}
        
        for mt, data in self.business_stats.items():
            if metric_type and metric_type != mt:
                continue
                
            values = list(data['values'])
            if values:
                stats[mt] = {
                    'current_value': values[-1] if values else 0,
                    'average_value': sum(values) / len(values),
                    'min_value': min(values),
                    'max_value': max(values),
                    'success_count': data['success_count'],
                    'failure_count': data['failure_count'],
                    'total_count': len(values)
                }
        
        return stats
    
    def get_system_stats(self) -> Dict:
        """获取系统资源统计"""
        stats = {}
        
        for metric_name, history in self.system_history.items():
            if history:
                values = [value for _, value in history]
                stats[metric_name] = {
                    'current': values[-1] if values else 0,
                    'average': sum(values) / len(values),
                    'min': min(values),
                    'max': max(values),
                    'trend': self._calculate_trend(values)
                }
        
        return stats
    
    def _calculate_percentile(self, values: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not values:
            return 0
        
        sorted_values = sorted(values)
        index = int((len(sorted_values) - 1) * percentile / 100)
        return sorted_values[index]
    
    def _calculate_trend(self, values: List[float]) -> str:
        """计算趋势"""
        if len(values) < 2:
            return "stable"
        
        recent = values[-5:]  # 最近5个数据点
        earlier = values[-10:-5] if len(values) >= 10 else values[:-5]
        
        if not earlier:
            return "stable"
        
        recent_avg = sum(recent) / len(recent)
        earlier_avg = sum(earlier) / len(earlier)
        
        if recent_avg > earlier_avg * 1.1:
            return "increasing"
        elif recent_avg < earlier_avg * 0.9:
            return "decreasing"
        else:
            return "stable"
    
    def get_metrics_summary(self) -> Dict:
        """获取指标摘要"""
        return {
            'api_metrics': {
                'total_endpoints': len(self.api_stats),
                'total_requests': sum(stats['count'] for stats in self.api_stats.values()),
                'total_errors': sum(stats['error_count'] for stats in self.api_stats.values()),
                'avg_response_time': self._get_overall_avg_response_time()
            },
            'business_metrics': {
                'total_metrics': len(self.business_stats),
                'active_metrics': len([mt for mt, data in self.business_stats.items() if data['values']])
            },
            'system_metrics': {
                'collection_status': 'running' if self.is_running else 'stopped',
                'last_collection': max([timestamp for timestamp, _ in self.system_history['cpu_usage']] or [0])
            }
        }
    
    def _get_overall_avg_response_time(self) -> float:
        """获取整体平均响应时间"""
        all_times = []
        for stats in self.api_stats.values():
            all_times.extend(stats['response_times'])
        
        return sum(all_times) / len(all_times) if all_times else 0


# 全局指标收集器实例
metrics_collector = MetricsCollector()