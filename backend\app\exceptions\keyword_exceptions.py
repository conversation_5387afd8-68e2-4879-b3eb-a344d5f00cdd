"""
Custom exceptions for keyword generation and literature recommendation services
"""
from typing import Optional, Dict, Any


class KeywordGenerationError(Exception):
    """Base exception for keyword generation errors"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "KEYWORD_GENERATION_ERROR"
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses"""
        return {
            "error_type": self.__class__.__name__,
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details
        }


class InvalidInputError(KeywordGenerationError):
    """Raised when input validation fails"""
    
    def __init__(
        self, 
        message: str, 
        field: Optional[str] = None,
        value: Optional[Any] = None
    ):
        super().__init__(
            message, 
            error_code="INVALID_INPUT",
            details={"field": field, "value": str(value) if value is not None else None}
        )
        self.field = field
        self.value = value


class TerminologyMappingError(KeywordGenerationError):
    """Raised when terminology mapping fails"""
    
    def __init__(
        self, 
        message: str, 
        term: Optional[str] = None,
        mapping_type: Optional[str] = None
    ):
        super().__init__(
            message,
            error_code="TERMINOLOGY_MAPPING_ERROR", 
            details={"term": term, "mapping_type": mapping_type}
        )
        self.term = term
        self.mapping_type = mapping_type


class DomainClassificationError(KeywordGenerationError):
    """Raised when domain classification fails"""
    
    def __init__(
        self, 
        message: str,
        keywords: Optional[list] = None,
        failed_domains: Optional[list] = None
    ):
        super().__init__(
            message,
            error_code="DOMAIN_CLASSIFICATION_ERROR",
            details={"keywords": keywords, "failed_domains": failed_domains}
        )
        self.keywords = keywords
        self.failed_domains = failed_domains


class TranslationError(KeywordGenerationError):
    """Raised when translation fails"""
    
    def __init__(
        self, 
        message: str,
        source_term: Optional[str] = None,
        target_language: Optional[str] = None
    ):
        super().__init__(
            message,
            error_code="TRANSLATION_ERROR",
            details={"source_term": source_term, "target_language": target_language}
        )
        self.source_term = source_term
        self.target_language = target_language


class AIServiceError(KeywordGenerationError):
    """Raised when AI service calls fail"""
    
    def __init__(
        self, 
        message: str,
        service_name: Optional[str] = None,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message,
            error_code="AI_SERVICE_ERROR",
            details={
                "service_name": service_name,
                "status_code": status_code,
                "response_data": response_data
            }
        )
        self.service_name = service_name
        self.status_code = status_code
        self.response_data = response_data


class CacheError(KeywordGenerationError):
    """Raised when cache operations fail"""
    
    def __init__(
        self, 
        message: str,
        cache_key: Optional[str] = None,
        operation: Optional[str] = None
    ):
        super().__init__(
            message,
            error_code="CACHE_ERROR",
            details={"cache_key": cache_key, "operation": operation}
        )
        self.cache_key = cache_key
        self.operation = operation


class RateLimitError(KeywordGenerationError):
    """Raised when rate limits are exceeded"""
    
    def __init__(
        self, 
        message: str,
        service: Optional[str] = None,
        limit: Optional[int] = None,
        window: Optional[int] = None,
        retry_after: Optional[int] = None
    ):
        super().__init__(
            message,
            error_code="RATE_LIMIT_EXCEEDED",
            details={
                "service": service,
                "limit": limit,
                "window": window,
                "retry_after": retry_after
            }
        )
        self.service = service
        self.limit = limit
        self.window = window
        self.retry_after = retry_after


class ValidationError(InvalidInputError):
    """Raised when data validation fails"""
    
    def __init__(
        self, 
        message: str,
        validation_errors: Optional[Dict[str, list]] = None
    ):
        super().__init__(
            message,
            field="validation",
            value=validation_errors
        )
        self.validation_errors = validation_errors or {}


class ConfigurationError(KeywordGenerationError):
    """Raised when configuration is invalid or missing"""
    
    def __init__(
        self, 
        message: str,
        config_key: Optional[str] = None,
        expected_type: Optional[str] = None
    ):
        super().__init__(
            message,
            error_code="CONFIGURATION_ERROR", 
            details={"config_key": config_key, "expected_type": expected_type}
        )
        self.config_key = config_key
        self.expected_type = expected_type


class PerformanceError(KeywordGenerationError):
    """Raised when performance thresholds are exceeded"""
    
    def __init__(
        self, 
        message: str,
        operation: Optional[str] = None,
        duration: Optional[float] = None,
        threshold: Optional[float] = None
    ):
        super().__init__(
            message,
            error_code="PERFORMANCE_ERROR",
            details={
                "operation": operation,
                "duration": duration,
                "threshold": threshold
            }
        )
        self.operation = operation
        self.duration = duration
        self.threshold = threshold