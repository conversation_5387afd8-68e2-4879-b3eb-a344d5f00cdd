# 🚀 CellForge AI 快速配置指南

## 📋 必需配置 (最小工作配置)

### 1. 复制环境文件
```bash
cp .env.example .env
```

### 2. 配置AI服务 (必需)
编辑 `.env` 文件，配置DeepSeek API：
```env
OPENAI_API_KEY=your-deepseek-api-key-here
```

### 3. 配置NCBI PubMed API (推荐)
申请NCBI API密钥：https://ncbiinsights.ncbi.nlm.nih.gov/2017/11/02/new-api-keys-for-the-e-utilities/

```env
PUBMED_API_KEY=your-ncbi-api-key-here
```

## 🔍 文献搜索能力

### 基础配置 (只有NCBI)
- ✅ 权威生物医学文献
- ✅ PubMed 3000万+论文
- ✅ 医学专业数据库

### 增强配置 (NCBI + Google Scholar) - 强烈推荐
在 `.env` 中添加：
```env
SERPAPI_KEY=your-serpapi-key-here
```
申请地址：https://serpapi.com/ (免费账户每月100次搜索)

获得：
- ✅ 跨学科学术文献 (不仅限于生物医学)
- ✅ Google Scholar 的海量论文库
- ✅ 最新预印本和研究进展
- ✅ 引用分析和被引文献追踪
- ✅ 计算机科学、工程学等相关领域文献

## 📊 配置效果对比

| 配置 | 文献来源 | 覆盖范围 | 推荐度 |
|------|----------|----------|--------|
| 仅AI | 内置知识 | 基础 | ⭐⭐ |
| AI + NCBI | PubMed | 生物医学专业 | ⭐⭐⭐⭐ |
| AI + NCBI + Scholar | 多源 | 全面学术 | ⭐⭐⭐⭐⭐ |

## 🎯 推荐方案

**对于生物医学项目（如PBMC细胞类型鉴定）**：
```env
# 必需
OPENAI_API_KEY=your-deepseek-key
PUBMED_API_KEY=your-ncbi-key

# 可选但推荐  
SERPAPI_KEY=your-serpapi-key
```

## 🔧 启动系统

```bash
# 启动后端
cd backend
python3 -m uvicorn app.main:app --reload --port 8000

# 启动前端 (另一个终端)
cd frontend  
npm run dev
```

## ✅ 验证配置

启动后检查日志，应该看到：
```
✅ 文献搜索功能已就绪
pubmed: 可用
google_scholar: 可用 (如果配置了SerpAPI)
```

现在您的PBMC项目将获得：
- 📚 实时PubMed文献搜索
- 🔍 Google Scholar学术资源  
- 🤖 AI智能分析
- 🎯 基于最新研究的专业方案