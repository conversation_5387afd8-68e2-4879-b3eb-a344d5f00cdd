"""
文献资源服务 - 使用外部API而非硬编码数据
"""
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func

from app.models.literature import Literature, LiteratureRecommendation, LiteratureCollection
from app.core.database import get_db
from app.services.unified_literature_api_service import get_unified_literature_api_service

logger = logging.getLogger(__name__)


class LiteratureService:
    """文献资源服务类 - 集成外部API"""
    
    def __init__(self):
        # 不再使用硬编码的降级数据，确保API失败时直接返回失败信息
        pass
    
    async def search_literature(
        self, 
        query: str, 
        category: Optional[str] = None,
        technology_tags: Optional[List[str]] = None,
        top_k: int = 10
    ) -> List[Dict[str, Any]]:
        """搜索文献 - 使用统一API服务"""
        try:
            logger.info(f"文献搜索: {query}")
            
            # 使用统一的文献API服务
            unified_service = await get_unified_literature_api_service()
            search_results = await unified_service.comprehensive_search(
                query=query,
                max_results_per_source=max(3, top_k // 3)
            )
            
            papers = search_results.get("papers", [])
            if papers:
                logger.info(f"统一API服务找到 {len(papers)} 篇文献")
                
                # 应用过滤条件
                filtered_papers = self._apply_filters(papers, category, technology_tags)
                return filtered_papers[:top_k]
            else:
                logger.warning(f"未找到相关文献，查询: {query}")
                return []
            
        except Exception as e:
            logger.error(f"文献搜索失败: {e}")
            return []
    
    def _apply_filters(
        self, 
        papers: List[Dict], 
        category: Optional[str], 
        technology_tags: Optional[List[str]]
    ) -> List[Dict]:
        """应用过滤条件到外部API结果"""
        if not category and not technology_tags:
            return papers
        
        filtered = []
        for paper in papers:
            # 简单的分类匹配（基于标题和摘要内容）
            if category:
                content = f"{paper.get('title', '')} {paper.get('abstract', '')}".lower()
                if category.lower() not in content:
                    continue
            
            # 技术标签匹配
            if technology_tags:
                content = f"{paper.get('title', '')} {paper.get('abstract', '')}".lower()
                if not any(tag.lower().replace('_', ' ') in content for tag in technology_tags):
                    continue
            
            filtered.append(paper)
        
        return filtered
    
    
    async def get_literature_recommendations(
        self,
        context: Dict[str, Any],
        recommendation_type: str = "mixed",
        top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """基于上下文获取文献推荐"""
        try:
            # 提取上下文中的关键信息
            user_query = context.get("user_message", "")
            requirements = context.get("requirements", {})
            user_profile = context.get("user_profile", {})
            
            # 构建搜索查询
            search_terms = []
            
            # 从用户消息中提取关键词
            if user_query:
                search_terms.append(user_query)
            
            # 从需求中提取技术相关词汇
            if requirements:
                if requirements.get("sequencing_type"):
                    search_terms.append(requirements["sequencing_type"])
                if requirements.get("sample_type"):
                    search_terms.append(requirements["sample_type"])
                if requirements.get("research_goal"):
                    search_terms.append(requirements["research_goal"])
            
            # 合并搜索词
            combined_query = " ".join(search_terms)
            
            # 搜索相关文献
            literature_results = await self.search_literature(combined_query, top_k=top_k * 2)
            
            # 根据推荐类型筛选和排序
            recommendations = []
            
            for lit in literature_results:
                recommendation = {
                    "literature": lit,
                    "recommendation_type": self._determine_recommendation_type(lit, context),
                    "relevance_explanation": self._generate_relevance_explanation(lit, context),
                    "business_value": lit.get("business_value", ""),
                    "key_support_points": self._extract_support_points(lit, context)
                }
                recommendations.append(recommendation)
            
            # 按推荐类型和相关性排序
            recommendations = self._rank_recommendations(recommendations, recommendation_type)
            
            return recommendations[:top_k]
            
        except Exception as e:
            logger.error(f"获取文献推荐失败: {e}")
            return []
    
    def _determine_recommendation_type(self, literature: Dict, context: Dict) -> str:
        """确定推荐类型"""
        impact_factor = literature.get("impact_factor", 0)
        if impact_factor > 30:
            return "authority"
        elif literature.get("category") == "methodology":
            return "methodology"
        elif literature.get("category") == "application":
            return "application"
        else:
            return "reference"
    
    def _generate_relevance_explanation(self, literature: Dict, context: Dict) -> str:
        """生成相关性解释"""
        explanations = []
        
        impact_factor = literature.get("impact_factor", 0)
        if impact_factor > 20:
            journal = literature.get("journal", "")
            explanations.append(f"高影响因子期刊({journal}, IF: {impact_factor})")
        
        citation_count = literature.get("citation_count", 0)
        if citation_count > 1000:
            explanations.append(f"高引用文献({citation_count}次引用)")
        
        relevance_score = literature.get("relevance_score", 0)
        if relevance_score > 0.9:
            explanations.append("与单细胞测序高度相关")
        
        source = literature.get("source", "")
        if source and source not in ["Fallback"]:
            explanations.append(f"来源: {source}")
        
        return "; ".join(explanations) if explanations else "相关文献"
    
    def _extract_support_points(self, literature: Dict, context: Dict) -> List[str]:
        """提取支持要点"""
        points = []
        
        if literature.get("key_findings"):
            points.append(f"关键发现: {literature['key_findings']}")
        
        if literature.get("methodology_summary"):
            points.append(f"方法学价值: {literature['methodology_summary']}")
        
        if literature.get("business_value"):
            points.append(f"商业价值: {literature['business_value']}")
        
        return points
    
    def _rank_recommendations(self, recommendations: List[Dict], preference_type: str) -> List[Dict]:
        """对推荐结果进行排序"""
        if preference_type == "authority":
            return sorted(recommendations, 
                         key=lambda x: x["literature"].get("impact_factor", 0), 
                         reverse=True)
        elif preference_type == "methodology":
            return sorted(recommendations,
                         key=lambda x: (x["literature"].get("category") == "methodology", 
                                      x["literature"].get("combined_score", 0)),
                         reverse=True)
        else:
            return sorted(recommendations,
                         key=lambda x: x["literature"].get("combined_score", 0),
                         reverse=True)


# 创建全局服务实例
literature_service = LiteratureService()