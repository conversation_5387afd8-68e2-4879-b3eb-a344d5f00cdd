"""
API诊断工具 - 检查外部API连接状态
"""
import asyncio
import aiohttp
import logging
from app.core.external_apis import get_api_manager
from app.core.config import settings

logger = logging.getLogger(__name__)


async def diagnose_apis():
    """诊断所有外部API的连接状态"""
    print("🔍 开始API诊断...")
    
    manager = get_api_manager()
    
    # 检查配置
    print("\n📋 配置检查:")
    print(f"LITERATURE_SEARCH_ENABLED: {settings.LITERATURE_SEARCH_ENABLED}")
    print(f"PUBMED_API_KEY: {'已配置' if settings.PUBMED_API_KEY else '未配置'}")
    print(f"SERPAPI_KEY: {'已配置' if settings.SERPAPI_KEY else '未配置'}")
    print(f"SEMANTIC_SCHOLAR_API_KEY: {'已配置' if settings.SEMANTIC_SCHOLAR_API_KEY else '未配置'}")
    
    # 检查API可用性
    print("\n🔌 API可用性:")
    for name, config in manager.apis.items():
        status = "✅ 可用" if config.is_available() else "❌ 不可用"
        reason = ""
        if not config.enabled:
            reason = "(已禁用)"
        elif config.requires_key() and not config.api_key:
            reason = "(缺少API密钥)"
        print(f"  {name}: {status} {reason}")
    
    # 测试连接
    print("\n🌐 连接测试:")
    
    # 测试PubMed
    if manager.is_api_available("pubmed"):
        await test_pubmed_connection()
    else:
        print("  PubMed: 跳过 (API不可用)")
    
    # 测试Google Scholar
    if manager.is_api_available("google_scholar"):
        await test_google_scholar_connection()
    else:
        print("  Google Scholar: 跳过 (API不可用)")
    
    # 测试Semantic Scholar
    if manager.is_api_available("semantic_scholar"):
        await test_semantic_scholar_connection()
    else:
        print("  Semantic Scholar: 跳过 (API不可用)")


async def test_pubmed_connection():
    """测试PubMed连接"""
    try:
        config = get_api_manager().get_api_config("pubmed")
        url = f"{config.base_url}/esearch.fcgi"
        params = {
            "db": "pubmed",
            "term": "single cell",
            "retmax": "1",
            "retmode": "json"
        }
        
        if config.api_key:
            params["api_key"] = config.api_key
        
        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    count = data.get("esearchresult", {}).get("count", "0")
                    print(f"  PubMed: ✅ 连接成功 (找到 {count} 条结果)")
                else:
                    print(f"  PubMed: ❌ HTTP {response.status}")
                    
    except Exception as e:
        print(f"  PubMed: ❌ 连接失败 - {str(e)}")


async def test_google_scholar_connection():
    """测试Google Scholar (SerpAPI)连接"""
    try:
        config = get_api_manager().get_api_config("google_scholar")
        url = config.base_url
        params = {
            "engine": "google_scholar",
            "q": "single cell",
            "num": 1,
            "api_key": config.api_key
        }
        
        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    results = len(data.get("organic_results", []))
                    print(f"  Google Scholar: ✅ 连接成功 (找到 {results} 条结果)")
                else:
                    error_text = await response.text()
                    print(f"  Google Scholar: ❌ HTTP {response.status} - {error_text[:100]}")
                    
    except Exception as e:
        print(f"  Google Scholar: ❌ 连接失败 - {str(e)}")


async def test_semantic_scholar_connection():
    """测试Semantic Scholar连接"""
    try:
        config = get_api_manager().get_api_config("semantic_scholar")
        url = f"{config.base_url}/paper/search"
        params = {
            "query": "single cell",
            "limit": 1
        }
        
        headers = config.get_headers()
        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(url, params=params, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    total = data.get("total", 0)
                    print(f"  Semantic Scholar: ✅ 连接成功 (共 {total} 条结果)")
                else:
                    error_text = await response.text()
                    print(f"  Semantic Scholar: ❌ HTTP {response.status} - {error_text[:100]}")
                    
    except Exception as e:
        print(f"  Semantic Scholar: ❌ 连接失败 - {str(e)}")


if __name__ == "__main__":
    asyncio.run(diagnose_apis())