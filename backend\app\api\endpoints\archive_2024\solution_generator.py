"""
方案生成API - CellForge AI的核心价值接口
"""
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from app.models.user import User
from app.core.auth import get_current_active_user
from app.services.enhanced_solution_generator import get_solution_generator

router = APIRouter()


class SolutionRequest(BaseModel):
    """方案生成请求"""
    requirements: Dict[str, Any]
    user_message: str = ""
    include_cost_analysis: bool = True
    include_risk_assessment: bool = True


class SolutionResponse(BaseModel):
    """方案生成响应"""
    solution_id: str
    generated_at: str
    client_requirements: Dict[str, Any]
    recommended_solution: Dict[str, Any]
    cost_analysis: Dict[str, Any]
    risk_assessment: Dict[str, Any]
    timeline: Dict[str, Any]
    deliverables: list
    next_steps: list
    contact_info: Dict[str, Any]


@router.post("/generate-solution", response_model=SolutionResponse)
async def generate_solution(
    request: SolutionRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    生成完整的单细胞测序解决方案
    这是CellForge AI的核心功能 - 不是搜索，而是方案生成
    """
    try:
        solution_generator = get_solution_generator()
        
        solution = await solution_generator.generate_comprehensive_solution(
            requirements=request.requirements,
            user_message=request.user_message
        )
        
        # 确保返回的是完整方案，不是搜索结果
        if solution.get("status") == "basic_solution":
            # 基础降级方案
            return {
                "solution_id": solution["solution_id"],
                "generated_at": solution.get("generated_at", ""),
                "client_requirements": request.requirements,
                "recommended_solution": solution["basic_recommendations"],
                "cost_analysis": {"total_cost_range": solution["basic_recommendations"]["estimated_cost"]},
                "risk_assessment": {"success_probability": "待专家评估"},
                "timeline": {"total_duration": solution["basic_recommendations"]["timeline"]},
                "deliverables": ["基础分析报告", "数据交付"],
                "next_steps": [solution["basic_recommendations"]["next_step"]],
                "contact_info": {"message": "请联系专家团队获取详细方案"}
            }
        
        return SolutionResponse(**solution)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"方案生成失败: {str(e)}")


@router.post("/quick-estimate")
async def quick_estimate(
    requirements: Dict[str, Any],
    current_user: User = Depends(get_current_active_user)
):
    """
    快速成本和时间估算
    用于初步咨询，不生成完整方案
    """
    try:
        solution_generator = get_solution_generator()
        
        # 只做快速估算，不生成完整方案
        sample_count = solution_generator._extract_sample_count(
            requirements.get("sampleCount", "1个样本")
        )
        
        # 基础成本估算
        if "5万以下" in requirements.get("budget", ""):
            cost_per_sample = (800, 1000)
            platform = "Smart-seq3 (经济型)"
        elif "肿瘤" in requirements.get("researchGoal", ""):
            cost_per_sample = (2000, 2500)
            platform = "10x Genomics + Spatial"
        else:
            cost_per_sample = (1000, 1300)
            platform = "10x Genomics (标准)"
        
        total_min = cost_per_sample[0] * sample_count
        total_max = cost_per_sample[1] * sample_count
        
        # 时间估算
        timeline = "4-6周" if "1个月" in requirements.get("timeline", "") else "6-8周"
        
        return {
            "quick_estimate": {
                "recommended_platform": platform,
                "estimated_cost": f"¥{total_min:,}-{total_max:,}",
                "estimated_timeline": timeline,
                "sample_count": sample_count,
                "cost_per_sample": f"¥{cost_per_sample[0]:,}-{cost_per_sample[1]:,}"
            },
            "next_steps": [
                "完善需求信息获取详细方案",
                "联系专家进行技术咨询",
                "预约方案讨论会议"
            ],
            "disclaimer": "此为初步估算，最终方案需要详细需求分析"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"快速估算失败: {str(e)}")


@router.get("/solution-templates")
async def get_solution_templates(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取方案模板
    为不同研究类型提供标准化模板
    """
    templates = {
        "细胞类型鉴定": {
            "recommended_platform": "10x Genomics Chromium",
            "typical_cell_count": "3,000-8,000",
            "estimated_cost": "¥8,000-12,000",
            "timeline": "4-5周",
            "key_analyses": ["聚类分析", "marker基因识别", "细胞类型注释"]
        },
        "发育轨迹分析": {
            "recommended_platform": "10x Genomics + RNA Velocity",
            "typical_cell_count": "5,000-15,000",
            "estimated_cost": "¥12,000-18,000",
            "timeline": "5-6周",
            "key_analyses": ["轨迹分析", "伪时间分析", "分化关键基因"]
        },
        "肿瘤异质性研究": {
            "recommended_platform": "10x Genomics + Spatial",
            "typical_cell_count": "8,000-20,000",
            "estimated_cost": "¥15,000-25,000",
            "timeline": "6-8周",
            "key_analyses": ["空间分析", "肿瘤微环境", "免疫浸润分析"]
        },
        "药物筛选": {
            "recommended_platform": "10x Genomics + 多时间点",
            "typical_cell_count": "10,000-30,000",
            "estimated_cost": "¥20,000-35,000",
            "timeline": "6-10周",
            "key_analyses": ["药物应答分析", "剂量效应", "毒性评估"]
        }
    }
    
    return {
        "available_templates": templates,
        "customization_note": "所有模板可根据具体需求进行定制",
        "consultation_available": "提供免费的方案设计咨询服务"
    }


@router.post("/validate-requirements")
async def validate_requirements(
    requirements: Dict[str, Any],
    current_user: User = Depends(get_current_active_user)
):
    """
    验证需求完整性
    确保有足够信息生成高质量方案
    """
    try:
        # 检查必要字段
        required_fields = ["researchGoal", "sampleType", "budget", "timeline"]
        missing_fields = []
        
        for field in required_fields:
            if not requirements.get(field):
                missing_fields.append(field)
        
        # 计算完整度
        total_fields = ["researchGoal", "sampleType", "experimentType", "budget", 
                       "timeline", "cellCount", "sampleCount", "analysisType"]
        completed_fields = [f for f in total_fields if requirements.get(f)]
        completeness = len(completed_fields) / len(total_fields) * 100
        
        # 给出建议
        recommendations = []
        if completeness < 60:
            recommendations.append("建议完善基础信息以获得更准确的方案")
        if not requirements.get("cellCount"):
            recommendations.append("请提供预期细胞数量以优化成本估算")
        if not requirements.get("analysisType"):
            recommendations.append("请明确数据分析需求以定制分析流程")
        
        can_generate_solution = completeness >= 50 and len(missing_fields) == 0
        
        return {
            "validation_result": {
                "is_valid": can_generate_solution,
                "completeness_percentage": round(completeness, 1),
                "missing_required_fields": missing_fields,
                "completed_fields": completed_fields,
                "recommendations": recommendations
            },
            "next_action": "generate_solution" if can_generate_solution else "complete_requirements"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"需求验证失败: {str(e)}")


@router.get("/platform-comparison")
async def get_platform_comparison(
    current_user: User = Depends(get_current_active_user)
):
    """
    技术平台对比
    帮助客户理解不同平台的优劣
    """
    platforms = {
        "10x_genomics": {
            "name": "10x Genomics Chromium",
            "advantages": ["高通量", "标准化流程", "成熟技术", "完善支持"],
            "disadvantages": ["成本较高", "通量固定"],
            "best_for": ["标准化研究", "大规模样本"],
            "cost_range": "¥1,000-1,500/样本",
            "cell_capacity": "500-10,000 cells/sample"
        },
        "smart_seq": {
            "name": "Smart-seq3",
            "advantages": ["全长转录本", "成本较低", "灵活性高"],
            "disadvantages": ["通量较低", "操作复杂"],
            "best_for": ["预算限制", "深度分析"],
            "cost_range": "¥800-1,200/样本",
            "cell_capacity": "96-384 cells/batch"
        },
        "spatial": {
            "name": "Spatial Transcriptomics",
            "advantages": ["保留空间信息", "组织结构分析"],
            "disadvantages": ["成本最高", "分辨率限制"],
            "best_for": ["肿瘤研究", "发育研究"],
            "cost_range": "¥2,000-3,000/样本",
            "cell_capacity": "3,000-5,000 spots/section"
        }
    }
    
    return {
        "platform_comparison": platforms,
        "selection_guide": {
            "budget_priority": "推荐 Smart-seq3",
            "throughput_priority": "推荐 10x Genomics",
            "spatial_info_needed": "推荐 Spatial Transcriptomics"
        },
        "consultation_note": "具体平台选择建议咨询技术专家"
    }