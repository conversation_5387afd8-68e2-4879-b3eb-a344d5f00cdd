"""
Input validation utilities for keyword generation service
"""
import re
import html
import unicodedata
from typing import List, Dict, Any, Optional, Union
from app.exceptions.keyword_exceptions import InvalidInputError, ValidationError
from app.config.keyword_config import get_keyword_config


class InputValidator:
    """Comprehensive input validation for keyword generation"""
    
    def __init__(self):
        self.config = get_keyword_config()
        
        # Security patterns
        self.security_patterns = [
            # Script injection patterns
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'data:',
            r'vbscript:',
            # SQL injection patterns
            r'(union|select|insert|update|delete|drop|create|alter)\s+',
            # Command injection patterns
            r'[;&|`$()]',
            # XSS patterns
            r'on\w+\s*=',
            r'<iframe',
            r'<object',
            r'<embed'
        ]
        
        # Suspicious character patterns
        self.suspicious_chars = re.compile(r'[<>{}[\]\\`~!@#$%^&*()+=|]')
        
        # Language detection patterns
        self.chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        self.english_pattern = re.compile(r'[a-zA-Z]')
        
        # Valid scientific term patterns
        self.scientific_patterns = [
            r'[a-zA-Z0-9\s\-_]+',  # Basic alphanumeric with spaces, hyphens, underscores
            r'[\u4e00-\u9fff\s]+',  # Chinese characters with spaces
            r'\d+x\s*genomics',     # Technology platform patterns
            r'smart-seq\d*',
            r'drop-seq',
            r'mars-seq',
            r'cel-seq'
        ]
    
    def validate_query(self, query: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Comprehensive query validation and sanitization
        
        Args:
            query: User input query
            context: Optional context for additional validation
            
        Returns:
            Sanitized and validated query
            
        Raises:
            InvalidInputError: If validation fails
        """
        if not query:
            raise InvalidInputError("Query cannot be empty", field="query", value=query)
        
        # Type validation
        if not isinstance(query, str):
            raise InvalidInputError(
                f"Query must be a string, got {type(query)}", 
                field="query", 
                value=str(query)
            )
        
        # Length validation
        if len(query) < self.config.MIN_QUERY_LENGTH:
            raise InvalidInputError(
                f"Query too short. Minimum length: {self.config.MIN_QUERY_LENGTH}",
                field="query",
                value=query
            )
        
        if len(query) > self.config.MAX_QUERY_LENGTH:
            raise InvalidInputError(
                f"Query too long. Maximum length: {self.config.MAX_QUERY_LENGTH}",
                field="query", 
                value=f"{query[:50]}..."
            )
        
        # Security validation
        self._validate_security(query)
        
        # Language validation
        self._validate_language(query)
        
        # Sanitize input
        sanitized_query = self._sanitize_input(query)
        
        # Content validation
        self._validate_content(sanitized_query)
        
        return sanitized_query
    
    def validate_context(self, context: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Validate context dictionary
        
        Args:
            context: Context dictionary to validate
            
        Returns:
            Validated context dictionary
            
        Raises:
            ValidationError: If context validation fails
        """
        if context is None:
            return None
        
        if not isinstance(context, dict):
            raise ValidationError(
                f"Context must be a dictionary, got {type(context)}",
                validation_errors={"context": ["invalid_type"]}
            )
        
        # Validate context keys and values
        validated_context = {}
        validation_errors = {}
        
        for key, value in context.items():
            try:
                validated_key = self._validate_context_key(key)
                validated_value = self._validate_context_value(value)
                validated_context[validated_key] = validated_value
            except Exception as e:
                validation_errors[str(key)] = [str(e)]
        
        if validation_errors:
            raise ValidationError(
                "Context validation failed",
                validation_errors=validation_errors
            )
        
        return validated_context
    
    def validate_max_keywords(self, max_keywords: int) -> int:
        """
        Validate max_keywords parameter
        
        Args:
            max_keywords: Maximum number of keywords to generate
            
        Returns:
            Validated max_keywords value
            
        Raises:
            InvalidInputError: If validation fails
        """
        if not isinstance(max_keywords, int):
            raise InvalidInputError(
                f"max_keywords must be an integer, got {type(max_keywords)}",
                field="max_keywords",
                value=max_keywords
            )
        
        if max_keywords < 1:
            raise InvalidInputError(
                "max_keywords must be at least 1",
                field="max_keywords",
                value=max_keywords
            )
        
        if max_keywords > self.config.MAX_KEYWORDS_PER_QUERY:
            raise InvalidInputError(
                f"max_keywords cannot exceed {self.config.MAX_KEYWORDS_PER_QUERY}",
                field="max_keywords",
                value=max_keywords
            )
        
        return max_keywords
    
    def _validate_security(self, query: str) -> None:
        """Check for security threats in query"""
        query_lower = query.lower()
        
        # Check for forbidden patterns
        for pattern in self.config.FORBIDDEN_PATTERNS:
            if pattern.lower() in query_lower:
                raise InvalidInputError(
                    "Query contains forbidden content",
                    field="query",
                    value="[SECURITY_FILTERED]"
                )
        
        # Check for injection patterns
        for pattern in self.security_patterns:
            if re.search(pattern, query_lower, re.IGNORECASE):
                raise InvalidInputError(
                    "Query contains potentially malicious content",
                    field="query",
                    value="[SECURITY_FILTERED]"
                )
        
        # Check for suspicious character density
        suspicious_count = len(self.suspicious_chars.findall(query))
        if suspicious_count > len(query) * 0.3:  # More than 30% suspicious characters
            raise InvalidInputError(
                "Query contains too many special characters",
                field="query",
                value="[FILTERED]"
            )
    
    def _validate_language(self, query: str) -> None:
        """Validate query language"""
        has_chinese = bool(self.chinese_pattern.search(query))
        has_english = bool(self.english_pattern.search(query))
        
        if not has_chinese and not has_english:
            # Check if it contains any letters at all
            has_letters = bool(re.search(r'[a-zA-Z\u4e00-\u9fff]', query))
            if not has_letters:
                raise InvalidInputError(
                    "Query must contain Chinese or English text",
                    field="query",
                    value=query
                )
    
    def _validate_content(self, query: str) -> None:
        """Validate query content for scientific relevance"""
        # Remove whitespace and check if meaningful content remains
        cleaned_query = re.sub(r'\s+', ' ', query.strip())
        
        if len(cleaned_query) < 2:
            raise InvalidInputError(
                "Query does not contain meaningful content",
                field="query",
                value=query
            )
        
        # Check if query contains only repeated characters
        unique_chars = set(cleaned_query.replace(' ', ''))
        if len(unique_chars) < 2:
            raise InvalidInputError(
                "Query content is too repetitive",
                field="query",
                value=query
            )
    
    def _sanitize_input(self, query: str) -> str:
        """Sanitize user input"""
        # HTML escape
        sanitized = html.escape(query)
        
        # Normalize unicode
        sanitized = unicodedata.normalize('NFC', sanitized)
        
        # Remove excessive whitespace
        sanitized = re.sub(r'\s+', ' ', sanitized)
        
        # Trim whitespace
        sanitized = sanitized.strip()
        
        # Remove null bytes and control characters (except newlines and tabs)
        sanitized = ''.join(char for char in sanitized 
                          if ord(char) >= 32 or char in '\n\t')
        
        return sanitized
    
    def _validate_context_key(self, key: str) -> str:
        """Validate context dictionary key"""
        if not isinstance(key, str):
            raise ValueError(f"Context key must be string, got {type(key)}")
        
        if len(key) > 100:
            raise ValueError("Context key too long")
        
        # Allow only alphanumeric, underscore, and hyphen
        if not re.match(r'^[a-zA-Z0-9_-]+$', key):
            raise ValueError("Context key contains invalid characters")
        
        return key
    
    def _validate_context_value(self, value: Any) -> Any:
        """Validate context dictionary value"""
        # Allow basic types only
        if value is None:
            return None
        
        if isinstance(value, (str, int, float, bool)):
            if isinstance(value, str):
                if len(value) > 1000:
                    raise ValueError("Context string value too long")
                return self._sanitize_input(value)
            return value
        
        if isinstance(value, list):
            if len(value) > 50:
                raise ValueError("Context list too long")
            return [self._validate_context_value(item) for item in value]
        
        if isinstance(value, dict):
            if len(value) > 20:
                raise ValueError("Context dict too large")
            return {
                self._validate_context_key(k): self._validate_context_value(v)
                for k, v in value.items()
            }
        
        raise ValueError(f"Context value type not allowed: {type(value)}")


class SecurityValidator:
    """Additional security validation utilities"""
    
    @staticmethod
    def check_rate_limit(user_id: str, requests_count: int, window_seconds: int) -> bool:
        """
        Check if user has exceeded rate limits
        
        Args:
            user_id: User identifier
            requests_count: Number of requests in current window
            window_seconds: Time window in seconds
            
        Returns:
            True if within limits, False if exceeded
        """
        config = get_keyword_config()
        
        if not config.ENABLE_RATE_LIMITING:
            return True
        
        return requests_count <= config.RATE_LIMIT_REQUESTS
    
    @staticmethod
    def validate_api_key(api_key: Optional[str]) -> bool:
        """
        Validate API key format
        
        Args:
            api_key: API key to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not api_key:
            return False
        
        # Basic API key format validation
        if len(api_key) < 16 or len(api_key) > 128:
            return False
        
        # Should contain only alphanumeric and basic symbols
        if not re.match(r'^[a-zA-Z0-9_.-]+$', api_key):
            return False
        
        return True
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """
        Sanitize filename for safe storage
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        # Remove directory traversal attempts
        filename = filename.replace('..', '').replace('/', '').replace('\\', '')
        
        # Keep only safe characters
        filename = re.sub(r'[^\w\-_.]', '', filename)
        
        # Limit length
        if len(filename) > 100:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            filename = name[:95] + ('.' + ext if ext else '')
        
        return filename or 'unnamed'


# Global validator instance
input_validator = InputValidator()
security_validator = SecurityValidator()


def get_input_validator() -> InputValidator:
    """Get input validator instance"""
    return input_validator


def get_security_validator() -> SecurityValidator:
    """Get security validator instance"""
    return security_validator