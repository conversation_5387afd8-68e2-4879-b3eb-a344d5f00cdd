# 任务3: 后端API开发

## 给Cursor/Augment的提示词

你是一个后端开发专家，需要为单细胞测序方案生成系统开发健壮的API服务。

### 系统架构
- Node.js + Express + TypeScript
- MongoDB数据库
- JWT认证
- Redis缓存
- 微服务架构设计

### API设计规范
- RESTful API设计
- OpenAPI 3.0文档
- 统一响应格式
- 错误处理机制
- 请求限流和安全防护

### 核心API模块

#### 1. 用户管理模块 (User Management)
```typescript
// 路由: /api/users
POST   /register      // 用户注册
POST   /login         // 用户登录
GET    /profile       // 获取用户信息
PUT    /profile       // 更新用户信息
POST   /logout        // 用户登出
POST   /forgot-password // 忘记密码
POST   /reset-password  // 重置密码
```

**数据模型：**
```typescript
interface User {
  _id: string;
  email: string;
  username: string;
  password: string; // 加密存储
  profile: {
    fullName: string;
    organization: string;
    role: string;
    phone?: string;
  };
  preferences: {
    language: string;
    notifications: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt: Date;
}
```

#### 2. 对话管理模块 (Chat Management)
```typescript
// 路由: /api/chat
POST   /sessions           // 创建对话会话
GET    /sessions/:id       // 获取对话历史
POST   /sessions/:id/messages // 发送消息
DELETE /sessions/:id       // 删除对话
GET    /sessions           // 获取用户所有对话
```

**数据模型：**
```typescript
interface ChatSession {
  _id: string;
  userId: string;
  title: string;
  status: 'active' | 'completed' | 'archived';
  messages: Message[];
  requirements: UserRequirement;
  generatedProtocols: string[]; // Protocol IDs
  createdAt: Date;
  updatedAt: Date;
}

interface Message {
  _id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    requirementUpdates?: Partial<UserRequirement>;
    suggestedProducts?: string[];
    nextQuestions?: string[];
  };
}
```

#### 3. 产品管理模块 (Product Management)
```typescript
// 路由: /api/products
GET    /                    // 获取产品列表
GET    /:id                 // 获取产品详情
GET    /categories          // 获取产品分类
GET    /search              // 产品搜索
POST   /                    // 创建产品（管理员）
PUT    /:id                 // 更新产品（管理员）
DELETE /:id                 // 删除产品（管理员）
```

**数据模型：**
```typescript
interface Product {
  _id: string;
  name: string;
  category: string;
  subcategory: string;
  description: string;
  specifications: {
    [key: string]: any;
  };
  pricing: {
    listPrice: number;
    currency: string;
    unit: string;
    bulkDiscounts?: {
      quantity: number;
      discount: number;
    }[];
  };
  inventory: {
    inStock: boolean;
    quantity: number;
    leadTime: string;
  };
  compatibility: {
    tissueTypes: string[];
    applications: string[];
    platforms: string[];
  };
  documentation: {
    datasheet: string;
    protocol: string;
    citations: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}
```

#### 4. 方案生成模块 (Protocol Generation)
```typescript
// 路由: /api/protocols
POST   /generate           // 生成实验方案
GET    /:id               // 获取方案详情
GET    /                  // 获取用户方案列表
POST   /:id/optimize      // 优化方案
POST   /:id/export        // 导出方案
PUT    /:id               // 更新方案
DELETE /:id               // 删除方案
```

**数据模型：**
```typescript
interface Protocol {
  _id: string;
  userId: string;
  sessionId: string;
  name: string;
  description: string;
  requirements: UserRequirement;
  experimentalSteps: ProtocolStep[];
  recommendedProducts: {
    productId: string;
    quantity: number;
    alternatives: string[];
  }[];
  costAnalysis: {
    totalCost: number;
    breakdown: {
      category: string;
      cost: number;
      items: {
        name: string;
        quantity: number;
        unitPrice: number;
        totalPrice: number;
      }[];
    }[];
  };
  timeline: {
    totalDuration: string;
    milestones: {
      name: string;
      description: string;
      estimatedDuration: string;
      dependencies: string[];
    }[];
  };
  qualityControl: {
    checkpoints: string[];
    expectedResults: string[];
    troubleshooting: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

interface ProtocolStep {
  stepNumber: number;
  title: string;
  description: string;
  duration: string;
  requiredProducts: string[];
  notes: string[];
  criticalPoints: string[];
}
```

#### 5. 知识库管理模块 (Knowledge Base)
```typescript
// 路由: /api/knowledge
GET    /documents          // 获取文档列表
GET    /documents/:id      // 获取文档内容
POST   /documents          // 创建文档（管理员）
PUT    /documents/:id      // 更新文档（管理员）
DELETE /documents/:id      // 删除文档（管理员）
GET    /search             // 全文搜索
```

### 业务逻辑服务

#### 1. AI对话服务 (AIService)
```typescript
class AIService {
  async processMessage(message: string, context: ChatContext): Promise<AIResponse> {
    // 1. 消息预处理和意图识别
    // 2. 调用OpenAI API
    // 3. 提取结构化信息
    // 4. 更新用户需求
    // 5. 生成响应和建议
  }
  
  async generateProtocol(requirements: UserRequirement): Promise<Protocol> {
    // 1. 需求分析和验证
    // 2. 产品匹配算法
    // 3. 方案生成逻辑
    // 4. 成本和时间估算
    // 5. 质控要点生成
  }
}
```

#### 2. 产品推荐服务 (RecommendationService)
```typescript
class RecommendationService {
  async recommendProducts(requirements: UserRequirement): Promise<Product[]> {
    // 1. 基于需求筛选产品
    // 2. 相似度计算
    // 3. 库存和价格考虑
    // 4. 用户历史偏好
    // 5. 排序和分页返回
  }
  
  async optimizeProtocol(protocol: Protocol, constraints: OptimizationConstraints): Promise<Protocol> {
    // 1. 成本优化算法
    // 2. 时间优化建议
    // 3. 产品替代分析
    // 4. 批量采购优化
  }
}
```

#### 3. 成本计算服务 (CostCalculationService)
```typescript
class CostCalculationService {
  async calculateProtocolCost(protocol: Protocol): Promise<CostBreakdown> {
    // 1. 产品成本计算
    // 2. 批量折扣应用
    // 3. 运费和税费估算
    // 4. 人工成本预估
    // 5. 总成本汇总
  }
}
```

### 数据库设计

#### MongoDB集合设计
- users: 用户信息
- chat_sessions: 对话会话
- messages: 消息记录
- products: 产品信息
- protocols: 实验方案
- knowledge_documents: 知识库文档
- audit_logs: 操作日志

#### 索引优化
```javascript
// 复合索引设计
db.products.createIndex({ category: 1, subcategory: 1 });
db.protocols.createIndex({ userId: 1, createdAt: -1 });
db.messages.createIndex({ sessionId: 1, timestamp: 1 });
db.knowledge_documents.createIndex({ "$**": "text" }); // 全文搜索
```

### 中间件开发

#### 1. 认证中间件
```typescript
const authMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // JWT token验证
  // 用户权限检查
  // 请求上下文设置
};
```

#### 2. 请求验证中间件
```typescript
const validateRequest = (schema: AnySchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Joi/Yup schema验证
    // 请求参数清洗
    // 错误响应格式化
  };
};
```

#### 3. 限流中间件
```typescript
const rateLimitMiddleware = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 最大请求数
  message: '请求过于频繁，请稍后再试'
});
```

### 错误处理

#### 统一错误响应格式
```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  path: string;
}
```

#### 全局错误处理器
```typescript
const errorHandler = (err: Error, req: Request, res: Response, next: NextFunction) => {
  // 错误日志记录
  // 错误类型判断
  // 统一响应格式
  // 敏感信息过滤
};
```

### 日志和监控

#### 日志配置
- 使用Winston进行结构化日志
- 不同级别的日志输出
- 日志轮转和归档
- 敏感信息脱敏

#### 健康检查端点
```typescript
GET /health
GET /health/db
GET /health/redis
GET /health/external-apis
```

### 安全措施

#### 1. 输入验证和清洗
- SQL注入防护
- XSS攻击防护
- CSRF令牌验证
- 文件上传安全

#### 2. 数据加密
- 密码哈希存储
- 敏感数据加密
- HTTPS强制使用
- API密钥管理

### 测试要求

#### 1. 单元测试
- Jest + Supertest
- 80%以上代码覆盖率
- 模拟外部依赖
- 测试用例文档

#### 2. 集成测试
- API端点测试
- 数据库操作测试
- 第三方服务集成测试
- 性能基准测试

### 部署配置

#### Docker配置
- 多阶段构建
- 生产环境优化
- 健康检查配置
- 环境变量管理

#### 环境配置
- 开发/测试/生产环境分离
- 配置文件管理
- 秘钥安全存储
- 监控和告警配置

### 交付物
- 完整的REST API服务
- 数据库模型和迁移脚本
- API文档（Swagger/OpenAPI）
- 单元测试和集成测试
- Docker部署配置
- 安全配置和最佳实践文档

请按照以上要求开发一个高质量、可扩展、安全的后端API服务。
