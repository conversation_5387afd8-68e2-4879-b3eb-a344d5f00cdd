from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional, List
import os


class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "CellForge AI Backend"
    DEBUG: bool = False

    # 数据库配置 - 使用SQLite用于开发
    DATABASE_URL: str = "sqlite:///./cellforge.db"
    REDIS_URL: str = "redis://localhost:6379/0"

    # 认证配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # AI 服务配置 - 主要配置
    DEEPSEEK_API_KEY: Optional[str] = None
    DEEPSEEK_BASE_URL: str = "https://api.deepseek.com/v1"  # DeepSeek API地址
    DEEPSEEK_MODEL: str = "deepseek-chat"  # DeepSeek模型，可选: deepseek-chat, deepseek-reasoner
    USE_REAL_AI: bool = True  # 是否使用真实AI服务
    
    # DeepSeek 模型配置选项
    AVAILABLE_DEEPSEEK_MODELS: List[str] = Field(
        default=["deepseek-chat", "deepseek-reasoner"],
        description="可用的DeepSeek模型列表"
    )

    # 兼容性配置字段（用于向后兼容OpenAI接口）
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_API_BASE: Optional[str] = None
    OPENAI_MODEL: Optional[str] = None

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 如果没有设置DEEPSEEK_API_KEY但设置了OPENAI_API_KEY，则使用OPENAI_API_KEY
        if not self.DEEPSEEK_API_KEY and self.OPENAI_API_KEY:
            self.DEEPSEEK_API_KEY = self.OPENAI_API_KEY
        if not self.DEEPSEEK_BASE_URL and self.OPENAI_API_BASE:
            self.DEEPSEEK_BASE_URL = self.OPENAI_API_BASE
        if not self.DEEPSEEK_MODEL and self.OPENAI_MODEL:
            self.DEEPSEEK_MODEL = self.OPENAI_MODEL

    # 向量数据库配置
    CHROMA_PERSIST_DIRECTORY: str = "./chroma_db"

    # 文件存储配置
    UPLOAD_DIRECTORY: str = "./uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB

    # 日志配置
    LOG_LEVEL: str = "INFO"

    # 单细胞分析配置
    SCANPY_SETTINGS_VERBOSITY: int = 1
    SCANPY_SETTINGS_N_JOBS: int = -1

    # 文献检索配置
    LITERATURE_SEARCH_ENABLED: bool = Field(default=True, description="是否启用文献检索功能")

    # 外部API配置
    PUBMED_API_KEY: Optional[str] = Field(default=None, description="PubMed API密钥")
    SEMANTIC_SCHOLAR_API_KEY: Optional[str] = Field(default=None, description="Semantic Scholar API密钥")
    SERPAPI_KEY: Optional[str] = Field(default=None, description="SerpAPI密钥(Google Scholar)")
    BIORXIV_API_ENABLED: bool = Field(default=True, description="是否启用bioRxiv API")
    GOOGLE_SCHOLAR_ENABLED: bool = Field(default=True, description="是否启用Google Scholar搜索")

    # API限流配置
    PUBMED_RATE_LIMIT: int = Field(default=3, description="PubMed API每秒请求数")
    SEMANTIC_SCHOLAR_RATE_LIMIT: int = Field(default=10, description="Semantic Scholar API每秒请求数")
    BIORXIV_RATE_LIMIT: int = Field(default=10, description="bioRxiv API每秒请求数")
    GOOGLE_SCHOLAR_RATE_LIMIT: int = Field(default=5, description="Google Scholar API每秒请求数")

    # 文献搜集配置
    LITERATURE_COLLECTION_MAX_PAPERS: int = Field(default=50, description="单次搜集最大文献数")
    LITERATURE_RELEVANCE_THRESHOLD: float = Field(default=0.7, description="文献相关性阈值")
    LITERATURE_CACHE_TTL: int = Field(default=3600, description="文献缓存时间(秒)")

    # 获取有效的API配置（优先使用OPENAI_*，如果没有则使用DEEPSEEK_*）
    def get_api_key(self) -> Optional[str]:
        return self.OPENAI_API_KEY or self.DEEPSEEK_API_KEY

    def get_api_base(self) -> str:
        return self.OPENAI_API_BASE or self.DEEPSEEK_BASE_URL

    def get_api_model(self) -> str:
        return self.OPENAI_MODEL or self.DEEPSEEK_MODEL
    
    def validate_deepseek_model(self) -> bool:
        """验证当前配置的DeepSeek模型是否有效"""
        return self.DEEPSEEK_MODEL in self.AVAILABLE_DEEPSEEK_MODELS
    
    def get_model_info(self) -> dict:
        """获取当前模型信息"""
        model = self.get_api_model()
        if model == "deepseek-chat":
            return {
                "name": "deepseek-chat",
                "description": "DeepSeek-V3-0324，适合通用对话和分析任务",
                "version": "V3-0324"
            }
        elif model == "deepseek-reasoner": 
            return {
                "name": "deepseek-reasoner",
                "description": "DeepSeek-R1-0528，专门优化推理和复杂分析任务",
                "version": "R1-0528"
            }
        else:
            return {
                "name": model,
                "description": "自定义模型",
                "version": "未知"
            }

    class Config:
        env_file = ".env"  # 统一使用backend/.env文件 
        case_sensitive = True


# 全局设置实例
settings = Settings()