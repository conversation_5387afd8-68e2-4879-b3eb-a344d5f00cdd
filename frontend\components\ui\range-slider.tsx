"use client"

import * as React from "react"
import * as SliderPrimitive from "@radix-ui/react-slider"
import { cn } from "@/lib/utils"

interface RangeSliderProps {
  value: number[]
  onValueChange: (value: number[]) => void
  min?: number
  max?: number
  step?: number
  className?: string
  disabled?: boolean
  formatValue?: (value: number) => string
  label?: string
  showValue?: boolean
}

export function RangeSlider({
  value,
  onValueChange,
  min = 0,
  max = 100,
  step = 1,
  className,
  disabled = false,
  formatValue = (val) => val.toString(),
  label,
  showValue = true,
  ...props
}: RangeSliderProps) {
  return (
    <div className={cn("space-y-3", className)}>
      {label && (
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-slate-700">
            {label}
          </label>
          {showValue && (
            <span className="text-sm text-slate-500">
              {value.length === 1 
                ? formatValue(value[0])
                : `${formatValue(value[0])} - ${formatValue(value[1])}`
              }
            </span>
          )}
        </div>
      )}
      
      <SliderPrimitive.Root
        className={cn(
          "relative flex w-full touch-none select-none items-center",
          disabled && "opacity-50 cursor-not-allowed"
        )}
        value={value}
        onValueChange={onValueChange}
        max={max}
        min={min}
        step={step}
        disabled={disabled}
        {...props}
      >
        <SliderPrimitive.Track className="relative h-2 w-full grow overflow-hidden rounded-full bg-slate-200">
          <SliderPrimitive.Range className="absolute h-full bg-gradient-to-r from-blue-500 to-indigo-600" />
        </SliderPrimitive.Track>
        {value.map((_, index) => (
          <SliderPrimitive.Thumb
            key={index}
            className="block h-5 w-5 rounded-full border-2 border-blue-600 bg-white ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-600 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-blue-50"
          />
        ))}
      </SliderPrimitive.Root>
    </div>
  )
}

// 预设范围的滑块组件
interface PresetRangeSliderProps {
  value: string
  onValueChange: (value: string) => void
  presets: { label: string; value: string; range: [number, number] }[]
  className?: string
  label?: string
}

export function PresetRangeSlider({
  value,
  onValueChange,
  presets,
  className,
  label
}: PresetRangeSliderProps) {
  const currentPreset = presets.find(p => p.value === value)
  const [sliderValue, setSliderValue] = React.useState([0])

  React.useEffect(() => {
    if (currentPreset) {
      const index = presets.findIndex(p => p.value === value)
      setSliderValue([index])
    }
  }, [value, presets, currentPreset])

  const handleSliderChange = (newValue: number[]) => {
    const index = newValue[0]
    if (presets[index]) {
      onValueChange(presets[index].value)
      setSliderValue(newValue)
    }
  }

  return (
    <div className={cn("space-y-3", className)}>
      {label && (
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-slate-700">
            {label}
          </label>
          <span className="text-sm font-medium text-blue-600">
            {currentPreset?.label || "请选择"}
          </span>
        </div>
      )}
      
      <div className="space-y-4">
        <SliderPrimitive.Root
          className="relative flex w-full touch-none select-none items-center"
          value={sliderValue}
          onValueChange={handleSliderChange}
          max={presets.length - 1}
          min={0}
          step={1}
        >
          <SliderPrimitive.Track className="relative h-2 w-full grow overflow-hidden rounded-full bg-slate-200">
            <SliderPrimitive.Range className="absolute h-full bg-gradient-to-r from-blue-500 to-indigo-600" />
          </SliderPrimitive.Track>
          <SliderPrimitive.Thumb className="block h-5 w-5 rounded-full border-2 border-blue-600 bg-white ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-600 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-blue-50" />
        </SliderPrimitive.Root>
        
        {/* 预设标签 */}
        <div className="flex justify-between text-xs text-slate-500">
          {presets.map((preset, index) => (
            <span
              key={preset.value}
              className={cn(
                "cursor-pointer hover:text-slate-700 transition-colors",
                sliderValue[0] === index && "text-blue-600 font-medium"
              )}
              onClick={() => handleSliderChange([index])}
            >
              {preset.label}
            </span>
          ))}
        </div>
      </div>
    </div>
  )
}

// 细胞数量滑块
export function CellCountSlider({
  value,
  onValueChange,
  className
}: {
  value: string
  onValueChange: (value: string) => void
  className?: string
}) {
  const presets = [
    { label: "< 1K", value: "< 1,000", range: [0, 1000] as [number, number] },
    { label: "1-5K", value: "1,000-5,000", range: [1000, 5000] as [number, number] },
    { label: "5-10K", value: "5,000-10,000", range: [5000, 10000] as [number, number] },
    { label: "10-20K", value: "10,000-20,000", range: [10000, 20000] as [number, number] },
    { label: "> 20K", value: "> 20,000", range: [20000, 100000] as [number, number] }
  ]

  return (
    <PresetRangeSlider
      value={value}
      onValueChange={onValueChange}
      presets={presets}
      className={className}
      label="预期细胞数量"
    />
  )
}

// 预算范围滑块
export function BudgetRangeSlider({
  value,
  onValueChange,
  className
}: {
  value: string
  onValueChange: (value: string) => void
  className?: string
}) {
  const presets = [
    { label: "< 5万", value: "5万以下", range: [0, 50000] as [number, number] },
    { label: "5-10万", value: "5-10万", range: [50000, 100000] as [number, number] },
    { label: "10-20万", value: "10-20万", range: [100000, 200000] as [number, number] },
    { label: "20-50万", value: "20-50万", range: [200000, 500000] as [number, number] },
    { label: "> 50万", value: "50万以上", range: [500000, 1000000] as [number, number] }
  ]

  return (
    <PresetRangeSlider
      value={value}
      onValueChange={onValueChange}
      presets={presets}
      className={className}
      label="预算范围"
    />
  )
}
