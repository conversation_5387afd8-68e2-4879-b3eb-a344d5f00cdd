"""
DynamicKeywordGenerator - 动态关键词生成服务
基于用户研究意图和上下文动态生成最相关的搜索关键词，与SmartLiteratureLinker深度集成
"""
import asyncio
import json
import logging
import hashlib
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.ai_service import AIService
# 移除对不存在模块的依赖 - DynamicKeywordGenerator是独立的核心服务
from app.models.research_intent import DynamicKeywordGeneration

logger = logging.getLogger(__name__)


@dataclass
class KeywordCluster:
    """关键词聚类数据结构"""
    cluster_name: str
    primary_keywords: List[str]
    secondary_keywords: List[str]
    weight: float
    relevance_score: float
    search_priority: int


@dataclass
class DynamicKeywordResult:
    """动态关键词生成结果"""
    session_id: str
    research_focus: str
    keyword_clusters: List[KeywordCluster]
    adaptive_keywords: List[str]
    context_keywords: List[str]
    trending_keywords: List[str]
    total_keywords: int
    confidence_score: float
    generation_strategy: str
    optimization_notes: str
    generation_timestamp: str


class DynamicKeywordGenerator:
    """动态关键词生成器 - 基于研究意图的智能关键词生成"""
    
    def __init__(self):
        self.ai_service = AIService()
        # 移除对外部依赖的引用 - 使用AIService作为核心依赖
        
        # 动态关键词生成策略
        self.generation_strategies = {
            "exploratory": "探索性研究策略",
            "focused": "专注性研究策略", 
            "comparative": "比较性研究策略",
            "methodological": "方法学研究策略",
            "clinical": "临床研究策略",
            "computational": "计算生物学策略"
        }
        
        # 领域特定的关键词权重配置
        self.domain_weights = {
            "immunology": {"weight_multiplier": 1.2, "priority_boost": 0.15},
            "oncology": {"weight_multiplier": 1.3, "priority_boost": 0.20},
            "neuroscience": {"weight_multiplier": 1.1, "priority_boost": 0.10},
            "developmental_biology": {"weight_multiplier": 1.0, "priority_boost": 0.05},
            "metabolism": {"weight_multiplier": 1.0, "priority_boost": 0.05},
            "stem_cell": {"weight_multiplier": 1.1, "priority_boost": 0.10}
        }
        
        # 时间权重配置 - 用于趋势关键词
        self.temporal_weights = {
            "recent": {"days": 90, "weight": 1.5},
            "current": {"days": 365, "weight": 1.2},
            "established": {"days": 1095, "weight": 1.0}
        }
        
        # AI提示词模板
        self.keyword_expansion_template = """
        你是一位专业的单细胞生物学文献检索专家。基于用户的研究意图和方向分析，生成一套高度优化的动态关键词策略。

        研究意图分析:
        {intent_analysis}

        研究方向信息:
        {research_direction}

        用户背景和约束:
        {user_context}

        现有基础关键词:
        {base_keywords}

        请生成动态关键词扩展策略，输出JSON格式：

        {{
            "keyword_expansion_strategy": {{
                "primary_expansion": {{
                    "core_concepts": [
                        {{
                            "concept": "核心概念",
                            "keywords": ["关键词1", "关键词2", "关键词3"],
                            "weight": 0.9,
                            "expansion_rationale": "扩展理由"
                        }}
                    ],
                    "technical_methods": [
                        {{
                            "method_category": "技术方法类别",
                            "keywords": ["方法关键词1", "方法关键词2"],
                            "weight": 0.8,
                            "specificity_level": "general/specific/highly_specific"
                        }}
                    ],
                    "biological_contexts": [
                        {{
                            "context": "生物学上下文",
                            "keywords": ["上下文关键词1", "上下文关键词2"],
                            "weight": 0.7,
                            "domain_relevance": "immunology/oncology/neuroscience/etc"
                        }}
                    ]
                }},
                "adaptive_expansion": {{
                    "synonyms_and_variants": [
                        {{
                            "base_term": "基础术语",
                            "variants": ["变体1", "变体2", "缩写", "全称"],
                            "usage_contexts": ["使用场景1", "使用场景2"]
                        }}
                    ],
                    "cross_domain_terms": [
                        {{
                            "term": "跨领域术语",
                            "domains": ["领域1", "领域2"],
                            "connection_type": "methodological/conceptual/clinical"
                        }}
                    ],
                    "emerging_concepts": [
                        {{
                            "concept": "新兴概念",
                            "keywords": ["新兴关键词1", "新兴关键词2"],
                            "novelty_score": 0.85,
                            "adoption_trend": "rising/stable/declining"
                        }}
                    ]
                }},
                "contextual_modifiers": {{
                    "temporal_qualifiers": ["recent", "latest", "2023", "2024", "current"],
                    "scale_qualifiers": ["large-scale", "high-throughput", "comprehensive"],
                    "quality_qualifiers": ["high-resolution", "single-cell level", "multi-modal"],
                    "application_qualifiers": ["clinical", "translational", "therapeutic"]
                }},
                "negative_keywords": [
                    "应该排除的术语（避免无关结果）"
                ],
                "search_optimization_notes": {{
                    "platform_specific_tips": {{
                        "pubmed": "PubMed优化建议",
                        "google_scholar": "Google Scholar优化建议"
                    }},
                    "boolean_logic_suggestions": [
                        "布尔逻辑组合建议1",
                        "布尔逻辑组合建议2"
                    ],
                    "field_search_recommendations": {{
                        "title_terms": ["标题搜索推荐词"],
                        "abstract_terms": ["摘要搜索推荐词"],
                        "mesh_terms": ["MeSH标准词汇"]
                    }}
                }}
            }}
        }}

        生成要求：
        1. 关键词要精确匹配用户的研究意图和技术需求
        2. 考虑不同搜索平台的特点和语法
        3. 平衡搜索的查全率和查准率
        4. 包含领域内的标准术语和新兴概念
        5. 提供明确的搜索策略指导
        """

    async def initialize(self):
        """初始化服务依赖"""
        try:
            # DynamicKeywordGenerator是独立的核心服务，无需额外依赖
            logger.info("DynamicKeywordGenerator initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize DynamicKeywordGenerator: {e}")
            raise

    async def generate_dynamic_keywords(
        self,
        research_direction: Dict[str, Any],
        intent_analysis: Dict[str, Any],
        user_context: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ) -> DynamicKeywordResult:
        """
        生成动态关键词集合
        
        Args:
            research_direction: 研究方向分析结果
            intent_analysis: 用户意图分析
            user_context: 用户上下文信息
            session_id: 会话ID
            
        Returns:
            DynamicKeywordResult: 完整的动态关键词结果
        """
        try:
            # DynamicKeywordGenerator已初始化，无需额外检查
            
            start_time = datetime.now()
            session_id = session_id or self._generate_session_id()
            
            logger.info(f"开始生成动态关键词 - 会话ID: {session_id}")
            
            # 1. 获取基础关键词
            base_keywords = await self._extract_base_keywords(research_direction, intent_analysis)
            
            # 2. 执行AI驱动的关键词扩展
            expanded_keywords = await self._ai_keyword_expansion(
                research_direction, intent_analysis, user_context, base_keywords
            )
            
            # 3. 生成关键词聚类
            keyword_clusters = await self._generate_keyword_clusters(
                expanded_keywords, research_direction, intent_analysis
            )
            
            # 4. 生成自适应关键词
            adaptive_keywords = await self._generate_adaptive_keywords(
                base_keywords, expanded_keywords, user_context
            )
            
            # 5. 提取上下文关键词
            context_keywords = await self._extract_context_keywords(
                intent_analysis, user_context
            )
            
            # 6. 生成趋势关键词
            trending_keywords = await self._generate_trending_keywords(
                research_direction, expanded_keywords
            )
            
            # 7. 确定生成策略
            generation_strategy = self._determine_generation_strategy(
                research_direction, intent_analysis
            )
            
            # 8. 计算置信度和优化建议
            confidence_score = self._calculate_generation_confidence(
                keyword_clusters, adaptive_keywords, context_keywords
            )
            
            optimization_notes = self._generate_optimization_notes(
                keyword_clusters, generation_strategy, research_direction
            )
            
            # 9. 构建结果
            result = DynamicKeywordResult(
                session_id=session_id,
                research_focus=research_direction.get("title", "研究方向"),
                keyword_clusters=keyword_clusters,
                adaptive_keywords=adaptive_keywords,
                context_keywords=context_keywords,
                trending_keywords=trending_keywords,
                total_keywords=sum(len(cluster.primary_keywords) + len(cluster.secondary_keywords) 
                                 for cluster in keyword_clusters),
                confidence_score=confidence_score,
                generation_strategy=generation_strategy,
                optimization_notes=optimization_notes,
                generation_timestamp=datetime.now().isoformat()
            )
            
            # 10. 保存生成记录
            await self._save_generation_record(result, research_direction, intent_analysis)
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            logger.info(f"动态关键词生成完成 - 用时{processing_time:.2f}ms，生成{result.total_keywords}个关键词")
            
            return result
            
        except Exception as e:
            logger.error(f"动态关键词生成失败: {str(e)}")
            return await self._generate_fallback_keywords(research_direction, str(e))

    async def optimize_keywords_for_platforms(
        self,
        keyword_result: DynamicKeywordResult,
        target_platforms: List[str]
    ) -> Dict[str, Dict[str, Any]]:
        """
        为特定搜索平台优化关键词
        
        Args:
            keyword_result: 动态关键词结果
            target_platforms: 目标平台列表
            
        Returns:
            按平台组织的优化关键词
        """
        try:
            platform_optimizations = {}
            
            for platform in target_platforms:
                optimization = await self._optimize_for_platform(keyword_result, platform)
                platform_optimizations[platform] = optimization
            
            return platform_optimizations
            
        except Exception as e:
            logger.error(f"平台关键词优化失败: {str(e)}")
            return {}

    async def _extract_base_keywords(
        self,
        research_direction: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> List[str]:
        """提取基础关键词"""
        try:
            # 从研究方向和意图分析中提取关键信息
            direction_text = f"{research_direction.get('title', '')} {research_direction.get('description', '')}"
            intent_text = json.dumps(intent_analysis.get('comprehensive_intent_analysis', {}), ensure_ascii=False)
            
            combined_text = f"{direction_text} {intent_text}"
            
            # 使用AIService直接生成关键词
            main_topic = research_direction.get('main_topic', '')
            experiment_type = research_direction.get('experiment_type', '')
            species = research_direction.get('species', '')
            
            # 构建关键词提取提示
            keyword_prompt = f"""
作为单细胞生物学专家，请从以下研究信息中提取10-15个最重要的英文科学关键词：

研究主题：{main_topic}
实验类型：{experiment_type}  
研究物种：{species}

要求：
1. 提取准确的科学术语和技术关键词
2. 包含实验方法和分析技术相关词汇
3. 关键词应该适合用于文献搜索
4. 返回格式：用逗号分隔的英文关键词列表

示例格式：single-cell RNA sequencing, scRNA-seq, cell type identification, differential gene expression
"""
            
            # 使用AIService生成关键词
            context = {
                "user_profile": {},
                "conversation_history": [],
                "relevant_knowledge": []
            }
            
            response = await self.ai_service.generate_response(
                message=keyword_prompt,
                context=context
            )
            
            # 解析响应中的关键词
            keywords_text = response.content.strip()
            keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]
            
            # 限制关键词数量并过滤
            filtered_keywords = [kw for kw in keywords[:15] if len(kw) > 2 and len(kw) < 50]
            
            logger.info(f"提取到基础关键词: {len(filtered_keywords)}个")
            return filtered_keywords
            
        except Exception as e:
            logger.error(f"基础关键词提取失败: {str(e)}")
            # 返回默认关键词作为后备
            return [
                "single-cell RNA sequencing", "scRNA-seq", "cell type identification",
                "differential gene expression", "cellular heterogeneity", "transcriptomics"
            ]

    async def _ai_keyword_expansion(
        self,
        research_direction: Dict[str, Any],
        intent_analysis: Dict[str, Any],
        user_context: Optional[Dict[str, Any]],
        base_keywords: List[str]
    ) -> Dict[str, Any]:
        """AI驱动的关键词扩展"""
        try:
            # 构建AI扩展提示
            expansion_prompt = self.keyword_expansion_template.format(
                intent_analysis=json.dumps(intent_analysis, ensure_ascii=False, indent=2),
                research_direction=json.dumps(research_direction, ensure_ascii=False, indent=2),
                user_context=json.dumps(user_context or {}, ensure_ascii=False, indent=2),
                base_keywords=json.dumps(base_keywords, ensure_ascii=False, indent=2)
            )
            
            # 执行AI扩展
            ai_response = await self.ai_service.generate_response(
                message=expansion_prompt,
                context={"conversation_type": "keyword_expansion"}
            )
            
            # 解析AI响应
            expansion_result = await self._parse_ai_response(ai_response.get("content", ""))
            
            return expansion_result.get("keyword_expansion_strategy", {})
            
        except Exception as e:
            logger.error(f"AI关键词扩展失败: {str(e)}")
            return {}

    async def _generate_keyword_clusters(
        self,
        expanded_keywords: Dict[str, Any],
        research_direction: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> List[KeywordCluster]:
        """生成关键词聚类"""
        try:
            clusters = []
            
            # 从扩展结果中提取不同类别的关键词
            primary_expansion = expanded_keywords.get("primary_expansion", {})
            
            # 核心概念聚类
            core_concepts = primary_expansion.get("core_concepts", [])
            for concept_data in core_concepts:
                cluster = KeywordCluster(
                    cluster_name=f"核心概念: {concept_data.get('concept', 'Unknown')}",
                    primary_keywords=concept_data.get("keywords", [])[:5],
                    secondary_keywords=concept_data.get("keywords", [])[5:],
                    weight=float(concept_data.get("weight", 0.8)),
                    relevance_score=self._calculate_cluster_relevance(concept_data, research_direction),
                    search_priority=1
                )
                clusters.append(cluster)
            
            # 技术方法聚类
            technical_methods = primary_expansion.get("technical_methods", [])
            for method_data in technical_methods:
                cluster = KeywordCluster(
                    cluster_name=f"技术方法: {method_data.get('method_category', 'Unknown')}",
                    primary_keywords=method_data.get("keywords", [])[:3],
                    secondary_keywords=method_data.get("keywords", [])[3:],
                    weight=float(method_data.get("weight", 0.7)),
                    relevance_score=self._calculate_cluster_relevance(method_data, research_direction),
                    search_priority=2
                )
                clusters.append(cluster)
            
            # 生物学上下文聚类
            biological_contexts = primary_expansion.get("biological_contexts", [])
            for context_data in biological_contexts:
                cluster = KeywordCluster(
                    cluster_name=f"生物学上下文: {context_data.get('context', 'Unknown')}",
                    primary_keywords=context_data.get("keywords", [])[:3],
                    secondary_keywords=context_data.get("keywords", [])[3:],
                    weight=float(context_data.get("weight", 0.6)),
                    relevance_score=self._calculate_cluster_relevance(context_data, research_direction),
                    search_priority=3
                )
                clusters.append(cluster)
            
            # 按相关性排序
            clusters.sort(key=lambda x: x.relevance_score, reverse=True)
            
            return clusters[:8]  # 最多返回8个聚类
            
        except Exception as e:
            logger.error(f"关键词聚类生成失败: {str(e)}")
            return []

    async def _generate_adaptive_keywords(
        self,
        base_keywords: List[str],
        expanded_keywords: Dict[str, Any],
        user_context: Optional[Dict[str, Any]]
    ) -> List[str]:
        """生成自适应关键词"""
        try:
            adaptive_keywords = set()
            
            # 从扩展结果中提取自适应关键词
            adaptive_expansion = expanded_keywords.get("adaptive_expansion", {})
            
            # 同义词和变体
            synonyms_variants = adaptive_expansion.get("synonyms_and_variants", [])
            for variant_data in synonyms_variants:
                adaptive_keywords.update(variant_data.get("variants", []))
            
            # 跨领域术语
            cross_domain_terms = adaptive_expansion.get("cross_domain_terms", [])
            for term_data in cross_domain_terms:
                adaptive_keywords.add(term_data.get("term", ""))
            
            # 新兴概念
            emerging_concepts = adaptive_expansion.get("emerging_concepts", [])
            for concept_data in emerging_concepts:
                adaptive_keywords.update(concept_data.get("keywords", []))
            
            # 基于用户上下文的适应性调整
            if user_context:
                experience_level = user_context.get("experience_level", "intermediate")
                if experience_level == "expert":
                    # 为专家用户添加更专业的术语
                    adaptive_keywords.update(self._get_expert_level_keywords(base_keywords))
                elif experience_level == "novice":
                    # 为新手用户添加更基础的术语
                    adaptive_keywords.update(self._get_novice_level_keywords(base_keywords))
            
            return list(adaptive_keywords)[:15]  # 限制数量
            
        except Exception as e:
            logger.error(f"自适应关键词生成失败: {str(e)}")
            return []

    async def _extract_context_keywords(
        self,
        intent_analysis: Dict[str, Any],
        user_context: Optional[Dict[str, Any]]
    ) -> List[str]:
        """提取上下文关键词"""
        try:
            context_keywords = set()
            
            # 从意图分析中提取上下文
            comprehensive_analysis = intent_analysis.get("comprehensive_intent_analysis", {})
            research_objectives = comprehensive_analysis.get("research_objectives", [])
            
            for objective in research_objectives:
                # 简单的关键词提取（可以进一步优化）
                words = objective.split()
                context_keywords.update([word for word in words if len(word) > 3])
            
            # 从用户上下文中提取
            if user_context:
                resource_constraints = user_context.get("resource_constraints", {})
                available_platforms = resource_constraints.get("available_platforms", [])
                context_keywords.update(available_platforms)
            
            return list(context_keywords)[:10]
            
        except Exception as e:
            logger.error(f"上下文关键词提取失败: {str(e)}")
            return []

    async def _generate_trending_keywords(
        self,
        research_direction: Dict[str, Any],
        expanded_keywords: Dict[str, Any]
    ) -> List[str]:
        """生成趋势关键词"""
        try:
            trending_keywords = set()
            
            # 从扩展结果中提取新兴概念
            adaptive_expansion = expanded_keywords.get("adaptive_expansion", {})
            emerging_concepts = adaptive_expansion.get("emerging_concepts", [])
            
            for concept_data in emerging_concepts:
                adoption_trend = concept_data.get("adoption_trend", "stable")
                if adoption_trend == "rising":
                    trending_keywords.update(concept_data.get("keywords", []))
            
            # 添加时间限定词
            contextual_modifiers = expanded_keywords.get("contextual_modifiers", {})
            temporal_qualifiers = contextual_modifiers.get("temporal_qualifiers", [])
            trending_keywords.update(temporal_qualifiers)
            
            # 基于研究方向添加领域特定的趋势词
            domain = self._infer_research_domain(research_direction)
            if domain:
                domain_trending = self._get_domain_trending_keywords(domain)
                trending_keywords.update(domain_trending)
            
            return list(trending_keywords)[:8]
            
        except Exception as e:
            logger.error(f"趋势关键词生成失败: {str(e)}")
            return []

    def _determine_generation_strategy(
        self,
        research_direction: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> str:
        """确定关键词生成策略"""
        try:
            # 基于研究深度确定策略
            comprehensive_analysis = intent_analysis.get("comprehensive_intent_analysis", {})
            research_depth = comprehensive_analysis.get("research_depth_level", "focused")
            technical_complexity = comprehensive_analysis.get("technical_complexity", "intermediate")
            
            if research_depth == "exploratory":
                return "exploratory"
            elif research_depth == "comprehensive" and technical_complexity == "advanced":
                return "computational"
            elif "clinical" in research_direction.get("description", "").lower():
                return "clinical"
            elif "method" in research_direction.get("research_focus", "").lower():
                return "methodological"
            elif research_depth == "focused":
                return "focused"
            else:
                return "comparative"
                
        except Exception as e:
            logger.error(f"策略确定失败: {str(e)}")
            return "focused"

    def _calculate_generation_confidence(
        self,
        keyword_clusters: List[KeywordCluster],
        adaptive_keywords: List[str],
        context_keywords: List[str]
    ) -> float:
        """计算生成置信度"""
        try:
            # 基础置信度
            confidence = 0.6
            
            # 聚类数量和质量加分
            if keyword_clusters:
                cluster_quality = sum(cluster.relevance_score for cluster in keyword_clusters) / len(keyword_clusters)
                confidence += min(cluster_quality * 0.2, 0.25)
                
                # 聚类数量加分
                confidence += min(len(keyword_clusters) * 0.02, 0.1)
            
            # 自适应关键词数量加分
            confidence += min(len(adaptive_keywords) * 0.01, 0.08)
            
            # 上下文关键词数量加分
            confidence += min(len(context_keywords) * 0.005, 0.05)
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"置信度计算失败: {str(e)}")
            return 0.5

    def _generate_optimization_notes(
        self,
        keyword_clusters: List[KeywordCluster],
        generation_strategy: str,
        research_direction: Dict[str, Any]
    ) -> str:
        """生成优化建议"""
        try:
            notes = []
            
            # 基于生成策略的建议
            strategy_notes = {
                "exploratory": "建议使用宽泛的搜索词组合，重点关注综述类文献",
                "focused": "使用精确的技术术语，结合布尔逻辑优化查准率",
                "comparative": "重点关注对比研究，包含多个研究对象的关键词",
                "methodological": "优先搜索方法学文献，关注技术和流程描述",
                "clinical": "结合临床术语和疾病关键词，重点关注转化研究",
                "computational": "包含计算方法和算法关键词，关注生物信息学资源"
            }
            
            notes.append(strategy_notes.get(generation_strategy, "使用标准搜索策略"))
            
            # 基于聚类的建议
            if keyword_clusters:
                high_priority_clusters = [c for c in keyword_clusters if c.search_priority <= 2]
                if high_priority_clusters:
                    notes.append(f"优先使用{len(high_priority_clusters)}个高优先级关键词聚类")
                
                # 权重建议
                avg_weight = sum(c.weight for c in keyword_clusters) / len(keyword_clusters)
                if avg_weight > 0.8:
                    notes.append("关键词权重较高，建议使用精确匹配搜索")
                else:
                    notes.append("关键词权重适中，可使用模糊匹配扩大搜索范围")
            
            return "; ".join(notes)
            
        except Exception as e:
            logger.error(f"优化建议生成失败: {str(e)}")
            return "使用标准关键词优化策略"

    async def _optimize_for_platform(
        self,
        keyword_result: DynamicKeywordResult,
        platform: str
    ) -> Dict[str, Any]:
        """为特定平台优化关键词"""
        try:
            platform_config = {
                "pubmed": {
                    "max_keywords": 15,
                    "boolean_support": True,
                    "mesh_terms": True,
                    "field_search": True
                },
                "google_scholar": {
                    "max_keywords": 20,
                    "boolean_support": True,
                    "mesh_terms": False,
                    "field_search": False
                },
                "semantic_scholar": {
                    "max_keywords": 12,
                    "boolean_support": False,
                    "mesh_terms": False,
                    "field_search": False
                }
            }
            
            config = platform_config.get(platform, platform_config["google_scholar"])
            
            # 基于平台特点选择关键词
            optimized_keywords = []
            
            # 从聚类中选择关键词
            for cluster in keyword_result.keyword_clusters:
                if len(optimized_keywords) >= config["max_keywords"]:
                    break
                optimized_keywords.extend(cluster.primary_keywords[:3])
            
            # 添加自适应关键词
            remaining_slots = config["max_keywords"] - len(optimized_keywords)
            if remaining_slots > 0:
                optimized_keywords.extend(keyword_result.adaptive_keywords[:remaining_slots])
            
            return {
                "platform": platform,
                "optimized_keywords": optimized_keywords[:config["max_keywords"]],
                "search_strategies": self._generate_platform_search_strategies(
                    optimized_keywords, config
                ),
                "optimization_notes": f"为{platform}平台优化的{len(optimized_keywords)}个关键词"
            }
            
        except Exception as e:
            logger.error(f"平台优化失败: {str(e)}")
            return {"platform": platform, "optimized_keywords": [], "error": str(e)}

    def _generate_platform_search_strategies(
        self,
        keywords: List[str],
        platform_config: Dict[str, Any]
    ) -> Dict[str, List[str]]:
        """生成平台特定的搜索策略"""
        strategies = {
            "exact_matches": [f'"{kw}"' for kw in keywords[:5]],
            "broad_matches": keywords[:8]
        }
        
        if platform_config.get("boolean_support"):
            strategies["boolean_queries"] = [
                f'({keywords[0]} AND {keywords[1]})' if len(keywords) >= 2 else "",
                f'"{keywords[0]}" OR "{keywords[1]}"' if len(keywords) >= 2 else ""
            ]
        
        if platform_config.get("field_search"):
            strategies["field_queries"] = [
                f'title:"{keywords[0]}"' if keywords else "",
                f'abstract:"{keywords[1]}"' if len(keywords) > 1 else ""
            ]
        
        return strategies

    async def _save_generation_record(
        self,
        result: DynamicKeywordResult,
        research_direction: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ):
        """保存生成记录到数据库"""
        try:
            db = next(get_db())
            
            # 创建生成记录
            generation_record = DynamicKeywordGeneration(
                session_id=result.session_id,
                research_direction_id=research_direction.get("direction_id"),
                generation_strategy=result.generation_strategy,
                keyword_clusters=json.dumps([
                    {
                        "cluster_name": cluster.cluster_name,
                        "primary_keywords": cluster.primary_keywords,
                        "secondary_keywords": cluster.secondary_keywords,
                        "weight": cluster.weight,
                        "relevance_score": cluster.relevance_score,
                        "search_priority": cluster.search_priority
                    } for cluster in result.keyword_clusters
                ], ensure_ascii=False),
                adaptive_keywords=result.adaptive_keywords,
                context_keywords=result.context_keywords,
                trending_keywords=result.trending_keywords,
                total_keywords_generated=result.total_keywords,
                generation_confidence=result.confidence_score,
                optimization_notes=result.optimization_notes
            )
            
            db.add(generation_record)
            db.commit()
            db.refresh(generation_record)
            
            logger.info(f"关键词生成记录已保存 - ID: {generation_record.id}")
            
        except Exception as e:
            logger.error(f"保存生成记录失败: {str(e)}")
            # 不影响主流程，继续执行

    async def _generate_fallback_keywords(
        self,
        research_direction: Dict[str, Any],
        error_msg: str
    ) -> DynamicKeywordResult:
        """生成降级关键词结果"""
        try:
            # 基本的降级关键词
            fallback_cluster = KeywordCluster(
                cluster_name="基础搜索词",
                primary_keywords=["single cell", "scRNA-seq", "RNA sequencing"],
                secondary_keywords=["transcriptome", "gene expression", "cell type"],
                weight=0.7,
                relevance_score=0.6,
                search_priority=1
            )
            
            return DynamicKeywordResult(
                session_id=self._generate_session_id(),
                research_focus=research_direction.get("title", "单细胞研究"),
                keyword_clusters=[fallback_cluster],
                adaptive_keywords=["single-cell analysis", "genomics", "bioinformatics"],
                context_keywords=["research", "analysis", "study"],
                trending_keywords=["2024", "recent", "latest"],
                total_keywords=12,
                confidence_score=0.4,
                generation_strategy="fallback",
                optimization_notes=f"降级模式 - 原因: {error_msg}",
                generation_timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            logger.error(f"降级关键词生成失败: {str(e)}")
            # 返回最基本的结果
            return DynamicKeywordResult(
                session_id="fallback",
                research_focus="默认搜索",
                keyword_clusters=[],
                adaptive_keywords=[],
                context_keywords=[],
                trending_keywords=[],
                total_keywords=0,
                confidence_score=0.0,
                generation_strategy="error",
                optimization_notes="系统错误，无法生成关键词",
                generation_timestamp=datetime.now().isoformat()
            )

    # 辅助方法
    def _generate_session_id(self) -> str:
        """生成会话ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        hash_suffix = hashlib.md5(str(datetime.now().timestamp()).encode()).hexdigest()[:8]
        return f"dkg_{timestamp}_{hash_suffix}"

    def _calculate_cluster_relevance(
        self,
        cluster_data: Dict[str, Any],
        research_direction: Dict[str, Any]
    ) -> float:
        """计算聚类相关性分数"""
        base_score = cluster_data.get("weight", 0.5)
        
        # 基于研究方向调整分数
        direction_text = research_direction.get("description", "").lower()
        cluster_keywords = cluster_data.get("keywords", [])
        
        # 检查关键词与研究方向的匹配度
        keyword_matches = sum(1 for kw in cluster_keywords if kw.lower() in direction_text)
        match_bonus = (keyword_matches / len(cluster_keywords)) * 0.2 if cluster_keywords else 0
        
        return min(base_score + match_bonus, 1.0)

    def _infer_research_domain(self, research_direction: Dict[str, Any]) -> Optional[str]:
        """推断研究领域"""
        direction_text = research_direction.get("description", "").lower()
        
        domain_indicators = {
            "immunology": ["immune", "t cell", "b cell", "antibody", "cytokine"],
            "oncology": ["cancer", "tumor", "malignant", "metastasis"],
            "neuroscience": ["neuron", "brain", "neural", "cognitive"],
            "developmental_biology": ["development", "differentiation", "embryonic"],
            "metabolism": ["metabolic", "glucose", "insulin", "lipid"]
        }
        
        for domain, indicators in domain_indicators.items():
            if any(indicator in direction_text for indicator in indicators):
                return domain
        
        return None

    def _get_domain_trending_keywords(self, domain: str) -> List[str]:
        """获取领域特定的趋势关键词"""
        domain_trending = {
            "immunology": ["CAR-T", "checkpoint inhibitor", "immunotherapy", "cytokine storm"],
            "oncology": ["liquid biopsy", "precision medicine", "tumor heterogeneity"],
            "neuroscience": ["neuroplasticity", "brain organoid", "connectome"],
            "developmental_biology": ["organogenesis", "cell fate", "lineage tracing"],
            "metabolism": ["metabolomics", "mitochondrial dysfunction", "metabolic syndrome"]
        }
        
        return domain_trending.get(domain, [])

    def _get_expert_level_keywords(self, base_keywords: List[str]) -> List[str]:
        """为专家用户生成高级关键词"""
        expert_keywords = []
        for keyword in base_keywords:
            if "single cell" in keyword.lower():
                expert_keywords.extend(["scRNA-seq", "scATAC-seq", "multiome", "spatial transcriptomics"])
            elif "analysis" in keyword.lower():
                expert_keywords.extend(["trajectory inference", "pseudotime", "RNA velocity"])
        return expert_keywords

    def _get_novice_level_keywords(self, base_keywords: List[str]) -> List[str]:
        """为新手用户生成基础关键词"""
        novice_keywords = []
        for keyword in base_keywords:
            if "single cell" in keyword.lower():
                novice_keywords.extend(["cell biology", "gene expression", "RNA sequencing"])
            elif "analysis" in keyword.lower():
                novice_keywords.extend(["data analysis", "bioinformatics", "computational biology"])
        return novice_keywords

    async def _parse_ai_response(self, content: str) -> Dict[str, Any]:
        """解析AI响应内容"""
        try:
            # 尝试直接解析JSON
            if content.strip().startswith('{'):
                return json.loads(content)
            
            # 提取JSON块
            import re
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            
            # 查找大括号内的内容
            json_match = re.search(r'(\{.*\})', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            
            # 如果都失败，返回空结构
            return {}
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return {}


# 全局服务实例
dynamic_keyword_generator = DynamicKeywordGenerator()


def get_dynamic_keyword_generator() -> DynamicKeywordGenerator:
    """获取动态关键词生成器实例"""
    return dynamic_keyword_generator