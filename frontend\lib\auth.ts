/**
 * 权限检查工具函数
 */

import { UserRole } from "@/contexts/auth-context"

// 权限定义
export const PERMISSIONS = {
  // 仪表盘权限
  view_dashboard: ["super_admin", "sales", "operations"],
  export_dashboard: ["super_admin", "sales"],
  customize_dashboard: ["super_admin"],

  // 客户管理权限
  view_customers: ["super_admin", "sales", "operations"],
  manage_customers: ["super_admin", "sales"],
  add_customers: ["super_admin", "sales"],
  edit_customers: ["super_admin", "sales"],
  delete_customers: ["super_admin"],
  export_customers: ["super_admin", "sales"],

  // 方案管理权限
  view_solutions: ["super_admin", "sales", "operations", "customer"],
  create_solutions: ["super_admin", "sales", "operations"],
  edit_solutions: ["super_admin", "sales", "operations"],
  delete_solutions: ["super_admin"],
  share_solutions: ["super_admin", "sales"],

  // 知识库权限
  view_knowledge: ["super_admin", "sales", "operations", "customer"],
  manage_knowledge: ["super_admin", "operations"],
  add_knowledge: ["super_admin", "operations"],
  edit_knowledge: ["super_admin", "operations"],
  delete_knowledge: ["super_admin"],
  approve_knowledge: ["super_admin", "operations"],

  // 系统管理权限
  manage_users: ["super_admin"],
  manage_permissions: ["super_admin"],
  manage_system: ["super_admin", "operations"],
  view_analytics: ["super_admin", "sales", "operations"],
} as const

export type PermissionKey = keyof typeof PERMISSIONS

/**
 * 检查用户是否具有指定权限
 * @param permission 权限名称
 * @param userRole 用户角色
 * @returns 是否具有权限
 */
export function hasPermission(permission: PermissionKey, userRole?: UserRole): boolean {
  if (!userRole) return false
  
  const allowedRoles = PERMISSIONS[permission]
  return allowedRoles.includes(userRole)
}

/**
 * 获取用户的所有权限
 * @param userRole 用户角色
 * @returns 权限列表
 */
export function getUserPermissions(userRole: UserRole): PermissionKey[] {
  return Object.keys(PERMISSIONS).filter(permission => 
    hasPermission(permission as PermissionKey, userRole)
  ) as PermissionKey[]
}

/**
 * 检查用户是否具有任一权限
 * @param permissions 权限列表
 * @param userRole 用户角色
 * @returns 是否具有任一权限
 */
export function hasAnyPermission(permissions: PermissionKey[], userRole?: UserRole): boolean {
  return permissions.some(permission => hasPermission(permission, userRole))
}

/**
 * 检查用户是否具有所有权限
 * @param permissions 权限列表
 * @param userRole 用户角色
 * @returns 是否具有所有权限
 */
export function hasAllPermissions(permissions: PermissionKey[], userRole?: UserRole): boolean {
  return permissions.every(permission => hasPermission(permission, userRole))
}
