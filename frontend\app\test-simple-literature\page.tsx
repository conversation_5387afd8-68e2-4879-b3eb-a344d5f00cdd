"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { BookOpen, CheckCircle, AlertCircle } from 'lucide-react'

export default function TestSimpleLiteraturePage() {
  const [enabled, setEnabled] = useState(false)
  const [status, setStatus] = useState({
    literature_search_enabled: false,
    is_ready: false,
    available_apis: ['biorxiv'],
    available_count: 1,
    total_apis: 3,
    recommendations: ['配置 LITERATURE_SEARCH_ENABLED=true 以启用文献搜索']
  })

  const handleToggle = (newEnabled: boolean) => {
    setEnabled(newEnabled)
    console.log(`文献搜索${newEnabled ? '已启用' : '已禁用'}`)
  }

  const testApiCall = async () => {
    try {
      const response = await fetch('http://localhost:8001/api/v1/literature/search-status', {
        headers: {
          'Authorization': 'Bearer test-token'
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setStatus(data)
        console.log('API调用成功:', data)
      } else {
        console.error('API调用失败:', response.status)
      }
    } catch (error) {
      console.error('网络错误:', error)
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">文献集成功能测试</h1>
      
      <div className="grid gap-6">
        {/* 功能状态卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              文献搜索功能状态
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span>启用文献搜索</span>
              <Switch
                checked={enabled}
                onCheckedChange={handleToggle}
              />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">功能状态:</span>
                <Badge variant={status.is_ready ? "default" : "secondary"}>
                  {status.is_ready ? (
                    <>
                      <CheckCircle className="h-3 w-3 mr-1" />
                      可用
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-3 w-3 mr-1" />
                      未配置
                    </>
                  )}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">可用数据源:</span>
                <span className="text-sm font-medium">
                  {status.available_count}/{status.total_apis}
                </span>
              </div>
              
              {status.available_apis.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {status.available_apis.map((api) => (
                    <Badge key={api} variant="outline" className="text-xs">
                      {api}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 配置建议 */}
        {status.recommendations.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">配置建议</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {status.recommendations.map((rec, index) => (
                  <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span>{rec}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}

        {/* 测试按钮 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">API测试</CardTitle>
          </CardHeader>
          <CardContent>
            <Button onClick={testApiCall} className="w-full">
              测试后端API连接
            </Button>
            <p className="text-xs text-gray-500 mt-2">
              点击测试与后端的连接状态
            </p>
          </CardContent>
        </Card>

        {/* 功能说明 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">功能说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <h4 className="font-medium text-sm mb-1">✅ 已实现功能</h4>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>• 可选文献搜索开关</li>
                  <li>• 统一的外部API配置管理</li>
                  <li>• 文献搜索状态检查</li>
                  <li>• 智能降级和错误处理</li>
                  <li>• 配置建议和故障排除</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium text-sm mb-1">🔧 配置要求</h4>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>• 后端: 设置 LITERATURE_SEARCH_ENABLED=true</li>
                  <li>• 可选: 配置 PUBMED_API_KEY</li>
                  <li>• 可选: 配置 SEMANTIC_SCHOLAR_API_KEY</li>
                  <li>• 默认: bioRxiv API (无需密钥)</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
