"use client"

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, Info } from 'lucide-react'

import EnhancedSolutionOverviewCard from './enhanced-solution-overview-card'
import ResearchIntentGuessPanel from './research-intent-guess-panel'
import PrecisionLiteratureSearch from './precision-literature-search'

interface StreamlinedSolutionData {
  solution_id: string
  generated_at: string
  generation_method: string
  solution_overview: {
    research_type: string
    estimated_cost: string
    recommended_platform: string
    project_timeline: string
    key_process_steps: string[]
    critical_considerations: string[]
    technical_details: {
      platform_specifications: string
      sample_prep_protocol: string
      analysis_pipeline: string
      expected_outcomes: string
    }
  }
  research_intent_guess: {
    primary_intent: string
    research_direction: string
    analysis_type: string
    confidence_level: string
    key_interests?: string[]
    inferred_goals?: string[]
  }
  literature_search: {
    research_focus: string
    precision_search_links: {
      pubmed: string
      google_scholar: string
      semantic_scholar: string
      biorxiv: string
    }
    featured_papers: Array<{
      title: string
      authors: string[]
      journal: string
      year: number
      relevance_score: number
      direct_links: {
        pubmed?: string
        doi?: string
        pdf?: string
        google_scholar?: string
      }
      why_relevant: string
    }>
    search_tips: string[]
  }
  agent_enhancement?: {
    literature_evidence: any
    professional_insights: any[]
    confidence_score: number
  }
}

interface StreamlinedSolutionDisplayProps {
  solutionData: StreamlinedSolutionData
}

const StreamlinedSolutionDisplay: React.FC<StreamlinedSolutionDisplayProps> = ({ solutionData }) => {
  if (!solutionData) {
    return (
      <Alert className="m-4">
        <Info className="h-4 w-4" />
        <AlertDescription>
          暂无方案数据，请先完成需求收集。
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* 页面标题和状态 */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          🧬 智能化单细胞测序解决方案
        </h1>
        <p className="text-gray-600 mb-4">
          基于AI分析的个性化方案，包含研究意图分析、技术方案和精准文献推荐
        </p>
        <div className="flex items-center justify-center gap-4">
          <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            方案已生成
          </Badge>
          <Badge variant="outline">
            方案ID: {solutionData.solution_id ? solutionData.solution_id.slice(-8) : '未知'}
          </Badge>
          <Badge variant="outline">
            生成方式: {solutionData.generation_method === 'streamlined' ? '简化版' : '标准版'}
          </Badge>
        </div>
      </div>

      {/* Agent增强信息提示 */}
      {solutionData.agent_enhancement && (
        <Alert className="bg-blue-50 border-blue-200">
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>AI增强分析：</strong>本方案采用了智能Agent技术进行深度文献分析和专业洞察，
            置信度评分：{Math.round((solutionData.agent_enhancement.confidence_score || 0.85) * 100)}%
          </AlertDescription>
        </Alert>
      )}

      {/* 三个核心模块 */}
      <div className="space-y-8">
        {/* 1. 方案概览（整合技术方案详情） */}
        <div>
          <div className="mb-4">
            <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
              <span className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</span>
              方案概览
            </h2>
            <p className="text-sm text-gray-600 ml-8">技术方案、流程安排和关键注意事项</p>
          </div>
          {solutionData.solution_overview ? (
            <EnhancedSolutionOverviewCard solution_overview={solutionData.solution_overview} />
          ) : (
            <div className="p-4 text-center text-gray-500">方案概览数据加载中...</div>
          )}
        </div>

        {/* 2. 研究意图猜测模块 */}
        <div>
          <div className="mb-4">
            <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
              <span className="w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</span>
              研究意图分析
            </h2>
            <p className="text-sm text-gray-600 ml-8">AI智能分析您的研究目标和关注重点</p>
          </div>
          {solutionData.research_intent_guess ? (
            <ResearchIntentGuessPanel research_intent_guess={solutionData.research_intent_guess} />
          ) : (
            <div className="p-4 text-center text-gray-500">研究意图分析数据加载中...</div>
          )}
        </div>

        {/* 3. 精准文献搜索模块 */}
        <div>
          <div className="mb-4">
            <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
              <span className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</span>
              精准文献搜索
            </h2>
            <p className="text-sm text-gray-600 ml-8">一键直达相关文献，推荐高质量参考资料</p>
          </div>
          {solutionData.literature_search ? (
            <PrecisionLiteratureSearch literature_search={solutionData.literature_search} />
          ) : (
            <div className="p-4 text-center text-gray-500">文献搜索数据加载中...</div>
          )}
        </div>
      </div>

      {/* 底部信息 */}
      <div className="mt-12 pt-8 border-t border-gray-200">
        <div className="text-center text-sm text-gray-500">
          <p>方案生成时间：{solutionData.generated_at ? new Date(solutionData.generated_at).toLocaleString('zh-CN') : '未知'}</p>
          <p className="mt-1">
            如需调整方案或有疑问，请联系我们的技术专家团队
          </p>
        </div>
      </div>
    </div>
  )
}

export default StreamlinedSolutionDisplay