"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { RoleBadge } from "@/components/role-badge"
import { PermissionGate } from "@/components/permission-gate"
import { Plus, Edit, Trash2, Users, Copy } from "lucide-react"
import type { UserRole } from "@/contexts/auth-context"

interface RoleDefinition {
  id: UserRole
  name: string
  description: string
  userCount: number
  isSystem: boolean
  createdAt?: string
  createdBy?: string
}

export default function RolesPage() {
  const [roles, setRoles] = useState<RoleDefinition[]>([
    {
      id: "super_admin",
      name: "超级管理员",
      description: "拥有系统所有权限，可以管理用户和权限",
      userCount: 1,
      isSystem: true,
    },
    {
      id: "sales",
      name: "销售人员",
      description: "可以管理客户和方案，查看分析数据",
      userCount: 5,
      isSystem: true,
    },
    {
      id: "operations",
      name: "运维人员",
      description: "负责知识库维护和系统运营",
      userCount: 3,
      isSystem: true,
    },
    {
      id: "customer",
      name: "客户",
      description: "可以查看方案和知识库内容",
      userCount: 25,
      isSystem: true,
    },
    {
      id: "sales_manager" as UserRole,
      name: "销售经理",
      description: "管理销售团队和客户关系",
      userCount: 2,
      isSystem: false,
      createdAt: "2023-12-15",
      createdBy: "管理员",
    },
    {
      id: "tech_support" as UserRole,
      name: "技术支持",
      description: "提供技术咨询和问题解决",
      userCount: 4,
      isSystem: false,
      createdAt: "2024-01-10",
      createdBy: "管理员",
    },
  ])

  const [editingRole, setEditingRole] = useState<RoleDefinition | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [newRole, setNewRole] = useState<Partial<RoleDefinition>>({
    name: "",
    description: "",
  })

  const handleSaveRole = () => {
    if (isCreating) {
      // 创建新角色的逻辑
      const roleId = newRole.name?.toLowerCase().replace(/\s+/g, "_") as UserRole
      const newRoleObj: RoleDefinition = {
        id: roleId,
        name: newRole.name || "",
        description: newRole.description || "",
        userCount: 0,
        isSystem: false,
        createdAt: new Date().toISOString().split("T")[0],
        createdBy: "当前用户",
      }
      setRoles([...roles, newRoleObj])
      setNewRole({ name: "", description: "" })
      setIsCreating(false)
    } else if (editingRole) {
      // 更新现有角色的逻辑
      const updatedRoles = roles.map((role) => (role.id === editingRole.id ? { ...role, ...editingRole } : role))
      setRoles(updatedRoles)
      setEditingRole(null)
    }
  }

  const handleDeleteRole = (roleId: UserRole) => {
    setRoles(roles.filter((role) => role.id !== roleId))
  }

  const handleDuplicateRole = (role: RoleDefinition) => {
    const newRoleObj: RoleDefinition = {
      ...role,
      id: `${role.id}_copy` as UserRole,
      name: `${role.name} (副本)`,
      userCount: 0,
      isSystem: false,
      createdAt: new Date().toISOString().split("T")[0],
      createdBy: "当前用户",
    }
    setRoles([...roles, newRoleObj])
  }

  return (
    <div className="container py-10">
      <PermissionGate permission="manage_permissions" fallback={<div>您没有权限访问此页面</div>}>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">角色管理</h1>
            <p className="text-muted-foreground">创建和管理系统角色</p>
          </div>
          <Dialog open={isCreating} onOpenChange={setIsCreating}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                创建角色
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>创建新角色</DialogTitle>
                <DialogDescription>创建新的用户角色并设置基本信息</DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="role-name">角色名称</Label>
                  <Input
                    id="role-name"
                    placeholder="输入角色名称"
                    value={newRole.name}
                    onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role-description">角色描述</Label>
                  <Input
                    id="role-description"
                    placeholder="描述该角色的权限和职责"
                    value={newRole.description}
                    onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreating(false)}>
                  取消
                </Button>
                <Button onClick={handleSaveRole} disabled={!newRole.name}>
                  创建角色
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>系统角色</CardTitle>
            <CardDescription>管理系统中的用户角色</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="all">所有角色</TabsTrigger>
                <TabsTrigger value="system">系统角色</TabsTrigger>
                <TabsTrigger value="custom">自定义角色</TabsTrigger>
              </TabsList>

              <TabsContent value="all">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>角色名称</TableHead>
                      <TableHead>描述</TableHead>
                      <TableHead>用户数</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {roles.map((role) => (
                      <TableRow key={role.id}>
                        <TableCell>
                          <div className="font-medium flex items-center gap-2">
                            {role.name}
                            <RoleBadge role={role.id} />
                          </div>
                        </TableCell>
                        <TableCell>{role.description}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Users className="h-4 w-4 text-muted-foreground" />
                            {role.userCount}
                          </div>
                        </TableCell>
                        <TableCell>
                          {role.isSystem ? (
                            <Badge variant="secondary">系统</Badge>
                          ) : (
                            <Badge variant="outline">自定义</Badge>
                          )}
                        </TableCell>
                        <TableCell>{role.createdAt || "-"}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDuplicateRole(role)}
                              title="复制角色"
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <Dialog
                              open={editingRole?.id === role.id}
                              onOpenChange={(open) => {
                                if (open) {
                                  setEditingRole(role)
                                } else {
                                  setEditingRole(null)
                                }
                              }}
                            >
                              <DialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  disabled={role.isSystem}
                                  title={role.isSystem ? "系统角色不可编辑" : "编辑角色"}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent>
                                <DialogHeader>
                                  <DialogTitle>编辑角色</DialogTitle>
                                  <DialogDescription>修改角色信息</DialogDescription>
                                </DialogHeader>
                                {editingRole && (
                                  <div className="space-y-4 py-4">
                                    <div className="space-y-2">
                                      <Label htmlFor="edit-role-name">角色名称</Label>
                                      <Input
                                        id="edit-role-name"
                                        value={editingRole.name}
                                        onChange={(e) => setEditingRole({ ...editingRole, name: e.target.value })}
                                      />
                                    </div>
                                    <div className="space-y-2">
                                      <Label htmlFor="edit-role-description">角色描述</Label>
                                      <Input
                                        id="edit-role-description"
                                        value={editingRole.description}
                                        onChange={(e) =>
                                          setEditingRole({ ...editingRole, description: e.target.value })
                                        }
                                      />
                                    </div>
                                  </div>
                                )}
                                <DialogFooter>
                                  <Button variant="outline" onClick={() => setEditingRole(null)}>
                                    取消
                                  </Button>
                                  <Button onClick={handleSaveRole}>保存更改</Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteRole(role.id)}
                              disabled={role.isSystem || role.userCount > 0}
                              title={
                                role.isSystem
                                  ? "系统角色不可删除"
                                  : role.userCount > 0
                                    ? "有用户使用此角色，无法删除"
                                    : "删除角色"
                              }
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>

              <TabsContent value="system">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>角色名称</TableHead>
                      <TableHead>描述</TableHead>
                      <TableHead>用户数</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {roles
                      .filter((role) => role.isSystem)
                      .map((role) => (
                        <TableRow key={role.id}>
                          <TableCell>
                            <div className="font-medium flex items-center gap-2">
                              {role.name}
                              <RoleBadge role={role.id} />
                            </div>
                          </TableCell>
                          <TableCell>{role.description}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Users className="h-4 w-4 text-muted-foreground" />
                              {role.userCount}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="icon" onClick={() => handleDuplicateRole(role)}>
                              <Copy className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TabsContent>

              <TabsContent value="custom">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>角色名称</TableHead>
                      <TableHead>描述</TableHead>
                      <TableHead>用户数</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead>创建者</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {roles.filter((role) => !role.isSystem).length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                          暂无自定义角色，点击"创建角色"按钮添加
                        </TableCell>
                      </TableRow>
                    ) : (
                      roles
                        .filter((role) => !role.isSystem)
                        .map((role) => (
                          <TableRow key={role.id}>
                            <TableCell>
                              <div className="font-medium flex items-center gap-2">
                                {role.name}
                                <RoleBadge role={role.id} />
                              </div>
                            </TableCell>
                            <TableCell>{role.description}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-1">
                                <Users className="h-4 w-4 text-muted-foreground" />
                                {role.userCount}
                              </div>
                            </TableCell>
                            <TableCell>{role.createdAt}</TableCell>
                            <TableCell>{role.createdBy}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button variant="ghost" size="icon" onClick={() => handleDuplicateRole(role)}>
                                  <Copy className="h-4 w-4" />
                                </Button>
                                <Dialog
                                  open={editingRole?.id === role.id}
                                  onOpenChange={(open) => {
                                    if (open) {
                                      setEditingRole(role)
                                    } else {
                                      setEditingRole(null)
                                    }
                                  }}
                                >
                                  <DialogTrigger asChild>
                                    <Button variant="ghost" size="icon">
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                  </DialogTrigger>
                                  <DialogContent>
                                    <DialogHeader>
                                      <DialogTitle>编辑角色</DialogTitle>
                                      <DialogDescription>修改角色信息</DialogDescription>
                                    </DialogHeader>
                                    {editingRole && (
                                      <div className="space-y-4 py-4">
                                        <div className="space-y-2">
                                          <Label htmlFor="edit-role-name">角色名称</Label>
                                          <Input
                                            id="edit-role-name"
                                            value={editingRole.name}
                                            onChange={(e) => setEditingRole({ ...editingRole, name: e.target.value })}
                                          />
                                        </div>
                                        <div className="space-y-2">
                                          <Label htmlFor="edit-role-description">角色描述</Label>
                                          <Input
                                            id="edit-role-description"
                                            value={editingRole.description}
                                            onChange={(e) =>
                                              setEditingRole({
                                                ...editingRole,
                                                description: e.target.value,
                                              })
                                            }
                                          />
                                        </div>
                                      </div>
                                    )}
                                    <DialogFooter>
                                      <Button variant="outline" onClick={() => setEditingRole(null)}>
                                        取消
                                      </Button>
                                      <Button onClick={handleSaveRole}>保存更改</Button>
                                    </DialogFooter>
                                  </DialogContent>
                                </Dialog>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleDeleteRole(role.id)}
                                  disabled={role.userCount > 0}
                                  title={role.userCount > 0 ? "有用户使用此角色，无法删除" : "删除角色"}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                    )}
                  </TableBody>
                </Table>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        <Card className="mt-6">
          <CardHeader>
            <CardTitle>角色分配</CardTitle>
            <CardDescription>管理用户的角色分配</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-4 mb-6">
              <div className="flex-1">
                <Label htmlFor="user-email" className="mb-2 block">
                  用户邮箱
                </Label>
                <Input id="user-email" placeholder="输入用户邮箱" />
              </div>
              <div className="w-48">
                <Label htmlFor="user-role" className="mb-2 block">
                  角色
                </Label>
                <select
                  id="user-role"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="">选择角色</option>
                  {roles.map((role) => (
                    <option key={role.id} value={role.id}>
                      {role.name}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex items-end">
                <Button>分配角色</Button>
              </div>
            </div>

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>用户</TableHead>
                  <TableHead>邮箱</TableHead>
                  <TableHead>当前角色</TableHead>
                  <TableHead>上次登录</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell>
                    <div className="font-medium">张三</div>
                  </TableCell>
                  <TableCell><EMAIL></TableCell>
                  <TableCell>
                    <RoleBadge role="super_admin" />
                  </TableCell>
                  <TableCell>2024-05-20 10:30</TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      修改角色
                    </Button>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <div className="font-medium">李四</div>
                  </TableCell>
                  <TableCell><EMAIL></TableCell>
                  <TableCell>
                    <RoleBadge role="sales" />
                  </TableCell>
                  <TableCell>2024-05-19 16:45</TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      修改角色
                    </Button>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <div className="font-medium">王五</div>
                  </TableCell>
                  <TableCell><EMAIL></TableCell>
                  <TableCell>
                    <RoleBadge role="operations" />
                  </TableCell>
                  <TableCell>2024-05-20 09:15</TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      修改角色
                    </Button>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </PermissionGate>
    </div>
  )
}
