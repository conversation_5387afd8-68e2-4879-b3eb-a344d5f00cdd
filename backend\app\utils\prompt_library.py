"""
后端提示词库管理脚本
用于评估和管理所有AI相关的提示词模板

功能：
- 集中管理所有提示词模板
- 提供提示词评估和测试功能
- 支持提示词版本管理
- 提供提示词性能监控
"""
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


class PromptType(Enum):
    """提示词类型枚举"""
    RESEARCH_INTENT_ANALYSIS = "research_intent_analysis"
    KEYWORD_GENERATION = "keyword_generation"
    LITERATURE_ANALYSIS = "literature_analysis"
    SOLUTION_FRAMEWORK = "solution_framework"
    RISK_ASSESSMENT = "risk_assessment"
    USER_CONVERSATION = "user_conversation"
    TERMINOLOGY_EXTRACTION = "terminology_extraction"


class PromptCategory(Enum):
    """提示词分类枚举"""
    ANALYSIS = "analysis"
    GENERATION = "generation"
    CLASSIFICATION = "classification"
    CONVERSATION = "conversation"
    EVALUATION = "evaluation"


@dataclass
class PromptTemplate:
    """提示词模板数据类"""
    id: str
    name: str
    type: PromptType
    category: PromptCategory
    template: str
    variables: List[str]
    description: str
    version: str = "1.0"
    created_at: str = ""
    updated_at: str = ""
    usage_count: int = 0
    success_rate: float = 0.0
    avg_response_time: float = 0.0
    tags: List[str] = None
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = self.created_at
        if self.tags is None:
            self.tags = []


class PromptLibrary:
    """后端提示词库管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "backend/app/config/prompt_templates.json"
        self.templates: Dict[str, PromptTemplate] = {}
        self.metrics: Dict[str, Dict[str, Any]] = {}
        self._load_default_templates()
    
    def _load_default_templates(self):
        """加载默认提示词模板"""
        
        # 1. 研究意图分析提示词
        self.add_template(PromptTemplate(
            id="research_intent_comprehensive",
            name="全面研究意图分析",
            type=PromptType.RESEARCH_INTENT_ANALYSIS,
            category=PromptCategory.ANALYSIS,
            template="""作为资深单细胞生物学专家，请全面分析用户的研究需求，识别多种可能的研究方向。

用户信息：
- 物种类型：{species_type}
- 样本类型：{sample_type}  
- 研究目标：{research_goal}
- 实验类型：{experiment_type}
- 细胞数量：{cell_count}
- 细胞活力：{cell_viability}
- 预算范围：{budget}
- 项目周期：{timeline}
- 用户描述：{user_message}

请提供全面的分析结果（JSON格式）：
{{
    "primary_research_domain": "主要研究领域",
    "research_possibilities": [
        {{
            "possibility_name": "研究可能性名称",
            "probability": 0.8,
            "research_focus": "具体研究焦点",
            "key_questions": ["关键研究问题1", "关键研究问题2"],
            "expected_outcomes": ["预期结果1", "预期结果2"],
            "technical_challenges": ["技术挑战1", "技术挑战2"]
        }}
    ],
    "sample_specific_considerations": ["样本特异性考虑"],
    "species_specific_factors": ["物种特异性因素"],
    "technical_requirements": {{
        "platform_recommendations": ["推荐平台"],
        "quality_control_points": ["质控要点"],
        "data_analysis_approaches": ["分析方法"]
    }},
    "overall_confidence": 0.85,
    "complexity_assessment": "high/medium/low"
}}

要求：
1. 基于用户信息识别2-4个最可能的研究方向
2. 每个可能性都要有具体的概率评估
3. 考虑样本和物种的特异性
4. 提供技术实现建议""",
            variables=["species_type", "sample_type", "research_goal", "experiment_type", 
                      "cell_count", "cell_viability", "budget", "timeline", "user_message"],
            description="用于全面分析用户研究意图的提示词模板",
            tags=["analysis", "research_intent", "comprehensive"]
        ))
        
        # 2. 关键词生成提示词
        self.add_template(PromptTemplate(
            id="keyword_generation_scientific",
            name="科学关键词生成",
            type=PromptType.KEYWORD_GENERATION,
            category=PromptCategory.GENERATION,
            template="""作为单细胞生物学专家，请从用户查询中提取关键的科学术语和概念。

用户查询："{user_query}"
上下文信息：{context}

请提取以下类型的关键词：
1. 细胞类型和亚群
2. 生物学过程和功能
3. 技术平台和方法
4. 疾病和病理状态
5. 组织和器官
6. 分子标志物
7. 分析方法

请以JSON格式返回：
{{
    "cell_types": ["提取的细胞类型"],
    "biological_processes": ["生物学过程"],
    "technologies": ["技术平台"],
    "diseases": ["疾病相关"],
    "tissues": ["组织器官"],
    "biomarkers": ["分子标志物"],
    "analysis_methods": ["分析方法"],
    "additional_terms": ["其他重要术语"]
}}

注意：
- 尽量提取具体的、科学准确的术语
- 避免过于宽泛的词汇
- 关注单细胞测序和生物学相关的专业术语""",
            variables=["user_query", "context"],
            description="用于从用户查询中提取科学关键词的提示词",
            tags=["keywords", "extraction", "scientific"]
        ))
        
        # 3. 文献分析提示词
        self.add_template(PromptTemplate(
            id="literature_analysis_comprehensive",
            name="文献综合分析",
            type=PromptType.LITERATURE_ANALYSIS,
            category=PromptCategory.ANALYSIS,
            template="""基于以下实际文献搜索结果和研究需求，生成5类智能关键词：

研究需求：
- 研究目标：{research_goal}
- 样本类型：{sample_type}
- 实验类型：{experiment_type}

实际文献内容：
{literature_content}

请基于实际文献内容生成以下5类关键词（每类5-8个）：
1. semantic_expansion: 从文献中提取的核心学术术语
2. trending_terms: 当前热点和新兴概念
3. molecular_targets: 涉及的分子、基因、蛋白质
4. clinical_terms: 临床应用和疾病相关术语
5. technical_methods: 技术方法和分析工具

请返回JSON格式，确保关键词准确且来源于实际文献内容。""",
            variables=["research_goal", "sample_type", "experiment_type", "literature_content"],
            description="基于实际文献内容生成智能关键词的提示词",
            tags=["literature", "analysis", "keywords"]
        ))
        
        # 4. 方案框架生成提示词
        self.add_template(PromptTemplate(
            id="solution_framework_generation",
            name="方案框架生成",
            type=PromptType.SOLUTION_FRAMEWORK,
            category=PromptCategory.GENERATION,
            template="""作为单细胞测序专家，请为以下研究需求生成完整的解决方案框架：

研究需求：
{requirements}

用户消息：{user_message}

框架模板：{framework_template}

请生成包含以下部分的完整方案框架：
1. 方案概览 (solution_overview)
2. 研究意图分析 (research_intent_analysis)
3. 技术平台推荐 (platform_recommendations)
4. 实验设计建议 (experimental_design)
5. 数据分析策略 (data_analysis_strategy)
6. 风险评估 (risk_assessment)
7. 项目时间线 (project_timeline)
8. 预算估算 (budget_estimation)

请以JSON格式返回，确保每个部分都有详细和实用的内容。""",
            variables=["requirements", "user_message", "framework_template"],
            description="生成完整单细胞测序解决方案框架的提示词",
            tags=["solution", "framework", "comprehensive"]
        ))
        
        # 5. 风险评估提示词
        self.add_template(PromptTemplate(
            id="risk_assessment_technical",
            name="技术风险评估",
            type=PromptType.RISK_ASSESSMENT,
            category=PromptCategory.EVALUATION,
            template="""作为技术专家，请对以下单细胞测序项目进行全面的风险评估：

项目信息：
- 样本类型：{sample_type}
- 物种：{species}  
- 预算：{budget}
- 时间线：{timeline}
- 技术需求：{technical_requirements}

请从以下维度评估风险：
1. 技术风险 (technical_risks)
2. 样本相关风险 (sample_risks)
3. 数据质量风险 (data_quality_risks)
4. 预算风险 (budget_risks)
5. 时间风险 (timeline_risks)
6. 分析复杂性风险 (analysis_complexity_risks)

请以JSON格式返回：
{{
    "overall_risk_level": "low/medium/high",
    "risk_categories": {{
        "technical": {{"level": "medium", "factors": ["因素1"], "mitigation": ["缓解策略1"]}},
        "sample": {{"level": "low", "factors": ["因素1"], "mitigation": ["缓解策略1"]}},
        // ... 其他类别
    }},
    "critical_success_factors": ["关键成功因素"],
    "recommendations": ["建议事项"],
    "contingency_plans": ["应急方案"]
}}""",
            variables=["sample_type", "species", "budget", "timeline", "technical_requirements"],
            description="评估单细胞测序项目技术风险的提示词",
            tags=["risk", "assessment", "technical"]
        ))
        
        # 6. 用户对话提示词
        self.add_template(PromptTemplate(
            id="user_conversation_consultation",
            name="用户咨询对话",
            type=PromptType.USER_CONVERSATION,
            category=PromptCategory.CONVERSATION,
            template="""你是一位专业的单细胞测序咨询专家，正在为用户提供技术咨询服务。

用户信息：
- 机构：{organization}
- 专业领域：{expertise}
- 研究兴趣：{research_interests}

对话历史：
{conversation_history}

相关知识：
{relevant_knowledge}

用户问题："{user_message}"

请提供专业、准确、有用的回答，注意：
1. 基于用户的背景调整回答的技术深度
2. 提供具体可行的建议
3. 如果涉及技术细节，要解释清楚
4. 可以推荐相关的资源或工具
5. 保持友好和专业的语调

请直接给出回答，不需要额外的格式。""",
            variables=["organization", "expertise", "research_interests", 
                      "conversation_history", "relevant_knowledge", "user_message"],
            description="用于与用户进行专业咨询对话的提示词",
            tags=["conversation", "consultation", "user_support"]
        ))
        
        # 7. 术语提取提示词
        self.add_template(PromptTemplate(
            id="terminology_extraction_dynamic",
            name="动态术语提取",
            type=PromptType.TERMINOLOGY_EXTRACTION,
            category=PromptCategory.EXTRACTION,
            template="""作为生物医学术语专家，请为以下单细胞研究需求动态生成准确的英文术语。

用户信息：
- 物种类型：{species_type}
- 样本类型：{sample_type}
- 研究目标：{research_goal}
- 主要研究领域：{primary_domain}

研究可能性：
{research_possibilities}

请生成准确的英文术语（JSON格式）：
{{
    "species_terminology": {{
        "standard_name": "标准物种英文名称",
        "common_variants": ["常用变体1", "常用变体2"],
        "taxonomy_info": "分类学信息"
    }},
    "sample_terminology": {{
        "precise_term": "精确的样本类型英文术语",
        "alternative_terms": ["备选术语1", "备选术语2"],
        "context_specific_modifiers": ["上下文修饰词1", "上下文修饰词2"]
    }},
    "research_goal_terminology": {{
        "primary_term": "主要研究目标英文术语",
        "technical_terms": ["技术术语1", "技术术语2"],
        "methodology_terms": ["方法学术语1", "方法学术语2"]
    }},
    "comprehensive_search_queries": [
        {{
            "query_type": "broad_exploration",
            "english_query": "宽泛探索性查询",
            "target_research": "针对的研究类型"
        }}
    ],
    "domain_specific_terms": {{
        "{primary_domain}": ["领域特异术语1", "领域特异术语2"]
    }}
}}

要求：
1. 所有英文术语必须是标准生物医学术语
2. 为每种研究可能性生成对应的搜索查询
3. 术语要考虑样本和物种的特异性
4. 查询要涵盖不同的研究角度""",
            variables=["species_type", "sample_type", "research_goal", 
                      "primary_domain", "research_possibilities"],
            description="动态生成生物医学英文术语的提示词",
            tags=["terminology", "extraction", "dynamic"]
        ))
    
    def add_template(self, template: PromptTemplate) -> bool:
        """添加提示词模板"""
        try:
            self.templates[template.id] = template
            logger.info(f"Added prompt template: {template.id}")
            return True
        except Exception as e:
            logger.error(f"Failed to add prompt template {template.id}: {e}")
            return False
    
    def get_template(self, template_id: str) -> Optional[PromptTemplate]:
        """获取提示词模板"""
        return self.templates.get(template_id)
    
    def update_template(self, template_id: str, **updates) -> bool:
        """更新提示词模板"""
        try:
            if template_id not in self.templates:
                logger.warning(f"Template {template_id} not found")
                return False
            
            template = self.templates[template_id]
            for key, value in updates.items():
                if hasattr(template, key):
                    setattr(template, key, value)
            
            template.updated_at = datetime.now().isoformat()
            logger.info(f"Updated prompt template: {template_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to update template {template_id}: {e}")
            return False
    
    def list_templates(
        self, 
        template_type: Optional[PromptType] = None,
        category: Optional[PromptCategory] = None,
        tags: Optional[List[str]] = None
    ) -> List[PromptTemplate]:
        """列出提示词模板"""
        filtered_templates = []
        
        for template in self.templates.values():
            # 按类型过滤
            if template_type and template.type != template_type:
                continue
            
            # 按分类过滤
            if category and template.category != category:
                continue
            
            # 按标签过滤
            if tags and not any(tag in template.tags for tag in tags):
                continue
            
            filtered_templates.append(template)
        
        return sorted(filtered_templates, key=lambda t: t.name)
    
    def format_template(self, template_id: str, **variables) -> Optional[str]:
        """格式化提示词模板"""
        try:
            template = self.get_template(template_id)
            if not template:
                logger.error(f"Template {template_id} not found")
                return None
            
            # 检查必需的变量
            missing_vars = []
            for var in template.variables:
                if var not in variables:
                    missing_vars.append(var)
            
            if missing_vars:
                logger.warning(f"Missing variables for template {template_id}: {missing_vars}")
                # 使用空字符串填充缺失的变量
                for var in missing_vars:
                    variables[var] = ""
            
            # 格式化模板
            formatted_prompt = template.template.format(**variables)
            
            # 更新使用统计
            template.usage_count += 1
            
            return formatted_prompt
            
        except Exception as e:
            logger.error(f"Failed to format template {template_id}: {e}")
            return None
    
    def evaluate_template_quality(self, template_id: str) -> Dict[str, Any]:
        """评估提示词模板质量"""
        template = self.get_template(template_id)
        if not template:
            return {"error": "Template not found"}
        
        quality_metrics = {
            "template_id": template_id,
            "name": template.name,
            "evaluation_timestamp": datetime.now().isoformat(),
            "metrics": {}
        }
        
        # 1. 基础质量指标
        quality_metrics["metrics"]["basic"] = {
            "has_description": bool(template.description),
            "has_variables": len(template.variables) > 0,
            "has_tags": len(template.tags) > 0,
            "template_length": len(template.template),
            "variable_count": len(template.variables)
        }
        
        # 2. 内容质量分析
        template_text = template.template.lower()
        quality_metrics["metrics"]["content"] = {
            "has_json_format": "json" in template_text,
            "has_requirements": "要求" in template.template or "requirement" in template_text,
            "has_examples": "例如" in template.template or "example" in template_text,
            "has_constraints": "注意" in template.template or "constraint" in template_text,
            "complexity_level": self._assess_complexity(template.template)
        }
        
        # 3. 使用统计
        quality_metrics["metrics"]["usage"] = {
            "usage_count": template.usage_count,
            "success_rate": template.success_rate,
            "avg_response_time": template.avg_response_time,
            "last_updated": template.updated_at
        }
        
        # 4. 综合评分
        quality_metrics["overall_score"] = self._calculate_quality_score(quality_metrics["metrics"])
        quality_metrics["recommendations"] = self._generate_quality_recommendations(
            template, quality_metrics["metrics"]
        )
        
        return quality_metrics
    
    def _assess_complexity(self, template_text: str) -> str:
        """评估模板复杂度"""
        # 简单的复杂度评估逻辑
        variable_count = template_text.count("{")
        line_count = len(template_text.split("\n"))
        
        if variable_count <= 3 and line_count <= 20:
            return "simple"
        elif variable_count <= 8 and line_count <= 50:
            return "medium"
        else:
            return "complex"
    
    def _calculate_quality_score(self, metrics: Dict[str, Any]) -> float:
        """计算质量评分"""
        score = 0.0
        
        # 基础指标权重
        basic = metrics["basic"]
        score += 20 if basic["has_description"] else 0
        score += 15 if basic["has_variables"] else 0
        score += 10 if basic["has_tags"] else 0
        score += min(20, basic["template_length"] / 50)  # 长度合理性
        
        # 内容质量权重
        content = metrics["content"]
        score += 15 if content["has_json_format"] else 0
        score += 10 if content["has_requirements"] else 0
        score += 5 if content["has_examples"] else 0
        score += 5 if content["has_constraints"] else 0
        
        return min(100.0, score)
    
    def _generate_quality_recommendations(
        self, 
        template: PromptTemplate, 
        metrics: Dict[str, Any]
    ) -> List[str]:
        """生成质量改进建议"""
        recommendations = []
        
        basic = metrics["basic"]
        content = metrics["content"]
        
        if not basic["has_description"]:
            recommendations.append("添加详细的模板描述")
        
        if basic["variable_count"] == 0:
            recommendations.append("考虑添加变量以提高模板灵活性")
        
        if not basic["has_tags"]:
            recommendations.append("添加标签以便分类和搜索")
        
        if not content["has_json_format"] and template.type in [
            PromptType.RESEARCH_INTENT_ANALYSIS, 
            PromptType.KEYWORD_GENERATION
        ]:
            recommendations.append("考虑添加JSON格式要求以标准化输出")
        
        if not content["has_requirements"]:
            recommendations.append("添加明确的要求说明")
        
        if template.usage_count == 0:
            recommendations.append("测试模板的实际使用效果")
        
        if template.success_rate < 0.8 and template.usage_count > 10:
            recommendations.append("优化模板以提高成功率")
        
        return recommendations
    
    def generate_test_cases(self, template_id: str) -> List[Dict[str, Any]]:
        """为提示词模板生成测试案例"""
        template = self.get_template(template_id)
        if not template:
            return []
        
        test_cases = []
        
        # 基于模板类型生成测试案例
        if template.type == PromptType.RESEARCH_INTENT_ANALYSIS:
            test_cases.extend([
                {
                    "name": "基础肿瘤研究",
                    "variables": {
                        "species_type": "人类",
                        "sample_type": "肿瘤组织",
                        "research_goal": "肿瘤异质性分析",
                        "experiment_type": "scRNA-seq",
                        "cell_count": "10000",
                        "cell_viability": "85%",
                        "budget": "15万",
                        "timeline": "3个月",
                        "user_message": "希望了解肿瘤细胞的异质性"
                    },
                    "expected_outputs": ["primary_research_domain", "research_possibilities"]
                },
                {
                    "name": "免疫细胞研究",
                    "variables": {
                        "species_type": "小鼠",
                        "sample_type": "PBMC",
                        "research_goal": "免疫反应机制",
                        "experiment_type": "10x Genomics",
                        "cell_count": "5000",
                        "cell_viability": "90%",
                        "budget": "10万",
                        "timeline": "2个月",
                        "user_message": "研究T细胞激活过程"
                    },
                    "expected_outputs": ["research_possibilities", "technical_requirements"]
                }
            ])
        
        elif template.type == PromptType.KEYWORD_GENERATION:
            test_cases.extend([
                {
                    "name": "简单查询",
                    "variables": {
                        "user_query": "单细胞RNA测序",
                        "context": "{}"
                    },
                    "expected_outputs": ["cell_types", "technologies"]
                },
                {
                    "name": "复杂查询",
                    "variables": {
                        "user_query": "T细胞免疫反应单细胞分析",
                        "context": '{"domain": "immunology"}'
                    },
                    "expected_outputs": ["cell_types", "biological_processes", "analysis_methods"]
                }
            ])
        
        return test_cases
    
    def run_template_tests(self, template_id: str) -> Dict[str, Any]:
        """运行提示词模板测试"""
        test_cases = self.generate_test_cases(template_id)
        if not test_cases:
            return {"error": "No test cases available"}
        
        test_results = {
            "template_id": template_id,
            "test_timestamp": datetime.now().isoformat(),
            "total_tests": len(test_cases),
            "passed_tests": 0,
            "failed_tests": 0,
            "results": []
        }
        
        for test_case in test_cases:
            result = {
                "test_name": test_case["name"],
                "status": "unknown",
                "formatted_prompt": None,
                "issues": []
            }
            
            try:
                # 尝试格式化模板
                formatted_prompt = self.format_template(template_id, **test_case["variables"])
                
                if formatted_prompt:
                    result["formatted_prompt"] = formatted_prompt
                    result["status"] = "passed"
                    test_results["passed_tests"] += 1
                    
                    # 检查输出格式
                    for expected_output in test_case.get("expected_outputs", []):
                        if expected_output not in formatted_prompt:
                            result["issues"].append(f"缺少预期输出字段: {expected_output}")
                else:
                    result["status"] = "failed"
                    result["issues"].append("模板格式化失败")
                    test_results["failed_tests"] += 1
                    
            except Exception as e:
                result["status"] = "failed"
                result["issues"].append(f"测试异常: {str(e)}")
                test_results["failed_tests"] += 1
            
            test_results["results"].append(result)
        
        test_results["success_rate"] = test_results["passed_tests"] / test_results["total_tests"]
        
        return test_results
    
    def export_templates(self, file_path: Optional[str] = None) -> bool:
        """导出提示词模板到文件"""
        try:
            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "templates": {}
            }
            
            for template_id, template in self.templates.items():
                export_data["templates"][template_id] = asdict(template)
            
            file_path = file_path or self.config_path
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Exported {len(self.templates)} templates to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export templates: {e}")
            return False
    
    def import_templates(self, file_path: str) -> bool:
        """从文件导入提示词模板"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            imported_count = 0
            for template_id, template_data in data.get("templates", {}).items():
                # 转换为PromptTemplate对象
                template_data["type"] = PromptType(template_data["type"])
                template_data["category"] = PromptCategory(template_data["category"])
                
                template = PromptTemplate(**template_data)
                self.templates[template_id] = template
                imported_count += 1
            
            logger.info(f"Imported {imported_count} templates from {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to import templates: {e}")
            return False
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """获取使用统计信息"""
        stats = {
            "total_templates": len(self.templates),
            "templates_by_type": {},
            "templates_by_category": {},
            "usage_statistics": {
                "total_usage": 0,
                "avg_success_rate": 0.0,
                "avg_response_time": 0.0
            },
            "top_used_templates": [],
            "least_used_templates": []
        }
        
        # 按类型统计
        for template in self.templates.values():
            type_name = template.type.value
            stats["templates_by_type"][type_name] = stats["templates_by_type"].get(type_name, 0) + 1
            
            category_name = template.category.value
            stats["templates_by_category"][category_name] = stats["templates_by_category"].get(category_name, 0) + 1
            
            stats["usage_statistics"]["total_usage"] += template.usage_count
        
        # 计算平均值
        if self.templates:
            stats["usage_statistics"]["avg_success_rate"] = sum(
                t.success_rate for t in self.templates.values()
            ) / len(self.templates)
            
            stats["usage_statistics"]["avg_response_time"] = sum(
                t.avg_response_time for t in self.templates.values()
            ) / len(self.templates)
        
        # 排序模板
        sorted_templates = sorted(
            self.templates.values(),
            key=lambda t: t.usage_count,
            reverse=True
        )
        
        stats["top_used_templates"] = [
            {"id": t.id, "name": t.name, "usage_count": t.usage_count}
            for t in sorted_templates[:5]
        ]
        
        stats["least_used_templates"] = [
            {"id": t.id, "name": t.name, "usage_count": t.usage_count}
            for t in sorted_templates[-5:]
        ]
        
        return stats


# 全局提示词库实例
prompt_library = PromptLibrary()


def get_prompt_library() -> PromptLibrary:
    """获取提示词库实例"""
    return prompt_library


# 便捷函数
def get_prompt(template_id: str, **variables) -> Optional[str]:
    """获取格式化的提示词"""
    return prompt_library.format_template(template_id, **variables)


def evaluate_prompt(template_id: str) -> Dict[str, Any]:
    """评估提示词质量"""
    return prompt_library.evaluate_template_quality(template_id)


def test_prompt(template_id: str) -> Dict[str, Any]:
    """测试提示词"""
    return prompt_library.run_template_tests(template_id)


if __name__ == "__main__":
    # 示例用法
    library = get_prompt_library()
    
    print("=== 提示词库统计 ===")
    stats = library.get_usage_statistics()
    print(f"总模板数: {stats['total_templates']}")
    print(f"按类型分布: {stats['templates_by_type']}")
    
    print("\n=== 模板测试 ===")
    for template_id in library.templates.keys():
        print(f"\n测试模板: {template_id}")
        test_result = library.run_template_tests(template_id)
        print(f"成功率: {test_result.get('success_rate', 0):.2%}")
        
        quality = library.evaluate_template_quality(template_id)
        print(f"质量评分: {quality.get('overall_score', 0):.1f}/100")
        
        if quality.get('recommendations'):
            print("改进建议:", quality['recommendations'][:2])
    
    print("\n=== 导出模板 ===")
    library.export_templates("backend/app/config/prompt_templates_export.json")
    print("模板已导出到配置文件")