/* Enhanced styling for formatted AI messages */
.formatted-message {
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Improve emoji spacing and alignment */
.formatted-message h1,
.formatted-message h2,
.formatted-message h3 {
  line-height: 1.4;
}

/* Better spacing for sections */
.formatted-message h2 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.formatted-message h3 {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

/* Enhanced list styling */
.formatted-message .list-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  padding-left: 0.5rem;
}

.formatted-message .list-bullet {
  margin-right: 0.5rem;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

/* Cost highlighting */
.formatted-message .cost-highlight {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid #bbf7d0;
  padding: 0.125rem 0.375rem;
  border-radius: 0.375rem;
  font-weight: 600;
  color: #166534;
}

/* Key-value pair styling */
.formatted-message .key-value {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  padding: 0.25rem 0;
}

.formatted-message .key-value .key {
  min-width: 120px;
  font-weight: 500;
  color: #374151;
  flex-shrink: 0;
}

.formatted-message .key-value .value {
  color: #6b7280;
  flex: 1;
}

/* Warning and info boxes */
.formatted-message .warning-box {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  border: 1px solid #fbbf24;
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin: 0.75rem 0;
}

.formatted-message .info-box {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #60a5fa;
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin: 0.75rem 0;
}

/* Separator styling */
.formatted-message .separator {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, #e2e8f0 20%, #e2e8f0 80%, transparent 100%);
  margin: 1.5rem 0;
}

/* Signature styling */
.formatted-message .signature {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
  text-align: center;
  font-style: italic;
  color: #64748b;
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .formatted-message {
    font-size: 0.875rem;
  }
  
  .formatted-message h1 {
    font-size: 1.125rem;
  }
  
  .formatted-message h2 {
    font-size: 1rem;
  }
  
  .formatted-message h3 {
    font-size: 0.875rem;
  }
  
  .formatted-message .key-value .key {
    min-width: 100px;
  }
}
