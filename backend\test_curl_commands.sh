#!/bin/bash

# CellForge AI API 测试脚本
# 使用curl命令测试所有智能研究API端点

echo "==================================================="
echo "         CellForge AI API 测试套件"
echo "==================================================="
echo

# 设置基础URL
BASE_URL="http://localhost:8000/api"

echo "🚀 开始API测试..."
echo

# 1. 健康检查
echo "📋 测试 1: 健康检查"
echo "----------------------------------------"
curl -X GET "$BASE_URL/health" \
     -H "Content-Type: application/json" \
     -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
     -s
echo
echo

# 2. 意图分析 - PBMC样本
echo "📋 测试 2: 意图分析API (PBMC样本)"
echo "----------------------------------------"
curl -X POST "$BASE_URL/intent-analysis" \
     -H "Content-Type: application/json" \
     -d '{
       "user_input": "我需要进行人类PBMC单细胞RNA测序项目，主要目标是免疫细胞功能分析",
       "requirements": {
         "speciesType": "人类 (Homo sapiens)",
         "experimentType": "单细胞RNA测序 (scRNA-seq)",
         "researchGoal": "免疫细胞功能分析",
         "sampleType": "PBMC (外周血单核细胞)",
         "budget": "10-20万",
         "timeline": "3-6个月",
         "completeness": 95
       },
       "user_id": 1,
       "user_context": {
         "research_background": "免疫学研究",
         "technical_level": "中级"
       }
     }' \
     -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
     -s
echo
echo

# 3. 研究方向生成
echo "📋 测试 3: 研究方向生成API"
echo "----------------------------------------"
curl -X POST "$BASE_URL/research-directions" \
     -H "Content-Type: application/json" \
     -d '{
       "session_id": "session_pbmc_001",
       "user_id": 1,
       "intent_analysis": {
         "research_query": "PBMC免疫细胞功能分析相关的研究方向",
         "domain_focus": "单细胞RNA测序 (scRNA-seq)",
         "user_profile": {
           "expertise_level": "intermediate",
           "research_interests": ["免疫细胞功能分析"]
         }
       }
     }' \
     -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
     -s
echo
echo

# 4. 文献链接生成
echo "📋 测试 4: 文献链接生成API"
echo "----------------------------------------"
curl -X POST "$BASE_URL/literature-links" \
     -H "Content-Type: application/json" \
     -d '{
       "research_direction": {
         "research_topic": "免疫细胞功能分析",
         "experiment_type": "单细胞RNA测序 (scRNA-seq)",
         "species": "人类 (Homo sapiens)"
       },
       "intent_analysis": {
         "domain_focus": "单细胞RNA测序 (scRNA-seq)",
         "research_goals": ["免疫细胞功能分析"]
       },
       "user_profile": {
         "expertise_level": "intermediate",
         "preferences": {
           "primary_databases": ["pubmed", "google_scholar"],
           "focus_areas": ["methodology", "analysis"]
         }
       }
     }' \
     -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
     -s
echo
echo

# 5. 动态关键词生成
echo "📋 测试 5: 动态关键词生成API"
echo "----------------------------------------"
curl -X POST "$BASE_URL/dynamic-keywords" \
     -H "Content-Type: application/json" \
     -d '{
       "research_direction": {
         "main_topic": "免疫细胞功能分析",
         "experiment_type": "单细胞RNA测序 (scRNA-seq)",
         "species": "人类 (Homo sapiens)"
       },
       "intent_analysis": {
         "research_domain": "single_cell_genomics",
         "technical_focus": "细胞类型注释",
         "urgency_level": "较为紧急"
       },
       "user_context": {
         "expertise_level": "intermediate",
         "optimization_level": "enhanced"
       },
       "session_id": "session_pbmc_001"
     }' \
     -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
     -s
echo
echo

# 6. 综合解决方案生成 - PBMC样本
echo "📋 测试 6: 综合解决方案API (PBMC样本)"
echo "----------------------------------------"
curl -X POST "$BASE_URL/comprehensive-solution" \
     -H "Content-Type: application/json" \
     -d '{
       "user_input": "请为我的人类PBMC单细胞RNA测序项目生成综合解决方案",
       "requirements": {
         "speciesType": "人类 (Homo sapiens)",
         "experimentType": "单细胞RNA测序 (scRNA-seq)",
         "researchGoal": "免疫细胞功能分析",
         "sampleType": "PBMC (外周血单核细胞)",
         "budget": "10-20万",
         "timeline": "3-6个月",
         "completeness": 95
       },
       "user_id": 1,
       "user_context": {
         "framework_template": "standard",
         "enable_literature_search": false,
         "research_background": "免疫学研究",
         "technical_level": "中级"
       }
     }' \
     -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
     -s
echo
echo

echo "==================================================="
echo "         API测试完成"
echo "==================================================="
echo
echo "所有测试已完成！"
echo "请检查上述响应结果以验证API功能。"
echo