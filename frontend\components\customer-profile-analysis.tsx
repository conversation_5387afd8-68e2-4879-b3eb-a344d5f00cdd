"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import {
  Globe,
  FileText,
  Brain,
  Lightbulb,
  Target,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  User,
  Building,
  GraduationCap,
  DollarSign
} from "lucide-react"
import { customerProfileApi, type CustomerProfile, type ProfileInsight } from "@/lib/api"

interface AnalysisResult {
  analysis_id: string
  profile_data: Record<string, any>
  confidence_score: number
  key_insights: string[]
  recommendations: string[]
}

export function CustomerProfileAnalysis() {
  const [activeTab, setActiveTab] = useState("url")
  const [loading, setLoading] = useState(false)
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null)
  const [error, setError] = useState<string | null>(null)

  // 表单状态
  const [urlInput, setUrlInput] = useState("")
  const [textInput, setTextInput] = useState("")
  const [analysisType, setAnalysisType] = useState("comprehensive")

  const handleUrlAnalysis = async () => {
    if (!urlInput.trim()) {
      setError("请输入有效的URL")
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await customerProfileApi.analyzeWebProfile({
        url: urlInput,
        analysis_type: analysisType,
        focus_areas: ["research_background", "technical_expertise", "business_potential"]
      })
      setAnalysisResult(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : "分析失败，请重试")
    } finally {
      setLoading(false)
    }
  }

  const handleTextAnalysis = async () => {
    if (!textInput.trim()) {
      setError("请输入要分析的文本内容")
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await customerProfileApi.analyzeTextProfile({
        text_content: textInput,
        content_type: "profile_description",
        analysis_depth: analysisType
      })
      setAnalysisResult(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : "分析失败，请重试")
    } finally {
      setLoading(false)
    }
  }

  const renderAnalysisResult = () => {
    if (!analysisResult) return null

    const { profile_data = {}, confidence_score, key_insights = [], recommendations = [] } = analysisResult

    // 安全地获取画像数据，提供默认值
    const safeProfileData = {
      research_profile: profile_data?.research_profile || null,
      technical_profile: profile_data?.technical_profile || null,
      business_profile: profile_data?.business_profile || null,
      basic_info: profile_data?.basic_info || null,
      communication_strategy: profile_data?.communication_strategy || null,
      risk_assessment: profile_data?.risk_assessment || null
    }

    // 渲染画像维度的辅助函数
    const renderProfileSection = (title: string, icon: any, data: any, iconColor: string) => {
      if (!data || typeof data !== 'object') return null

      const entries = Object.entries(data).filter(([key, value]) =>
        value !== null && value !== undefined && value !== ''
      )

      if (entries.length === 0) return null

      return (
        <div>
          <h4 className="font-medium mb-3 flex items-center gap-2">
            {icon}
            {title}
          </h4>
          <div className="space-y-2">
            {entries.map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span className="text-sm text-slate-600 capitalize">
                  {key.replace(/_/g, ' ')}:
                </span>
                <Badge variant="outline">
                  {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                </Badge>
              </div>
            ))}
          </div>
        </div>
      )
    }

    return (
      <div className="space-y-6">
        {/* 分析概览 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-blue-600" />
              分析结果概览
            </CardTitle>
            <CardDescription>
              基于提供的信息生成的客户画像分析
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {Math.round((confidence_score || 0) * 100)}%
                </div>
                <p className="text-sm text-slate-500">分析置信度</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {key_insights.length}
                </div>
                <p className="text-sm text-slate-500">关键洞察</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {recommendations.length}
                </div>
                <p className="text-sm text-slate-500">行动建议</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 画像维度 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5 text-green-600" />
              客户画像维度
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 研究画像 */}
              {renderProfileSection(
                "研究画像",
                <GraduationCap className="h-4 w-4" />,
                safeProfileData.research_profile,
                "text-blue-600"
              )}

              {/* 技术画像 */}
              {renderProfileSection(
                "技术画像",
                <Brain className="h-4 w-4" />,
                safeProfileData.technical_profile,
                "text-purple-600"
              )}

              {/* 商业画像 */}
              {renderProfileSection(
                "商业画像",
                <DollarSign className="h-4 w-4" />,
                safeProfileData.business_profile,
                "text-green-600"
              )}

              {/* 基础信息 */}
              {renderProfileSection(
                "基础信息",
                <Building className="h-4 w-4" />,
                safeProfileData.basic_info,
                "text-slate-600"
              )}

              {/* 沟通策略 */}
              {renderProfileSection(
                "沟通策略",
                <Target className="h-4 w-4" />,
                safeProfileData.communication_strategy,
                "text-orange-600"
              )}

              {/* 风险评估 */}
              {renderProfileSection(
                "风险评估",
                <AlertCircle className="h-4 w-4" />,
                safeProfileData.risk_assessment,
                "text-red-600"
              )}
            </div>
          </CardContent>
        </Card>

        {/* 关键洞察 */}
        {key_insights.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-yellow-500" />
                关键洞察
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {key_insights.map((insight, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
                    <CheckCircle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                    <p className="text-sm text-slate-700">{insight}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 行动建议 */}
        {recommendations.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-green-500" />
                行动建议
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                    <TrendingUp className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <p className="text-sm text-slate-700">{recommendation}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-slate-900">客户画像分析</h1>
        <p className="text-slate-600 mt-1">
          通过网页内容或文本信息分析，生成详细的客户画像和商业洞察
        </p>
      </div>

      {/* 分析输入界面 */}
      <Card>
        <CardHeader>
          <CardTitle>分析数据源</CardTitle>
          <CardDescription>
            选择分析方式并提供相关信息
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="url" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                网页分析
              </TabsTrigger>
              <TabsTrigger value="text" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                文本分析
              </TabsTrigger>
            </TabsList>

            <TabsContent value="url" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="url">网页URL</Label>
                <Input
                  id="url"
                  placeholder="https://example.com/profile"
                  value={urlInput}
                  onChange={(e) => setUrlInput(e.target.value)}
                />
                <p className="text-xs text-slate-500">
                  支持个人主页、实验室页面、学术档案等
                </p>
              </div>

              {/* 增强搜索选项 */}
              <div className="space-y-3 p-3 bg-slate-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="enableKeywordSearch"
                    checked={analysisType === "enhanced"}
                    onChange={(e) => setAnalysisType(e.target.checked ? "enhanced" : "comprehensive")}
                    className="rounded"
                  />
                  <Label htmlFor="enableKeywordSearch" className="text-sm">
                    启用关键词增强搜索
                  </Label>
                </div>
                {analysisType === "enhanced" && (
                  <p className="text-xs text-slate-600">
                    将自动提取关键词并搜索相关信息，提供更全面的客户画像分析
                  </p>
                )}
              </div>

              <Button
                onClick={handleUrlAnalysis}
                disabled={loading || !urlInput.trim()}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    {analysisType === "enhanced" ? "深度分析中..." : "分析中..."}
                  </>
                ) : (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    {analysisType === "enhanced" ? "开始深度分析" : "开始分析"}
                  </>
                )}
              </Button>
            </TabsContent>

            <TabsContent value="text" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="text">文本内容</Label>
                <Textarea
                  id="text"
                  placeholder="请输入客户背景信息、简历内容或其他相关文本..."
                  value={textInput}
                  onChange={(e) => setTextInput(e.target.value)}
                  rows={6}
                />
                <p className="text-xs text-slate-500">
                  支持简历、项目描述、邮件内容等文本信息
                </p>
              </div>

              {/* 增强搜索选项 */}
              <div className="space-y-3 p-3 bg-slate-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="enableKeywordSearchText"
                    checked={analysisType === "enhanced"}
                    onChange={(e) => setAnalysisType(e.target.checked ? "enhanced" : "comprehensive")}
                    className="rounded"
                  />
                  <Label htmlFor="enableKeywordSearchText" className="text-sm">
                    启用关键词增强搜索
                  </Label>
                </div>
                {analysisType === "enhanced" && (
                  <p className="text-xs text-slate-600">
                    将自动提取关键词并搜索相关信息，提供更全面的客户画像分析
                  </p>
                )}
              </div>

              <Button
                onClick={handleTextAnalysis}
                disabled={loading || !textInput.trim()}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    {analysisType === "enhanced" ? "深度分析中..." : "分析中..."}
                  </>
                ) : (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    {analysisType === "enhanced" ? "开始深度分析" : "开始分析"}
                  </>
                )}
              </Button>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 分析结果 */}
      {renderAnalysisResult()}
    </div>
  )
}
