from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager
import uvicorn

from app.core.config import settings
from app.core.database import init_db, close_db
# 暂时使用简化路由器进行测试
# from app.api.router import api_router
from app.api.simple_router import simple_router as api_router
from app.middleware.logging import LoggingMiddleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时初始化数据库
    init_db()

    yield

    # 关闭时清理资源
    close_db()


# 创建 FastAPI 应用
app = FastAPI(
    title="CellForge AI Backend",
    description="单细胞测序方案咨询系统后端 API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS 中间件配置 - 必须在其他中间件之前
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发环境允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有头部
    expose_headers=[
        "X-Request-ID",
        "X-Process-Time"
    ]
)

# 信任主机中间件 - 开发环境暂时禁用
# app.add_middleware(
#     TrustedHostMiddleware,
#     allowed_hosts=[
#         "localhost",
#         "127.0.0.1",
#         "localhost:8000",
#         "127.0.0.1:8000",
#         "localhost:3000",
#         "127.0.0.1:3000",
#         "cellforge-api.com"
#     ]
# )

# 日志中间件
app.add_middleware(LoggingMiddleware)

# 引入 API 路由
try:
    app.include_router(api_router, prefix="/api")
    print("✅ API路由器加载成功")
except Exception as e:
    print(f"❌ API路由器加载失败: {e}")
    import traceback
    traceback.print_exc()

@app.get("/")
async def root():
    return {"message": "CellForge AI Backend API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "cellforge-ai-backend"}

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )