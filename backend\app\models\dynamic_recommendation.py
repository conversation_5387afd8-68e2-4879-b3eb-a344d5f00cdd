"""
动态文献推荐数据模型
基于AI驱动的个性化文献发现和推荐系统
"""
from sqlalchemy import Column, Integer, String, Text, Float, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Dict, List, Any, Optional

from app.core.database import Base


class DynamicLiteratureRecommendation(Base):
    """动态文献推荐记录 - AI生成的个性化文献推荐"""
    __tablename__ = "dynamic_literature_recommendations"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    session_id = Column(String(100), nullable=False)  # 关联研究意图会话
    recommendation_id = Column(String(50), unique=True, nullable=False)  # UUID
    
    # 推荐上下文
    research_context = Column(JSON, nullable=False)  # 研究背景上下文
    user_expertise_context = Column(JSON)  # 用户专业背景
    personalization_factors = Column(JSON)  # 个性化因素
    
    # AI推荐策略
    recommendation_strategy = Column(String(100))  # 推荐策略类型
    search_diversification = Column(Float)  # 搜索多样化程度
    relevance_threshold = Column(Float, default=0.7)  # 相关性阈值
    
    # 推荐结果
    recommended_papers = Column(JSON, nullable=False)  # 推荐的文献列表
    papers_by_category = Column(JSON)  # 按类别组织的文献
    priority_ranking = Column(JSON)  # 优先级排序
    
    # 推荐质量指标
    total_papers_found = Column(Integer)
    relevance_score_avg = Column(Float)  # 平均相关性评分
    diversity_score = Column(Float)  # 多样性评分
    novelty_score = Column(Float)  # 新颖性评分
    
    # 用户交互
    papers_viewed = Column(Integer, default=0)  # 查看的文献数
    papers_saved = Column(Integer, default=0)  # 保存的文献数
    user_rating = Column(Float)  # 用户评分
    user_feedback = Column(JSON)  # 用户详细反馈
    
    # 推荐效果
    recommendation_success = Column(Boolean)  # 推荐是否成功
    time_to_find_relevant = Column(Integer)  # 找到相关文献的时间(秒)
    follow_up_searches = Column(Integer, default=0)  # 后续搜索次数
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    paper_analytics = relationship("LiteraturePaperAnalytics", back_populates="recommendation")


class LiteraturePaperAnalytics(Base):
    """文献论文分析 - 每篇推荐文献的详细分析和用户交互"""
    __tablename__ = "literature_paper_analytics"
    
    id = Column(Integer, primary_key=True)
    recommendation_id = Column(Integer, ForeignKey("dynamic_literature_recommendations.id"), nullable=False)
    paper_id = Column(String(100), nullable=False)  # 文献唯一标识(DOI/PMID等)
    
    # 文献基本信息
    title = Column(Text, nullable=False)
    authors = Column(JSON)  # 作者列表
    journal = Column(String(200))
    publication_year = Column(Integer)
    doi = Column(String(200))
    pubmed_id = Column(String(50))
    abstract = Column(Text)
    
    # AI分析结果
    ai_relevance_analysis = Column(JSON, nullable=False)  # AI相关性分析
    methodology_relevance = Column(JSON)  # 方法学相关性
    research_value_assessment = Column(JSON)  # 研究价值评估
    risk_attention_points = Column(JSON)  # 风险和注意事项
    
    # 个性化匹配度
    user_match_score = Column(Float)  # 用户匹配度评分
    experience_level_match = Column(String(50))  # 经验水平匹配
    research_goal_alignment = Column(Float)  # 研究目标契合度
    technical_feasibility = Column(Float)  # 技术可行性
    
    # 文献质量指标
    citation_count = Column(Integer, default=0)
    impact_factor = Column(Float)  # 期刊影响因子
    open_access = Column(Boolean, default=False)
    quality_score = Column(Float)  # 综合质量评分
    
    # 搜索和访问信息
    source_database = Column(String(50))  # 来源数据库
    search_rank_position = Column(Integer)  # 搜索排名位置
    retrieval_confidence = Column(Float)  # 检索置信度
    
    # 用户交互数据
    is_viewed = Column(Boolean, default=False)
    view_timestamp = Column(DateTime(timezone=True))
    time_spent_reading = Column(Integer)  # 阅读时间(秒)
    is_saved = Column(Boolean, default=False)
    is_shared = Column(Boolean, default=False)
    user_rating = Column(Integer)  # 用户评分1-5
    user_notes = Column(Text)  # 用户笔记
    
    # 链接信息
    direct_links = Column(JSON)  # 直接访问链接
    search_links = Column(JSON)  # 搜索链接
    link_click_count = Column(Integer, default=0)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    recommendation = relationship("DynamicLiteratureRecommendation", back_populates="paper_analytics")


class PersonalizedSolutionFramework(Base):
    """个性化方案框架 - AI生成的完整研究解决方案"""
    __tablename__ = "personalized_solution_frameworks"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    session_id = Column(String(100), nullable=False)
    framework_id = Column(String(50), unique=True, nullable=False)
    
    # 方案基本信息
    framework_title = Column(String(300), nullable=False)
    research_objective = Column(Text)
    target_outcomes = Column(JSON)  # 预期成果
    
    # 个性化调整
    user_customization = Column(JSON, nullable=False)  # 用户定制化信息
    experience_level_adaptation = Column(JSON)  # 经验水平适配
    resource_constraint_adaptation = Column(JSON)  # 资源约束适配
    timeline_adaptation = Column(JSON)  # 时间线适配
    
    # 方案组件
    solution_overview = Column(JSON, nullable=False)  # 方案概览
    detailed_workflow = Column(JSON)  # 详细工作流程
    risk_mitigation_plan = Column(JSON)  # 风险缓解计划
    quality_assurance_plan = Column(JSON)  # 质量保证计划
    
    # 成本和时间分析
    cost_breakdown = Column(JSON)  # 成本分解
    timeline_milestones = Column(JSON)  # 时间线里程碑
    resource_allocation = Column(JSON)  # 资源分配
    contingency_plans = Column(JSON)  # 应急计划
    
    # 文献支持
    supporting_literature = Column(JSON)  # 支持文献
    methodology_references = Column(JSON)  # 方法学参考
    best_practices_guide = Column(JSON)  # 最佳实践指南
    
    # 智能建议
    optimization_suggestions = Column(JSON)  # 优化建议
    alternative_approaches = Column(JSON)  # 替代方案
    upgrade_pathways = Column(JSON)  # 升级路径
    
    # 用户交互和反馈
    user_modifications = Column(JSON)  # 用户修改记录
    implementation_feedback = Column(JSON)  # 实施反馈
    success_metrics = Column(JSON)  # 成功指标
    
    # 方案状态
    framework_status = Column(String(50), default="draft")  # draft, active, completed, archived
    completion_percentage = Column(Float, default=0.0)
    last_updated_component = Column(String(100))
    
    # 版本控制
    version_number = Column(String(20), default="1.0")
    previous_version_id = Column(String(50))  # 前一版本ID
    change_summary = Column(JSON)  # 变更摘要
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    implementation_logs = relationship("SolutionImplementationLog", back_populates="solution_framework")


class SolutionImplementationLog(Base):
    """方案实施日志 - 跟踪方案执行过程和效果"""
    __tablename__ = "solution_implementation_logs"
    
    id = Column(Integer, primary_key=True)
    framework_id = Column(Integer, ForeignKey("personalized_solution_frameworks.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    log_id = Column(String(50), nullable=False)
    
    # 实施阶段
    implementation_phase = Column(String(100))  # 实施阶段
    milestone_achieved = Column(String(200))  # 达成的里程碑
    phase_status = Column(String(50))  # 阶段状态
    
    # 进展记录
    progress_description = Column(Text)  # 进展描述
    achievements = Column(JSON)  # 成就列表
    challenges_encountered = Column(JSON)  # 遇到的挑战
    solutions_applied = Column(JSON)  # 应用的解决方案
    
    # 数据和结果
    experimental_data = Column(JSON)  # 实验数据摘要
    preliminary_results = Column(JSON)  # 初步结果
    quality_metrics = Column(JSON)  # 质量指标
    
    # 资源使用
    actual_cost = Column(Float)  # 实际成本
    time_spent = Column(Integer)  # 实际耗时(小时)
    resources_used = Column(JSON)  # 使用的资源
    
    # 调整和优化
    adjustments_made = Column(JSON)  # 做出的调整
    optimization_applied = Column(JSON)  # 应用的优化
    lessons_learned = Column(JSON)  # 经验教训
    
    # 下一步计划
    next_steps = Column(JSON)  # 下一步计划
    recommendations = Column(JSON)  # 建议
    support_needed = Column(JSON)  # 需要的支持
    
    # 记录信息
    log_type = Column(String(50))  # 日志类型: milestone, progress, issue, completion
    recorded_by = Column(String(100))  # 记录者
    verification_status = Column(String(50), default="pending")  # 验证状态
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联关系
    solution_framework = relationship("PersonalizedSolutionFramework", back_populates="implementation_logs")


class UserLearningProfile(Base):
    """用户学习档案 - 基于交互行为的个性化学习曲线"""
    __tablename__ = "user_learning_profiles"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, nullable=False)
    
    # 学习特征
    learning_style = Column(String(50))  # visual, analytical, hands-on, collaborative
    information_processing_preference = Column(String(50))  # sequential, holistic, mixed
    decision_making_pattern = Column(String(50))  # quick, deliberate, consultative
    
    # 专业发展轨迹
    skill_development_areas = Column(JSON)  # 技能发展领域
    knowledge_gaps_identified = Column(JSON)  # 识别的知识缺口
    learning_goals = Column(JSON)  # 学习目标
    progress_tracking = Column(JSON)  # 进展跟踪
    
    # 交互行为模式
    session_duration_preference = Column(Integer)  # 会话时长偏好(分钟)
    information_depth_preference = Column(String(50))  # overview, detailed, comprehensive
    help_seeking_behavior = Column(JSON)  # 求助行为模式
    
    # 反馈和改进
    feedback_frequency = Column(String(50))  # 反馈频率偏好
    suggestion_acceptance_rate = Column(Float)  # 建议接受率
    system_adaptation_speed = Column(Float)  # 系统适应速度
    
    # 成功指标
    project_completion_rate = Column(Float)  # 项目完成率
    solution_satisfaction_avg = Column(Float)  # 方案满意度平均值
    recommendation_follow_rate = Column(Float)  # 推荐采纳率
    
    # 个性化调整记录
    personalization_history = Column(JSON)  # 个性化历史
    effective_adaptations = Column(JSON)  # 有效的适配
    unsuccessful_attempts = Column(JSON)  # 不成功的尝试
    
    # 更新信息
    last_interaction_date = Column(DateTime(timezone=True))
    profile_confidence_score = Column(Float, default=0.5)
    update_frequency = Column(Integer, default=0)  # 更新频率
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())