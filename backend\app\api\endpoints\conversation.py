"""
对话相关API端点
"""
from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect, status
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime
import json
import logging

from app.core.database import get_db
from app.core.auth import get_current_active_user
from app.models.user import User
from app.models.conversation import Conversation, Message, ConversationType, MessageRole, ConversationStatus
from app.schemas.conversation import (
    ConversationRequest,
    AIResponse,
    ConversationResponse,
    ConversationWithMessages,
    MessageResponse,
    ConversationCreate,
    ConversationHistory
)
from app.schemas.common import BaseResponse, PaginatedResponse
from app.services.ai_service import AIService
from app.services.knowledge_service import KnowledgeService

router = APIRouter()
logger = logging.getLogger(__name__)

# AI服务实例
ai_service = AIService()
knowledge_service = KnowledgeService()


class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)


manager = ConnectionManager()


@router.post("/chat")
async def chat_with_ai(
    request: ConversationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    与 AI 进行单细胞测序方案咨询对话
    """
    try:
        # 获取相关知识库内容
        relevant_knowledge = await knowledge_service.search_knowledge(
            request.message,
            top_k=5
        )

        # 构建上下文
        context = {
            "user_profile": {
                "id": current_user.id,
                "organization": getattr(current_user, 'organization', ''),
                "expertise": getattr(current_user, 'expertise_areas', ''),
                "research_interests": getattr(current_user, 'research_interests', '')
            },
            "conversation_history": request.history or [],
            "relevant_knowledge": relevant_knowledge
        }

        # 如果请求中包含额外的上下文信息，合并到context中
        if request.context:
            context.update(request.context)

        # 检查是否启用文献搜索
        enable_literature_search = request.context.get("enable_literature_search", False) if request.context else False

        # 生成 AI 回复
        ai_response = await ai_service.generate_response(
            message=request.message,
            context=context,
            conversation_type=request.conversation_type,
            enable_literature_search=enable_literature_search
        )

        # 暂时不保存到数据库，直接返回响应
        return {
            "message": ai_response.content,
            "confidence": ai_response.confidence,
            "sources": ai_response.sources or [],
            "suggestions": ai_response.suggestions or [],
            "timestamp": datetime.utcnow().isoformat(),
            "conversation_id": 1  # 临时ID
        }

    except Exception as e:
        logger.error(f"对话处理失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"对话处理失败: {str(e)}"
        )


@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: int):
    """
    WebSocket 实时对话端点
    """
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)

            # 处理消息
            ai_service = AIService()
            response = await ai_service.generate_streaming_response(
                message_data["message"],
                context=message_data.get("context", {})
            )

            # 流式发送响应
            async for chunk in response:
                await manager.send_personal_message(
                    json.dumps({"type": "chunk", "content": chunk}),
                    websocket
                )

            # 发送完成信号
            await manager.send_personal_message(
                json.dumps({"type": "complete"}),
                websocket
            )

    except WebSocketDisconnect:
        manager.disconnect(websocket)


@router.get("/history")
async def get_conversation_history(
    page: int = 1,
    size: int = 20,
    current_user: User = Depends(get_current_active_user)
):
    """
    获取用户的对话历史
    """
    try:
        # 暂时返回空的对话历史
        return {
            "conversations": [],
            "total": 0,
            "page": page,
            "size": size,
            "pages": 0
        }

    except Exception as e:
        logger.error(f"获取对话历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取对话历史失败"
        )


@router.delete("/history/{conversation_id}")
async def delete_conversation(
    conversation_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """
    删除特定对话记录
    """
    try:
        # 暂时只返回成功消息
        return {"message": "对话记录已删除"}

    except Exception as e:
        logger.error(f"删除对话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除对话失败"
        )