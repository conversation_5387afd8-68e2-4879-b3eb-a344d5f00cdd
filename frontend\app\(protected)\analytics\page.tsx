"use client"

import { useAuth } from "@/contexts/auth-context"
import { AnalyticsDashboard } from "@/components/analytics-dashboard"
import { AccessDenied } from "@/components/access-denied"

export default function AnalyticsPage() {
  const { hasPermission } = useAuth()

  return (
    <div className="min-h-[calc(100vh-64px)]">
      {hasPermission("view_dashboard") ? <AnalyticsDashboard /> : <AccessDenied />}
    </div>
  )
}
