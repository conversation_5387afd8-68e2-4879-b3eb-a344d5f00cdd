'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { AlertCircle, CheckCircle, Loader2, ArrowRight, Sparkles } from 'lucide-react'
import ComprehensiveRecommendationPlatform from '@/components/comprehensive-recommendation-platform'
import { intelligentRecommendationApi } from '@/lib/api'

interface ProgressStep {
  id: string
  title: string
  description: string
  status: 'pending' | 'loading' | 'completed' | 'error'
  progress: number
}

const DEMO_REQUIREMENTS = {
  speciesType: "大鼠 (Rattus norvegicus)",
  experimentType: "单细胞RNA测序 (scRNA-seq)",
  researchGoal: "发育轨迹分析",
  sampleType: "心脏组织",
  sampleCount: "4-5个样本",
  sampleStatus: "新鲜样本",
  processingMethod: "酶解离",
  cellCount: "5,000-10,000",
  cellViability: "> 90%",
  budgetRange: "10-20万",
  projectDuration: "3-6个月", 
  urgency: "非常紧急（加急处理）",
  sequencingDepth: "50,000 reads/cell",
  analysisType: "细胞类型注释",
  dataAnalysisNeeds: "基础分析报告",
  cellSorting: "需要细胞分选"
}

export default function IntelligentRecommendationPage() {
  const [currentStep, setCurrentStep] = useState(0)
  const [steps, setSteps] = useState<ProgressStep[]>([
    {
      id: 'analysis',
      title: '需求分析',
      description: '分析项目需求和参数',
      status: 'pending',
      progress: 0
    },
    {
      id: 'query_generation',
      title: '智能查询生成',
      description: '生成优化的文献搜索查询',
      status: 'pending', 
      progress: 0
    },
    {
      id: 'literature_search',
      title: '文献搜索',
      description: '搜索相关文献和热点论文',
      status: 'pending',
      progress: 0
    },
    {
      id: 'keyword_expansion',
      title: '关键词扩展',
      description: '生成语义和跨学科关键词',
      status: 'pending',
      progress: 0
    },
    {
      id: 'tech_recommendation',
      title: '技术方案推荐',
      description: '分析技术平台和成本',
      status: 'pending',
      progress: 0
    },
    {
      id: 'project_planning',
      title: '项目规划',
      description: '生成时间线和风险评估',
      status: 'pending',
      progress: 0
    },
    {
      id: 'report_generation',
      title: '报告生成',
      description: '生成综合推荐报告',
      status: 'pending',
      progress: 0
    }
  ])
  
  const [showResults, setShowResults] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  const updateStepStatus = (stepIndex: number, status: ProgressStep['status'], progress: number = 100) => {
    setSteps(prev => prev.map((step, index) => 
      index === stepIndex ? { ...step, status, progress } : step
    ))
  }

  const simulateProcessing = async () => {
    setIsProcessing(true)
    
    try {
      // 模拟各个步骤的处理过程
      for (let i = 0; i < steps.length; i++) {
        setCurrentStep(i)
        updateStepStatus(i, 'loading', 0)
        
        // 模拟进度更新
        for (let progress = 0; progress <= 100; progress += 20) {
          updateStepStatus(i, 'loading', progress)
          await new Promise(resolve => setTimeout(resolve, 200))
        }
        
        updateStepStatus(i, 'completed', 100)
        await new Promise(resolve => setTimeout(resolve, 300))
      }
      
      // 预加载推荐数据（可选）
      try {
        await intelligentRecommendationApi.healthCheck()
      } catch (error) {
        console.warn('API健康检查失败，将使用模拟数据:', error)
      }
      
      setIsProcessing(false)
      setShowResults(true)
    } catch (error) {
      console.error('处理过程出错:', error)
      setIsProcessing(false)
      // 即使出错也显示结果，让用户能看到推荐
      setShowResults(true)
    }
  }

  const resetProcess = () => {
    setSteps(prev => prev.map(step => ({ ...step, status: 'pending', progress: 0 })))
    setCurrentStep(0)
    setShowResults(false)
    setIsProcessing(false)
  }

  const getStepIcon = (step: ProgressStep) => {
    switch (step.status) {
      case 'loading':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <div className="w-4 h-4 rounded-full border-2 border-gray-300" />
    }
  }

  if (showResults) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Button 
            variant="outline" 
            onClick={resetProcess}
            className="mb-4"
          >
            ← 返回处理流程
          </Button>
        </div>
        <ComprehensiveRecommendationPlatform 
          requirementData={DEMO_REQUIREMENTS}
        />
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="h-8 w-8 text-blue-500" />
            <h1 className="text-3xl font-bold">CellForge AI 智能推荐引擎</h1>
          </div>
          <p className="text-xl text-gray-600 mb-6">
            基于您的项目需求，生成个性化的技术方案与文献推荐
          </p>
          
          {/* 需求概览 */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                项目需求概览
              </CardTitle>
              <CardDescription>基于您提供的需求信息</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="font-semibold text-blue-600">
                    {DEMO_REQUIREMENTS.speciesType}
                  </div>
                  <div className="text-sm text-gray-600">研究物种</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="font-semibold text-green-600">
                    {DEMO_REQUIREMENTS.researchGoal}
                  </div>
                  <div className="text-sm text-gray-600">研究目标</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="font-semibold text-purple-600">
                    {DEMO_REQUIREMENTS.budgetRange}
                  </div>
                  <div className="text-sm text-gray-600">预算范围</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 处理流程 */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>智能分析处理流程</CardTitle>
            <CardDescription>
              AI正在基于您的需求生成个性化推荐方案
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center gap-4">
                  <div className="flex-shrink-0">
                    {getStepIcon(step)}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex justify-between items-center mb-1">
                      <h3 className={`font-medium ${
                        step.status === 'completed' ? 'text-green-600' :
                        step.status === 'loading' ? 'text-blue-600' :
                        'text-gray-600'
                      }`}>
                        {index + 1}. {step.title}
                      </h3>
                      <Badge variant={
                        step.status === 'completed' ? 'default' :
                        step.status === 'loading' ? 'secondary' :
                        'outline'
                      }>
                        {step.status === 'completed' ? '已完成' :
                         step.status === 'loading' ? '处理中' :
                         '等待中'}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{step.description}</p>
                    {step.status === 'loading' && (
                      <Progress value={step.progress} className="h-2" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 操作按钮 */}
        <div className="text-center">
          {!isProcessing ? (
            <Button 
              onClick={simulateProcessing}
              size="lg"
              className="px-8"
            >
              <Sparkles className="h-5 w-5 mr-2" />
              开始智能分析
              <ArrowRight className="h-5 w-5 ml-2" />
            </Button>
          ) : (
            <div className="flex items-center justify-center Gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span className="ml-2">正在生成智能推荐...</span>
            </div>
          )}
        </div>

        {/* 处理时间估算 */}
        <Card className="mt-8">
          <CardContent className="pt-6">
            <div className="text-center text-sm text-gray-600">
              <p className="mb-2">预计处理时间: 30-60秒</p>
              <p>我们正在整合多个数据源，包括：</p>
              <div className="flex justify-center gap-4 mt-2">
                <Badge variant="outline">PubMed</Badge>
                <Badge variant="outline">Semantic Scholar</Badge>
                <Badge variant="outline">专业知识库</Badge>
                <Badge variant="outline">技术规格数据库</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}