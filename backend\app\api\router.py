"""
API路由汇总
"""
from fastapi import APIRouter

# 创建主路由器
api_router = APIRouter()

# 先只加载智能研究API，其他的暂时注释掉用于调试
try:
    from app.api.endpoints import intelligent_research_api
    api_router.include_router(
        intelligent_research_api.router,
        prefix="/intelligent-research",
        tags=["intelligent_research"]
    )
    print("✅ 智能研究API路由加载成功")
except Exception as e:
    print(f"❌ 智能研究API路由加载失败: {e}")

# 暂时注释掉其他路由进行调试
# try:
#     from app.api.endpoints import auth
#     api_router.include_router(
#         auth.router,
#         prefix="/auth",
#         tags=["authentication"]
#     )
# except Exception as e:
#     print(f"❌ Auth路由加载失败: {e}")

# try:
#     from app.api.endpoints import conversation
#     api_router.include_router(
#         conversation.router,
#         prefix="/conversation",
#         tags=["conversation"]
#     )
# except Exception as e:
#     print(f"❌ Conversation路由加载失败: {e}")

# 健康检查端点
@api_router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "cellforge-ai-backend",
        "version": "2.0.0",
        "core_services": [
            "intelligent_research_advisor",
            "smart_literature_linker", 
            "dynamic_keyword_generator",
            "personalized_solution_generator"
        ]
    }
