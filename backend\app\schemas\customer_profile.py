"""
客户画像相关Pydantic模式
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum


class ProfileDimensionType(str, Enum):
    """画像维度类型"""
    RESEARCH_PROFILE = "research_profile"
    TECHNICAL_PROFILE = "technical_profile"
    BUSINESS_PROFILE = "business_profile"
    BEHAVIORAL_PROFILE = "behavioral_profile"
    PREFERENCE_PROFILE = "preference_profile"


class BehaviorEventType(str, Enum):
    """行为事件类型"""
    PAGE_VIEW = "page_view"
    FEATURE_USE = "feature_use"
    CONVERSATION_START = "conversation_start"
    REQUIREMENT_SUBMIT = "requirement_submit"
    LITERATURE_ACCESS = "literature_access"
    DOWNLOAD_RESOURCE = "download_resource"
    FEEDBACK_SUBMIT = "feedback_submit"


class ProfileDimensionBase(BaseModel):
    """画像维度基础模式"""
    dimension_type: ProfileDimensionType
    dimension_name: str = Field(..., max_length=100)
    string_value: Optional[str] = Field(None, max_length=500)
    numeric_value: Optional[float] = None
    json_value: Optional[Dict[str, Any]] = None
    boolean_value: Optional[bool] = None
    confidence: float = Field(0.0, ge=0.0, le=1.0)
    source: Optional[str] = Field(None, max_length=100)


class ProfileDimensionCreate(ProfileDimensionBase):
    """画像维度创建模式"""
    pass


class ProfileDimensionResponse(ProfileDimensionBase):
    """画像维度响应模式"""
    id: int
    profile_id: int
    last_updated: datetime

    class Config:
        from_attributes = True


class BehaviorEventBase(BaseModel):
    """行为事件基础模式"""
    event_type: BehaviorEventType
    event_name: str = Field(..., max_length=100)
    event_data: Optional[Dict[str, Any]] = None
    session_id: Optional[str] = Field(None, max_length=100)
    page_url: Optional[str] = Field(None, max_length=500)
    session_duration: Optional[int] = None


class BehaviorEventCreate(BehaviorEventBase):
    """行为事件创建模式"""
    user_agent: Optional[str] = Field(None, max_length=500)
    ip_address: Optional[str] = Field(None, max_length=50)


class BehaviorEventResponse(BehaviorEventBase):
    """行为事件响应模式"""
    id: int
    profile_id: int
    user_id: int
    event_timestamp: datetime

    class Config:
        from_attributes = True


class RequirementHistoryBase(BaseModel):
    """需求历史基础模式"""
    requirement_data: Dict[str, Any]
    requirement_type: Optional[str] = Field(None, max_length=50)
    completeness_score: float = Field(0.0, ge=0.0, le=1.0)


class RequirementHistoryCreate(RequirementHistoryBase):
    """需求历史创建模式"""
    extracted_insights: Optional[Dict[str, Any]] = None
    recommended_solutions: Optional[Dict[str, Any]] = None
    estimated_budget: Optional[float] = None
    complexity_score: float = Field(0.0, ge=0.0, le=1.0)


class RequirementHistoryResponse(RequirementHistoryBase):
    """需求历史响应模式"""
    id: int
    profile_id: int
    user_id: int
    extracted_insights: Optional[Dict[str, Any]] = None
    recommended_solutions: Optional[Dict[str, Any]] = None
    estimated_budget: Optional[float] = None
    complexity_score: float
    submitted_at: datetime

    class Config:
        from_attributes = True


class CustomerProfileBase(BaseModel):
    """客户画像基础模式"""
    profile_version: str = Field("1.0", max_length=20)
    confidence_score: float = Field(0.0, ge=0.0, le=1.0)
    completeness_score: float = Field(0.0, ge=0.0, le=1.0)
    
    # 研究画像
    research_maturity: Optional[str] = Field(None, max_length=50)
    research_focus: Optional[Dict[str, Any]] = None
    publication_level: Optional[str] = Field(None, max_length=50)
    collaboration_preference: Optional[str] = Field(None, max_length=50)
    
    # 技术画像
    technical_expertise: Optional[Dict[str, Any]] = None
    platform_preference: Optional[Dict[str, Any]] = None
    analysis_complexity: Optional[str] = Field(None, max_length=50)
    data_handling_capability: Optional[str] = Field(None, max_length=50)
    
    # 商业画像
    budget_range: Optional[str] = Field(None, max_length=50)
    decision_authority: Optional[str] = Field(None, max_length=50)
    procurement_cycle: Optional[str] = Field(None, max_length=50)
    cost_sensitivity: float = Field(0.5, ge=0.0, le=1.0)
    
    # 行为画像
    engagement_level: Optional[str] = Field(None, max_length=50)
    learning_style: Optional[str] = Field(None, max_length=50)
    communication_preference: Optional[str] = Field(None, max_length=50)
    response_speed_preference: Optional[str] = Field(None, max_length=50)
    
    # 偏好画像
    content_preference: Optional[Dict[str, Any]] = None
    interaction_style: Optional[str] = Field(None, max_length=50)
    information_depth: Optional[str] = Field(None, max_length=50)
    
    # 预测性指标
    conversion_probability: float = Field(0.0, ge=0.0, le=1.0)
    lifetime_value_prediction: float = Field(0.0, ge=0.0)
    churn_risk_score: float = Field(0.0, ge=0.0, le=1.0)
    next_best_action: Optional[Dict[str, Any]] = None


class CustomerProfileCreate(CustomerProfileBase):
    """客户画像创建模式"""
    user_id: int


class CustomerProfileUpdate(BaseModel):
    """客户画像更新模式"""
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    completeness_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    research_maturity: Optional[str] = Field(None, max_length=50)
    research_focus: Optional[Dict[str, Any]] = None
    technical_expertise: Optional[Dict[str, Any]] = None
    platform_preference: Optional[Dict[str, Any]] = None
    budget_range: Optional[str] = Field(None, max_length=50)
    engagement_level: Optional[str] = Field(None, max_length=50)
    next_best_action: Optional[Dict[str, Any]] = None


class CustomerProfileResponse(CustomerProfileBase):
    """客户画像响应模式"""
    id: int
    user_id: int
    last_updated: datetime
    created_at: datetime

    class Config:
        from_attributes = True


class CustomerProfileWithDetails(CustomerProfileResponse):
    """包含详细信息的客户画像响应"""
    dimensions: List[ProfileDimensionResponse] = []
    recent_behaviors: List[BehaviorEventResponse] = []
    requirement_history: List[RequirementHistoryResponse] = []


class ProfileAnalysisRequest(BaseModel):
    """画像分析请求"""
    user_id: int
    analysis_type: str = Field(..., description="分析类型：full/incremental/specific")
    specific_dimensions: Optional[List[ProfileDimensionType]] = None
    include_predictions: bool = True


class ProfileAnalysisResponse(BaseModel):
    """画像分析响应"""
    user_id: int
    analysis_timestamp: datetime
    profile_summary: Dict[str, Any]
    key_insights: List[str]
    recommendations: List[str]
    confidence_scores: Dict[str, float]
    next_actions: List[Dict[str, Any]]


class ProfileInsight(BaseModel):
    """画像洞察"""
    insight_type: str
    title: str
    description: str
    confidence: float = Field(..., ge=0.0, le=1.0)
    supporting_data: Dict[str, Any]
    actionable_recommendations: List[str]


class CustomerSegmentResponse(BaseModel):
    """客户分群响应"""
    segment_id: str
    segment_name: str
    description: str
    user_count: int
    key_characteristics: List[str]
    recommended_strategies: List[str]
