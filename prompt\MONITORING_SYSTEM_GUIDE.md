# CellForge AI 性能监控系统

## 系统概述

CellForge AI 性能监控系统是一个完整的应用程序监控解决方案，提供实时性能监控、健康检查、告警机制和业务指标分析。

## 🏗️ 系统架构

### 核心组件

1. **指标收集器 (MetricsCollector)**
   - 自动收集API性能指标
   - 监控系统资源使用情况
   - 记录业务指标数据
   - 数据持久化到SQLite数据库

2. **性能分析器 (PerformanceAnalyzer)**
   - 分析系统性能趋势
   - 识别性能瓶颈和异常
   - 生成性能报告和建议
   - 计算整体性能得分

3. **健康检查器 (HealthChecker)**
   - 监控系统组件健康状态
   - 检查数据库连接
   - 验证外部API可用性
   - 计算服务可用性百分比

4. **监控中间件**
   - 自动拦截所有API请求
   - 记录响应时间和状态码
   - 追踪错误模式
   - 监控缓存命中率

## 📊 监控指标

### API性能指标
- **响应时间**: 平均响应时间、P95/P99百分位数
- **请求频次**: 每个端点的请求数量统计
- **错误率**: HTTP错误状态码比例
- **并发性能**: 请求处理能力分析

### 系统资源指标
- **CPU使用率**: 实时和历史CPU使用情况
- **内存使用率**: 内存消耗监控和趋势分析
- **磁盘使用率**: 存储空间使用情况
- **网络IO**: 网络字节传输统计

### 业务指标
- **文献搜索成功率**: 文献检索服务质量
- **AI服务响应质量**: 基于响应时间的质量评分
- **用户满意度指标**: 基于性能推断的满意度
- **缓存命中率**: 缓存系统效率指标

## 🚀 快速开始

### 1. 启动监控系统

监控系统随应用自动启动，无需额外配置：

```bash
cd backend
python -m uvicorn app.main:app --reload
```

### 2. 访问监控端点

所有监控API都在 `/api/v1/api/monitoring/` 路径下：

```bash
# 健康检查
curl http://localhost:8000/api/v1/api/monitoring/health

# 性能指标
curl http://localhost:8000/api/v1/api/monitoring/metrics

# 性能分析报告
curl http://localhost:8000/api/v1/api/monitoring/performance

# 告警信息
curl http://localhost:8000/api/v1/api/monitoring/alerts
```

### 3. 运行测试脚本

```bash
cd backend
python app/monitoring/demo.py
```

## 🔧 API端点说明

### 健康检查端点

#### `GET /api/monitoring/health`
获取系统整体健康状态

**响应示例:**
```json
{
  "status": "success",
  "data": {
    "summary": {
      "overall_status": "healthy",
      "total_services": 3,
      "healthy_services": 3,
      "degraded_services": 0,
      "unhealthy_services": 0,
      "average_response_time": 245.67,
      "uptime_percentage": 99.85
    },
    "services": {
      "database": {
        "status": "healthy",
        "uptime_percentage": 100.0,
        "last_check": "2024-01-20T10:30:00",
        "response_time": 12.5,
        "issues": null
      }
    }
  }
}
```

#### `GET /api/monitoring/health/{component}`
获取特定组件的健康状态

**参数:**
- `component`: 组件名称 (database, openai_api, openalex_api等)

### 性能监控端点

#### `GET /api/monitoring/metrics`
获取系统性能指标概览

**查询参数:**
- `time_window`: 时间窗口（秒，默认3600）

**响应示例:**
```json
{
  "status": "success",
  "data": {
    "summary": {
      "total_requests": 1250,
      "avg_response_time": 345.67
    },
    "api_metrics": {
      "POST:/api/v1/conversation": {
        "request_count": 150,
        "error_count": 2,
        "error_rate": 0.013,
        "avg_response_time": 1250.5,
        "p95_response_time": 2100.0
      }
    },
    "system_metrics": {
      "cpu_usage_percent": {
        "current": 45.2,
        "average": 42.8,
        "trend": "stable"
      }
    }
  }
}
```

#### `GET /api/monitoring/performance`
获取性能分析报告

**查询参数:**
- `time_window`: 分析时间窗口（秒，默认3600）

**响应示例:**
```json
{
  "status": "success", 
  "data": {
    "overall_score": 85.5,
    "issues": [
      {
        "issue_type": "elevated_response_time",
        "severity": "warning",
        "description": "API响应时间偏高 (1250.50ms)",
        "recommendations": [
          "优化数据库查询",
          "增加缓存层"
        ]
      }
    ],
    "recommendations": [
      "优化API响应时间是当前优先级"
    ]
  }
}
```

### 告警端点

#### `GET /api/monitoring/alerts`
获取当前系统告警信息

**响应示例:**
```json
{
  "status": "success",
  "data": {
    "total_alerts": 3,
    "critical_alerts": 0,
    "warning_alerts": 3,
    "alerts": [
      {
        "type": "performance_alert",
        "severity": "warning",
        "component": "api_response_time",
        "message": "API响应时间偏高 (1250.50ms)",
        "timestamp": **********
      }
    ]
  }
}
```

### 管理端点

#### `POST /api/monitoring/collect/start`
启动指标收集器

#### `POST /api/monitoring/collect/stop`  
停止指标收集器

#### `POST /api/monitoring/health/start`
启动健康检查器

#### `POST /api/monitoring/reset`
重置监控数据（清除缓存）

## ⚙️ 配置说明

### 监控配置 (`monitoring_config.json`)

```json
{
  "monitoring": {
    "enabled": true,
    "collection_interval": 60,
    "retention_days": 30,
    "metrics": {
      "api_performance": {
        "enabled": true,
        "track_response_time": true,
        "percentiles": [50, 90, 95, 99]
      }
    },
    "health_checks": {
      "enabled": true,
      "check_interval": 30,
      "endpoints": [
        {
          "name": "database",
          "type": "database_connection",
          "timeout": 5
        }
      ]
    }
  }
}
```

### 告警规则 (`alerting_rules.json`)

```json
{
  "alerting_rules": {
    "performance_alerts": [
      {
        "name": "high_response_time",
        "metric": "api_response_time", 
        "condition": "avg_over_time(5m) > 5000",
        "severity": "warning",
        "actions": ["log", "console"]
      }
    ]
  }
}
```

## 🎯 性能阈值

### 默认阈值设置

| 指标 | 警告阈值 | 严重阈值 |
|------|----------|----------|
| API响应时间 | 2000ms | 5000ms |
| API错误率 | 5% | 10% |
| CPU使用率 | 70% | 90% |
| 内存使用率 | 80% | 95% |
| 磁盘使用率 | 85% | 95% |
| 文献搜索成功率 | 80% | 60% |

## 📈 数据存储

### 数据库表结构

**metrics_api** - API指标
```sql
CREATE TABLE metrics_api (
    id INTEGER PRIMARY KEY,
    timestamp REAL,
    endpoint TEXT,
    method TEXT,
    response_time REAL,
    status_code INTEGER,
    user_id TEXT
);
```

**metrics_business** - 业务指标
```sql
CREATE TABLE metrics_business (
    id INTEGER PRIMARY KEY,
    timestamp REAL,
    metric_type TEXT,
    value REAL,
    context TEXT
);
```

**health_checks** - 健康检查记录
```sql
CREATE TABLE health_checks (
    id INTEGER PRIMARY KEY,
    component TEXT,
    status TEXT,
    response_time REAL,
    timestamp REAL,
    message TEXT
);
```

## 🚨 告警机制

### 告警类型

1. **性能告警**
   - 响应时间超过阈值
   - 错误率过高
   - 系统资源使用异常

2. **业务告警**
   - 文献搜索成功率下降
   - AI服务质量降级
   - 缓存命中率过低

3. **健康告警**
   - 服务组件不可用
   - 数据库连接失败
   - 外部API异常

### 告警处理

- **控制台输出**: 实时显示告警信息
- **日志记录**: 持久化告警历史
- **防重复机制**: 5分钟内相同告警不重复发送

## 🔍 故障排查

### 常见问题

1. **监控数据不更新**
   - 检查指标收集器是否运行: `GET /api/monitoring/status`
   - 重启收集器: `POST /api/monitoring/collect/start`

2. **健康检查失败**
   - 检查数据库连接
   - 验证外部API访问权限
   - 查看健康检查日志

3. **性能分析无数据**
   - 确保有足够的请求流量
   - 检查时间窗口设置
   - 清除缓存重新分析

### 调试模式

启用详细日志记录:
```python
import logging
logging.getLogger('app.monitoring').setLevel(logging.DEBUG)
```

## 🚀 扩展建议

### 未来增强功能

1. **可视化仪表板**
   - 集成Grafana或自定义Web界面
   - 实时图表和趋势分析

2. **高级告警**
   - 邮件/短信通知
   - 钉钉/企业微信集成
   - 告警升级策略

3. **分布式监控**
   - 多实例监控支持
   - 服务发现机制
   - 集群健康管理

4. **机器学习**
   - 异常检测算法
   - 预测性维护
   - 智能告警过滤

## 📞 技术支持

如有问题或建议，请通过以下方式联系：

- 查看详细日志文件
- 检查API文档: http://localhost:8000/docs
- 运行诊断脚本: `python app/monitoring/demo.py`

---

*CellForge AI 监控系统 v1.0 - 为您的单细胞测序应用提供全面的性能监控和健康管理*