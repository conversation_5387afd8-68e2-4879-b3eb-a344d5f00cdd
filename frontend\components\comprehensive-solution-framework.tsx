"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import {
  Microscope,
  BookOpen,
  Target,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  Search,
  Zap,
  Star,
  ChevronDown,
  ChevronUp,
  Calendar,
  DollarSign,
  Users,
  TrendingUp,
  Shield,
  ArrowRight,
  Award,
  Lightbulb,
  BarChart3
} from 'lucide-react'

interface ComprehensiveSolutionFrameworkProps {
  frameworkData: any
  onSearchClick?: (platform: string, url: string) => void
  onOptimize?: (feedback: any) => void
}

export function ComprehensiveSolutionFramework({
  frameworkData,
  onSearchClick,
  onOptimize
}: ComprehensiveSolutionFrameworkProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [expandedSections, setExpandedSections] = useState<string[]>(['overview'])

  const toggleSection = (section: string) => {
    setExpandedSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    )
  }

  if (!frameworkData) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Microscope className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">加载方案框架中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* 框架标题和状态 */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-100 rounded-xl p-6 border border-blue-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
              <Microscope className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-slate-800">
                🧬 综合单细胞测序解决方案
              </h1>
              <p className="text-slate-600 mt-1">
                {frameworkData.template_used} - {frameworkData.complexity_level}复杂度
              </p>
            </div>
          </div>
          <div className="text-right">
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              ✅ 已生成
            </Badge>
            <p className="text-sm text-slate-500 mt-1">
              {new Date(frameworkData.generated_at).toLocaleString('zh-CN')}
            </p>
          </div>
        </div>
      </div>

      {/* 主要内容选项卡（按用户要求的新结构） */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">方案概览</TabsTrigger>
          <TabsTrigger value="risk">风险评估</TabsTrigger>
          <TabsTrigger value="research">研究建议</TabsTrigger>
          <TabsTrigger value="literature">文献推荐</TabsTrigger>
        </TabsList>

        {/* 方案概览选项卡 */}
        <TabsContent value="overview" className="space-y-6">
          <SolutionOverviewCard
            overview={frameworkData.solution_overview}
            researchIntent={frameworkData.research_intent_analysis?.intent_analysis}
          />
        </TabsContent>

        {/* 风险评估选项卡（调整为积极的、促成交易的风险分析） */}
        <TabsContent value="risk" className="space-y-6">
          {frameworkData.risk_assessment && (
            <PositiveRiskAssessmentCard assessment={frameworkData.risk_assessment} />
          )}
        </TabsContent>

        {/* 研究建议选项卡（新增模块） */}
        <TabsContent value="research" className="space-y-6">
          {frameworkData.research_recommendations && (
            <ResearchRecommendationsCard recommendations={frameworkData.research_recommendations} />
          )}
        </TabsContent>

        {/* 文献推荐选项卡（合并精准搜索和文献推荐功能） */}
        <TabsContent value="literature" className="space-y-6">
          {frameworkData.literature_recommendations ? (
            <EnhancedLiteraturePanel
              recommendations={frameworkData.literature_recommendations}
              onSearchClick={onSearchClick}
            />
          ) : (
            <div className="text-center py-8">
              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">文献推荐功能未启用</p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* 用户操作区域 */}
      <div className="bg-gray-50 rounded-xl p-6 border">
        <h3 className="text-lg font-semibold mb-4">下一步操作</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button 
            onClick={() => onSearchClick?.('all', '')}
            className="flex items-center gap-2"
          >
            <Search className="h-4 w-4" />
            开始文献搜索
          </Button>
          <Button 
            variant="outline"
            onClick={() => onOptimize?.({})}
            className="flex items-center gap-2"
          >
            <TrendingUp className="h-4 w-4" />
            优化方案
          </Button>
          <Button 
            variant="outline"
            className="flex items-center gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>
    </div>
  )
}

// 方案概览卡片组件
function SolutionOverviewCard({ overview, researchIntent }: { overview: any, researchIntent: any }) {
  if (!overview) return null

  const getRiskColor = (level: string) => {
    const colors = {
      low: 'text-green-600 bg-green-50 border-green-200',
      medium: 'text-yellow-600 bg-yellow-50 border-yellow-200',
      high: 'text-red-600 bg-red-50 border-red-200'
    }
    return colors[level as keyof typeof colors] || colors.medium
  }

  return (
    <Card className="bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Microscope className="h-5 w-5 text-blue-600" />
          方案概览
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-slate-600">研究类型</label>
              <p className="text-lg font-semibold">{overview.research_type}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">推荐平台</label>
              <p className="text-lg font-semibold">{overview.recommended_platform}</p>
            </div>
          </div>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-slate-600">预估成本</label>
              <p className="text-lg font-semibold">{overview.estimated_cost}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">项目周期</label>
              <p className="text-lg font-semibold">{overview.expected_timeline}</p>
            </div>
          </div>
        </div>

        <Separator />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className={`p-4 rounded-lg border ${getRiskColor(overview.risk_level)}`}>
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              <span className="font-medium">风险等级</span>
            </div>
            <p className="text-lg font-bold mt-1">{overview.risk_level.toUpperCase()}</p>
          </div>
          <div className="p-4 bg-white rounded-lg border border-slate-200">
            <div className="flex items-center gap-2">
              <Star className="h-4 w-4 text-yellow-500" />
              <span className="font-medium">置信度</span>
            </div>
            <div className="flex items-center gap-2 mt-1">
              <Progress value={overview.confidence * 100} className="flex-1" />
              <span className="text-lg font-bold">{Math.round(overview.confidence * 100)}%</span>
            </div>
          </div>
        </div>

        {overview.solution_highlights && (
          <div>
            <h4 className="font-medium mb-2">方案亮点</h4>
            <ul className="space-y-1">
              {overview.solution_highlights.map((highlight: string, index: number) => (
                <li key={index} className="flex items-start gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  {highlight}
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// 研究意图精准搜索面板
function ResearchIntentSearchPanel({ searchData, onSearchClick }: { searchData: any, onSearchClick?: Function }) {
  if (!searchData?.precision_search_links) return null

  const platforms = searchData.precision_search_links.primary_links || {}
  const researchFocus = searchData.research_focus_display || {}

  return (
    <div className="space-y-6">
      <Card className="border-indigo-200 bg-gradient-to-r from-indigo-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-indigo-600" />
            🎯 精准文献搜索
          </CardTitle>
          <p className="text-sm text-slate-600">
            {searchData.precision_search_links.research_summary}
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 研究焦点展示 */}
          <div className="bg-white p-4 rounded-lg border border-slate-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                <Search className="h-5 w-5 text-white" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-slate-800">研究焦点</h4>
                <p className="text-sm bg-slate-50 px-3 py-2 rounded-lg border mt-1">
                  {researchFocus.main_focus || searchData.integrated_keywords?.research_focus}
                </p>
              </div>
            </div>
          </div>

          {/* 一键搜索按钮组 */}
          <div>
            <h4 className="font-medium text-slate-700 mb-3 flex items-center gap-2">
              <Zap className="h-4 w-4 text-yellow-500" />
              一键直达相关文献
            </h4>
            <div className="grid grid-cols-2 gap-3">
              {Object.entries(platforms).map(([key, platform]: [string, any]) => (
                <Button
                  key={key}
                  onClick={() => onSearchClick?.(key, platform.url)}
                  className="flex items-center gap-3 p-4 h-auto bg-gradient-to-r hover:scale-105 transition-all"
                  variant="outline"
                  asChild
                >
                  <a href={platform.url} target="_blank" rel="noopener noreferrer">
                    <span className="text-2xl">{platform.icon}</span>
                    <div className="flex-1 text-left">
                      <div className="font-semibold">{platform.name}</div>
                      <div className="text-xs opacity-90">{platform.description}</div>
                    </div>
                    <ExternalLink className="h-4 w-4 opacity-70" />
                  </a>
                </Button>
              ))}
            </div>
          </div>

          {/* 备选搜索建议 */}
          {searchData.precision_search_links.alternative_queries?.length > 0 && (
            <AlternativeQueriesSection 
              queries={searchData.precision_search_links.alternative_queries}
            />
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// 备选搜索查询组件
function AlternativeQueriesSection({ queries }: { queries: string[] }) {
  const [showAlternatives, setShowAlternatives] = useState(false)

  return (
    <div className="pt-4 border-t border-slate-200">
      <Button
        variant="ghost"
        onClick={() => setShowAlternatives(!showAlternatives)}
        className="flex items-center gap-2 text-sm text-slate-600 hover:text-slate-800"
      >
        {showAlternatives ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        备选搜索建议 ({queries.length}个)
      </Button>

      {showAlternatives && (
        <div className="mt-3 space-y-2">
          {queries.map((query, index) => (
            <div key={index} className="flex items-center gap-2 p-2 bg-slate-50 rounded-lg">
              <span className="text-sm text-slate-700 flex-1">{query}</span>
              <div className="flex gap-1">
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 px-2 text-xs"
                  asChild
                >
                  <a href={`https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(query)}`}
                     target="_blank" rel="noopener noreferrer">
                    PubMed
                  </a>
                </Button>
                <Button
                  size="sm"
                  variant="outline" 
                  className="h-6 px-2 text-xs"
                  asChild
                >
                  <a href={`https://scholar.google.com/scholar?q=${encodeURIComponent(query)}`}
                     target="_blank" rel="noopener noreferrer">
                    Scholar
                  </a>
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

// 关键要素分析组件
function KeyFactorsAnalysis({ factors }: { factors: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-600" />
          关键要素分析
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-green-700 mb-2">成功关键因素</h4>
            <ul className="space-y-1">
              {factors.success_factors?.map((factor: string, index: number) => (
                <li key={index} className="flex items-start gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  {factor}
                </li>
              ))}
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-yellow-700 mb-2">风险关注点</h4>
            <ul className="space-y-1">
              {factors.risk_factors?.map((risk: any, index: number) => (
                <li key={index} className="flex items-start gap-2 text-sm">
                  <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  {typeof risk === 'string' ? risk : risk.description}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// 平台对比矩阵组件
function PlatformComparisonMatrix({ comparison }: { comparison: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-blue-600" />
          技术平台对比
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2">平台</th>
                <th className="text-left p-2">通量</th>
                <th className="text-left p-2">成本</th>
                <th className="text-left p-2">数据质量</th>
                <th className="text-left p-2">推荐度</th>
              </tr>
            </thead>
            <tbody>
              {comparison.comparison_matrix?.map((platform: any, index: number) => (
                <tr key={index} className="border-b hover:bg-slate-50">
                  <td className="p-2 font-medium">{platform.name}</td>
                  <td className="p-2">{platform.throughput}</td>
                  <td className="p-2">{platform.cost}</td>
                  <td className="p-2">{platform.data_quality}</td>
                  <td className="p-2">{platform.推荐度}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  )
}

// 智能文献面板组件
function SmartLiteraturePanel({ recommendations }: { recommendations: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BookOpen className="h-5 w-5 text-green-600" />
          📚 推荐文献精选
        </CardTitle>
        <p className="text-sm text-slate-600">
          {recommendations.strategy}
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {recommendations.key_papers?.map((paper: any, index: number) => (
          <div key={index} className="p-4 border rounded-lg hover:bg-slate-50">
            <h4 className="font-semibold text-slate-800 mb-2">{paper.title}</h4>
            <p className="text-sm text-slate-600 mb-2">{paper.focus}</p>
            <Badge variant="outline" className="text-xs">
              {paper.significance}
            </Badge>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

// 积极的风险评估组件
function PositiveRiskAssessmentCard({ assessment }: { assessment: any }) {
  return (
    <Card className="border-green-200 bg-green-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-green-800">
          <Shield className="h-5 w-5" />
          项目可行性分析
        </CardTitle>
        <CardDescription className="text-green-700">
          基于专业评估，该项目具有很高的成功概率
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 整体可行性 */}
        <div className="bg-white rounded-lg p-4 border border-green-200">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-semibold text-green-800">整体可行性评估</h4>
            <Badge className="bg-green-100 text-green-800 border-green-300">
              {assessment.overall_feasibility || "高度可行"}
            </Badge>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm text-gray-600">成功概率</span>
              <p className="font-semibold text-green-700">{assessment.success_probability || "85-95%"}</p>
            </div>
            <div>
              <span className="text-sm text-gray-600">风险等级</span>
              <p className="font-semibold text-green-700">{assessment.risk_level || "可控风险"}</p>
            </div>
          </div>
        </div>

        {/* 客户优势 */}
        {assessment.client_advantages && (
          <div className="bg-white rounded-lg p-4 border border-green-200">
            <h4 className="font-semibold text-green-800 mb-3">项目优势</h4>
            <div className="space-y-2">
              {assessment.client_advantages.map((advantage: any, index: number) => (
                <div key={index} className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-gray-700">
                    {typeof advantage === 'string' ? advantage : advantage?.name || JSON.stringify(advantage)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 可控风险 */}
        {assessment.manageable_risks && (
          <div className="bg-white rounded-lg p-4 border border-green-200">
            <h4 className="font-semibold text-green-800 mb-3">风险控制措施</h4>
            <div className="space-y-4">
              {Object.entries(assessment.manageable_risks).map(([key, risk]: [string, any]) => (
                <div key={key} className="border-l-4 border-green-400 pl-4">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-gray-800">{risk.level}</span>
                    <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                      {risk.level}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{risk.description}</p>
                  <p className="text-sm text-blue-600 mb-1">
                    <strong>应对措施：</strong>{risk.mitigation}
                  </p>
                  <p className="text-sm text-green-600">
                    <strong>客户收益：</strong>{risk.client_benefit}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 价值主张 */}
        {assessment.value_proposition && (
          <div className="bg-white rounded-lg p-4 border border-green-200">
            <h4 className="font-semibold text-green-800 mb-3">项目价值</h4>
            <div className="grid md:grid-cols-2 gap-4">
              {assessment.value_proposition.immediate_benefits && (
                <div>
                  <h5 className="font-medium text-gray-800 mb-2">即时收益</h5>
                  <ul className="space-y-1">
                    {assessment.value_proposition.immediate_benefits.map((benefit: string, index: number) => (
                      <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                        <ArrowRight className="h-3 w-3 text-green-500 mt-1 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {assessment.value_proposition.long_term_value && (
                <div>
                  <h5 className="font-medium text-gray-800 mb-2">长期价值</h5>
                  <ul className="space-y-1">
                    {assessment.value_proposition.long_term_value.map((value: string, index: number) => (
                      <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                        <ArrowRight className="h-3 w-3 text-green-500 mt-1 flex-shrink-0" />
                        {value}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 信心声明 */}
        {assessment.confidence_statement && (
          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4 border border-green-200">
            <div className="flex items-start gap-3">
              <Award className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-green-800 mb-2">专家信心评估</h4>
                <p className="text-sm text-gray-700">{assessment.confidence_statement}</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// 原有的风险评估矩阵组件（保留作为备用）
function RiskAssessmentMatrix({ assessment }: { assessment: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-yellow-600" />
          风险评估矩阵
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Object.entries(assessment.risk_categories || {}).map(([category, risk]: [string, any]) => (
            <div key={category} className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">{category}</h4>
              <Badge variant="outline" className={`mb-2 ${
                risk.level === 'low' ? 'text-green-600' :
                risk.level === 'medium' ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {risk.level}
              </Badge>
              <p className="text-sm text-slate-600">{risk.impact}</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

// 实施规划卡片组件
function ImplementationPlanCard({ plan }: { plan: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5 text-purple-600" />
          项目实施规划
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-4">
          {Object.entries(plan.project_phases || {}).map(([phase, details]: [string, any]) => (
            <div key={phase} className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">{phase}</h4>
              <div className="space-y-2">
                <div>
                  <span className="text-sm font-medium text-slate-600">任务:</span>
                  <p className="text-sm">{details.tasks?.map((task: any) => typeof task === 'string' ? task : task?.name || JSON.stringify(task)).join(', ')}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-slate-600">交付物:</span>
                  <p className="text-sm">{details.deliverables?.map((deliverable: any) => typeof deliverable === 'string' ? deliverable : deliverable?.name || JSON.stringify(deliverable)).join(', ')}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

// 研究建议组件
function ResearchRecommendationsCard({ recommendations }: { recommendations: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5" />
          专业研究建议
        </CardTitle>
        <p className="text-sm text-slate-600">
          基于您的研究目标和样本特点，为您提供专业的实验设计和分析建议
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 实验策略 */}
        {recommendations.experimental_strategy && (
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <h4 className="font-semibold text-blue-800 mb-3">实验策略建议</h4>

            {recommendations.experimental_strategy.primary_objectives && (
              <div className="mb-4">
                <h5 className="font-medium text-gray-800 mb-2">主要目标</h5>
                <ul className="space-y-1">
                  {recommendations.experimental_strategy.primary_objectives.map((objective: string, index: number) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                      <Target className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                      {objective}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {recommendations.experimental_strategy.recommended_approach && (
              <div className="mb-4">
                <h5 className="font-medium text-gray-800 mb-2">推荐方法</h5>
                <ul className="space-y-1">
                  {recommendations.experimental_strategy.recommended_approach.map((approach: string, index: number) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                      <ArrowRight className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                      {approach}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {recommendations.experimental_strategy.quality_control && (
              <div>
                <h5 className="font-medium text-gray-800 mb-2">质量控制</h5>
                <ul className="space-y-1">
                  {recommendations.experimental_strategy.quality_control.map((qc: string, index: number) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                      <Shield className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                      {qc}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* 数据分析策略 */}
        {recommendations.data_analysis_strategy && (
          <div className="bg-green-50 rounded-lg p-4 border border-green-200">
            <h4 className="font-semibold text-green-800 mb-3">数据分析策略</h4>

            <div className="grid md:grid-cols-2 gap-4">
              {recommendations.data_analysis_strategy.core_analyses && (
                <div>
                  <h5 className="font-medium text-gray-800 mb-2">核心分析</h5>
                  <ul className="space-y-1">
                    {recommendations.data_analysis_strategy.core_analyses.map((analysis: string, index: number) => (
                      <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                        <BarChart3 className="h-3 w-3 text-green-500 mt-1 flex-shrink-0" />
                        {analysis}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {recommendations.data_analysis_strategy.advanced_analyses && (
                <div>
                  <h5 className="font-medium text-gray-800 mb-2">高级分析</h5>
                  <ul className="space-y-1">
                    {recommendations.data_analysis_strategy.advanced_analyses.map((analysis: string, index: number) => (
                      <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                        <TrendingUp className="h-3 w-3 text-green-500 mt-1 flex-shrink-0" />
                        {analysis}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 成功优化建议 */}
        {recommendations.success_optimization && (
          <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
            <h4 className="font-semibold text-yellow-800 mb-3">成功关键因素</h4>

            {recommendations.success_optimization.critical_factors && (
              <div className="mb-4">
                <h5 className="font-medium text-gray-800 mb-2">关键考虑因素</h5>
                <ul className="space-y-1">
                  {recommendations.success_optimization.critical_factors.map((factor: string, index: number) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                      <AlertTriangle className="h-3 w-3 text-yellow-500 mt-1 flex-shrink-0" />
                      {factor}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {recommendations.success_optimization.expected_outcomes && (
              <div>
                <h5 className="font-medium text-gray-800 mb-2">预期成果</h5>
                <ul className="space-y-1">
                  {recommendations.success_optimization.expected_outcomes.map((outcome: string, index: number) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                      <Award className="h-3 w-3 text-yellow-500 mt-1 flex-shrink-0" />
                      {outcome}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* 后续建议 */}
        {recommendations.follow_up_suggestions && (
          <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
            <h4 className="font-semibold text-purple-800 mb-3">后续研究建议</h4>
            <ul className="space-y-2">
              {recommendations.follow_up_suggestions.map((suggestion: string, index: number) => (
                <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                  <ArrowRight className="h-3 w-3 text-purple-500 mt-1 flex-shrink-0" />
                  {suggestion}
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// 增强的文献推荐组件（合并精准搜索和文献推荐功能）
function EnhancedLiteraturePanel({ recommendations, onSearchClick }: { recommendations: any, onSearchClick?: Function }) {
  return (
    <div className="space-y-6">
      {/* 精准搜索链接 */}
      {recommendations.precision_search_links && (
        <Card className="border-indigo-200 bg-gradient-to-r from-indigo-50 to-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-indigo-600" />
              🎯 精准文献搜索
            </CardTitle>
            <p className="text-sm text-slate-600">
              {recommendations.precision_search_links.description}
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 主要搜索 */}
            {recommendations.precision_search_links.primary_search && (
              <div className="bg-white p-4 rounded-lg border border-slate-200">
                <h4 className="font-medium text-slate-800 mb-3">主要搜索查询</h4>
                <p className="text-sm bg-slate-50 px-3 py-2 rounded-lg border mb-3">
                  {recommendations.precision_search_links.primary_search.query}
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {Object.entries(recommendations.precision_search_links.primary_search.platforms || {}).map(([key, url]: [string, any]) => (
                    <Button
                      key={key}
                      onClick={() => onSearchClick?.(key, url)}
                      className="flex items-center gap-2 p-3 h-auto"
                      variant="outline"
                      asChild
                    >
                      <a href={url} target="_blank" rel="noopener noreferrer">
                        <span className="font-semibold">{key.toUpperCase()}</span>
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* 备选搜索 */}
            {recommendations.precision_search_links.alternative_searches?.length > 0 && (
              <div className="bg-white p-4 rounded-lg border border-slate-200">
                <h4 className="font-medium text-slate-800 mb-3">备选搜索查询</h4>
                <div className="space-y-2">
                  {recommendations.precision_search_links.alternative_searches.map((search: any, index: number) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-slate-50 rounded-lg">
                      <span className="text-sm text-slate-700 flex-1">{search.query}</span>
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" className="h-6 px-2 text-xs" asChild>
                          <a href={search.pubmed_link} target="_blank" rel="noopener noreferrer">
                            PubMed
                          </a>
                        </Button>
                        <Button size="sm" variant="outline" className="h-6 px-2 text-xs" asChild>
                          <a href={search.scholar_link} target="_blank" rel="noopener noreferrer">
                            Scholar
                          </a>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 精选文献推荐 */}
      {recommendations.curated_literature && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-green-600" />
              📚 精选文献推荐
            </CardTitle>
            <p className="text-sm text-slate-600">
              基于您的研究需求精心筛选的高质量文献
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 核心文献 */}
            {recommendations.curated_literature.core_papers?.length > 0 && (
              <div>
                <h4 className="font-medium text-slate-800 mb-3">核心文献</h4>
                <div className="space-y-3">
                  {recommendations.curated_literature.core_papers.map((paper: any, index: number) => (
                    <div key={index} className="p-3 border rounded-lg hover:bg-slate-50">
                      <h5 className="font-semibold text-slate-800 text-sm mb-1">{paper.title}</h5>
                      <p className="text-xs text-slate-600 mb-2">{paper.authors} - {paper.journal}</p>
                      <Badge variant="outline" className="text-xs">
                        {paper.significance || "核心参考"}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 最新进展 */}
            {recommendations.curated_literature.recent_advances?.length > 0 && (
              <div>
                <h4 className="font-medium text-slate-800 mb-3">最新研究进展</h4>
                <div className="space-y-3">
                  {recommendations.curated_literature.recent_advances.map((paper: any, index: number) => (
                    <div key={index} className="p-3 border rounded-lg hover:bg-slate-50">
                      <h5 className="font-semibold text-slate-800 text-sm mb-1">{paper.title}</h5>
                      <p className="text-xs text-slate-600 mb-2">{paper.authors} - {paper.journal}</p>
                      <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">
                        最新进展
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 搜索优化建议 */}
      {recommendations.search_optimization && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5 text-purple-600" />
              🔍 搜索优化建议
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 英文关键词 */}
            {recommendations.search_optimization.english_keywords?.length > 0 && (
              <div>
                <h4 className="font-medium text-slate-800 mb-2">推荐英文关键词</h4>
                <div className="flex flex-wrap gap-2">
                  {recommendations.search_optimization.english_keywords.map((keyword: string, index: number) => (
                    <Badge key={index} variant="outline" className="bg-purple-50 text-purple-700">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* 搜索技巧 */}
            {recommendations.search_optimization.search_tips?.length > 0 && (
              <div>
                <h4 className="font-medium text-slate-800 mb-2">搜索技巧</h4>
                <ul className="space-y-1">
                  {recommendations.search_optimization.search_tips.map((tip: string, index: number) => (
                    <li key={index} className="text-sm text-slate-600 flex items-start gap-2">
                      <Lightbulb className="h-3 w-3 text-yellow-500 mt-1 flex-shrink-0" />
                      {tip}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}