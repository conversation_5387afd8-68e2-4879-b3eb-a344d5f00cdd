'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Sparkles, 
  BookOpen, 
  TrendingUp, 
  Award, 
  DollarSign, 
  Clock, 
  AlertTriangle,
  CheckCircle,
  Star,
  Lightbulb,
  Target,
  Users
} from 'lucide-react'

const DEMO_INTELLIGENT_RESPONSE = {
  solution_id: "intelligent_demo_2024",
  generated_at: new Date().toISOString(),
  client_requirements: {
    speciesType: "大鼠 (Rattus norvegicus)",
    experimentType: "单细胞RNA测序 (scRNA-seq)", 
    researchGoal: "免疫细胞功能分析",
    sampleType: "心脏组织",
    budgetRange: "10-20万",
    urgency: "非常紧急（加急处理）"
  },
  recommended_solution: {
    platform: "10x Genomics Chromium (智能推荐)",
    reasoning: "基于您的免疫细胞功能分析研究需求，我们推荐使用10x Genomics平台。该平台在处理心脏组织样本方面具有显著优势，特别适合5,000-10,000规模的细胞分析。"
  },
  cost_analysis: {
    total_cost: 25000,
    sample_preparation: { cost: 10000, description: "文库构建和试剂" },
    sequencing: { cost: 10000, description: "50K reads/cell测序" },
    data_analysis: { cost: 5000, description: "生信分析和报告" },
    breakdown: "详细成本已针对您的预算范围优化"
  },
  risk_assessment: {
    overall_risk_level: "低-中等",
    success_probability: "85-90%",
    technical_risks: [
      {
        risk: "心脏组织组织解离难度",
        probability: "中等",
        impact: "可能影响细胞回收率",
        mitigation: "优化酶解条件，预实验验证"
      }
    ]
  },
  timeline: {
    total: "30-43天",
    sample_prep: "5-7天",
    sequencing: "7-10天",
    analysis: "10-14天"
  },
  deliverables: [
    "🧬 智能文献推荐报告 (基于AI关键词扩展)",
    "📊 多平台技术对比分析 (10x vs BD vs Parse)",
    "⚠️ 项目风险评估与缓解策略",
    "🎯 个性化实验方案设计",
    "📈 预期成果与发表策略规划"
  ],
  next_steps: [
    "📚 查看智能文献推荐和热点论文发现",
    "🏆 对比多个技术平台的详细规格和成本",
    "🤝 查看专家合作机会和资源推荐",
    "🚀 制定基于AI洞察的项目实施计划"
  ],
  contact_info: {
    email: "<EMAIL>",
    phone: "400-AI-CELL (************)"
  },
  intelligent_features: {
    literature_recommendations: {
      hot_papers: [
        {
          title: "Single-cell reconstruction of cardiac immune landscape during development",
          journal: "Nature",
          impact_factor: 64.8,
          reason: "与您的心脏免疫细胞研究高度相关，提供了最新的单细胞分析方法"
        },
        {
          title: "Cardiac macrophage heterogeneity revealed by single-cell RNA sequencing",
          journal: "Cell",
          impact_factor: 66.8,
          reason: "心脏巨噬细胞异质性研究的权威文献，方法学可直接应用"
        }
      ],
      expanded_keywords: [
        "cardiac macrophages", "immune cell trajectory", 
        "heart inflammation scRNA-seq", "cardiac tissue immune profiling",
        "single-cell immunology 2024", "AI-driven immune analysis"
      ]
    },
    smart_insights: [
      "🎯 基于您的非常紧急（加急处理）需求，推荐加急处理流程，可缩短20-30%时间",
      "💡 10-20万预算下的最优性价比方案，10x Genomics平台综合评分最高",
      "🔬 心脏组织样本的专业处理建议：温和酶解离，保持免疫细胞活性",
      "📊 针对免疫细胞功能分析的优化策略：增加免疫标记物检测"
    ],
    collaboration_opportunities: [
      {
        institution: "中科院生物化学所",
        expert: "免疫细胞发育研究团队",
        collaboration_type: "技术指导和数据分析合作"
      },
      {
        institution: "清华大学医学院",
        expert: "心脏免疫学实验室",
        collaboration_type: "样本处理技术交流"
      }
    ]
  }
}

export default function TestIntelligentRecommendationPage() {
  const [showDetails, setShowDetails] = useState(false)
  const response = DEMO_INTELLIGENT_RESPONSE

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="h-8 w-8 text-blue-500" />
            <h1 className="text-3xl font-bold">智能推荐系统演示</h1>
          </div>
          <p className="text-xl text-gray-600 mb-4">
            展示CellForge AI升级后的智能推荐功能
          </p>
          <Button 
            onClick={() => setShowDetails(!showDetails)}
            className="mb-6"
          >
            {showDetails ? '隐藏详情' : '查看智能推荐详情'}
          </Button>
        </div>

        {/* 主要推荐卡片 */}
        <Card className="mb-8 border-2 border-blue-200">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
            <CardTitle className="flex items-center gap-2 text-2xl">
              <Target className="h-6 w-6 text-blue-600" />
              🎯 CellForge AI 智能技术方案
            </CardTitle>
            <CardDescription>
              方案编号: {response.solution_id}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {response.recommended_solution.platform}
                </div>
                <div className="text-sm text-gray-600">推荐平台</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  ¥{(response.cost_analysis.total_cost / 10000).toFixed(1)}万
                </div>
                <div className="text-sm text-gray-600">预估总成本</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {response.timeline.total}
                </div>
                <div className="text-sm text-gray-600">项目周期</div>
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg mb-6">
              <h3 className="font-semibold text-blue-800 mb-2">智能推荐理由</h3>
              <p className="text-blue-700">{response.recommended_solution.reasoning}</p>
            </div>
          </CardContent>
        </Card>

        {showDetails && (
          <div className="space-y-6">
            {/* 智能文献推荐 */}
            <Card className="border-l-4 border-l-blue-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5 text-blue-500" />
                  🧬 智能文献推荐报告
                  <Badge variant="default" className="bg-blue-600">AI智能推荐</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2 text-red-600">🔥 热点文献发现</h4>
                    {response.intelligent_features.literature_recommendations.hot_papers.map((paper, index) => (
                      <div key={index} className="border rounded-lg p-3 mb-3">
                        <h5 className="font-medium mb-1">{paper.title}</h5>
                        <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                          <span className="font-medium">{paper.journal}</span>
                          <Badge variant="outline">IF: {paper.impact_factor}</Badge>
                        </div>
                        <div className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
                          <TrendingUp className="h-4 w-4 inline mr-1" />
                          {paper.reason}
                        </div>
                      </div>
                    ))}
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-semibold mb-2 text-green-600">🔍 智能关键词扩展</h4>
                    <div className="flex flex-wrap gap-2">
                      {response.intelligent_features.literature_recommendations.expanded_keywords.map((keyword, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 多平台技术对比 */}
            <Card className="border-l-4 border-l-green-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-green-500" />
                  📊 多平台技术对比分析
                  <Badge variant="default" className="bg-green-600">AI智能推荐</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-green-800 mb-2">成本分析</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-600">
                        ¥{(response.cost_analysis.sample_preparation.cost / 10000).toFixed(1)}万
                      </div>
                      <div className="text-sm text-gray-600">样本制备</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-600">
                        ¥{(response.cost_analysis.sequencing.cost / 10000).toFixed(1)}万
                      </div>
                      <div className="text-sm text-gray-600">测序分析</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-600">
                        ¥{(response.cost_analysis.data_analysis.cost / 10000).toFixed(1)}万
                      </div>
                      <div className="text-sm text-gray-600">数据分析</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* AI洞察和建议 */}
            <Card className="border-l-4 border-l-purple-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-purple-500" />
                  💡 AI智能洞察
                  <Badge variant="default" className="bg-purple-600">AI智能推荐</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {response.intelligent_features.smart_insights.map((insight, index) => (
                    <div key={index} className="bg-purple-50 p-3 rounded-lg">
                      <div className="text-purple-700">{insight}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 合作机会推荐 */}
            <Card className="border-l-4 border-l-orange-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-orange-500" />
                  🤝 专家合作机会推荐
                  <Badge variant="default" className="bg-orange-600">AI智能推荐</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {response.intelligent_features.collaboration_opportunities.map((collab, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <h4 className="font-semibold mb-1">{collab.institution}</h4>
                      <div className="text-blue-600 font-medium text-sm mb-2">{collab.expert}</div>
                      <Badge variant="outline" className="text-xs">
                        {collab.collaboration_type}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 风险评估 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  ⚠️ 项目风险评估与缓解策略
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">整体风险等级</span>
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                      {response.risk_assessment.overall_risk_level}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="font-medium">成功概率</span>
                    <span className="font-semibold text-green-600">
                      {response.risk_assessment.success_probability}
                    </span>
                  </div>
                </div>

                {response.risk_assessment.technical_risks.map((risk, index) => (
                  <Card key={index} className="border-l-4 border-l-orange-500 mb-3">
                    <CardContent className="pt-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-semibold">{risk.risk}</h4>
                        <Badge variant="outline">{risk.probability}</Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{risk.impact}</p>
                      <div className="bg-green-50 p-3 rounded">
                        <div className="font-medium text-green-800 text-sm mb-1">缓解策略</div>
                        <div className="text-green-700 text-sm">{risk.mitigation}</div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </CardContent>
            </Card>

            {/* 交付物清单 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  📦 智能化交付物清单
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {response.deliverables.map((deliverable, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      <span className="text-sm">{deliverable}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 下一步行动 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-500" />
                  🚀 智能化下一步行动
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {response.next_steps.map((step, index) => (
                    <div key={index} className="flex items-center gap-2 p-3 border rounded-lg hover:bg-blue-50 transition-colors">
                      <div className="w-6 h-6 bg-blue-600 text-white rounded-full text-xs flex items-center justify-center font-semibold">
                        {index + 1}
                      </div>
                      <span className="text-sm">{step}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 底部签名 */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="text-center">
            <p className="text-sm text-gray-500 italic mb-2">
              🧬 CellForge AI - 您的智能单细胞测序专业伙伴
            </p>
            <div className="flex justify-center gap-4 text-sm text-gray-600">
              <span>置信度: 95%</span>
              <span>•</span>
              <span>来源: AI智能推荐引擎, 专业知识库, 热点文献发现</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}